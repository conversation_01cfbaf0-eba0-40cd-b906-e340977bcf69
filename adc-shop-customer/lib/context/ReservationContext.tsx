"use client";

import React, { createContext, useContext, useState } from 'react';

export interface Reservation {
  id: string;
  date: string;
  time: string;
  restaurant: string;
  partySize: number;
  status: 'confirmed' | 'pending' | 'cancelled' | 'completed';
  location?: string;
  specialRequests?: string;
  customerName?: string;
  phoneNumber?: string;
  email?: string;
  createdAt: Date;
}

interface ReservationContextType {
  reservations: Reservation[];
  createReservation: (reservation: Omit<Reservation, 'id' | 'createdAt'>) => string;
  updateReservation: (id: string, updates: Partial<Reservation>) => void;
  cancelReservation: (id: string) => void;
  getReservationsByStatus: (status: Reservation['status']) => Reservation[];
}

const ReservationContext = createContext<ReservationContextType | undefined>(undefined);

export function ReservationProvider({ children }: { children: React.ReactNode }) {
  const [reservations, setReservations] = useState<Reservation[]>([
    {
      id: "RES-001",
      date: "July 15, 2024",
      time: "7:00 PM",
      restaurant: "The Golden Spoon",
      partySize: 4,
      status: "confirmed",
      location: "Downtown",
      specialRequests: "Window table preferred",
      customerName: "John Doe",
      phoneNumber: "+****************",
      email: "<EMAIL>",
      createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    },
    {
      id: "RES-002", 
      date: "July 20, 2024",
      time: "8:30 PM",
      restaurant: "The Cozy Corner",
      partySize: 2,
      status: "pending",
      location: "Midtown",
      customerName: "Jane Smith",
      phoneNumber: "+****************",
      email: "<EMAIL>",
      createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
    },
    {
      id: "RES-003",
      date: "August 5, 2024", 
      time: "6:00 PM",
      restaurant: "The Seaside Grill",
      partySize: 6,
      status: "cancelled",
      location: "Waterfront",
      specialRequests: "Outdoor seating if available",
      customerName: "Mike Johnson",
      phoneNumber: "+****************",
      email: "<EMAIL>",
      createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000)
    },
    {
      id: "RES-004",
      date: "June 28, 2024",
      time: "7:30 PM", 
      restaurant: "Bella Vista",
      partySize: 3,
      status: "completed",
      location: "Uptown",
      customerName: "Sarah Wilson",
      phoneNumber: "+****************",
      email: "<EMAIL>",
      createdAt: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000)
    }
  ]);

  const createReservation = (reservationData: Omit<Reservation, 'id' | 'createdAt'>): string => {
    const id = `RES-${Date.now()}`;
    const newReservation: Reservation = {
      ...reservationData,
      id,
      createdAt: new Date()
    };

    setReservations(prev => [newReservation, ...prev]);
    return id;
  };

  const updateReservation = (id: string, updates: Partial<Reservation>) => {
    setReservations(prev =>
      prev.map(reservation =>
        reservation.id === id ? { ...reservation, ...updates } : reservation
      )
    );
  };

  const cancelReservation = (id: string) => {
    updateReservation(id, { status: 'cancelled' });
  };

  const getReservationsByStatus = (status: Reservation['status']) => {
    return reservations.filter(reservation => reservation.status === status);
  };

  const value = {
    reservations,
    createReservation,
    updateReservation,
    cancelReservation,
    getReservationsByStatus
  };

  return (
    <ReservationContext.Provider value={value}>
      {children}
    </ReservationContext.Provider>
  );
}

export function useReservations() {
  const context = useContext(ReservationContext);
  if (context === undefined) {
    throw new Error('useReservations must be used within a ReservationProvider');
  }
  return context;
}
