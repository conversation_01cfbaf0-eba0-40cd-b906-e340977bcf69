{"name": "@img/sharp-darwin-arm64", "version": "0.34.2", "description": "Prebuilt sharp for use with macOS 64-bit ARM", "author": "<PERSON><PERSON> <<EMAIL>>", "homepage": "https://sharp.pixelplumbing.com", "repository": {"type": "git", "url": "git+https://github.com/lovell/sharp.git", "directory": "npm/darwin-arm64"}, "license": "Apache-2.0", "funding": {"url": "https://opencollective.com/libvips"}, "preferUnplugged": true, "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.1.0"}, "files": ["lib"], "publishConfig": {"access": "public"}, "type": "commonjs", "exports": {"./sharp.node": "./lib/sharp-darwin-arm64.node", "./package": "./package.json"}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "os": ["darwin"], "cpu": ["arm64"]}