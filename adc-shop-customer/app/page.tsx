"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { List, Grid3X3 } from "lucide-react";
import Header from "@/components/Header";
import { useCart } from "@/lib/context/CartContext";

export default function MenuPage() {
  const { addToCart } = useCart();
  const menuItems = [
    {
      id: 1,
      name: "Crispy Calamari",
      category: "Vegetarian",
      description: "Tender calamari, lightly battered and fried to a golden crisp. Served with a side of zesty marinara sauce.",
      image: "https://images.unsplash.com/photo-1599487488170-d11ec9c172f0?w=400&h=300&fit=crop",
      price: "$14.99"
    },
    {
      id: 2,
      name: "Grilled Salmon",
      category: "Chef's Special",
      description: "Fresh Atlantic salmon, grilled to perfection and topped with a lemon-dill sauce. Served with roasted vegetables and mashed potatoes.",
      image: "https://images.unsplash.com/photo-1467003909585-2f8a72700288?w=400&h=300&fit=crop",
      price: "$26.99"
    },
    {
      id: 3,
      name: "Pasta Primavera",
      category: "Vegetarian",
      description: "A vibrant mix of seasonal vegetables tossed with al dente pasta in a light garlic and olive oil sauce. Add grilled chicken or shrimp for an extra charge.",
      image: "https://images.unsplash.com/photo-1621996346565-e3dbc353d2e5?w=400&h=300&fit=crop",
      price: "$18.99"
    },
    {
      id: 4,
      name: "New York Strip Steak",
      category: "Chef's Special",
      description: "A classic cut of beef, grilled to your liking and served with a side of creamy horseradish sauce. Accompanied by a baked potato and steamed asparagus.",
      image: "https://images.unsplash.com/photo-1546833999-b9f581a1996d?w=400&h=300&fit=crop",
      price: "$32.99"
    },
    {
      id: 5,
      name: "Chocolate Lava Cake",
      category: "Spicy",
      description: "A decadent chocolate cake with a molten chocolate center. Served warm with a scoop of vanilla bean ice cream.",
      image: "https://images.unsplash.com/photo-1606313564200-e75d5e30476c?w=400&h=300&fit=crop",
      price: "$9.99"
    }
  ];

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-background">
      <div className="layout-container flex h-full grow flex-col">
        <Header />

        {/* Main Content */}
        <div className="px-40 flex flex-1 justify-center py-5">
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
            <h2 className="text-foreground tracking-light text-[28px] font-bold leading-tight px-4 text-left pb-3 pt-5">Table 7</h2>

            {/* Filter Tags */}
            <div className="flex gap-3 p-3 flex-wrap pr-4">
              <Badge variant="secondary" className="h-8 px-4">Vegetarian</Badge>
              <Badge variant="secondary" className="h-8 px-4">Chef's Special</Badge>
              <Badge variant="secondary" className="h-8 px-4">Spicy</Badge>
            </div>

            {/* Tab Navigation */}
            <div className="pb-3">
              <div className="flex border-b border-border px-4 gap-8">
                <a className="flex flex-col items-center justify-center border-b-[3px] border-b-primary text-foreground pb-[13px] pt-4" href="#">
                  <p className="text-foreground text-sm font-bold leading-normal tracking-[0.015em]">Featured</p>
                </a>
                <a className="flex flex-col items-center justify-center border-b-[3px] border-b-transparent text-muted-foreground pb-[13px] pt-4" href="#">
                  <p className="text-muted-foreground text-sm font-bold leading-normal tracking-[0.015em]">Appetizers</p>
                </a>
                <a className="flex flex-col items-center justify-center border-b-[3px] border-b-transparent text-muted-foreground pb-[13px] pt-4" href="#">
                  <p className="text-muted-foreground text-sm font-bold leading-normal tracking-[0.015em]">Main Courses</p>
                </a>
              </div>
            </div>

            {/* View Toggle */}
            <div className="flex justify-between gap-2 px-4 py-3">
              <div className="flex gap-2">
                <Button variant="ghost" size="sm" className="p-2">
                  <List className="h-6 w-6" />
                </Button>
                <Button variant="ghost" size="sm" className="p-2">
                  <Grid3X3 className="h-6 w-6" />
                </Button>
              </div>
            </div>

            {/* Menu Items */}
            {menuItems.map((item) => (
              <div key={item.id} className="p-4">
                <div className="flex flex-col items-stretch justify-start rounded-xl xl:flex-row xl:items-start">
                  <div
                    className="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl"
                    style={{ backgroundImage: `url("${item.image}")` }}
                  />
                  <div className="flex w-full min-w-72 grow flex-col items-stretch justify-center gap-1 py-4 xl:px-4">
                    <p className="text-muted-foreground text-sm font-normal leading-normal">{item.category}</p>
                    <p className="text-foreground text-lg font-bold leading-tight tracking-[-0.015em]">{item.name}</p>
                    <div className="flex items-end gap-3 justify-between">
                      <p className="text-muted-foreground text-base font-normal leading-normal">
                        {item.description}
                      </p>
                      <div className="flex flex-col items-end gap-2">
                        <p className="text-foreground text-lg font-bold">{item.price}</p>
                        <Button
                          className="bg-secondary text-secondary-foreground hover:bg-secondary/80"
                          onClick={() => addToCart({
                            id: item.id,
                            name: item.name,
                            description: item.description,
                            image: item.image,
                            price: parseFloat(item.price.replace('$', ''))
                          })}
                        >
                          Add to Cart
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
