import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Search, ShoppingCart, List, Grid3X3 } from "lucide-react";

export default function MenuPage() {
  const menuItems = [
    {
      id: 1,
      name: "Crispy <PERSON>",
      category: "Vegetarian",
      description: "Tender calamari, lightly battered and fried to a golden crisp. Served with a side of zesty marinara sauce.",
      image: "https://images.unsplash.com/photo-1599487488170-d11ec9c172f0?w=400&h=300&fit=crop",
      price: "$14.99"
    },
    {
      id: 2,
      name: "Grilled Salmon",
      category: "Chef's Special",
      description: "Fresh Atlantic salmon, grilled to perfection and topped with a lemon-dill sauce. Served with roasted vegetables and mashed potatoes.",
      image: "https://images.unsplash.com/photo-1467003909585-2f8a72700288?w=400&h=300&fit=crop",
      price: "$26.99"
    },
    {
      id: 3,
      name: "Pasta Primavera",
      category: "Vegetarian",
      description: "A vibrant mix of seasonal vegetables tossed with al dente pasta in a light garlic and olive oil sauce. Add grilled chicken or shrimp for an extra charge.",
      image: "https://images.unsplash.com/photo-1621996346565-e3dbc353d2e5?w=400&h=300&fit=crop",
      price: "$18.99"
    },
    {
      id: 4,
      name: "New York Strip Steak",
      category: "Chef's Special",
      description: "A classic cut of beef, grilled to your liking and served with a side of creamy horseradish sauce. Accompanied by a baked potato and steamed asparagus.",
      image: "https://images.unsplash.com/photo-1546833999-b9f581a1996d?w=400&h=300&fit=crop",
      price: "$32.99"
    },
    {
      id: 5,
      name: "Chocolate Lava Cake",
      category: "Spicy",
      description: "A decadent chocolate cake with a molten chocolate center. Served warm with a scoop of vanilla bean ice cream.",
      image: "https://images.unsplash.com/photo-1606313564200-e75d5e30476c?w=400&h=300&fit=crop",
      price: "$9.99"
    }
  ];

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-background">
      <div className="layout-container flex h-full grow flex-col">
        {/* Header */}
        <header className="flex items-center justify-between whitespace-nowrap border-b border-solid border-border px-10 py-3">
          <div className="flex items-center gap-4 text-foreground">
            <div className="size-4">
              <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M13.8261 17.4264C16.7203 18.1174 20.2244 18.5217 24 18.5217C27.7756 18.5217 31.2797 18.1174 34.1739 17.4264C36.9144 16.7722 39.9967 15.2331 41.3563 14.1648L24.8486 40.6391C24.4571 41.267 23.5429 41.267 23.1514 40.6391L6.64374 14.1648C8.00331 15.2331 11.0856 16.7722 13.8261 17.4264Z"
                  fill="currentColor"
                />
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M39.998 12.236C39.9944 12.2537 39.9875 12.2845 39.9748 12.3294C39.9436 12.4399 39.8949 12.5741 39.8346 12.7175C39.8168 12.7597 39.7989 12.8007 39.7813 12.8398C38.5103 13.7113 35.9788 14.9393 33.7095 15.4811C30.9875 16.131 27.6413 16.5217 24 16.5217C20.3587 16.5217 17.0125 16.131 14.2905 15.4811C12.0012 14.9346 9.44505 13.6897 8.18538 12.8168C8.17384 12.7925 8.16216 12.767 8.15052 12.7408C8.09919 12.6249 8.05721 12.5114 8.02977 12.411C8.00356 12.3152 8.00039 12.2667 8.00004 12.2612C8.00004 12.261 8 12.2607 8.00004 12.2612C8.00004 12.2359 8.0104 11.9233 8.68485 11.3686C9.34546 10.8254 10.4222 10.2469 11.9291 9.72276C14.9242 8.68098 19.1919 8 24 8C28.8081 8 33.0758 8.68098 36.0709 9.72276C37.5778 10.2469 38.6545 10.8254 39.3151 11.3686C39.9006 11.8501 39.9857 12.1489 39.998 12.236Z"
                  fill="currentColor"
                />
              </svg>
            </div>
            <h2 className="text-foreground text-lg font-bold leading-tight tracking-[-0.015em]">DineEase</h2>
          </div>
          <div className="flex flex-1 justify-end gap-8">
            <div className="flex items-center gap-9">
              <a className="text-foreground text-sm font-medium leading-normal" href="#">Menu</a>
              <a className="text-muted-foreground text-sm font-medium leading-normal" href="#">Order</a>
              <a className="text-muted-foreground text-sm font-medium leading-normal" href="#">Account</a>
            </div>
            <div className="flex gap-2">
              <Button variant="ghost" size="sm" className="h-10 w-10 p-0 bg-muted">
                <Search className="h-5 w-5" />
              </Button>
              <Button variant="ghost" size="sm" className="h-10 w-10 p-0 bg-muted">
                <ShoppingCart className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <div className="px-40 flex flex-1 justify-center py-5">
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
            <h2 className="text-foreground tracking-light text-[28px] font-bold leading-tight px-4 text-left pb-3 pt-5">Table 7</h2>
            
            {/* Filter Tags */}
            <div className="flex gap-3 p-3 flex-wrap pr-4">
              <Badge variant="secondary" className="h-8 px-4">Vegetarian</Badge>
              <Badge variant="secondary" className="h-8 px-4">Chef's Special</Badge>
              <Badge variant="secondary" className="h-8 px-4">Spicy</Badge>
            </div>

            {/* Tab Navigation */}
            <div className="pb-3">
              <div className="flex border-b border-border px-4 gap-8">
                <a className="flex flex-col items-center justify-center border-b-[3px] border-b-primary text-foreground pb-[13px] pt-4" href="#">
                  <p className="text-foreground text-sm font-bold leading-normal tracking-[0.015em]">Featured</p>
                </a>
                <a className="flex flex-col items-center justify-center border-b-[3px] border-b-transparent text-muted-foreground pb-[13px] pt-4" href="#">
                  <p className="text-muted-foreground text-sm font-bold leading-normal tracking-[0.015em]">Appetizers</p>
                </a>
                <a className="flex flex-col items-center justify-center border-b-[3px] border-b-transparent text-muted-foreground pb-[13px] pt-4" href="#">
                  <p className="text-muted-foreground text-sm font-bold leading-normal tracking-[0.015em]">Main Courses</p>
                </a>
              </div>
            </div>

            {/* View Toggle */}
            <div className="flex justify-between gap-2 px-4 py-3">
              <div className="flex gap-2">
                <Button variant="ghost" size="sm" className="p-2">
                  <List className="h-6 w-6" />
                </Button>
                <Button variant="ghost" size="sm" className="p-2">
                  <Grid3X3 className="h-6 w-6" />
                </Button>
              </div>
            </div>

            {/* Menu Items */}
            {menuItems.map((item) => (
              <div key={item.id} className="p-4">
                <div className="flex flex-col items-stretch justify-start rounded-xl xl:flex-row xl:items-start">
                  <div
                    className="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl"
                    style={{ backgroundImage: `url("${item.image}")` }}
                  />
                  <div className="flex w-full min-w-72 grow flex-col items-stretch justify-center gap-1 py-4 xl:px-4">
                    <p className="text-muted-foreground text-sm font-normal leading-normal">{item.category}</p>
                    <p className="text-foreground text-lg font-bold leading-tight tracking-[-0.015em]">{item.name}</p>
                    <div className="flex items-end gap-3 justify-between">
                      <p className="text-muted-foreground text-base font-normal leading-normal">
                        {item.description}
                      </p>
                      <div className="flex flex-col items-end gap-2">
                        <p className="text-foreground text-lg font-bold">{item.price}</p>
                        <Button className="bg-secondary text-secondary-foreground hover:bg-secondary/80">
                          Add to Cart
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
