"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ShoppingCart, Minus, Plus, Trash2 } from "lucide-react";
import Header from "@/components/Header";
import { useCart } from "@/lib/context/CartContext";

export default function CartPage() {
  const { cartItems, updateQuantity, removeFromCart } = useCart();

  const subtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const tax = subtotal * 0.08;
  const deliveryFee = 2.99;
  const discount = -2.00;
  const total = subtotal + tax + deliveryFee + discount;

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-background">
      <div className="layout-container flex h-full grow flex-col">
        <Header />

        {/* Main Content */}
        <div className="px-40 flex flex-1 justify-center py-5">
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
            <div className="flex flex-wrap justify-between gap-3 p-4">
              <p className="text-foreground tracking-light text-[32px] font-bold leading-tight min-w-72">Your Cart</p>
            </div>

            {/* Cart Items */}
            {cartItems.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-16">
                <ShoppingCart className="h-16 w-16 text-muted-foreground mb-4" />
                <p className="text-foreground text-lg font-medium mb-2">Your cart is empty</p>
                <p className="text-muted-foreground text-sm mb-6">Add some delicious items from our menu</p>
                <Button asChild>
                  <a href="/">Browse Menu</a>
                </Button>
              </div>
            ) : (
              <>
                {cartItems.map((item) => (
                  <div key={item.id} className="flex items-center gap-4 bg-card px-4 min-h-[72px] py-2 justify-between border-b border-border">
                    <div className="flex items-center gap-4">
                      <div
                        className="bg-center bg-no-repeat aspect-square bg-cover rounded-lg size-14"
                        style={{ backgroundImage: `url("${item.image}")` }}
                      />
                      <div className="flex flex-col justify-center">
                        <p className="text-foreground text-base font-medium leading-normal line-clamp-1">{item.name}</p>
                        <p className="text-muted-foreground text-sm font-normal leading-normal line-clamp-2">{item.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={() => updateQuantity(item.id, item.quantity - 1)}
                        >
                          <Minus className="h-4 w-4" />
                        </Button>
                        <span className="text-foreground text-sm font-medium min-w-[2rem] text-center">
                          {item.quantity}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={() => updateQuantity(item.id, item.quantity + 1)}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="flex items-center gap-2">
                        <p className="text-foreground text-base font-normal leading-normal min-w-[4rem] text-right">
                          ${(item.price * item.quantity).toFixed(2)}
                        </p>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 text-muted-foreground hover:text-destructive"
                          onClick={() => removeFromCart(item.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}

                {/* Order Summary */}
                <h3 className="text-foreground text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Order Summary</h3>
                <div className="p-4">
                  <div className="flex justify-between gap-x-6 py-2">
                    <p className="text-muted-foreground text-sm font-normal leading-normal">Subtotal</p>
                    <p className="text-foreground text-sm font-normal leading-normal text-right">${subtotal.toFixed(2)}</p>
                  </div>
                  <div className="flex justify-between gap-x-6 py-2">
                    <p className="text-muted-foreground text-sm font-normal leading-normal">Tax</p>
                    <p className="text-foreground text-sm font-normal leading-normal text-right">${tax.toFixed(2)}</p>
                  </div>
                  <div className="flex justify-between gap-x-6 py-2">
                    <p className="text-muted-foreground text-sm font-normal leading-normal">Delivery Fee</p>
                    <p className="text-foreground text-sm font-normal leading-normal text-right">${deliveryFee.toFixed(2)}</p>
                  </div>
                  <div className="flex justify-between gap-x-6 py-2">
                    <p className="text-muted-foreground text-sm font-normal leading-normal">Discount</p>
                    <p className="text-foreground text-sm font-normal leading-normal text-right">${discount.toFixed(2)}</p>
                  </div>
                  <div className="flex justify-between gap-x-6 py-2 border-t border-border pt-4">
                    <p className="text-foreground text-base font-bold leading-normal">Total</p>
                    <p className="text-foreground text-base font-bold leading-normal text-right">${total.toFixed(2)}</p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col gap-3 px-4 py-3">
                  <Button className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-12 px-5 flex-1 bg-primary text-primary-foreground hover:bg-primary/90">
                    <span className="truncate">Proceed to Payment</span>
                  </Button>
                  <Button variant="outline" className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-12 px-5 flex-1" asChild>
                    <a href="/">
                      <span className="truncate">Continue Shopping</span>
                    </a>
                  </Button>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
