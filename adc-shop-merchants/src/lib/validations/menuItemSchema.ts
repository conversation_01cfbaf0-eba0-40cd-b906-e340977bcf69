import { z } from 'zod';

// Menu item schema
export const menuItemSchema = z.object({
  name: z.string().min(1, 'Item name is required'),
  description: z.string().optional(),
  price: z.number().min(0, 'Price must be a positive number'),
  category: z.string().min(1, 'Category is required'),
  image: z.string().url().optional().or(z.literal('')),
  available: z.boolean().default(true),
  preparationTime: z.number().int().min(0).optional(),
  ingredients: z.array(z.string()).optional(),
  allergens: z.array(z.string()).optional(),
  nutritionalInfo: z.record(z.any()).optional(),
});

// Type for menu item
export type MenuItem = z.infer<typeof menuItemSchema>;

// Schema for creating a menu item (includes merchantId)
export const createMenuItemSchema = menuItemSchema.extend({
  merchantId: z.string().min(1, 'Merchant ID is required'),
});

// Type for creating a menu item
export type CreateMenuItem = z.infer<typeof createMenuItemSchema>;

// Schema for updating a menu item
export const updateMenuItemSchema = menuItemSchema.partial().extend({
  id: z.string().min(1, 'Item ID is required'),
  merchantId: z.string().min(1, 'Merchant ID is required'),
});

// Type for updating a menu item
export type UpdateMenuItem = z.infer<typeof updateMenuItemSchema>;
