import { z } from 'zod';

// Appointment schema
export const appointmentSchema = z.object({
  serviceId: z.string().min(1, 'Service is required'),
  staffId: z.string().optional(),
  userId: z.string().min(1, 'User ID is required'),
  date: z.string().min(1, 'Date is required'),
  startTime: z.string().min(1, 'Start time is required'),
  endTime: z.string().min(1, 'End time is required'),
  status: z.enum(['scheduled', 'confirmed', 'completed', 'cancelled', 'no-show']),
  notes: z.string().optional(),
  customerName: z.string().min(1, 'Customer name is required'),
  customerEmail: z.string().email('Invalid email address'),
  customerPhone: z.string().optional(),
  numberOfPeople: z.number().int().min(1, 'Number of people must be at least 1'),
});

// Type for appointment
export type Appointment = z.infer<typeof appointmentSchema>;

// Schema for creating an appointment (includes merchantId)
export const createAppointmentSchema = appointmentSchema.extend({
  merchantId: z.string().min(1, 'Merchant ID is required'),
});

// Type for creating an appointment
export type CreateAppointment = z.infer<typeof createAppointmentSchema>;

// Schema for updating an appointment
export const updateAppointmentSchema = appointmentSchema.partial().extend({
  id: z.string().min(1, 'Appointment ID is required'),
  merchantId: z.string().min(1, 'Merchant ID is required'),
});

// Type for updating an appointment
export type UpdateAppointment = z.infer<typeof updateAppointmentSchema>;

// Time slot schema
export const timeSlotSchema = z.object({
  date: z.string(),
  startTime: z.string(),
  endTime: z.string(),
  available: z.boolean(),
  staffId: z.string().optional(),
  serviceId: z.string(),
});

// Type for time slot
export type TimeSlot = z.infer<typeof timeSlotSchema>;
