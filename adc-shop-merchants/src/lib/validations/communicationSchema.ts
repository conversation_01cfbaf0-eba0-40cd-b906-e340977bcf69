import { z } from 'zod';

// Communication template schema
export const communicationTemplateSchema = z.object({
  name: z.string().min(1, 'Template name is required'),
  type: z.enum(['email', 'sms'], {
    errorMap: () => ({ message: 'Type must be either email or sms' }),
  }),
  subject: z.string().optional(),
  content: z.string().min(1, 'Content is required'),
  variables: z.array(z.string()).default([]),
  isDefault: z.boolean().default(false),
  category: z.enum([
    'appointment_confirmation',
    'appointment_reminder',
    'appointment_cancellation',
    'marketing',
    'custom'
  ], {
    errorMap: () => ({ message: 'Invalid category' }),
  }),
});

// Type for communication template
export type CommunicationTemplate = z.infer<typeof communicationTemplateSchema>;

// Schema for creating a communication template (includes merchantId)
export const createCommunicationTemplateSchema = communicationTemplateSchema.extend({
  merchantId: z.string().min(1, 'Merchant ID is required'),
});

// Communication schema
export const communicationSchema = z.object({
  type: z.enum(['email', 'sms'], {
    errorMap: () => ({ message: 'Type must be either email or sms' }),
  }),
  recipient: z.string().min(1, 'Recipient is required'),
  subject: z.string().optional(),
  content: z.string().min(1, 'Content is required'),
  status: z.enum(['pending', 'sent', 'failed']).default('pending'),
  scheduledFor: z.string().optional().transform(val => val ? new Date(val) : undefined),
  metadata: z.record(z.any()).optional(),
});

// Type for communication
export type Communication = z.infer<typeof communicationSchema>;

// Schema for creating a communication (includes merchantId)
export const createCommunicationSchema = communicationSchema.extend({
  merchantId: z.string().min(1, 'Merchant ID is required'),
  userId: z.string().optional(),
  templateId: z.string().optional(),
});

// Schema for sending a communication with a template
export const sendWithTemplateSchema = z.object({
  merchantId: z.string().min(1, 'Merchant ID is required'),
  templateId: z.string().min(1, 'Template ID is required'),
  userId: z.string().optional(),
  recipient: z.string().min(1, 'Recipient is required'),
  variables: z.record(z.string()),
  scheduledFor: z.string().optional().transform(val => val ? new Date(val) : undefined),
});

// Communication campaign schema
export const communicationCampaignSchema = z.object({
  name: z.string().min(1, 'Campaign name is required'),
  type: z.enum(['email', 'sms'], {
    errorMap: () => ({ message: 'Type must be either email or sms' }),
  }),
  templateId: z.string().min(1, 'Template ID is required'),
  recipients: z.array(z.string()).min(1, 'At least one recipient is required'),
  scheduledFor: z.string().optional().transform(val => val ? new Date(val) : undefined),
  status: z.enum(['draft', 'scheduled', 'in_progress', 'completed', 'cancelled']).default('draft'),
  metadata: z.record(z.any()).optional(),
});

// Type for communication campaign
export type CommunicationCampaign = z.infer<typeof communicationCampaignSchema>;

// Schema for creating a communication campaign (includes merchantId)
export const createCommunicationCampaignSchema = communicationCampaignSchema.extend({
  merchantId: z.string().min(1, 'Merchant ID is required'),
});
