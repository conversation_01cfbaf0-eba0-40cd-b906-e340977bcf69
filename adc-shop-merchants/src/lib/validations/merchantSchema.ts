import { z } from 'zod';

// Base merchant schema
export const merchantSchema = z.object({
  name: z.string().min(1, 'Merchant name is required'),
  email: z.string().email('Invalid email address'),
  phoneNumber: z.string().min(1, 'Phone number is required'),
  address: z.string().min(1, 'Address is required'),
  ownerId: z.string().min(1, 'Owner ID is required'),
  status: z.enum(['active', 'pending', 'inactive']),
  logo: z.string().url().optional().or(z.literal('')),
  type: z.enum(['restaurant', 'retail', 'service', 'digital', 'convenience', 'custom']),
});

// Restaurant-specific schema
export const restaurantSettingsSchema = z.object({
  cuisineType: z.string().optional(),
  priceRange: z.string().optional(),
  seatingCapacity: z.number().int().nonnegative().optional(),
  reservationEnabled: z.boolean().optional(),
  deliveryEnabled: z.boolean().optional(),
  takeoutEnabled: z.boolean().optional(),
  openingHours: z.record(z.object({
    open: z.string(),
    close: z.string(),
  })).optional(),
});

// Retail-specific schema
export const retailSettingsSchema = z.object({
  inventoryManagement: z.boolean().optional(),
  allowBackorders: z.boolean().optional(),
  shippingMethods: z.array(z.string()).optional(),
  returnPolicy: z.string().optional(),
  openingHours: z.record(z.object({
    open: z.string(),
    close: z.string(),
  })).optional(),
});

// Service-specific schema
export const serviceSettingsSchema = z.object({
  appointmentEnabled: z.boolean().optional(),
  serviceLocations: z.array(z.string()).optional(),
  cancellationPolicy: z.string().optional(),
  openingHours: z.record(z.object({
    open: z.string(),
    close: z.string(),
  })).optional(),
});

// Digital-specific schema
export const digitalSettingsSchema = z.object({
  automaticDelivery: z.boolean().optional(),
  downloadLimitPerPurchase: z.number().int().nonnegative().optional(),
  downloadExpiryDays: z.number().int().nonnegative().optional(),
  watermarkEnabled: z.boolean().optional(),
  licenseTypes: z.array(z.string()).optional(),
});

// Convenience-specific schema
export const convenienceSettingsSchema = z.object({
  inventoryManagement: z.boolean().optional(),
  deliveryEnabled: z.boolean().optional(),
  deliveryRadius: z.number().nonnegative().optional(),
  minimumOrderAmount: z.number().nonnegative().optional(),
  openingHours: z.record(z.object({
    open: z.string(),
    close: z.string(),
  })).optional(),
});

// Custom-specific schema
export const customSettingsSchema = z.record(z.any()).optional();

// Type-specific merchant schemas
export const restaurantMerchantSchema = merchantSchema.extend({
  type: z.literal('restaurant'),
  settings: restaurantSettingsSchema,
});

export const retailMerchantSchema = merchantSchema.extend({
  type: z.literal('retail'),
  settings: retailSettingsSchema,
});

export const serviceMerchantSchema = merchantSchema.extend({
  type: z.literal('service'),
  settings: serviceSettingsSchema,
});

export const digitalMerchantSchema = merchantSchema.extend({
  type: z.literal('digital'),
  settings: digitalSettingsSchema,
});

export const convenienceMerchantSchema = merchantSchema.extend({
  type: z.literal('convenience'),
  settings: convenienceSettingsSchema,
});

export const customMerchantSchema = merchantSchema.extend({
  type: z.literal('custom'),
  settings: customSettingsSchema,
});

// Combined merchant schema
export const combinedMerchantSchema = z.discriminatedUnion('type', [
  restaurantMerchantSchema,
  retailMerchantSchema,
  serviceMerchantSchema,
  digitalMerchantSchema,
  convenienceMerchantSchema,
  customMerchantSchema,
]);

// Types
export type Merchant = z.infer<typeof merchantSchema>;
export type RestaurantSettings = z.infer<typeof restaurantSettingsSchema>;
export type RetailSettings = z.infer<typeof retailSettingsSchema>;
export type ServiceSettings = z.infer<typeof serviceSettingsSchema>;
export type DigitalSettings = z.infer<typeof digitalSettingsSchema>;
export type ConvenienceSettings = z.infer<typeof convenienceSettingsSchema>;
export type CustomSettings = z.infer<typeof customSettingsSchema>;

export type RestaurantMerchant = z.infer<typeof restaurantMerchantSchema>;
export type RetailMerchant = z.infer<typeof retailMerchantSchema>;
export type ServiceMerchant = z.infer<typeof serviceMerchantSchema>;
export type DigitalMerchant = z.infer<typeof digitalMerchantSchema>;
export type ConvenienceMerchant = z.infer<typeof convenienceMerchantSchema>;
export type CustomMerchant = z.infer<typeof customMerchantSchema>;

export type CombinedMerchant = z.infer<typeof combinedMerchantSchema>;
