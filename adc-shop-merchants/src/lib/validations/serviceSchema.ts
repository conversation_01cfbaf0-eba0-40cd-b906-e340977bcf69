import { z } from 'zod';

// Service schema
export const serviceSchema = z.object({
  name: z.string().min(1, 'Service name is required'),
  description: z.string().optional(),
  price: z.number().min(0, 'Price must be a positive number'),
  duration: z.number().int().min(1, 'Duration must be at least 1 minute'),
  category: z.string().min(1, 'Category is required'),
  categoryId: z.string().optional(),
  image: z.string().url().optional().or(z.literal('')),
  available: z.boolean().default(true),
  maxCapacity: z.number().int().min(1, 'Maximum capacity must be at least 1').optional(),
  requiresStaff: z.boolean().default(true),
  staffIds: z.array(z.string()).optional(),
  preparationTime: z.number().int().min(0, 'Preparation time must be a non-negative integer').optional(),
  cleanupTime: z.number().int().min(0, 'Cleanup time must be a non-negative integer').optional(),
});

// Type for service
export type Service = z.infer<typeof serviceSchema>;

// Schema for creating a service (includes merchantId)
export const createServiceSchema = serviceSchema.extend({
  merchantId: z.string().min(1, 'Merchant ID is required'),
});

// Type for creating a service
export type CreateService = z.infer<typeof createServiceSchema>;

// Schema for updating a service
export const updateServiceSchema = serviceSchema.partial().extend({
  id: z.string().min(1, 'Service ID is required'),
  merchantId: z.string().min(1, 'Merchant ID is required'),
});

// Type for updating a service
export type UpdateService = z.infer<typeof updateServiceSchema>;

// Staff schema
export const staffSchema = z.object({
  name: z.string().min(1, 'Staff name is required'),
  email: z.string().email('Invalid email address'),
  phone: z.string().min(1, 'Phone number is required'),
  role: z.string().min(1, 'Role is required'),
  specialties: z.array(z.string()).optional(),
  availability: z.record(z.array(z.object({
    start: z.string(),
    end: z.string(),
  }))).optional(),
  image: z.string().url().optional().or(z.literal('')),
});

// Type for staff
export type Staff = z.infer<typeof staffSchema>;

// Schema for creating a staff member (includes merchantId)
export const createStaffSchema = staffSchema.extend({
  merchantId: z.string().min(1, 'Merchant ID is required'),
});

// Type for creating a staff member
export type CreateStaff = z.infer<typeof createStaffSchema>;

// Schema for updating a staff member
export const updateStaffSchema = staffSchema.partial().extend({
  id: z.string().min(1, 'Staff ID is required'),
  merchantId: z.string().min(1, 'Merchant ID is required'),
});

// Type for updating a staff member
export type UpdateStaff = z.infer<typeof updateStaffSchema>;
