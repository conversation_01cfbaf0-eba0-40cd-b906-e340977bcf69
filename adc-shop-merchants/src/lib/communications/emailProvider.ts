/**
 * Email provider integration
 *
 * This file contains the implementation for sending emails using various providers.
 * Currently supports Brevo, Mailgun, and a mock implementation.
 */

export interface EmailOptions {
  to: string;
  subject: string;
  text?: string;
  html?: string;
  from?: string;
  replyTo?: string;
  type?: 'transactional' | 'marketing';
  attachments?: Array<{
    filename: string;
    content: string | Buffer;
    contentType?: string;
  }>;
}

export interface EmailProvider {
  sendEmail(options: EmailOptions): Promise<{ success: boolean; messageId?: string; error?: string }>;
}

/**
 * Mock email provider for development and testing
 */
export class MockEmailProvider implements EmailProvider {
  async sendEmail(options: EmailOptions): Promise<{ success: boolean; messageId?: string; error?: string }> {
    console.log('MOCK EMAIL PROVIDER');
    console.log('To:', options.to);
    console.log('Subject:', options.subject);
    console.log('Text:', options.text);
    console.log('HTML:', options.html);

    // Simulate a delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Simulate success (or random failure for testing)
    const success = Math.random() > 0.1; // 10% chance of failure

    if (success) {
      return {
        success: true,
        messageId: `mock_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`,
      };
    } else {
      return {
        success: false,
        error: 'Mock email sending failed',
      };
    }
  }
}

