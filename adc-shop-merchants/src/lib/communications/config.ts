/**
 * Configuration for email and SMS providers
 *
 * This file contains the configuration for the email and SMS providers.
 * It reads the configuration from environment variables and provides
 * default values for development and testing.
 */

/**
 * Email provider configuration
 */
export const emailConfig = {
  // Provider to use: 'brevo', 'mailgun', or 'mock'
  provider: process.env.EMAIL_PROVIDER || 'brevo',

  // Brevo configuration
  brevo: {
    apiKey: process.env.BREVO_API_KEY || '',
    fromEmail: process.env.EMAIL_SENDER_ADDRESS || '<EMAIL>',
    fromName: process.env.EMAIL_SENDER_NAME || 'Company Name',
  },



  // Mailgun configuration
  mailgun: {
    apiKey: process.env.MAILGUN_API_KEY || '',
    domain: process.env.MAILGUN_DOMAIN || '',
    fromEmail: process.env.MAILGUN_FROM_EMAIL || '<EMAIL>',
  },

  // Default configuration
  defaultFromEmail: process.env.DEFAULT_FROM_EMAIL || '<EMAIL>',
  defaultReplyToEmail: process.env.DEFAULT_REPLY_TO_EMAIL || '<EMAIL>',
};

/**
 * SMS provider configuration
 *
 * Note: Currently only supporting mock provider for development and testing.
 * Real SMS providers will be implemented in the future.
 */
export const smsConfig = {
  // Provider is always 'mock' for now
  provider: 'mock',

  // Default configuration
  defaultSender: process.env.DEFAULT_SMS_SENDER || 'COMPANY',

  // Placeholder configurations for future implementation
  twilio: { accountSid: '', authToken: '', phoneNumber: '' },
  nexmo: { apiKey: '', apiSecret: '', sender: '' },
};

/**
 * Check if email provider is properly configured
 */
export function isEmailProviderConfigured(): boolean {
  switch (emailConfig.provider.toLowerCase()) {
    case 'brevo':
      return !!emailConfig.brevo.apiKey;

    case 'mailgun':
      return !!emailConfig.mailgun.apiKey && !!emailConfig.mailgun.domain;
    case 'mock':
      return true;
    default:
      return false;
  }
}

/**
 * Check if SMS provider is properly configured
 *
 * Note: Currently always returns true since we only support the mock provider.
 * This will be updated when real SMS providers are implemented.
 */
export function isSMSProviderConfigured(): boolean {
  return true;
}

/**
 * Get a description of the current email provider configuration
 */
export function getEmailProviderDescription(): string {
  switch (emailConfig.provider.toLowerCase()) {
    case 'brevo':
      return `Brevo (${emailConfig.brevo.fromEmail})`;

    case 'mailgun':
      return `Mailgun (${emailConfig.mailgun.domain}, ${emailConfig.mailgun.fromEmail})`;
    case 'mock':
      return 'Mock Email Provider (for development/testing)';
    default:
      return 'Unknown Email Provider';
  }
}

/**
 * Get a description of the current SMS provider configuration
 *
 * Note: Currently always returns the mock provider description.
 * This will be updated when real SMS providers are implemented.
 */
export function getSMSProviderDescription(): string {
  return 'Mock SMS Provider (for development/testing)';
}
