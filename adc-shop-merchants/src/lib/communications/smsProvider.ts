/**
 * SMS provider integration
 *
 * This file contains the implementation for sending SMS messages.
 * Currently only supports a mock implementation for development and testing.
 */

export interface SMSOptions {
  to: string;
  message: string;
  from?: string;
}

export interface SMSProvider {
  sendSMS(options: SMSOptions): Promise<{ success: boolean; messageId?: string; error?: string }>;
}

/**
 * Mock SMS provider for development and testing
 */
export class MockSMSProvider implements SMSProvider {
  async sendSMS(options: SMSOptions): Promise<{ success: boolean; messageId?: string; error?: string }> {
    console.log('MOCK SMS PROVIDER');
    console.log('To:', options.to);
    console.log('Message:', options.message);
    console.log('From:', options.from || 'DEFAULT_SENDER');

    // Simulate a delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Simulate success (or random failure for testing)
    const success = Math.random() > 0.1; // 10% chance of failure

    if (success) {
      return {
        success: true,
        messageId: `mock_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`,
      };
    } else {
      return {
        success: false,
        error: 'Mock SMS sending failed',
      };
    }
  }
}

/**
 * Note: Additional SMS providers like Twilio and Nexmo/Vonage will be implemented in the future.
 * For now, we're only using the mock provider for development and testing.
 */

// Factory function to create the SMS provider
export function createSMSProvider(): SMSProvider {
  // For now, we only support the mock provider
  // In the future, this will be expanded to support real SMS providers

  // In production, warn if using mock provider
  if (process.env.NODE_ENV === 'production') {
    console.warn('WARNING: Using mock SMS provider in production environment');
  }

  return new MockSMSProvider();
}
