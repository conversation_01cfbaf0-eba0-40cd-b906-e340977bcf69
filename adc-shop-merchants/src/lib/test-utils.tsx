import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { Provider } from 'react-redux'
import { configureStore, Store } from '@reduxjs/toolkit'
import { merchantApi } from '@/lib/redux/api/endpoints/restaurant/shopApi'
import { tablesApi } from '@/lib/redux/api/endpoints/restaurant/tablesApi'
import { reservationsApi } from '@/lib/redux/api/endpoints/restaurant/reservationsApi'

// Mock data for testing
export const mockMerchantData = {
  id: 'merchant-1',
  slug: 'test-restaurant',
  name: 'Test Restaurant',
  description: 'A test restaurant for testing purposes',
  email: '<EMAIL>',
  phone: '+**********',
  address: {
    street: '123 Test St',
    city: 'Test City',
    state: 'TS',
    zipCode: '12345',
    country: 'Test Country',
  },
  branches: [
    {
      id: 'branch-1',
      slug: 'main-branch',
      name: 'Main Branch',
      address: '123 Test St',
      phone: '+**********',
      email: '<EMAIL>',
      isActive: true,
    },
  ],
}

export const mockTablesData = [
  {
    id: 'table-1',
    number: 1,
    name: 'Table 1',
    capacity: 4,
    status: 'available' as const,
    area: 'dining',
    position: { x: 100, y: 100 },
  },
  {
    id: 'table-2',
    number: 2,
    name: 'Table 2',
    capacity: 2,
    status: 'occupied' as const,
    area: 'dining',
    position: { x: 200, y: 100 },
  },
  {
    id: 'table-3',
    number: 3,
    name: 'Table 3',
    capacity: 6,
    status: 'reserved' as const,
    area: 'outdoor',
    position: { x: 300, y: 100 },
  },
]

export const mockAreasData = [
  {
    id: 'area-1',
    name: 'Dining Area',
    description: 'Main dining area',
    capacity: 50,
  },
  {
    id: 'area-2',
    name: 'Outdoor Patio',
    description: 'Outdoor seating area',
    capacity: 30,
  },
]

export const mockReservationsData = [
  {
    id: 'reservation-1',
    time: '7:00 PM',
    customerName: 'John Doe',
    partySize: 4,
    tableId: 'table-1',
    tableName: 'Table 1',
    status: 'confirmed' as const,
    date: '2024-01-15',
    phone: '+**********',
    email: '<EMAIL>',
    notes: 'Birthday celebration',
  },
  {
    id: 'reservation-2',
    time: '8:00 PM',
    customerName: 'Jane Smith',
    partySize: 2,
    tableId: 'table-2',
    tableName: 'Table 2',
    status: 'pending' as const,
    date: '2024-01-15',
    phone: '+1234567891',
    email: '<EMAIL>',
    notes: 'Anniversary dinner',
  },
]

// Create a mock store with default state
export const createMockStore = (preloadedState?: any): Store => {
  return configureStore({
    reducer: {
      shopApi: merchantApi.reducer,
      tablesApi: tablesApi.reducer,
      reservationsApi: reservationsApi.reducer,
    },
    preloadedState,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: {
          ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
        },
      }).concat(
        merchantApi.middleware,
        tablesApi.middleware,
        reservationsApi.middleware
      ),
  })
}

// Custom render function that includes Redux Provider
interface ExtendedRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  preloadedState?: any
  store?: Store
}

export function renderWithProviders(
  ui: ReactElement,
  {
    preloadedState = {},
    store = createMockStore(preloadedState),
    ...renderOptions
  }: ExtendedRenderOptions = {}
) {
  function Wrapper({ children }: { children: React.ReactNode }) {
    return <Provider store={store}>{children}</Provider>
  }

  return { store, ...render(ui, { wrapper: Wrapper, ...renderOptions }) }
}

// Mock API responses for testing
export const mockApiResponses = {
  merchants: {
    success: {
      data: [mockMerchantData],
    },
    error: {
      error: 'Failed to fetch merchants',
    },
    empty: {
      data: [],
    },
  },
  tables: {
    success: mockTablesData,
    error: {
      error: 'Failed to fetch tables',
    },
    empty: [],
  },
  areas: {
    success: mockAreasData,
    error: {
      error: 'Failed to fetch areas',
    },
    empty: [],
  },
  reservations: {
    success: {
      data: mockReservationsData,
    },
    error: {
      error: 'Failed to fetch reservations',
    },
    empty: {
      data: [],
    },
  },
}

// Helper function to create mock API hooks
export const createMockApiHooks = (scenario: 'success' | 'error' | 'loading' | 'empty' = 'success') => {
  const baseReturn = {
    isLoading: scenario === 'loading',
    error: scenario === 'error' ? { message: 'API Error' } : null,
    refetch: jest.fn(),
    isSuccess: scenario === 'success' || scenario === 'empty',
    isError: scenario === 'error',
  }

  return {
    useGetMerchantsQuery: jest.fn(() => ({
      ...baseReturn,
      data: scenario === 'success' ? mockApiResponses.merchants.success :
            scenario === 'empty' ? mockApiResponses.merchants.empty : undefined,
    })),
    useGetTablesQuery: jest.fn(() => ({
      ...baseReturn,
      data: scenario === 'success' ? mockApiResponses.tables.success :
            scenario === 'empty' ? mockApiResponses.tables.empty : undefined,
    })),
    useGetTableAreasQuery: jest.fn(() => ({
      ...baseReturn,
      data: scenario === 'success' ? mockApiResponses.areas.success :
            scenario === 'empty' ? mockApiResponses.areas.empty : undefined,
    })),
    useGetReservationsQuery: jest.fn(() => ({
      ...baseReturn,
      data: scenario === 'success' ? mockApiResponses.reservations.success :
            scenario === 'empty' ? mockApiResponses.reservations.empty : undefined,
    })),
  }
}

// Helper to wait for async operations in tests
export const waitForAsync = (ms: number = 0) =>
  new Promise(resolve => setTimeout(resolve, ms))

// Helper to create test params
export const createTestParams = (slugShop = 'test-restaurant', slugBranch = 'main-branch') =>
  Promise.resolve({ slugShop, slugBranch })

// Helper to check if element has specific class
export const hasClass = (element: Element, className: string): boolean => {
  return element.classList.contains(className)
}

// Helper to get element by test id
export const getByTestId = (container: HTMLElement, testId: string): HTMLElement | null => {
  return container.querySelector(`[data-testid="${testId}"]`)
}

// Helper to simulate API delay
export const simulateApiDelay = (ms: number = 1000) =>
  new Promise(resolve => setTimeout(resolve, ms))

// Additional mock data for comprehensive testing
export const mockOrdersData = [
  {
    id: '#ORD-001',
    status: 'preparing',
    customer: { name: 'John Doe', phone: '+**********' },
    total: 25.99,
    createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    orderType: 'dine-in',
    tableId: 'Table 5',
    estimatedTime: 15,
    items: [
      { name: 'Burger Deluxe', quantity: 1, price: 15.99, total: 15.99 },
      { name: 'French Fries', quantity: 1, price: 5.99, total: 5.99 },
    ],
  },
  {
    id: '#ORD-002',
    status: 'completed',
    customer: { name: 'Jane Smith', phone: '+1234567891' },
    total: 18.50,
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    completedAt: new Date(Date.now() - 90 * 60 * 1000).toISOString(),
    orderType: 'takeout',
    items: [
      { name: 'Caesar Salad', quantity: 2, price: 9.25, total: 18.50 },
    ],
  },
]

export const mockStaffData = [
  {
    id: 'staff-1',
    slug: 'john-doe',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    position: 'Manager',
    roleName: 'Manager',
    status: 'active',
    schedule: [
      { dayOfWeek: 'Monday', startTime: '09:00', endTime: '17:00' },
      { dayOfWeek: 'Tuesday', startTime: '09:00', endTime: '17:00' },
    ],
  },
  {
    id: 'staff-2',
    slug: 'jane-smith',
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
    position: 'Server',
    roleName: 'Server',
    status: 'inactive',
    schedule: [],
  },
]

export const mockDashboardStats = {
  todayOrders: 45,
  todayRevenue: 2850.75,
  todayReservations: 12,
  activeStaff: 8,
  ordersGrowth: 15.2,
  revenueGrowth: 8.7,
  reservationsGrowth: -2.1,
}

// Helper to create mock API responses with different scenarios
export const createMockApiResponse = (data: any, scenario: 'success' | 'error' | 'empty' = 'success') => {
  switch (scenario) {
    case 'success':
      return { data, isLoading: false, error: null, isSuccess: true, isError: false }
    case 'error':
      return { data: undefined, isLoading: false, error: { message: 'API Error' }, isSuccess: false, isError: true }
    case 'empty':
      return { data: Array.isArray(data) ? [] : null, isLoading: false, error: null, isSuccess: true, isError: false }
    default:
      return { data, isLoading: false, error: null, isSuccess: true, isError: false }
  }
}

// Helper to test error boundaries
export const ThrowError = ({ shouldThrow }: { shouldThrow: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test error')
  }
  return <div>No error</div>
}

// Helper to test async operations
export const AsyncComponent = ({ delay = 1000 }: { delay?: number }) => {
  const [loading, setLoading] = React.useState(true)

  React.useEffect(() => {
    const timer = setTimeout(() => setLoading(false), delay)
    return () => clearTimeout(timer)
  }, [delay])

  if (loading) return <div data-testid="async-loading">Loading...</div>
  return <div data-testid="async-loaded">Loaded</div>
}

// Helper to create test routes for MSW
export const createTestRoutes = (baseUrl = 'http://localhost:4000/api') => {
  return {
    merchants: `${baseUrl}/merchants`,
    tables: `${baseUrl}/merchants/:merchantId/branches/:branchId/tables`,
    tableAreas: `${baseUrl}/merchants/:merchantId/branches/:branchId/tables/areas`,
    reservations: `${baseUrl}/merchants/:merchantId/branches/:branchId/reservations`,
    menuItems: `${baseUrl}/menu/items`,
    orders: `${baseUrl}/orders`,
    staff: `${baseUrl}/staff`,
    dashboardStats: `${baseUrl}/dashboard/stats`,
  }
}

// Re-export everything from React Testing Library
export * from '@testing-library/react'
export { default as userEvent } from '@testing-library/user-event'
