import { apiSlice } from '../apiSlice';
import { Appointment } from '@/lib/validations/appointmentSchema';

// Create the appointment API
export const appointmentApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all appointments for a merchant
    getAppointments: builder.query<Appointment[], { merchantId: string }>({
      query: ({ merchantId }) => `/merchants/${merchantId}/appointments`,
      providesTags: ['Appointments'],
    }),
    
    // Get an appointment by ID
    getAppointmentById: builder.query<Appointment, { merchantId: string; appointmentId: string }>({
      query: ({ merchantId, appointmentId }) => `/merchants/${merchantId}/appointments/${appointmentId}`,
      providesTags: ['Appointments'],
    }),
    
    // Create a new appointment
    createAppointment: builder.mutation<Appointment, { merchantId: string; appointment: Omit<Appointment, 'id'> }>({
      query: ({ merchantId, appointment }) => ({
        url: `/merchants/${merchantId}/appointments`,
        method: 'POST',
        body: appointment,
      }),
      invalidatesTags: ['Appointments'],
    }),
    
    // Update an appointment
    updateAppointment: builder.mutation<Appointment, { merchantId: string; appointmentId: string; appointment: Partial<Appointment> }>({
      query: ({ merchantId, appointmentId, appointment }) => ({
        url: `/merchants/${merchantId}/appointments/${appointmentId}`,
        method: 'PUT',
        body: appointment,
      }),
      invalidatesTags: ['Appointments'],
    }),
    
    // Cancel an appointment
    cancelAppointment: builder.mutation<Appointment, { merchantId: string; appointmentId: string }>({
      query: ({ merchantId, appointmentId }) => ({
        url: `/merchants/${merchantId}/appointments/${appointmentId}/cancel`,
        method: 'POST',
      }),
      invalidatesTags: ['Appointments'],
    }),
    
    // Send a communication for an appointment
    sendAppointmentCommunication: builder.mutation<any, { 
      merchantId: string; 
      appointmentId: string; 
      type: 'confirmation' | 'reminder' | 'cancellation';
      method: 'email' | 'sms';
    }>({
      query: ({ merchantId, appointmentId, type, method }) => ({
        url: `/merchants/${merchantId}/appointments/${appointmentId}/communications`,
        method: 'POST',
        body: { type, method },
      }),
    }),
    
    // Schedule reminders for an appointment
    scheduleAppointmentReminders: builder.mutation<any, { merchantId: string; appointmentId: string }>({
      query: ({ merchantId, appointmentId }) => ({
        url: `/merchants/${merchantId}/appointments/${appointmentId}/communications/schedule-reminders`,
        method: 'POST',
      }),
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetAppointmentsQuery,
  useGetAppointmentByIdQuery,
  useCreateAppointmentMutation,
  useUpdateAppointmentMutation,
  useCancelAppointmentMutation,
  useSendAppointmentCommunicationMutation,
  useScheduleAppointmentRemindersMutation,
} = appointmentApi;
