import { apiSlice } from '../apiSlice';

// Types
export interface Tag {
  id: string;
  branch_id: string;
  name: string;
  slug: string;
  description: string;
  category: string;
  color: string;
  icon: string;
  usage_count: number;
  is_system: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface TagCategory {
  id: string;
  branch_id: string;
  name: string;
  slug: string;
  description: string;
  color: string;
  icon: string;
  sort_order: number;
  is_system: boolean;
  is_active: boolean;
  tags?: Tag[];
  created_at: string;
  updated_at: string;
}

export interface EntityTag {
  id: string;
  tag_id: string;
  entity_type: string;
  entity_id: string;
  tag: Tag;
  created_at: string;
}

export interface CreateTagRequest {
  name: string;
  description?: string;
  category: string;
  color?: string;
  icon?: string;
}

export interface UpdateTagRequest {
  name?: string;
  description?: string;
  category?: string;
  color?: string;
  icon?: string;
  is_active?: boolean;
}

export interface CreateTagCategoryRequest {
  name: string;
  description?: string;
  color?: string;
  icon?: string;
  sort_order?: number;
}

export interface AssignTagsRequest {
  tag_ids: string[];
}

export interface TagSuggestionRequest {
  query: string;
  category?: string;
  entity_type?: string;
  limit?: number;
}

export interface TagFilters {
  category?: string;
  is_active?: boolean;
  is_system?: boolean;
  search?: string;
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface TagsResponse {
  tags: Tag[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface TagCategoriesResponse {
  categories: TagCategory[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface PopularTagsResponse {
  tags: Tag[];
}

export interface TagSuggestionResponse {
  tags: Tag[];
}

export interface TagAnalyticsResponse {
  total_tags: number;
  total_categories: number;
  most_used_tags: Tag[];
  tags_by_category: Record<string, number>;
  recently_created: Tag[];
  unused_tags: Tag[];
  tag_usage_over_time: Array<{ date: string; count: number }>;
  category_distribution: Array<{ category: string; count: number; color: string }>;
}

export const tagApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Tag operations
    getTags: builder.query<TagsResponse, { shopId: string; branchId: string; filters?: TagFilters }>({
      query: ({ shopId, branchId, filters = {} }) => {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            params.append(key, String(value));
          }
        });
        return `/v1/shops/${shopId}/branches/${branchId}/tags?${params.toString()}`;
      },
      providesTags: ['Tag'],
    }),

    createTag: builder.mutation<Tag, { shopId: string; branchId: string; tag: CreateTagRequest }>({
      query: ({ shopId, branchId, tag }) => ({
        url: `/v1/shops/${shopId}/branches/${branchId}/tags`,
        method: 'POST',
        body: tag,
      }),
      invalidatesTags: ['Tag'],
    }),

    getTag: builder.query<Tag, { shopId: string; branchId: string; tagId: string }>({
      query: ({ shopId, branchId, tagId }) => `/v1/shops/${shopId}/branches/${branchId}/tags/${tagId}`,
      providesTags: (result, error, { tagId }) => [{ type: 'Tag', id: tagId }],
    }),

    getTagBySlug: builder.query<Tag, { shopId: string; branchId: string; tagSlug: string }>({
      query: ({ shopId, branchId, tagSlug }) => `/v1/shops/${shopId}/branches/${branchId}/tags/slug/${tagSlug}`,
      providesTags: (result, error, { tagSlug }) => [{ type: 'Tag', id: tagSlug }],
    }),

    updateTag: builder.mutation<Tag, { shopId: string; branchId: string; tagId: string; tag: UpdateTagRequest }>({
      query: ({ shopId, branchId, tagId, tag }) => ({
        url: `/v1/shops/${shopId}/branches/${branchId}/tags/${tagId}`,
        method: 'PUT',
        body: tag,
      }),
      invalidatesTags: (result, error, { tagId }) => [{ type: 'Tag', id: tagId }, 'Tag'],
    }),

    deleteTag: builder.mutation<void, { shopId: string; branchId: string; tagId: string }>({
      query: ({ shopId, branchId, tagId }) => ({
        url: `/v1/shops/${shopId}/branches/${branchId}/tags/${tagId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Tag'],
    }),

    getPopularTags: builder.query<PopularTagsResponse, { shopId: string; branchId: string; limit?: number }>({
      query: ({ shopId, branchId, limit = 10 }) =>
        `/v1/shops/${shopId}/branches/${branchId}/tags/popular?limit=${limit}`,
      providesTags: ['Tag'],
    }),

    searchTags: builder.mutation<TagSuggestionResponse, { shopId: string; branchId: string; request: TagSuggestionRequest }>({
      query: ({ shopId, branchId, request }) => ({
        url: `/v1/shops/${shopId}/branches/${branchId}/tags/search`,
        method: 'POST',
        body: request,
      }),
    }),

    getTagsByCategory: builder.query<TagsResponse, { shopId: string; branchId: string; category: string }>({
      query: ({ shopId, branchId, category }) => `/v1/shops/${shopId}/branches/${branchId}/tags/category/${category}`,
      providesTags: ['Tag'],
    }),

    getTagAnalytics: builder.query<TagAnalyticsResponse, { shopId: string; branchId: string }>({
      query: ({ shopId, branchId }) => `/v1/shops/${shopId}/branches/${branchId}/tags/analytics`,
      providesTags: ['Tag'],
    }),

    // Tag Category operations
    getTagCategories: builder.query<TagCategoriesResponse, { shopId: string; branchId: string; filters?: any }>({
      query: ({ shopId, branchId, filters = {} }) => {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            params.append(key, String(value));
          }
        });
        return `/v1/shops/${shopId}/branches/${branchId}/tag-categories?${params.toString()}`;
      },
      providesTags: ['TagCategory'],
    }),

    createTagCategory: builder.mutation<TagCategory, { shopId: string; branchId: string; category: CreateTagCategoryRequest }>({
      query: ({ shopId, branchId, category }) => ({
        url: `/v1/shops/${shopId}/branches/${branchId}/tag-categories`,
        method: 'POST',
        body: category,
      }),
      invalidatesTags: ['TagCategory'],
    }),

    // Entity Tag operations
    assignTagsToEntity: builder.mutation<void, { shopId: string; branchId: string; entityType: string; entityId: string; request: AssignTagsRequest }>({
      query: ({ shopId, branchId, entityType, entityId, request }) => ({
        url: `/v1/shops/${shopId}/branches/${branchId}/entities/${entityType}/${entityId}/tags`,
        method: 'POST',
        body: request,
      }),
      invalidatesTags: ['EntityTag', 'Tag'],
    }),

    getEntityTags: builder.query<EntityTag[], { shopId: string; branchId: string; entityType: string; entityId: string }>({
      query: ({ shopId, branchId, entityType, entityId }) =>
        `/v1/shops/${shopId}/branches/${branchId}/entities/${entityType}/${entityId}/tags`,
      providesTags: (result, error, { entityType, entityId }) => [
        { type: 'EntityTag', id: `${entityType}-${entityId}` }
      ],
    }),
  }),
});

export const {
  useGetTagsQuery,
  useCreateTagMutation,
  useGetTagQuery,
  useGetTagBySlugQuery,
  useUpdateTagMutation,
  useDeleteTagMutation,
  useGetPopularTagsQuery,
  useSearchTagsMutation,
  useGetTagsByCategoryQuery,
  useGetTagAnalyticsQuery,
  useGetTagCategoriesQuery,
  useCreateTagCategoryMutation,
  useAssignTagsToEntityMutation,
  useGetEntityTagsQuery,
} = tagApi;
