import { apiSlice } from '../../apiSlice';

// Core Restaurant/Merchant types
export interface Restaurant {
  id: string;
  name: string;
  description?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  timezone: string;
  currency: string;
  status: 'active' | 'inactive' | 'suspended';
  logo?: string;
  createdAt: string;
  updatedAt: string;
  ownerId: string;
  branches?: Branch[];
  settings?: RestaurantSettings;
}

export interface Branch {
  id: string;
  merchantId: string;
  name: string;
  address: string;
  city: string;
  state?: string;
  country: string;
  postalCode?: string;
  phone?: string;
  email?: string;
  status: 'active' | 'inactive';
  isMain: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface RestaurantFilters {
  search?: string;
  status?: 'active' | 'inactive' | 'suspended';
  city?: string;
  country?: string;
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

export interface RestaurantsResponse {
  data: Restaurant[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface CreateRestaurantRequest {
  name: string;
  description?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  timezone: string;
  currency: string;
  logo?: string;
}

export interface UpdateRestaurantRequest extends Partial<CreateRestaurantRequest> {
  status?: 'active' | 'inactive' | 'suspended';
}

// Restaurant-specific settings types
export interface RestaurantSettings {
  cuisineType: string;
  priceRange: string;
  seatingCapacity: number;
  reservationEnabled: boolean;
  deliveryEnabled: boolean;
  takeoutEnabled: boolean;
  openingHours: Record<string, { open: string; close: string }>;
}

export interface MenuItem {
  id: string;
  merchantId: string;
  name: string;
  description: string;
  price: number;
  category: string;
  image: string;
  available: boolean;
  preparationTime: number;
  ingredients: string[];
  allergens: string[];
  nutritionalInfo: Record<string, any>;
}

export interface Table {
  id: string;
  merchantId: string;
  number: number;
  capacity: number;
  status: 'available' | 'occupied' | 'reserved' | 'unavailable';
  location: string;
}

export interface Reservation {
  id: string;
  merchantId: string;
  userId: string;
  tableId: string;
  date: string;
  time: string;
  duration: number;
  partySize: number;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  specialRequests: string;
}

export interface Reviewer {
  id: string;
  name: string;
  email?: string;
  avatar?: string;
}

export interface ReviewedItem {
  id: string;
  type: 'menu_item' | 'service' | 'ambiance' | 'other';
  name: string;
}

export interface Review {
  id: string;
  merchantId: string;
  reviewer: Reviewer;
  rating: number;
  date: string;
  content: string;
  reviewedItems: ReviewedItem[];
  status: 'published' | 'pending' | 'flagged' | 'archived';
  response?: string;
}

// Create the restaurant API
export const restaurantApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Restaurant/Merchant CRUD operations
    getRestaurants: builder.query<RestaurantsResponse, RestaurantFilters>({
      query: (filters) => {
        const params = new URLSearchParams();

        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/shops?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result) => [
        'Merchants',
        ...(result?.data || []).map(({ id }) => ({ type: 'Merchants' as const, id })),
      ],
    }),

    getRestaurantById: builder.query<Restaurant, string>({
      query: (id) => `/merchants/${id}`,
      providesTags: (result, error, id) => [{ type: 'Merchants', id }],
    }),

    createRestaurant: builder.mutation<Restaurant, CreateRestaurantRequest>({
      query: (data) => ({
        url: '/merchants',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Merchants'],
    }),

    updateRestaurant: builder.mutation<Restaurant, { id: string; data: UpdateRestaurantRequest }>({
      query: ({ id, data }) => ({
        url: `/merchants/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        'Merchants',
        { type: 'Merchants', id },
      ],
    }),

    deleteRestaurant: builder.mutation<void, string>({
      query: (id) => ({
        url: `/merchants/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [
        'Merchants',
        { type: 'Merchants', id },
      ],
    }),

    uploadRestaurantLogo: builder.mutation<{ logoUrl: string }, { id: string; file: File }>({
      query: ({ id, file }) => {
        const formData = new FormData();
        formData.append('logo', file);

        return {
          url: `/merchants/${id}/logo`,
          method: 'POST',
          body: formData,
        };
      },
      invalidatesTags: (result, error, { id }) => [
        { type: 'Merchants', id },
      ],
    }),

    // Branch management (now uses shops API)
    getBranches: builder.query<Branch[], string>({
      query: (shopId) => `/shops/${shopId}/branches`,
      providesTags: (result, error, shopId) => [
        { type: 'Branches', id: shopId },
        ...(result || []).map(({ id }) => ({ type: 'Branches' as const, id })),
      ],
    }),

    // Menu Items (now uses shops API)
    getMenuItems: builder.query<MenuItem[], string>({
      query: (shopId) => `/shops/${shopId}/menu-items`,
      providesTags: ['Items'],
    }),

    createMenuItem: builder.mutation<MenuItem, Partial<MenuItem> & { shopId: string }>({
      query: ({ shopId, ...item }) => ({
        url: `/shops/${shopId}/menu-items`,
        method: 'POST',
        body: item,
      }),
      invalidatesTags: ['Items'],
    }),

    updateMenuItem: builder.mutation<MenuItem, { merchantId: string; itemId: string; data: Partial<MenuItem> }>({
      query: ({ merchantId, itemId, data }) => ({
        url: `/merchants/${merchantId}/menu-items/${itemId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Items'],
    }),

    // Tables
    getTables: builder.query<Table[], string>({
      query: (merchantId) => `/merchants/${merchantId}/tables`,
      providesTags: ['Tables'],
    }),

    getTableById: builder.query<Table, { merchantId: string; tableId: string }>({
      query: ({ merchantId, tableId }) => `/merchants/${merchantId}/tables/${tableId}`,
      providesTags: (result, error, { tableId }) => [{ type: 'Tables', id: tableId }],
    }),

    createTable: builder.mutation<Table, Partial<Table> & { merchantId: string }>({
      query: ({ merchantId, ...table }) => ({
        url: `/merchants/${merchantId}/tables`,
        method: 'POST',
        body: table,
      }),
      invalidatesTags: ['Tables'],
    }),

    updateTable: builder.mutation<Table, { merchantId: string; tableId: string; data: Partial<Table> }>({
      query: ({ merchantId, tableId, data }) => ({
        url: `/merchants/${merchantId}/tables/${tableId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Tables'],
    }),

    deleteTable: builder.mutation<void, { merchantId: string; tableId: string }>({
      query: ({ merchantId, tableId }) => ({
        url: `/merchants/${merchantId}/tables/${tableId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Tables'],
    }),

    // Table Availability
    getTableAvailability: builder.query<any[], { merchantId: string; date: string; partySize: number }>({
      query: ({ merchantId, date, partySize }) =>
        `/merchants/${merchantId}/tables/availability?date=${date}&partySize=${partySize}`,
      providesTags: ['Tables', 'Reservations'],
    }),

    // Reservations
    getReservations: builder.query<Reservation[], string>({
      query: (merchantId) => `/merchants/${merchantId}/reservations`,
      providesTags: ['Reservations'],
    }),

    getReservationById: builder.query<Reservation, { merchantId: string; reservationId: string }>({
      query: ({ merchantId, reservationId }) => `/merchants/${merchantId}/reservations/${reservationId}`,
      providesTags: (result, error, { reservationId }) => [{ type: 'Reservations', id: reservationId }],
    }),

    createReservation: builder.mutation<Reservation, Partial<Reservation> & { merchantId: string }>({
      query: ({ merchantId, ...reservation }) => ({
        url: `/merchants/${merchantId}/reservations`,
        method: 'POST',
        body: reservation,
      }),
      invalidatesTags: ['Reservations'],
    }),

    updateReservation: builder.mutation<Reservation, { merchantId: string; reservationId: string; data: Partial<Reservation> }>({
      query: ({ merchantId, reservationId, data }) => ({
        url: `/merchants/${merchantId}/reservations/${reservationId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Reservations'],
    }),

    deleteReservation: builder.mutation<void, { merchantId: string; reservationId: string }>({
      query: ({ merchantId, reservationId }) => ({
        url: `/merchants/${merchantId}/reservations/${reservationId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Reservations'],
    }),

    // Reviews
    getReviews: builder.query<Review[], { merchantId: string; status?: string }>({
      query: ({ merchantId, status }) => {
        let url = `/merchants/${merchantId}/reviews`;
        if (status) {
          url += `?status=${status}`;
        }
        return url;
      },
      providesTags: ['Reviews'],
    }),

    getReviewById: builder.query<Review, { merchantId: string; reviewId: string }>({
      query: ({ merchantId, reviewId }) => `/merchants/${merchantId}/reviews/${reviewId}`,
      providesTags: (result, error, { reviewId }) => [{ type: 'Reviews', id: reviewId }],
    }),

    respondToReview: builder.mutation<Review, { merchantId: string; reviewId: string; response: string }>({
      query: ({ merchantId, reviewId, response }) => ({
        url: `/merchants/${merchantId}/reviews/${reviewId}/respond`,
        method: 'POST',
        body: { response },
      }),
      invalidatesTags: (result, error, { reviewId }) => [{ type: 'Reviews', id: reviewId }],
    }),

    updateReviewStatus: builder.mutation<Review, { merchantId: string; reviewId: string; status: string }>({
      query: ({ merchantId, reviewId, status }) => ({
        url: `/merchants/${merchantId}/reviews/${reviewId}/status`,
        method: 'PUT',
        body: { status },
      }),
      invalidatesTags: (result, error, { reviewId }) => [{ type: 'Reviews', id: reviewId }],
    }),
  }),
});

// Export hooks for usage in components
export const {
  // Restaurant/Merchant management
  useGetRestaurantsQuery,
  useGetRestaurantByIdQuery,
  useCreateRestaurantMutation,
  useUpdateRestaurantMutation,
  useDeleteRestaurantMutation,
  useUploadRestaurantLogoMutation,
  // Branch management
  useGetBranchesQuery,
  // Menu Items
  useGetMenuItemsQuery,
  useCreateMenuItemMutation,
  useUpdateMenuItemMutation,
  // Tables
  useGetTablesQuery,
  useGetTableByIdQuery,
  useCreateTableMutation,
  useUpdateTableMutation,
  useDeleteTableMutation,
  useGetTableAvailabilityQuery,
  // Reservations
  useGetReservationsQuery,
  useGetReservationByIdQuery,
  useCreateReservationMutation,
  useUpdateReservationMutation,
  useDeleteReservationMutation,
  // Reviews
  useGetReviewsQuery,
  useGetReviewByIdQuery,
  useRespondToReviewMutation,
  useUpdateReviewStatusMutation,
} = restaurantApi;
