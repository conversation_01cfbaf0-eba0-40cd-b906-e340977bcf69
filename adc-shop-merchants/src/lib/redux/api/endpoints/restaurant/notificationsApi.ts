/**
 * Enhanced Notifications API with Backend-Driven Filtering, Sorting, and Pagination
 * Following the Purchase Orders pattern for consistent implementation
 */

import { apiSlice } from '../../apiSlice';

// Enhanced Notification interfaces
export interface Notification {
  id: string;
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  type: 'order' | 'reservation' | 'review' | 'system' | 'staff' | 'inventory' | 'payment' | 'promotion';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  link?: string;
  actionLabel?: string;
  data?: Record<string, any>;
  shopId: string;
  branchId: string;
  userId?: string;
  createdAt: string;
  updatedAt: string;
}

// Enhanced filtering interface
export interface NotificationsFilters {
  // Pagination
  page: number;
  limit: number;

  // Sorting
  sort_by: 'timestamp' | 'title' | 'type' | 'priority' | 'isRead' | 'createdAt';
  sort_order: 'asc' | 'desc';

  // Filtering
  type?: 'order' | 'reservation' | 'review' | 'system' | 'staff' | 'inventory' | 'payment' | 'promotion';
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  isRead?: boolean;
  search?: string;

  // Date filtering
  startDate?: string;
  endDate?: string;
  dateRange?: 'today' | 'yesterday' | 'week' | 'month' | 'quarter' | 'year';
}

// Response interfaces
export interface NotificationsListResponse {
  data: Notification[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  summary: {
    totalNotifications: number;
    unreadNotifications: number;
    readNotifications: number;
    urgentNotifications: number;
    highPriorityNotifications: number;
    byType: Record<string, number>;
    byPriority: Record<string, number>;
  };
}

// Request interfaces
export interface CreateNotificationRequest {
  title: string;
  message: string;
  type: 'order' | 'reservation' | 'review' | 'system' | 'staff' | 'inventory' | 'payment' | 'promotion';
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  link?: string;
  actionLabel?: string;
  data?: Record<string, any>;
  userId?: string;
}

export interface UpdateNotificationRequest {
  title?: string;
  message?: string;
  isRead?: boolean;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  link?: string;
  actionLabel?: string;
  data?: Record<string, any>;
}

export interface BulkUpdateNotificationsRequest {
  notificationIds: string[];
  updates: {
    isRead?: boolean;
    priority?: 'low' | 'medium' | 'high' | 'urgent';
  };
}

// Query parameters interface
export interface GetNotificationsParams {
  shopSlug: string;
  branchSlug: string;
  filters: NotificationsFilters;
}

// Enhanced Notifications API
export const notificationsApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get notifications with backend-driven filtering, sorting, and pagination
    getNotifications: builder.query<NotificationsListResponse, GetNotificationsParams>({
      query: ({ shopSlug, branchSlug, filters }) => {
        const params = new URLSearchParams();

        // Pagination
        params.append('page', filters.page.toString());
        params.append('limit', filters.limit.toString());

        // Sorting
        params.append('sort_by', filters.sort_by);
        params.append('sort_order', filters.sort_order);

        // Filtering
        if (filters.type) params.append('type', filters.type);
        if (filters.priority) params.append('priority', filters.priority);
        if (filters.isRead !== undefined) params.append('isRead', filters.isRead.toString());
        if (filters.search) params.append('search', filters.search);

        // Date filtering
        if (filters.startDate) params.append('startDate', filters.startDate);
        if (filters.endDate) params.append('endDate', filters.endDate);
        if (filters.dateRange) params.append('dateRange', filters.dateRange);

        return {
          url: `shops/slug/${shopSlug}/branches/slug/${branchSlug}/notifications?${params.toString()}`,
          method: 'GET',
        };
      },
      transformResponse: (response: any): NotificationsListResponse => {
        // Transform snake_case backend response to camelCase frontend format
        const transformNotification = (notification: any): Notification => ({
          ...notification,
          isRead: notification.is_read, // Convert snake_case to camelCase
          shopId: notification.shop_id,
          branchId: notification.branch_id,
          userId: notification.user_id,
          actionLabel: notification.action_label,
          createdAt: notification.created_at,
          updatedAt: notification.updated_at,
        });

        return {
          ...response,
          data: response.data?.map(transformNotification) || [],
        };
      },
      providesTags: (result) => [
        { type: 'Notifications', id: 'LIST' },
        ...(result?.data || []).map(({ id }) => ({ type: 'Notifications' as const, id })),
      ],
    }),

    // Get notification by ID
    getNotificationById: builder.query<Notification, { shopId: string; branchId: string; notificationId: string }>({
      query: ({ shopId, branchId, notificationId }) => ({
        url: `notifications/${notificationId}?shopId=${shopId}&branchId=${branchId}`,
        method: 'GET',
      }),
      providesTags: (result, error, { notificationId }) => [
        { type: 'Notifications', id: notificationId },
      ],
    }),

    // Create notification
    createNotification: builder.mutation<Notification, { shopId: string; branchId: string; notification: CreateNotificationRequest }>({
      query: ({ shopId, branchId, notification }) => ({
        url: `notifications?shopId=${shopId}&branchId=${branchId}`,
        method: 'POST',
        body: notification,
      }),
      invalidatesTags: [{ type: 'Notifications', id: 'LIST' }],
    }),

    // Update notification
    updateNotification: builder.mutation<Notification, { shopSlug: string; branchSlug: string; notificationId: string; updates: UpdateNotificationRequest }>({
      query: ({ shopSlug, branchSlug, notificationId, updates }) => ({
        url: `shops/slug/${shopSlug}/branches/slug/${branchSlug}/notifications/${notificationId}`,
        method: 'PATCH',
        body: updates,
      }),
      transformResponse: (response: any): Notification => ({
        ...response,
        isRead: response.is_read, // Convert snake_case to camelCase
        shopId: response.shop_id,
        branchId: response.branch_id,
        userId: response.user_id,
        actionLabel: response.action_label,
        createdAt: response.created_at,
        updatedAt: response.updated_at,
      }),
      invalidatesTags: (_, __, { notificationId }) => [
        { type: 'Notifications', id: notificationId },
        { type: 'Notifications', id: 'LIST' },
      ],
    }),

    // Bulk update notifications (mark as read/unread, change priority)
    bulkUpdateNotifications: builder.mutation<{ updated: number }, { shopSlug: string; branchSlug: string; request: BulkUpdateNotificationsRequest }>({
      query: ({ shopSlug, branchSlug, request }) => ({
        url: `shops/slug/${shopSlug}/branches/slug/${branchSlug}/notifications/bulk-update`,
        method: 'PATCH',
        body: request,
      }),
      invalidatesTags: [{ type: 'Notifications', id: 'LIST' }],
    }),

    // Delete notification
    deleteNotification: builder.mutation<{ success: boolean }, { shopSlug: string; branchSlug: string; notificationId: string }>({
      query: ({ shopSlug, branchSlug, notificationId }) => ({
        url: `shops/slug/${shopSlug}/branches/slug/${branchSlug}/notifications/${notificationId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (_, __, { notificationId }) => [
        { type: 'Notifications', id: notificationId },
        { type: 'Notifications', id: 'LIST' },
      ],
    }),

    // Mark all notifications as read
    markAllNotificationsAsRead: builder.mutation<{ message: string }, { shopSlug: string; branchSlug: string }>({
      query: ({ shopSlug, branchSlug }) => ({
        url: `shops/slug/${shopSlug}/branches/slug/${branchSlug}/notifications/mark-all-read`,
        method: 'PATCH',
      }),
      invalidatesTags: [{ type: 'Notifications', id: 'LIST' }],
    }),

    // Clear all notifications
    clearAllNotifications: builder.mutation<{ message: string }, { shopSlug: string; branchSlug: string }>({
      query: ({ shopSlug, branchSlug }) => ({
        url: `shops/slug/${shopSlug}/branches/slug/${branchSlug}/notifications/clear-all`,
        method: 'DELETE',
      }),
      invalidatesTags: [{ type: 'Notifications', id: 'LIST' }],
    }),

    // Get notification statistics
    getNotificationStats: builder.query<NotificationsListResponse['summary'], { shopId: string; branchId: string }>({
      query: ({ shopId, branchId }) => ({
        url: `notifications/stats?shopId=${shopId}&branchId=${branchId}`,
        method: 'GET',
      }),
      providesTags: [{ type: 'Notifications', id: 'STATS' }],
    }),
  }),
});

// Export hooks
export const {
  useGetNotificationsQuery,
  useGetNotificationByIdQuery,
  useCreateNotificationMutation,
  useUpdateNotificationMutation,
  useBulkUpdateNotificationsMutation,
  useDeleteNotificationMutation,
  useMarkAllNotificationsAsReadMutation,
  useClearAllNotificationsMutation,
  useGetNotificationStatsQuery,
} = notificationsApi;

// Export types
export type {
  Notification,
  NotificationsFilters,
  NotificationsListResponse,
  CreateNotificationRequest,
  UpdateNotificationRequest,
  BulkUpdateNotificationsRequest,
  GetNotificationsParams,
};
