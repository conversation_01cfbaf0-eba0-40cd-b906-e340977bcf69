/**
 * RTK Query API endpoints for reservations
 * Handles all reservation-related API calls
 */

import { apiSlice } from '../../apiSlice';

export interface Reservation {
  id: string;
  slug: string;
  customerName: string;
  customerPhone: string;
  customerEmail?: string;
  partySize: number;
  date: string;
  time: string;
  duration: number;
  tableId?: string;
  tableName?: string;
  status: 'pending' | 'confirmed' | 'seated' | 'completed' | 'cancelled' | 'no-show';
  specialRequests?: string;
  notes?: string;
  source: 'phone' | 'website' | 'walk-in' | 'app';
  createdAt: string;
  updatedAt: string;
}

export interface ReservationFilters {
  status?: string;
  date?: string;
  startDate?: string;
  endDate?: string;
  search?: string;
  tableId?: string;
  page?: number;
  limit?: number;
}

export interface CreateReservationRequest {
  customerName: string;
  customerPhone: string;
  customerEmail?: string;
  partySize: number;
  date: string;
  time: string;
  duration?: number;
  tableId?: string;
  specialRequests?: string;
  notes?: string;
  source?: string;
}

export interface UpdateReservationRequest {
  slug: string;
  customerName?: string;
  customerPhone?: string;
  customerEmail?: string;
  partySize?: number;
  date?: string;
  time?: string;
  duration?: number;
  tableId?: string;
  status?: string;
  specialRequests?: string;
  notes?: string;
}

export interface ReservationsResponse {
  data: Reservation[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export interface AvailableSlot {
  time: string;
  availableTables: number;
  suggestedTables: string[];
}

// Helper function to transform backend reservation data to frontend format
const transformReservation = (backendReservation: any): Reservation => {
  // Extract date from reservation_date (ISO string)
  let date = '';
  let time = '';

  if (backendReservation.reservation_date) {
    try {
      const reservationDate = new Date(backendReservation.reservation_date);
      date = reservationDate.toISOString().split('T')[0]; // YYYY-MM-DD format
    } catch (error) {
      console.warn('Failed to parse reservation_date:', backendReservation.reservation_date);
    }
  }

  // Extract time from reservation_time (handle both proper and malformed time)
  if (backendReservation.reservation_time) {
    try {
      const timeStr = backendReservation.reservation_time;

      // Try to parse as a full datetime first (for properly formatted data)
      if (timeStr.includes('T') && !timeStr.startsWith('0000-01-01') && !timeStr.startsWith('0000-01-02')) {
        // This is a proper datetime, extract time from it
        const dateTime = new Date(timeStr);
        if (!isNaN(dateTime.getTime())) {
          time = dateTime.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
          });
        }
      } else if (timeStr.includes('T')) {
        // Handle malformed time like "0000-01-02T01:42:04+06:42"
        const timePart = timeStr.split('T')[1];
        if (timePart) {
          // Extract HH:MM from the time part
          const timeMatch = timePart.match(/(\d{2}):(\d{2})/);
          if (timeMatch) {
            time = `${timeMatch[1]}:${timeMatch[2]}`;
          }
        }
      } else {
        // If it's already in HH:MM format
        time = timeStr;
      }
    } catch (error) {
      console.warn('Failed to parse reservation_time:', backendReservation.reservation_time);
    }
  }

  return {
    id: backendReservation.id,
    slug: backendReservation.slug || backendReservation.id, // Use slug if available, fallback to id
    customerName: backendReservation.customer_name || '',
    customerPhone: backendReservation.customer_phone || '',
    customerEmail: backendReservation.customer_email || '',
    partySize: backendReservation.party_size || 0,
    date,
    time,
    duration: backendReservation.duration || 120,
    tableId: backendReservation.table_id,
    tableName: backendReservation.table_name,
    status: backendReservation.status || 'pending',
    specialRequests: backendReservation.special_requests || '',
    notes: backendReservation.notes || '',
    source: backendReservation.source || 'website',
    createdAt: backendReservation.created_at || '',
    updatedAt: backendReservation.updated_at || '',
  };
};

export const reservationsApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get reservations with filters and pagination (now uses shop/branch slugs)
    getReservations: builder.query<ReservationsResponse, {
      shopSlug: string;
      branchSlug: string;
      filters?: ReservationFilters;
    }>({
      query: ({ shopSlug, branchSlug, filters = {} }) => {
        const params = new URLSearchParams();

        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/reservations?${params.toString()}`,
          method: 'GET',
        };
      },
      transformResponse: (response: any) => {
        if (Array.isArray(response)) {
          // If response is directly an array (no pagination wrapper)
          return {
            data: response.map(transformReservation),
            pagination: {
              page: 1,
              limit: response.length,
              totalItems: response.length,
              totalPages: 1,
            }
          };
        } else if (response?.data) {
          // If response has pagination wrapper
          return {
            ...response,
            data: response.data.map(transformReservation),
          };
        }
        return response;
      },
      providesTags: (result) => [
        'Reservations',
        ...(result?.data || []).map(({ id }) => ({ type: 'Reservations' as const, id })),
      ],
    }),

    // Get single reservation (now uses shop/branch slugs and reservation slug)
    getReservation: builder.query<Reservation, {
      shopSlug: string;
      branchSlug: string;
      reservationSlug: string;
    }>({
      query: ({ shopSlug, branchSlug, reservationSlug }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/reservations/slug/${reservationSlug}`,
        method: 'GET',
      }),
      transformResponse: (response: any) => {
        return transformReservation(response);
      },
      providesTags: (result, error, { reservationSlug }) => [
        { type: 'Reservations', id: reservationSlug },
      ],
    }),

    // Get today's reservations (now uses shop/branch slugs)
    getTodayReservations: builder.query<Reservation[], {
      shopSlug: string;
      branchSlug: string;
    }>({
      query: ({ shopSlug, branchSlug }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/reservations/today`,
        method: 'GET',
      }),
      transformResponse: (response: any) => {
        if (Array.isArray(response)) {
          return response.map(transformReservation);
        }
        return response;
      },
      providesTags: ['Reservations'],
    }),

    // Get available time slots (now uses shop/branch slugs)
    getAvailableSlots: builder.query<AvailableSlot[], {
      shopSlug: string;
      branchSlug: string;
      date: string;
      partySize: number;
    }>({
      query: ({ shopSlug, branchSlug, date, partySize }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/reservations/availability?date=${date}&partySize=${partySize}`,
        method: 'GET',
      }),
      providesTags: ['Reservations'],
    }),

    // Create new reservation (now uses shop/branch slugs)
    createReservation: builder.mutation<Reservation, {
      shopSlug: string;
      branchSlug: string;
      reservationData: CreateReservationRequest;
    }>({
      query: ({ shopSlug, branchSlug, reservationData }) => {
        // Transform frontend field names to backend field names
        const backendData = {
          customer_name: reservationData.customerName,
          customer_phone: reservationData.customerPhone,
          customer_email: reservationData.customerEmail || '',
          party_size: reservationData.partySize,
          reservation_date: reservationData.date,
          reservation_time: reservationData.time,
          duration: reservationData.duration || 120,
          table_id: reservationData.tableId,
          special_requests: reservationData.specialRequests || '',
          notes: reservationData.notes || '',
          source: reservationData.source || 'website',
        };

        return {
          url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/reservations`,
          method: 'POST',
          body: backendData,
        };
      },
      transformResponse: (response: any) => {
        return transformReservation(response);
      },
      invalidatesTags: ['Reservations'],
    }),

    // Update reservation (now uses shop/branch slugs and reservation slug)
    updateReservation: builder.mutation<Reservation, {
      shopSlug: string;
      branchSlug: string;
      reservationData: UpdateReservationRequest;
    }>({
      query: ({ shopSlug, branchSlug, reservationData }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/reservations/slug/${reservationData.slug}`,
        method: 'PUT',
        body: reservationData,
      }),
      transformResponse: (response: any) => {
        return transformReservation(response);
      },
      invalidatesTags: (result, error, { reservationData }) => [
        { type: 'Reservations', id: reservationData.slug },
        'Reservations',
      ],
    }),

    // Update reservation status (now uses shop/branch slugs and reservation slug)
    updateReservationStatus: builder.mutation<Reservation, {
      shopSlug: string;
      branchSlug: string;
      reservationSlug: string;
      status: string;
    }>({
      query: ({ shopSlug, branchSlug, reservationSlug, status }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/reservations/slug/${reservationSlug}/status`,
        method: 'PATCH',
        body: { status },
      }),
      transformResponse: (response: any) => {
        return transformReservation(response);
      },
      invalidatesTags: (result, error, { reservationSlug }) => [
        { type: 'Reservations', id: reservationSlug },
        'Reservations',
      ],
    }),

    // Cancel reservation (now uses shop/branch slugs and reservation slug)
    cancelReservation: builder.mutation<void, {
      shopSlug: string;
      branchSlug: string;
      reservationSlug: string;
      reason?: string;
    }>({
      query: ({ shopSlug, branchSlug, reservationSlug, reason }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/reservations/slug/${reservationSlug}/cancel`,
        method: 'POST',
        body: { reason },
      }),
      invalidatesTags: (result, error, { reservationSlug }) => [
        { type: 'Reservations', id: reservationSlug },
        'Reservations',
      ],
    }),

    // Check in reservation (now uses shop/branch slugs and reservation slug)
    checkInReservation: builder.mutation<Reservation, {
      shopSlug: string;
      branchSlug: string;
      reservationSlug: string;
      tableId?: string;
    }>({
      query: ({ shopSlug, branchSlug, reservationSlug, tableId }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/reservations/slug/${reservationSlug}/checkin`,
        method: 'POST',
        body: { tableId },
      }),
      transformResponse: (response: any) => {
        return transformReservation(response);
      },
      invalidatesTags: (result, error, { reservationSlug }) => [
        { type: 'Reservations', id: reservationSlug },
        'Reservations',
      ],
    }),

    // Mark as no-show (now uses shop/branch slugs and reservation slug)
    markNoShow: builder.mutation<Reservation, {
      shopSlug: string;
      branchSlug: string;
      reservationSlug: string;
    }>({
      query: ({ shopSlug, branchSlug, reservationSlug }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/reservations/slug/${reservationSlug}/no-show`,
        method: 'POST',
      }),
      transformResponse: (response: any) => {
        return transformReservation(response);
      },
      invalidatesTags: (result, error, { reservationSlug }) => [
        { type: 'Reservations', id: reservationSlug },
        'Reservations',
      ],
    }),

    // Send confirmation (now uses shop/branch slugs and reservation slug)
    sendConfirmation: builder.mutation<{ success: boolean }, {
      shopSlug: string;
      branchSlug: string;
      reservationSlug: string;
      method: 'email' | 'sms';
    }>({
      query: ({ shopSlug, branchSlug, reservationSlug, method }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/reservations/slug/${reservationSlug}/confirm`,
        method: 'POST',
        body: { method },
      }),
    }),

    // Get reservation statistics (now uses shop/branch slugs)
    getReservationStats: builder.query<{
      totalReservations: number;
      todayReservations: number;
      upcomingReservations: number;
      completedReservations: number;
      cancelledReservations: number;
      noShowRate: number;
    }, {
      shopSlug: string;
      branchSlug: string;
      period?: string;
    }>({
      query: ({ shopSlug, branchSlug, period = '30d' }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/reservations/stats?period=${period}`,
        method: 'GET',
      }),
      providesTags: ['Reservations'],
    }),
  }),
});

export const {
  useGetReservationsQuery,
  useGetReservationQuery,
  useGetTodayReservationsQuery,
  useGetAvailableSlotsQuery,
  useCreateReservationMutation,
  useUpdateReservationMutation,
  useUpdateReservationStatusMutation,
  useCancelReservationMutation,
  useCheckInReservationMutation,
  useMarkNoShowMutation,
  useSendConfirmationMutation,
  useGetReservationStatsQuery,
} = reservationsApi;
