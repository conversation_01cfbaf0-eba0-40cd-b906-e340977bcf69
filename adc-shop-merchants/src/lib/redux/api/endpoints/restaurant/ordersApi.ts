import { apiSlice } from '../../apiSlice';

// Enhanced Order Filters interface
export interface OrderFilters {
  // Filtering
  status?: 'pending' | 'preparing' | 'ready' | 'delivered' | 'completed' | 'cancelled';
  order_type?: 'dine-in' | 'takeout' | 'delivery';
  table_id?: string;
  customer_name?: string;
  payment_status?: 'pending' | 'paid' | 'failed' | 'refunded';
  payment_method?: string;
  date_from?: string; // ISO date string
  date_to?: string;   // ISO date string
  min_amount?: number;
  max_amount?: number;
  search?: string; // Search by order number, customer name, phone

  // Sorting
  sort_by?: 'order_number' | 'customer_name' | 'total_amount' | 'status' | 'created_at' | 'updated_at';
  sort_order?: 'asc' | 'desc';

  // Pagination
  page?: number;
  limit?: number;
}

// Response interfaces
export interface OrdersResponse {
  data: Order[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

// Order-specific types (matching backend OrderResponse)
export interface Order {
  id: string;
  branch_id: string;
  table_id?: string;
  order_number: string;
  customer_name: string;
  customer_phone: string;
  customer_email: string;
  order_type: 'dine_in' | 'takeaway' | 'delivery';
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'served' | 'completed' | 'cancelled';
  items: OrderItem[];
  subtotal: number;
  tax_amount: number;
  tip_amount: number;
  total_amount: number;
  notes: string;
  delivery_address?: {
    street: string;
    city: string;
    state: string;
    zip_code: string;
    country: string;
    notes: string;
  };
  estimated_time?: number;
  scheduled_for?: string;
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded';
  payment_method: string;
  created_at: string;
  updated_at: string;
}

export interface OrderItem {
  id: string;
  menu_item_id: string;
  menu_item?: {
    id: string;
    name: string;
    description: string;
    price: number;
    image_url: string;
  };
  name?: string; // fallback for compatibility
  price?: number; // fallback for compatibility
  unit_price: number;
  total_price: number;
  quantity: number;
  options: OrderItemOption[];
  notes: string;
  specialInstructions?: string; // fallback for compatibility
  status: string;
  created_at: string;
}

export interface OrderItemOption {
  id: string;
  name: string;
  value: string;
  price: number;
}

// Create the orders API
export const ordersApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all orders for a branch with enhanced filtering (using slugs)
    getOrders: builder.query<OrdersResponse, {
      shopSlug: string;
      branchSlug: string;
      filters?: OrderFilters;
    }>({
      query: ({ shopSlug, branchSlug, filters = {} }) => {
        const params = new URLSearchParams();

        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        const queryString = params.toString();
        const url = `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/orders${queryString ? `?${queryString}` : ''}`;

        return {
          url,
          method: 'GET',
        };
      },
      providesTags: (result) => [
        'Orders',
        ...(result?.data || []).map(({ id }) => ({ type: 'Orders' as const, id })),
      ],
    }),

    // Get active orders (pending, preparing, ready)
    getActiveOrders: builder.query<Order[], { shopId: string; branchId: string }>({
      query: ({ shopId, branchId }) => `/shops/${shopId}/branches/${branchId}/orders/active`,
      providesTags: ['Orders'],
    }),

    // Get completed orders
    getCompletedOrders: builder.query<Order[], { shopId: string; branchId: string }>({
      query: ({ shopId, branchId }) => `/shops/${shopId}/branches/${branchId}/orders/completed`,
      providesTags: ['Orders'],
    }),

    // Get order by order number (using slugs)
    getOrderByNumber: builder.query<Order, { shopSlug: string; branchSlug: string; orderNumber: string }>({
      query: ({ shopSlug, branchSlug, orderNumber }) => `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/orders/number/${orderNumber}`,
      providesTags: (result, error, arg) => [{ type: 'Orders', id: arg.orderNumber }],
    }),

    // Create a new order
    createOrder: builder.mutation<Order, { shopId: string; branchId: string; order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'> }>({
      query: ({ shopId, branchId, order }) => ({
        url: `/shops/${shopId}/branches/${branchId}/orders`,
        method: 'POST',
        body: order,
      }),
      invalidatesTags: ['Orders'],
    }),

    // Update an order
    updateOrder: builder.mutation<Order, { shopId: string; branchId: string; orderId: string; order: Partial<Order> }>({
      query: ({ shopId, branchId, orderId, order }) => ({
        url: `/shops/${shopId}/branches/${branchId}/orders/${orderId}`,
        method: 'PUT',
        body: order,
      }),
      invalidatesTags: (result, error, arg) => [
        'Orders',
        { type: 'Orders', id: arg.orderId },
      ],
    }),

    // Update order status by order number (using slugs)
    updateOrderStatusByNumber: builder.mutation<Order, { shopSlug: string; branchSlug: string; orderNumber: string; status: Order['status'] }>({
      query: ({ shopSlug, branchSlug, orderNumber, status }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/orders/number/${orderNumber}/status`,
        method: 'PATCH',
        body: { status },
      }),
      invalidatesTags: (result, error, arg) => [
        'Orders',
        { type: 'Orders', id: arg.orderNumber },
      ],
    }),

    // Cancel an order
    cancelOrder: builder.mutation<Order, { shopId: string; branchId: string; orderId: string; reason?: string }>({
      query: ({ shopId, branchId, orderId, reason }) => ({
        url: `/shops/${shopId}/branches/${branchId}/orders/${orderId}`,
        method: 'DELETE',
        body: reason ? { reason } : undefined,
      }),
      invalidatesTags: (result, error, arg) => [
        'Orders',
        { type: 'Orders', id: arg.orderId },
      ],
    }),

    // Process payment for an order
    processPayment: builder.mutation<{ success: boolean; transactionId?: string }, { shopId: string; branchId: string; orderId: string; paymentDetails: any }>({
      query: ({ shopId, branchId, orderId, paymentDetails }) => ({
        url: `/shops/${shopId}/branches/${branchId}/orders/${orderId}/payment`,
        method: 'POST',
        body: paymentDetails,
      }),
      invalidatesTags: (result, error, arg) => [
        'Orders',
        { type: 'Orders', id: arg.orderId },
      ],
    }),

    // Issue refund for an order
    issueRefund: builder.mutation<{ success: boolean; refundId?: string }, { shopId: string; branchId: string; orderId: string; amount?: number; reason?: string }>({
      query: ({ shopId, branchId, orderId, amount, reason }) => ({
        url: `/shops/${shopId}/branches/${branchId}/orders/${orderId}/refund`,
        method: 'POST',
        body: { amount, reason },
      }),
      invalidatesTags: (result, error, arg) => [
        'Orders',
        { type: 'Orders', id: arg.orderId },
      ],
    }),

    // Get order analytics
    getOrderAnalytics: builder.query<any, { shopId: string; branchId: string; startDate?: string; endDate?: string }>({
      query: ({ shopId, branchId, startDate, endDate }) => {
        let url = `/shops/${shopId}/branches/${branchId}/orders/analytics`;
        const params = new URLSearchParams();
        if (startDate) params.append('startDate', startDate);
        if (endDate) params.append('endDate', endDate);
        const queryString = params.toString();
        if (queryString) url += `?${queryString}`;
        return url;
      },
      providesTags: ['OrderAnalytics'],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetOrdersQuery,
  useGetOrdersBySlugQuery,
  useGetActiveOrdersQuery,
  useGetCompletedOrdersQuery,
  useGetOrderByIdQuery,
  useGetOrderByNumberQuery,
  useCreateOrderMutation,
  useUpdateOrderMutation,
  useUpdateOrderStatusMutation,
  useUpdateOrderStatusByNumberMutation,
  useCancelOrderMutation,
  useProcessPaymentMutation,
  useIssueRefundMutation,
  useGetOrderAnalyticsQuery,
} = ordersApi;
