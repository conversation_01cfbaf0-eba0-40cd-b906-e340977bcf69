import { apiSlice } from '../apiSlice';

// Menu item types
export interface MenuItem {
  id: string;
  slug: string;
  name: string;
  category: string;
  description: string;
  price: number;
  image: string;
  available: boolean;
  merchantId?: string;
  branchId?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface MenuCategory {
  id: string;
  name: string;
  description?: string;
  sortOrder: number;
  merchantId: string;
  branchId?: string;
}

export interface MenuItemFilters {
  // Filtering
  categoryId?: string;
  isAvailable?: boolean;
  isVegetarian?: boolean;
  isVegan?: boolean;
  isGlutenFree?: boolean;
  isSpicy?: boolean;
  minPrice?: number;
  maxPrice?: number;
  search?: string;
  tags?: string[];

  // Sorting
  sortBy?: 'name' | 'price' | 'created_at' | 'updated_at' | 'category';
  sortOrder?: 'asc' | 'desc';

  // Pagination
  page?: number;
  limit?: number;
}

export interface MenuItemsResponse {
  data: MenuItem[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface MenuItemFilters {
  // Filtering
  category_id?: string;
  is_available?: boolean;
  is_vegetarian?: boolean;
  is_vegan?: boolean;
  is_gluten_free?: boolean;
  is_spicy?: boolean;
  min_price?: number;
  max_price?: number;
  search?: string;
  tags?: string[];

  // Sorting
  sort_by?: 'name' | 'price' | 'created_at' | 'updated_at' | 'category';
  sort_order?: 'asc' | 'desc';

  // Pagination
  page?: number;
  limit?: number;
}

export interface MenuItemsResponse {
  data: MenuItem[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface CreateMenuItemRequest {
  name: string;
  description: string;
  price: number;
  category_id?: string;
  images?: string[];
  is_available?: boolean;
  is_vegetarian?: boolean;
  is_vegan?: boolean;
  is_gluten_free?: boolean;
  is_spicy?: boolean;
  spice_level?: number;
  ingredients?: string[];
  allergens?: string[];
  tags?: string[];
  preparation_time?: number;
}

export interface UpdateMenuItemRequest extends Partial<CreateMenuItemRequest> {
  id: string;
}

// Create the menu API
export const menuApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get menu items with filters, sorting, and pagination
    getMenuItems: builder.query<MenuItemsResponse, {
      shopId: string;
      branchId: string;
      filters?: MenuItemFilters
    }>({
      query: ({ shopId, branchId, filters = {} }) => {
        const params = new URLSearchParams();

        // Add filter parameters
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(v => params.append(key, String(v)));
            } else {
              params.append(key, String(value));
            }
          }
        });

        // Set default pagination if not provided
        if (!params.has('page')) {
          params.append('page', '1');
        }
        if (!params.has('limit')) {
          params.append('limit', '20');
        }

        // Use the proper backend route structure
        return `/shops/${shopId}/branches/${branchId}/menu/items?${params.toString()}`;
      },
      providesTags: ['Items'],
    }),

    // Get menu item by ID
    getMenuItem: builder.query<MenuItem, { merchantId: string; itemId: string; branchId?: string }>({
      query: ({ merchantId, itemId, branchId }) => {
        const baseUrl = branchId
          ? `/merchants/${merchantId}/branches/${branchId}/menu/items/${itemId}`
          : `/merchants/${merchantId}/menu/items/${itemId}`;
        return baseUrl;
      },
      providesTags: (result, error, { itemId }) => [{ type: 'Items', id: itemId }],
    }),

    // Get menu item by slug
    getMenuItemBySlug: builder.query<MenuItem, { merchantId: string; slug: string; branchId?: string }>({
      query: ({ merchantId, slug, branchId }) => {
        const baseUrl = branchId
          ? `/merchants/${merchantId}/branches/${branchId}/menu/items/slug/${slug}`
          : `/merchants/${merchantId}/menu/items/slug/${slug}`;
        return baseUrl;
      },
      providesTags: (result, error, { slug }) => [{ type: 'Items', id: result?.id }],
    }),

    // Get menu categories
    getMenuCategories: builder.query<MenuCategory[], { merchantId: string; branchId?: string }>({
      query: ({ merchantId, branchId }) => {
        const baseUrl = branchId
          ? `/merchants/${merchantId}/branches/${branchId}/menu/categories`
          : `/merchants/${merchantId}/menu/categories`;
        return baseUrl;
      },
      providesTags: ['Items'],
    }),

    // Create menu item
    createMenuItem: builder.mutation<MenuItem, CreateMenuItemRequest & { shopId: string; branchId: string }>({
      query: ({ shopId, branchId, ...item }) => {
        return {
          url: `/shops/${shopId}/branches/${branchId}/menu/items`,
          method: 'POST',
          body: item,
        };
      },
      invalidatesTags: ['Items'],
    }),

    // Update menu item
    updateMenuItem: builder.mutation<MenuItem, UpdateMenuItemRequest & { merchantId: string; branchId?: string }>({
      query: ({ merchantId, branchId, id, ...item }) => {
        const baseUrl = branchId
          ? `/merchants/${merchantId}/branches/${branchId}/menu/items/${id}`
          : `/merchants/${merchantId}/menu/items/${id}`;
        return {
          url: baseUrl,
          method: 'PUT',
          body: item,
        };
      },
      invalidatesTags: (result, error, { id }) => [{ type: 'Items', id }],
    }),

    // Delete menu item
    deleteMenuItem: builder.mutation<void, { merchantId: string; itemId: string; branchId?: string }>({
      query: ({ merchantId, itemId, branchId }) => {
        const baseUrl = branchId
          ? `/merchants/${merchantId}/branches/${branchId}/menu/items/${itemId}`
          : `/merchants/${merchantId}/menu/items/${itemId}`;
        return {
          url: baseUrl,
          method: 'DELETE',
        };
      },
      invalidatesTags: (result, error, { itemId }) => [{ type: 'Items', id: itemId }],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetMenuItemsQuery,
  useGetMenuItemQuery,
  useGetMenuItemBySlugQuery,
  useGetMenuCategoriesQuery,
  useCreateMenuItemMutation,
  useUpdateMenuItemMutation,
  useDeleteMenuItemMutation,
} = menuApi;
