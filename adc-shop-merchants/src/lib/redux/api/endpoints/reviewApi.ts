import { apiSlice } from '../apiSlice';

// Review types
export interface Review {
  id: string;
  merchantId: string;
  reviewer: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  rating: number;
  date: string;
  content: string;
  reviewedItems?: {
    id: string;
    type: 'menu_item' | 'service' | 'product';
    name: string;
  }[];
  status: 'pending' | 'published' | 'hidden';
  response?: string;
  responseDate?: string;
  helpful?: number;
  reported?: boolean;
}

export interface ReviewStats {
  totalReviews: number;
  averageRating: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
  responseRate: number;
  recentReviews: number;
}

export interface CreateReviewRequest {
  rating: number;
  content: string;
  reviewedItems?: {
    id: string;
    type: 'menu_item' | 'service' | 'product';
    name: string;
  }[];
}

export interface UpdateReviewRequest {
  status?: 'pending' | 'published' | 'hidden';
  response?: string;
}

// Create the review API
export const reviewApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get reviews
    getReviews: builder.query<{
      data: Review[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }, { 
      merchantId: string; 
      page?: number; 
      limit?: number; 
      status?: string;
      rating?: number;
      sortBy?: 'date' | 'rating' | 'helpful';
      sortOrder?: 'asc' | 'desc';
    }>({
      query: ({ merchantId, page = 1, limit = 20, status, rating, sortBy = 'date', sortOrder = 'desc' }) => {
        const params = new URLSearchParams();
        params.append('page', page.toString());
        params.append('limit', limit.toString());
        params.append('sortBy', sortBy);
        params.append('sortOrder', sortOrder);
        if (status) params.append('status', status);
        if (rating) params.append('rating', rating.toString());
        
        return `/merchants/${merchantId}/reviews?${params.toString()}`;
      },
      providesTags: ['Reviews'],
    }),

    // Get review by ID
    getReview: builder.query<Review, { merchantId: string; reviewId: string }>({
      query: ({ merchantId, reviewId }) => 
        `/merchants/${merchantId}/reviews/${reviewId}`,
      providesTags: (result, error, { reviewId }) => [{ type: 'Reviews', id: reviewId }],
    }),

    // Get review stats
    getReviewStats: builder.query<ReviewStats, { merchantId: string }>({
      query: ({ merchantId }) => `/merchants/${merchantId}/reviews/stats`,
      providesTags: ['Reviews'],
    }),

    // Create review (for customers)
    createReview: builder.mutation<Review, CreateReviewRequest & { merchantId: string }>({
      query: ({ merchantId, ...review }) => ({
        url: `/merchants/${merchantId}/reviews`,
        method: 'POST',
        body: review,
      }),
      invalidatesTags: ['Reviews'],
    }),

    // Update review (for merchants - respond, change status)
    updateReview: builder.mutation<Review, { 
      merchantId: string; 
      reviewId: string; 
      data: UpdateReviewRequest 
    }>({
      query: ({ merchantId, reviewId, data }) => ({
        url: `/merchants/${merchantId}/reviews/${reviewId}`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: (result, error, { reviewId }) => [
        { type: 'Reviews', id: reviewId },
        'Reviews'
      ],
    }),

    // Respond to review
    respondToReview: builder.mutation<Review, { 
      merchantId: string; 
      reviewId: string; 
      response: string 
    }>({
      query: ({ merchantId, reviewId, response }) => ({
        url: `/merchants/${merchantId}/reviews/${reviewId}/respond`,
        method: 'POST',
        body: { response },
      }),
      invalidatesTags: (result, error, { reviewId }) => [
        { type: 'Reviews', id: reviewId },
        'Reviews'
      ],
    }),

    // Mark review as helpful
    markReviewHelpful: builder.mutation<Review, { merchantId: string; reviewId: string }>({
      query: ({ merchantId, reviewId }) => ({
        url: `/merchants/${merchantId}/reviews/${reviewId}/helpful`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, { reviewId }) => [{ type: 'Reviews', id: reviewId }],
    }),

    // Report review
    reportReview: builder.mutation<Review, { 
      merchantId: string; 
      reviewId: string; 
      reason: string 
    }>({
      query: ({ merchantId, reviewId, reason }) => ({
        url: `/merchants/${merchantId}/reviews/${reviewId}/report`,
        method: 'POST',
        body: { reason },
      }),
      invalidatesTags: (result, error, { reviewId }) => [{ type: 'Reviews', id: reviewId }],
    }),

    // Delete review
    deleteReview: builder.mutation<void, { merchantId: string; reviewId: string }>({
      query: ({ merchantId, reviewId }) => ({
        url: `/merchants/${merchantId}/reviews/${reviewId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { reviewId }) => [
        { type: 'Reviews', id: reviewId },
        'Reviews'
      ],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetReviewsQuery,
  useGetReviewQuery,
  useGetReviewStatsQuery,
  useCreateReviewMutation,
  useUpdateReviewMutation,
  useRespondToReviewMutation,
  useMarkReviewHelpfulMutation,
  useReportReviewMutation,
  useDeleteReviewMutation,
} = reviewApi;
