import { apiSlice } from '../apiSlice';
import { CommunicationAnalytics, CampaignAnalytics } from '@/services/communicationAnalyticsService';

// Create the communication analytics API
export const communicationAnalyticsApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get merchant communication analytics
    getMerchantAnalytics: builder.query<any, { 
      merchantId: string; 
      startDate?: string;
      endDate?: string;
    }>({
      query: ({ merchantId, startDate, endDate }) => {
        let url = `/merchants/${merchantId}/communications/analytics`;
        const params = new URLSearchParams();
        if (startDate) params.append('startDate', startDate);
        if (endDate) params.append('endDate', endDate);
        const queryString = params.toString();
        if (queryString) url += `?${queryString}`;
        return url;
      },
      providesTags: ['CommunicationAnalytics'],
    }),
    
    // Get communication analytics
    getCommunicationAnalytics: builder.query<CommunicationAnalytics, { 
      merchantId: string; 
      communicationId: string;
    }>({
      query: ({ merchantId, communicationId }) => 
        `/merchants/${merchantId}/communications/${communicationId}/analytics`,
      providesTags: ['CommunicationAnalytics'],
    }),
    
    // Get campaign analytics
    getCampaignAnalytics: builder.query<CampaignAnalytics, { 
      merchantId: string; 
      campaignId: string;
    }>({
      query: ({ merchantId, campaignId }) => 
        `/merchants/${merchantId}/campaigns/${campaignId}/analytics`,
      providesTags: ['CommunicationAnalytics'],
    }),
    
    // Track a communication event
    trackEvent: builder.mutation<any, { 
      merchantId: string; 
      communicationId: string;
      type: 'delivered' | 'opened' | 'clicked' | 'bounced' | 'complained' | 'unsubscribed';
      timestamp?: string;
      metadata?: Record<string, any>;
    }>({
      query: ({ merchantId, ...event }) => ({
        url: `/merchants/${merchantId}/communications/events`,
        method: 'POST',
        body: event,
      }),
      invalidatesTags: ['CommunicationAnalytics'],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetMerchantAnalyticsQuery,
  useGetCommunicationAnalyticsQuery,
  useGetCampaignAnalyticsQuery,
  useTrackEventMutation,
} = communicationAnalyticsApi;
