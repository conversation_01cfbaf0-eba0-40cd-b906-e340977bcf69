import { apiSlice } from '../apiSlice';

// Service-specific types
export interface ServiceSettings {
  appointmentEnabled: boolean;
  serviceLocations: string[];
  cancellationPolicy: string;
  openingHours: Record<string, { open: string; close: string }>;
}

export interface Service {
  id: string;
  merchantId: string;
  name: string;
  description: string;
  price: number;
  duration: number; // in minutes
  category: string;
  image: string;
  available: boolean;
  maxCapacity: number;
  requiresStaff: boolean;
  staffIds: string[];
  preparationTime: number;
  cleanupTime: number;
}

export interface Appointment {
  id: string;
  merchantId: string;
  userId: string;
  serviceId: string;
  staffId: string | null;
  date: string;
  time: string;
  duration: number;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  notes: string;
  price: number;
}

export interface Staff {
  id: string;
  merchantId: string;
  name: string;
  email: string;
  phone: string;
  role: string;
  specialties: string[];
  availability: Record<string, { start: string; end: string }[]>;
  image: string;
}

// Create the service API
export const serviceApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Services
    getServices: builder.query<Service[], string>({
      query: (merchantId) => `/services?merchantId=${merchantId}`,
      providesTags: ['Items'],
    }),

    createService: builder.mutation<Service, Partial<Service> & { merchantId: string }>({
      query: ({ merchantId, ...service }) => ({
        url: `/merchants/${merchantId}/services`,
        method: 'POST',
        body: service,
      }),
      invalidatesTags: ['Items'],
    }),

    updateService: builder.mutation<Service, { merchantId: string; serviceId: string; data: Partial<Service> }>({
      query: ({ merchantId, serviceId, data }) => ({
        url: `/merchants/${merchantId}/services/${serviceId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Items'],
    }),

    // Appointments
    getAppointments: builder.query<Appointment[], string>({
      query: (merchantId) => `/service-appointments?merchantId=${merchantId}`,
      providesTags: ['Appointments'],
    }),

    createAppointment: builder.mutation<Appointment, Partial<Appointment> & { merchantId: string }>({
      query: ({ merchantId, ...appointment }) => ({
        url: `/merchants/${merchantId}/appointments`,
        method: 'POST',
        body: appointment,
      }),
      invalidatesTags: ['Appointments'],
    }),

    updateAppointment: builder.mutation<Appointment, { merchantId: string; appointmentId: string; data: Partial<Appointment> }>({
      query: ({ merchantId, appointmentId, data }) => ({
        url: `/merchants/${merchantId}/appointments/${appointmentId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Appointments'],
    }),

    // Cancel appointment
    cancelAppointment: builder.mutation<void, { merchantId: string; appointmentId: string }>({
      query: ({ merchantId, appointmentId }) => ({
        url: `/merchants/${merchantId}/appointments/${appointmentId}/cancel`,
        method: 'POST',
      }),
      invalidatesTags: ['Appointments'],
    }),

    // Staff
    getStaff: builder.query<Staff[], string>({
      query: (merchantId) => `/merchants/${merchantId}/staff`,
      providesTags: ['Staff'],
    }),

    createStaff: builder.mutation<Staff, Partial<Staff> & { merchantId: string }>({
      query: ({ merchantId, ...staff }) => ({
        url: `/merchants/${merchantId}/staff`,
        method: 'POST',
        body: staff,
      }),
      invalidatesTags: ['Staff'],
    }),

    updateStaff: builder.mutation<Staff, { merchantId: string; staffId: string; data: Partial<Staff> }>({
      query: ({ merchantId, staffId, data }) => ({
        url: `/merchants/${merchantId}/staff/${staffId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Staff'],
    }),

    // Availability
    getAvailability: builder.query<Record<string, boolean[]>, { merchantId: string; serviceId: string; date: string }>({
      query: ({ merchantId, serviceId, date }) =>
        `/merchants/${merchantId}/services/${serviceId}/availability?date=${date}`,
      providesTags: ['Appointments'],
    }),

    // Available time slots
    getAvailableTimeSlots: builder.query<string[], { merchantId: string; serviceId: string; date: string; staffId?: string }>({
      query: ({ merchantId, serviceId, date, staffId }) => {
        let url = `/merchants/${merchantId}/services/${serviceId}/time-slots?date=${date}`;
        if (staffId) url += `&staffId=${staffId}`;
        return url;
      },
      providesTags: ['Appointments'],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetServicesQuery,
  useCreateServiceMutation,
  useUpdateServiceMutation,
  useGetAppointmentsQuery,
  useCreateAppointmentMutation,
  useUpdateAppointmentMutation,
  useCancelAppointmentMutation,
  useGetStaffQuery,
  useCreateStaffMutation,
  useUpdateStaffMutation,
  useGetAvailabilityQuery,
  useGetAvailableTimeSlotsQuery,
} = serviceApi;
