/**
 * Application-wide message constants
 * Centralized location for all user-facing messages
 */

export const MESSAGES = {
  // Success messages
  SUCCESS: {
    CREATED: 'Successfully created',
    UPDATED: 'Successfully updated',
    DELETED: 'Successfully deleted',
    SAVED: 'Changes saved successfully',
    SUBMITTED: 'Form submitted successfully',
    UPLOADED: 'File uploaded successfully',
    RESERVATION_CREATED: 'Reservation created successfully',
    RESERVATION_UPDATED: 'Reservation updated successfully',
    RESERVATION_CANCELLED: 'Reservation cancelled successfully',
    MENU_ITEM_CREATED: 'Menu item created successfully',
    MENU_ITEM_UPDATED: 'Menu item updated successfully',
    MENU_ITEM_DELETED: 'Menu item deleted successfully',
    ORDER_CREATED: 'Order created successfully',
    ORDER_UPDATED: 'Order status updated successfully',
    LOGIN_SUCCESS: 'Login successful',
    LOGOUT_SUCCESS: 'Logout successful',
    PASSWORD_CHANGED: 'Password changed successfully',
    PROFILE_UPDATED: 'Profile updated successfully',
  },

  // Error messages
  ERROR: {
    GENERIC: 'Something went wrong. Please try again.',
    NET<PERSON>OR<PERSON>: 'Network error. Please check your connection.',
    UNAUTHORIZED: 'You are not authorized to perform this action.',
    FORBIDDEN: 'Access denied.',
    NOT_FOUND: 'The requested resource was not found.',
    VALIDATION: 'Please check your input and try again.',
    SERVER: 'Server error. Please try again later.',
    TIMEOUT: 'Request timed out. Please try again.',
    FILE_TOO_LARGE: 'File size is too large.',
    INVALID_FILE_TYPE: 'Invalid file type.',
    REQUIRED_FIELD: 'This field is required',
    INVALID_EMAIL: 'Please enter a valid email address',
    INVALID_PHONE: 'Please enter a valid phone number',
    PASSWORD_TOO_SHORT: 'Password must be at least 8 characters',
    PASSWORDS_DONT_MATCH: 'Passwords do not match',
    LOGIN_FAILED: 'Invalid email or password',
    SESSION_EXPIRED: 'Your session has expired. Please login again.',
    RESERVATION_CONFLICT: 'This time slot is not available',
    INSUFFICIENT_STOCK: 'Insufficient stock available',
    PAYMENT_FAILED: 'Payment processing failed',
  },

  // Warning messages
  WARNING: {
    UNSAVED_CHANGES: 'You have unsaved changes. Are you sure you want to leave?',
    DELETE_CONFIRMATION: 'Are you sure you want to delete this item?',
    CANCEL_CONFIRMATION: 'Are you sure you want to cancel this reservation?',
    IRREVERSIBLE_ACTION: 'This action cannot be undone.',
    LOW_STOCK: 'Low stock warning',
    APPROACHING_CAPACITY: 'Approaching maximum capacity',
  },

  // Info messages
  INFO: {
    LOADING: 'Loading...',
    SAVING: 'Saving...',
    PROCESSING: 'Processing...',
    UPLOADING: 'Uploading...',
    NO_DATA: 'No data available',
    NO_RESULTS: 'No results found',
    EMPTY_STATE: 'Nothing to show here yet',
    COMING_SOON: 'This feature is coming soon',
    MAINTENANCE: 'System maintenance in progress',
    BETA_FEATURE: 'This is a beta feature',
  },

  // Form validation messages
  VALIDATION: {
    REQUIRED: 'This field is required',
    MIN_LENGTH: (min: number) => `Must be at least ${min} characters`,
    MAX_LENGTH: (max: number) => `Must be no more than ${max} characters`,
    MIN_VALUE: (min: number) => `Must be at least ${min}`,
    MAX_VALUE: (max: number) => `Must be no more than ${max}`,
    INVALID_FORMAT: 'Invalid format',
    INVALID_EMAIL: 'Please enter a valid email address',
    INVALID_PHONE: 'Please enter a valid phone number',
    INVALID_URL: 'Please enter a valid URL',
    INVALID_DATE: 'Please enter a valid date',
    FUTURE_DATE_REQUIRED: 'Date must be in the future',
    PAST_DATE_REQUIRED: 'Date must be in the past',
  },

  // Status messages
  STATUS: {
    ACTIVE: 'Active',
    INACTIVE: 'Inactive',
    PENDING: 'Pending',
    APPROVED: 'Approved',
    REJECTED: 'Rejected',
    CANCELLED: 'Cancelled',
    COMPLETED: 'Completed',
    IN_PROGRESS: 'In Progress',
    DRAFT: 'Draft',
    PUBLISHED: 'Published',
    ARCHIVED: 'Archived',
    CONFIRMED: 'Confirmed',
    PREPARING: 'Preparing',
    READY: 'Ready',
    DELIVERED: 'Delivered',
    PAID: 'Paid',
    UNPAID: 'Unpaid',
    REFUNDED: 'Refunded',
  },

  // Action messages
  ACTION: {
    CREATE: 'Create',
    EDIT: 'Edit',
    UPDATE: 'Update',
    DELETE: 'Delete',
    SAVE: 'Save',
    CANCEL: 'Cancel',
    SUBMIT: 'Submit',
    CONFIRM: 'Confirm',
    APPROVE: 'Approve',
    REJECT: 'Reject',
    PUBLISH: 'Publish',
    ARCHIVE: 'Archive',
    RESTORE: 'Restore',
    DUPLICATE: 'Duplicate',
    EXPORT: 'Export',
    IMPORT: 'Import',
    DOWNLOAD: 'Download',
    UPLOAD: 'Upload',
    SEARCH: 'Search',
    FILTER: 'Filter',
    SORT: 'Sort',
    REFRESH: 'Refresh',
    RESET: 'Reset',
    CLEAR: 'Clear',
    VIEW: 'View',
    VIEW_DETAILS: 'View Details',
    EDIT_DETAILS: 'Edit Details',
    ADD_NEW: 'Add New',
    LEARN_MORE: 'Learn More',
    GET_STARTED: 'Get Started',
    TRY_AGAIN: 'Try Again',
    BACK: 'Back',
    NEXT: 'Next',
    PREVIOUS: 'Previous',
    CONTINUE: 'Continue',
    FINISH: 'Finish',
    CLOSE: 'Close',
    OPEN: 'Open',
    EXPAND: 'Expand',
    COLLAPSE: 'Collapse',
    SHOW_MORE: 'Show More',
    SHOW_LESS: 'Show Less',
  },

  // Navigation messages
  NAVIGATION: {
    HOME: 'Home',
    DASHBOARD: 'Dashboard',
    MENU: 'Menu',
    RESERVATIONS: 'Reservations',
    ORDERS: 'Orders',
    CUSTOMERS: 'Customers',
    STAFF: 'Staff',
    REPORTS: 'Reports',
    SETTINGS: 'Settings',
    PROFILE: 'Profile',
    HELP: 'Help',
    LOGOUT: 'Logout',
    LOGIN: 'Login',
    REGISTER: 'Register',
    FORGOT_PASSWORD: 'Forgot Password',
    RESET_PASSWORD: 'Reset Password',
  },

  // Placeholder messages
  PLACEHOLDER: {
    SEARCH: 'Search...',
    SEARCH_CUSTOMERS: 'Search customers...',
    SEARCH_MENU_ITEMS: 'Search menu items...',
    SEARCH_RESERVATIONS: 'Search reservations...',
    SEARCH_ORDERS: 'Search orders...',
    ENTER_NAME: 'Enter name',
    ENTER_EMAIL: 'Enter email address',
    ENTER_PHONE: 'Enter phone number',
    ENTER_MESSAGE: 'Enter your message',
    SELECT_OPTION: 'Select an option',
    SELECT_DATE: 'Select date',
    SELECT_TIME: 'Select time',
    CHOOSE_FILE: 'Choose file',
    OPTIONAL: 'Optional',
  },

  // Time and date messages
  TIME: {
    TODAY: 'Today',
    YESTERDAY: 'Yesterday',
    TOMORROW: 'Tomorrow',
    THIS_WEEK: 'This Week',
    LAST_WEEK: 'Last Week',
    THIS_MONTH: 'This Month',
    LAST_MONTH: 'Last Month',
    THIS_YEAR: 'This Year',
    LAST_YEAR: 'Last Year',
    JUST_NOW: 'Just now',
    MINUTES_AGO: (minutes: number) => `${minutes} minute${minutes === 1 ? '' : 's'} ago`,
    HOURS_AGO: (hours: number) => `${hours} hour${hours === 1 ? '' : 's'} ago`,
    DAYS_AGO: (days: number) => `${days} day${days === 1 ? '' : 's'} ago`,
  },

  // Restaurant specific messages
  RESTAURANT: {
    TABLE_AVAILABLE: 'Table available',
    TABLE_OCCUPIED: 'Table occupied',
    TABLE_RESERVED: 'Table reserved',
    KITCHEN_BUSY: 'Kitchen is busy',
    ESTIMATED_WAIT: (minutes: number) => `Estimated wait: ${minutes} minutes`,
    PARTY_SIZE: 'Party size',
    SPECIAL_REQUESTS: 'Special requests',
    DIETARY_RESTRICTIONS: 'Dietary restrictions',
    ALLERGIES: 'Allergies',
    SEATING_PREFERENCE: 'Seating preference',
    INDOOR_SEATING: 'Indoor seating',
    OUTDOOR_SEATING: 'Outdoor seating',
    WINDOW_SEAT: 'Window seat',
    QUIET_AREA: 'Quiet area',
    HIGH_CHAIR_NEEDED: 'High chair needed',
    WHEELCHAIR_ACCESSIBLE: 'Wheelchair accessible',
  },
} as const;

// Type for message keys to ensure type safety
export type MessageKey = keyof typeof MESSAGES;
export type SuccessMessageKey = keyof typeof MESSAGES.SUCCESS;
export type ErrorMessageKey = keyof typeof MESSAGES.ERROR;
export type ValidationMessageKey = keyof typeof MESSAGES.VALIDATION;
