import { cn, generateSlug } from '../utils'

describe('Utils', () => {
  describe('cn function', () => {
    it('should merge class names correctly', () => {
      const result = cn('text-red-500', 'bg-blue-500')
      expect(result).toContain('text-red-500')
      expect(result).toContain('bg-blue-500')
    })

    it('should handle conditional classes', () => {
      const result = cn('base-class', true && 'conditional-class', false && 'hidden-class')
      expect(result).toContain('base-class')
      expect(result).toContain('conditional-class')
      expect(result).not.toContain('hidden-class')
    })

    it('should handle undefined and null values', () => {
      const result = cn('base-class', undefined, null, 'another-class')
      expect(result).toContain('base-class')
      expect(result).toContain('another-class')
    })
  })

  describe('generateSlug function', () => {
    it('should convert text to lowercase slug', () => {
      const result = generateSlug('Hello World')
      expect(result).toBe('hello-world')
    })

    it('should remove special characters', () => {
      const result = generateSlug('Hello, World!')
      expect(result).toBe('hello-world')
    })

    it('should handle multiple spaces', () => {
      const result = generateSlug('Hello    World   Test')
      expect(result).toBe('hello-world-test')
    })

    it('should handle empty string', () => {
      const result = generateSlug('')
      expect(result).toBe('')
    })

    it('should handle string with only special characters', () => {
      const result = generateSlug('!@#$%^&*()')
      expect(result).toBe('')
    })

    it('should trim whitespace', () => {
      const result = generateSlug('  Hello World  ')
      expect(result).toBe('hello-world')
    })
  })
})
