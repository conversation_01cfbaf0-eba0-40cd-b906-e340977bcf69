'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { NavigationSettings, NavigationType, defaultNavigationSettings } from '@/lib/types/navigation';

interface NavigationContextType {
  settings: NavigationSettings;
  updateSettings: (settings: Partial<NavigationSettings>) => void;
  resetSettings: () => void;
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

export function NavigationProvider({ children }: { children: React.ReactNode }) {
  // Initialize with default settings
  const [settings, setSettings] = useState<NavigationSettings>(defaultNavigationSettings);

  // Load settings from localStorage on mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('navigationSettings');
    if (savedSettings) {
      try {
        const parsedSettings = JSON.parse(savedSettings);
        setSettings(parsedSettings);
      } catch (error) {
        console.error('Failed to parse navigation settings:', error);
        // If parsing fails, reset to default
        localStorage.removeItem('navigationSettings');
      }
    }
  }, []);

  // Update settings
  const updateSettings = (newSettings: Partial<NavigationSettings>) => {
    setSettings(prevSettings => {
      const updatedSettings = { ...prevSettings, ...newSettings };
      // Save to localStorage
      localStorage.setItem('navigationSettings', JSON.stringify(updatedSettings));
      return updatedSettings;
    });
  };

  // Reset settings to default
  const resetSettings = () => {
    setSettings(defaultNavigationSettings);
    localStorage.setItem('navigationSettings', JSON.stringify(defaultNavigationSettings));
  };

  return (
    <NavigationContext.Provider value={{ settings, updateSettings, resetSettings }}>
      {children}
    </NavigationContext.Provider>
  );
}

export function useNavigation() {
  const context = useContext(NavigationContext);
  if (context === undefined) {
    throw new Error('useNavigation must be used within a NavigationProvider');
  }
  return context;
}
