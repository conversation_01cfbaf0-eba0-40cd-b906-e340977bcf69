/**
 * Fetch client for communicating with the Golang backend API
 * Handles authentication, error handling, and request/response formatting
 */

// Backend API configuration
const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:8080/api/v1';

interface FetchOptions extends RequestInit {
  timeout?: number;
}

interface ApiError {
  message: string;
  status: number;
  code?: string;
}

class BackendApiError extends Error {
  status: number;
  code?: string;

  constructor(message: string, status: number, code?: string) {
    super(message);
    this.name = 'BackendApiError';
    this.status = status;
    this.code = code;
  }
}

/**
 * Enhanced fetch client for backend API communication
 */
export async function fetchClient(
  endpoint: string,
  options: FetchOptions = {}
): Promise<Response> {
  const {
    timeout = 30000, // 30 seconds default timeout
    headers = {},
    ...restOptions
  } = options;

  // Ensure endpoint starts with /
  const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  const url = `${BACKEND_API_URL}${normalizedEndpoint}`;

  // Default headers
  const defaultHeaders: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...headers,
  };

  // Add authentication header if available
  // You might want to get this from cookies, session, or other auth mechanism
  const authToken = getAuthToken();
  if (authToken) {
    defaultHeaders['Authorization'] = `Bearer ${authToken}`;
  }

  // Create abort controller for timeout
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(url, {
      ...restOptions,
      headers: defaultHeaders,
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    // Handle non-2xx responses
    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      let errorCode: string | undefined;

      try {
        const errorData = await response.json();
        if (errorData.message) {
          errorMessage = errorData.message;
        }
        if (errorData.code) {
          errorCode = errorData.code;
        }
      } catch {
        // If we can't parse the error response, use the default message
      }

      throw new BackendApiError(errorMessage, response.status, errorCode);
    }

    return response;
  } catch (error) {
    clearTimeout(timeoutId);

    if (error instanceof BackendApiError) {
      throw error;
    }

    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        throw new BackendApiError('Request timeout', 408, 'TIMEOUT');
      }

      // Network or other fetch errors
      throw new BackendApiError(
        `Network error: ${error.message}`,
        0,
        'NETWORK_ERROR'
      );
    }

    throw new BackendApiError('Unknown error occurred', 500, 'UNKNOWN_ERROR');
  }
}

/**
 * Convenience method for GET requests
 */
export async function fetchGet(endpoint: string, options: FetchOptions = {}) {
  return fetchClient(endpoint, { ...options, method: 'GET' });
}

/**
 * Convenience method for POST requests
 */
export async function fetchPost(
  endpoint: string,
  data?: any,
  options: FetchOptions = {}
) {
  return fetchClient(endpoint, {
    ...options,
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
  });
}

/**
 * Convenience method for PUT requests
 */
export async function fetchPut(
  endpoint: string,
  data?: any,
  options: FetchOptions = {}
) {
  return fetchClient(endpoint, {
    ...options,
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined,
  });
}

/**
 * Convenience method for DELETE requests
 */
export async function fetchDelete(endpoint: string, options: FetchOptions = {}) {
  return fetchClient(endpoint, { ...options, method: 'DELETE' });
}

/**
 * Get authentication token from NextAuth session
 * This function will be updated to work with NextAuth JWT tokens
 */
function getAuthToken(): string | null {
  // For server-side API routes, we'll handle token extraction differently
  // This function is mainly for client-side usage

  // Option 1: From environment variable (for server-side)
  if (typeof process !== 'undefined' && process.env.API_TOKEN) {
    return process.env.API_TOKEN;
  }

  // For client-side, we'll need to get the token from NextAuth session
  // This will be handled by a separate function that can access the session
  return null;
}

/**
 * Helper to parse JSON response safely
 */
export async function parseJsonResponse<T = any>(response: Response): Promise<T> {
  try {
    return await response.json();
  } catch (error) {
    throw new BackendApiError(
      'Failed to parse JSON response',
      response.status,
      'PARSE_ERROR'
    );
  }
}

/**
 * Helper to handle API responses with proper error handling
 */
export async function handleApiResponse<T = any>(
  response: Response
): Promise<T> {
  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
    try {
      const errorData = await response.json();
      if (errorData.message) {
        errorMessage = errorData.message;
      }
    } catch {
      // Use default error message if JSON parsing fails
    }
    throw new BackendApiError(errorMessage, response.status);
  }

  return parseJsonResponse<T>(response);
}

export { BackendApiError };
export type { ApiError, FetchOptions };
