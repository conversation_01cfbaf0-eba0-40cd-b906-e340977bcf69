/**
 * Image utility functions for WebP conversion and optimization
 */

export interface ImageConversionOptions {
  quality?: number; // 0-1, default 0.8
  maxWidth?: number;
  maxHeight?: number;
  format?: 'webp' | 'jpeg' | 'png';
}

/**
 * Check if the browser supports WebP format
 */
export function supportsWebP(): Promise<boolean> {
  return new Promise((resolve) => {
    const webP = new Image();
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2);
    };
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
  });
}

/**
 * Convert an image file to WebP format with optional resizing
 */
export function convertToWebP(
  file: File,
  options: ImageConversionOptions = {}
): Promise<{ file: File; originalSize: number; newSize: number; compressionRatio: number }> {
  return new Promise((resolve, reject) => {
    const {
      quality = 0.8,
      maxWidth = 1920,
      maxHeight = 1920,
      format = 'webp'
    } = options;

    const img = new Image();
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      reject(new Error('Canvas context not available'));
      return;
    }

    img.onload = () => {
      // Calculate new dimensions while maintaining aspect ratio
      let { width, height } = img;
      
      if (width > maxWidth || height > maxHeight) {
        const aspectRatio = width / height;
        
        if (width > height) {
          width = Math.min(width, maxWidth);
          height = width / aspectRatio;
        } else {
          height = Math.min(height, maxHeight);
          width = height * aspectRatio;
        }
      }

      // Set canvas dimensions
      canvas.width = width;
      canvas.height = height;

      // Draw and compress image
      ctx.drawImage(img, 0, 0, width, height);

      // Convert to blob
      canvas.toBlob(
        (blob) => {
          if (!blob) {
            reject(new Error('Failed to convert image'));
            return;
          }

          // Create new file with WebP extension
          const originalName = file.name.replace(/\.[^/.]+$/, '');
          const newFile = new File([blob], `${originalName}.${format}`, {
            type: `image/${format}`,
            lastModified: Date.now(),
          });

          const originalSize = file.size;
          const newSize = newFile.size;
          const compressionRatio = ((originalSize - newSize) / originalSize) * 100;

          resolve({
            file: newFile,
            originalSize,
            newSize,
            compressionRatio
          });
        },
        `image/${format}`,
        quality
      );
    };

    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };

    // Load the image
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Resize an image file while maintaining aspect ratio
 */
export function resizeImage(
  file: File,
  maxWidth: number,
  maxHeight: number,
  quality: number = 0.8
): Promise<File> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      reject(new Error('Canvas context not available'));
      return;
    }

    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;
      const aspectRatio = width / height;

      if (width > maxWidth) {
        width = maxWidth;
        height = width / aspectRatio;
      }

      if (height > maxHeight) {
        height = maxHeight;
        width = height * aspectRatio;
      }

      // Set canvas dimensions
      canvas.width = width;
      canvas.height = height;

      // Draw image
      ctx.drawImage(img, 0, 0, width, height);

      // Convert to blob
      canvas.toBlob(
        (blob) => {
          if (!blob) {
            reject(new Error('Failed to resize image'));
            return;
          }

          const resizedFile = new File([blob], file.name, {
            type: file.type,
            lastModified: Date.now(),
          });

          resolve(resizedFile);
        },
        file.type,
        quality
      );
    };

    img.onerror = () => {
      reject(new Error('Failed to load image for resizing'));
    };

    img.src = URL.createObjectURL(file);
  });
}

/**
 * Get image dimensions from a file
 */
export function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      });
    };

    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };

    img.src = URL.createObjectURL(file);
  });
}

/**
 * Create a thumbnail from an image file
 */
export function createThumbnail(
  file: File,
  size: number = 150,
  quality: number = 0.7
): Promise<File> {
  return resizeImage(file, size, size, quality);
}

/**
 * Validate image file type and size
 */
export function validateImageFile(
  file: File,
  options: {
    allowedTypes?: string[];
    maxSizeBytes?: number;
    minWidth?: number;
    minHeight?: number;
    maxWidth?: number;
    maxHeight?: number;
  } = {}
): Promise<{ isValid: boolean; errors: string[] }> {
  return new Promise(async (resolve) => {
    const {
      allowedTypes = ['image/jpeg', 'image/png', 'image/webp'],
      maxSizeBytes = 5 * 1024 * 1024, // 5MB
      minWidth,
      minHeight,
      maxWidth,
      maxHeight
    } = options;

    const errors: string[] = [];

    // Check file type
    if (!allowedTypes.includes(file.type)) {
      const formats = allowedTypes.map(type => type.replace('image/', '').toUpperCase()).join(', ');
      errors.push(`File must be one of: ${formats}`);
    }

    // Check file size
    if (file.size > maxSizeBytes) {
      const maxSizeMB = Math.round(maxSizeBytes / (1024 * 1024));
      errors.push(`File size must be less than ${maxSizeMB}MB`);
    }

    // Check dimensions if specified
    if (minWidth || minHeight || maxWidth || maxHeight) {
      try {
        const dimensions = await getImageDimensions(file);
        
        if (minWidth && dimensions.width < minWidth) {
          errors.push(`Image width must be at least ${minWidth}px`);
        }
        
        if (minHeight && dimensions.height < minHeight) {
          errors.push(`Image height must be at least ${minHeight}px`);
        }
        
        if (maxWidth && dimensions.width > maxWidth) {
          errors.push(`Image width must be no more than ${maxWidth}px`);
        }
        
        if (maxHeight && dimensions.height > maxHeight) {
          errors.push(`Image height must be no more than ${maxHeight}px`);
        }
      } catch (error) {
        errors.push('Failed to read image dimensions');
      }
    }

    resolve({
      isValid: errors.length === 0,
      errors
    });
  });
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Check if a file is an image
 */
export function isImageFile(file: File): boolean {
  return file.type.startsWith('image/');
}

/**
 * Get the optimal image format based on browser support and file type
 */
export async function getOptimalImageFormat(originalType: string): Promise<'webp' | 'jpeg' | 'png'> {
  const webpSupported = await supportsWebP();
  
  if (webpSupported) {
    return 'webp';
  }
  
  // Fallback to original format or JPEG
  if (originalType === 'image/png') {
    return 'png';
  }
  
  return 'jpeg';
}

/**
 * Auto-optimize an image file for web use
 */
export async function optimizeImageForWeb(
  file: File,
  options: {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
    forceWebP?: boolean;
  } = {}
): Promise<{ file: File; optimized: boolean; savings: number }> {
  const {
    maxWidth = 1920,
    maxHeight = 1920,
    quality = 0.8,
    forceWebP = false
  } = options;

  try {
    // Check if optimization is needed
    const dimensions = await getImageDimensions(file);
    const needsResize = dimensions.width > maxWidth || dimensions.height > maxHeight;
    const isWebP = file.type === 'image/webp';
    const webpSupported = await supportsWebP();
    const shouldConvertToWebP = forceWebP || (webpSupported && !isWebP);

    if (!needsResize && !shouldConvertToWebP) {
      return {
        file,
        optimized: false,
        savings: 0
      };
    }

    // Optimize the image
    const result = await convertToWebP(file, {
      maxWidth,
      maxHeight,
      quality,
      format: shouldConvertToWebP ? 'webp' : (file.type.includes('png') ? 'png' : 'jpeg')
    });

    return {
      file: result.file,
      optimized: true,
      savings: result.compressionRatio
    };
  } catch (error) {
    console.error('Failed to optimize image:', error);
    return {
      file,
      optimized: false,
      savings: 0
    };
  }
}
