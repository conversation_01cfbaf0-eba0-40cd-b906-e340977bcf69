/**
 * Standardized status color utilities for consistent theming across the application
 * Uses ShadCN semantic classes with dark mode support
 */

export type StatusType = 
  | 'success' | 'error' | 'warning' | 'info' | 'neutral'
  | 'pending' | 'approved' | 'cancelled' | 'completed'
  | 'active' | 'inactive' | 'available' | 'occupied' | 'reserved'
  | 'confirmed' | 'preparing' | 'ready' | 'delivered'
  | 'critical' | 'high' | 'medium' | 'low';

export type CategoryType = 
  | 'appetizer' | 'main' | 'dessert' | 'beverage' | 'side'
  | 'order' | 'reservation' | 'review' | 'system' | 'staff' 
  | 'inventory' | 'payment' | 'promotion';

/**
 * Get standardized status colors using ShadCN semantic classes
 */
export const getStatusColors = (status: StatusType): string => {
  const statusMap: Record<StatusType, string> = {
    // General status types
    success: 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300',
    error: 'bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300',
    warning: 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300',
    info: 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300',
    neutral: 'bg-muted text-muted-foreground border-border',

    // Order/Process status
    pending: 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300',
    approved: 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300',
    cancelled: 'bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300',
    completed: 'bg-muted text-muted-foreground border-border',

    // Activity status
    active: 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300',
    inactive: 'bg-muted text-muted-foreground border-border',

    // Table status
    available: 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300',
    occupied: 'bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300',
    reserved: 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300',

    // Reservation status
    confirmed: 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300',

    // Order preparation status
    preparing: 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300',
    ready: 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300',
    delivered: 'bg-muted text-muted-foreground border-border',

    // Priority levels
    critical: 'bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300',
    high: 'bg-orange-50 text-orange-700 border-orange-200 dark:bg-orange-950 dark:text-orange-300',
    medium: 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300',
    low: 'bg-muted text-muted-foreground border-border',
  };

  return statusMap[status] || statusMap.neutral;
};

/**
 * Get standardized category colors using ShadCN semantic classes
 */
export const getCategoryColors = (category: CategoryType): string => {
  const categoryMap: Record<CategoryType, string> = {
    // Menu categories
    appetizer: 'bg-orange-50 text-orange-700 border-orange-200 dark:bg-orange-950 dark:text-orange-300',
    main: 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300',
    dessert: 'bg-pink-50 text-pink-700 border-pink-200 dark:bg-pink-950 dark:text-pink-300',
    beverage: 'bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-950 dark:text-purple-300',
    side: 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300',

    // Notification categories
    order: 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300',
    reservation: 'bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-950 dark:text-purple-300',
    review: 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300',
    system: 'bg-muted text-muted-foreground border-border',
    staff: 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300',
    inventory: 'bg-orange-50 text-orange-700 border-orange-200 dark:bg-orange-950 dark:text-orange-300',
    payment: 'bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-950 dark:text-emerald-300',
    promotion: 'bg-pink-50 text-pink-700 border-pink-200 dark:bg-pink-950 dark:text-pink-300',
  };

  return categoryMap[category] || categoryMap.system;
};

/**
 * Get semantic color classes for common UI elements
 */
export const getSemanticColors = () => ({
  // Primary elements
  primary: 'bg-primary text-primary-foreground',
  secondary: 'bg-secondary text-secondary-foreground',
  accent: 'bg-accent text-accent-foreground',
  muted: 'bg-muted text-muted-foreground',

  // Interactive elements
  card: 'bg-card text-card-foreground border-border',
  button: 'bg-primary text-primary-foreground hover:bg-primary/90',
  buttonSecondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
  buttonOutline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',

  // Text elements
  heading: 'text-foreground',
  body: 'text-foreground',
  caption: 'text-muted-foreground',
  link: 'text-primary hover:text-primary/80',

  // Form elements
  input: 'bg-background border-border text-foreground',
  inputFocus: 'border-ring ring-ring/50',
  label: 'text-foreground',

  // Feedback elements
  success: 'text-green-700 dark:text-green-300',
  error: 'text-red-700 dark:text-red-300',
  warning: 'text-yellow-700 dark:text-yellow-300',
  info: 'text-blue-700 dark:text-blue-300',
});

/**
 * Helper function to get consistent badge classes
 */
export const getBadgeClasses = (variant: StatusType | CategoryType = 'neutral'): string => {
  const baseClasses = 'inline-flex items-center rounded-md border px-2 py-1 text-xs font-medium';
  const colorClasses = getStatusColors(variant as StatusType) || getCategoryColors(variant as CategoryType);
  
  return `${baseClasses} ${colorClasses}`;
};

/**
 * Helper function to get consistent card classes
 */
export const getCardClasses = (variant: 'default' | 'elevated' | 'outlined' = 'default'): string => {
  const baseClasses = 'bg-card text-card-foreground border-border rounded-lg border';
  
  const variantClasses = {
    default: '',
    elevated: 'shadow-md',
    outlined: 'border-2',
  };

  return `${baseClasses} ${variantClasses[variant]}`;
};
