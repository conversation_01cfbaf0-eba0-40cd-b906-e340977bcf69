import { toast } from 'sonner';

/**
 * Error types for better error handling
 */
export enum ErrorType {
  VALIDATION = 'validation',
  NETWORK = 'network',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  NOT_FOUND = 'not_found',
  SERVER = 'server',
  UNKNOWN = 'unknown'
}

/**
 * Custom error class with additional context
 */
export class AppError extends Error {
  public readonly type: ErrorType;
  public readonly statusCode?: number;
  public readonly context?: Record<string, any>;

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN,
    statusCode?: number,
    context?: Record<string, any>
  ) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.statusCode = statusCode;
    this.context = context;
  }
}

/**
 * Error handling utilities
 */
export const errorHandlers = {
  /**
   * Handle API errors from RTK Query
   */
  handleApiError: (error: any): AppError => {
    if (error?.status) {
      switch (error.status) {
        case 400:
          return new AppError(
            error.data?.message || 'Invalid request',
            ErrorType.VALIDATION,
            400,
            error.data
          );
        case 401:
          return new AppError(
            'Authentication required',
            ErrorType.AUTHENTICATION,
            401
          );
        case 403:
          return new AppError(
            'Access denied',
            ErrorType.AUTHORIZATION,
            403
          );
        case 404:
          return new AppError(
            'Resource not found',
            ErrorType.NOT_FOUND,
            404
          );
        case 500:
          return new AppError(
            'Internal server error',
            ErrorType.SERVER,
            500
          );
        default:
          return new AppError(
            error.data?.message || 'An error occurred',
            ErrorType.UNKNOWN,
            error.status
          );
      }
    }

    if (error?.message) {
      return new AppError(error.message, ErrorType.NETWORK);
    }

    return new AppError('An unexpected error occurred', ErrorType.UNKNOWN);
  },

  /**
   * Handle form validation errors
   */
  handleValidationError: (errors: Record<string, any>): AppError => {
    const firstError = Object.values(errors)[0];
    const message = Array.isArray(firstError) ? firstError[0] : firstError?.message || 'Validation failed';
    
    return new AppError(
      message,
      ErrorType.VALIDATION,
      400,
      errors
    );
  },

  /**
   * Show error toast notification
   */
  showErrorToast: (error: AppError | Error | string) => {
    let message: string;
    let description: string | undefined;

    if (typeof error === 'string') {
      message = error;
    } else if (error instanceof AppError) {
      message = error.message;
      description = error.type !== ErrorType.UNKNOWN ? `Error type: ${error.type}` : undefined;
    } else {
      message = error.message || 'An unexpected error occurred';
    }

    toast.error(message, {
      description,
      duration: 5000,
    });
  },

  /**
   * Show success toast notification
   */
  showSuccessToast: (message: string, description?: string) => {
    toast.success(message, {
      description,
      duration: 3000,
    });
  },

  /**
   * Show info toast notification
   */
  showInfoToast: (message: string, description?: string) => {
    toast.info(message, {
      description,
      duration: 4000,
    });
  },

  /**
   * Show warning toast notification
   */
  showWarningToast: (message: string, description?: string) => {
    toast.warning(message, {
      description,
      duration: 4000,
    });
  }
};

/**
 * Error boundary fallback component props
 */
export interface ErrorFallbackProps {
  error: Error;
  resetError: () => void;
}

/**
 * Retry utility for failed operations
 */
export async function retryOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxRetries) {
        throw lastError;
      }
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  throw lastError!;
}

/**
 * Safe async operation wrapper
 */
export async function safeAsync<T>(
  operation: () => Promise<T>,
  fallback?: T
): Promise<{ data?: T; error?: AppError }> {
  try {
    const data = await operation();
    return { data };
  } catch (error) {
    const appError = errorHandlers.handleApiError(error);
    return { error: appError, data: fallback };
  }
}

/**
 * Validation error formatter for forms
 */
export function formatValidationErrors(errors: any): Record<string, string> {
  const formatted: Record<string, string> = {};

  if (errors?.inner) {
    // Yup validation errors
    errors.inner.forEach((error: any) => {
      if (error.path) {
        formatted[error.path] = error.message;
      }
    });
  } else if (errors?.issues) {
    // Zod validation errors
    errors.issues.forEach((issue: any) => {
      const path = issue.path.join('.');
      formatted[path] = issue.message;
    });
  } else if (typeof errors === 'object') {
    // Generic object errors
    Object.entries(errors).forEach(([key, value]) => {
      formatted[key] = Array.isArray(value) ? value[0] : String(value);
    });
  }

  return formatted;
}
