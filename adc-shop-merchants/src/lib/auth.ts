import { NextRequest } from 'next/server';

/**
 * Check if the request is authenticated
 * This is a mock implementation for development purposes
 */
export async function checkAuth(request: NextRequest) {
  // In a real app, we would check for a valid session or token
  // For now, we'll just return true for all requests
  return {
    authenticated: true,
    user: {
      id: 'user-1',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'merchant',
    },
  };
}
