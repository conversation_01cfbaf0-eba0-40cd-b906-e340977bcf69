/**
 * Backend authentication utilities
 * Handles token generation and validation for backend API calls
 */

import { getToken } from 'next-auth/jwt'
import { NextRequest } from 'next/server'

/**
 * Generate a simple authentication token for backend
 * This creates a basic token that your Golang backend can validate
 */
export function generateBackendAuthToken(userId: string, email: string, role: string): string {
  const payload = {
    user_id: userId,
    email: email,
    role: role,
    issued_at: Math.floor(Date.now() / 1000),
    expires_at: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours
  }

  // For now, we'll use a simple base64 encoded token
  // In production, you should use proper JWT signing with a shared secret
  const tokenString = JSON.stringify(payload)
  return Buffer.from(tokenString).toString('base64')
}

/**
 * Get authentication headers for backend API calls
 */
export async function getBackendAuthHeaders(request: NextRequest): Promise<Record<string, string>> {
  try {
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    })

    console.log('NextAuth token:', token ? 'Found' : 'Not found');
    if (token) {
      console.log('Token details:', {
        sub: token.sub,
        email: token.email,
        name: token.name,
        role: token.role
      });
    }

    if (!token) {
      console.log('No NextAuth token found, returning empty headers');
      return {}
    }

    // Extract role name from role object or use string directly
    const roleName = typeof token.role === 'object' && token.role?.name
      ? token.role.name
      : (token.role as string) || 'user';

    // Generate a backend token
    const backendToken = generateBackendAuthToken(
      token.sub || '',
      token.email || '',
      roleName
    )

    const headers = {
      'Authorization': `Bearer ${backendToken}`,
      'X-User-ID': token.sub || '',
      'X-User-Email': token.email || '',
      'X-User-Role': roleName,
      'X-Auth-Source': 'nextauth',
    };

    console.log('Generated auth headers:', headers);
    return headers;
  } catch (error) {
    console.error('Failed to get backend auth headers:', error)
    return {}
  }
}

/**
 * Validate backend token (for your Golang backend to implement)
 * This is the logic your backend should use to validate tokens
 */
export function validateBackendToken(token: string): { valid: boolean; payload?: any } {
  try {
    // Remove 'Bearer ' prefix if present
    const cleanToken = token.replace(/^Bearer\s+/, '')

    // Decode base64 token
    const tokenString = Buffer.from(cleanToken, 'base64').toString('utf-8')
    const payload = JSON.parse(tokenString)

    // Check if token is expired
    const now = Math.floor(Date.now() / 1000)
    if (payload.expires_at && payload.expires_at < now) {
      return { valid: false }
    }

    // Validate required fields
    if (!payload.user_id || !payload.email) {
      return { valid: false }
    }

    return { valid: true, payload }
  } catch (error) {
    return { valid: false }
  }
}

/**
 * Create authentication middleware for API routes
 */
export async function withAuth<T>(
  request: NextRequest,
  handler: (request: NextRequest, user: any) => Promise<T>
): Promise<T> {
  const token = await getToken({
    req: request,
    secret: process.env.NEXTAUTH_SECRET,
  })

  if (!token) {
    throw new Error('Authentication required')
  }

  const user = {
    id: token.sub,
    email: token.email,
    name: token.name,
    role: token.role || 'user',
  }

  return handler(request, user)
}
