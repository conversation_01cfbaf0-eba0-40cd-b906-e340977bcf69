/**
 * Custom hook for dashboard statistics using shop slugs
 * Replaces the merchant-based dashboard reports
 */

import React, { useState, useCallback } from 'react';

export interface DashboardStats {
  todayOrders: number;
  todayRevenue: number;
  todayReservations: number;
  activeStaff: number;
  ordersGrowth: number;
  revenueGrowth: number;
  reservationsGrowth: number;
}

export interface RealTimeMetrics {
  activeOrders: number;
  todayRevenue: number;
  onlineCustomers: number;
  averageWaitTime: number;
}

export function useDashboardStats(shopSlug: string, branchSlug: string) {
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [realTimeMetrics, setRealTimeMetrics] = useState<RealTimeMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchDashboardStats = useCallback(async () => {
    if (!shopSlug || !branchSlug) return;

    setIsLoading(true);
    setError(null);

    try {
      // Fetch dashboard stats via Next.js API proxy
      const statsResponse = await fetch(`/api/shops/slug/${shopSlug}/dashboard/stats?branchSlug=${branchSlug}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setDashboardStats(statsData);
      } else {
        console.warn('Failed to fetch dashboard stats:', statsResponse.status);
        // Set default values for dashboard stats
        setDashboardStats({
          todayOrders: 0,
          todayRevenue: 0,
          todayReservations: 0,
          activeStaff: 0,
          ordersGrowth: 0,
          revenueGrowth: 0,
          reservationsGrowth: 0,
        });
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      setDashboardStats({
        todayOrders: 0,
        todayRevenue: 0,
        todayReservations: 0,
        activeStaff: 0,
        ordersGrowth: 0,
        revenueGrowth: 0,
        reservationsGrowth: 0,
      });
    }

    try {
      // Fetch real-time metrics via Next.js API proxy
      const metricsResponse = await fetch(`/api/shops/slug/${shopSlug}/metrics/realtime?branchSlug=${branchSlug}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (metricsResponse.ok) {
        const metricsData = await metricsResponse.json();
        setRealTimeMetrics(metricsData);
      } else {
        console.warn('Failed to fetch real-time metrics:', metricsResponse.status);
        // Set default values for real-time metrics
        setRealTimeMetrics({
          activeOrders: 0,
          todayRevenue: 0,
          onlineCustomers: 0,
          averageWaitTime: 0,
        });
      }
    } catch (error) {
      console.error('Error fetching real-time metrics:', error);
      setRealTimeMetrics({
        activeOrders: 0,
        todayRevenue: 0,
        onlineCustomers: 0,
        averageWaitTime: 0,
      });
    }

    setIsLoading(false);
  }, [shopSlug, branchSlug]);

  const refetch = useCallback(() => {
    fetchDashboardStats();
  }, [fetchDashboardStats]);

  // Auto-fetch on mount and when dependencies change
  React.useEffect(() => {
    fetchDashboardStats();
  }, [fetchDashboardStats]);

  return {
    dashboardStats,
    realTimeMetrics,
    isLoading,
    error,
    refetch,
  };
}
