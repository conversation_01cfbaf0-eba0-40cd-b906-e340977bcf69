import { useState } from 'react'

interface RegisterData {
  name: string
  email: string
  password: string
}

interface RegisterResponse {
  message: string
  user: {
    id: string
    email: string
    name: string
  }
  needsVerification: boolean
}

export function useRegister() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  const register = async (data: RegisterData): Promise<RegisterResponse | null> => {
    setIsLoading(true)
    setError(null)
    setSuccess(null)

    try {
      const response = await fetch('/api/auth/register-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Registration failed')
      }

      setSuccess(result.message)
      return result

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Registration failed'
      setError(errorMessage)
      return null
    } finally {
      setIsLoading(false)
    }
  }

  const clearMessages = () => {
    setError(null)
    setSuccess(null)
  }

  return {
    register,
    isLoading,
    error,
    success,
    clearMessages,
  }
}
