/**
 * Enhanced useNotifications Hook with Backend-Driven Filtering, Sorting, and Pagination
 * Following the Purchase Orders pattern for consistent implementation
 */

import { useState, useCallback, useMemo } from 'react';
import {
  useGetNotificationsQuery,
  useUpdateNotificationMutation,
  useBulkUpdateNotificationsMutation,
  useDeleteNotificationMutation,
  useMarkAllNotificationsAsReadMutation,
  useClearAllNotificationsMutation,
  type NotificationsFilters,
  type Notification,
} from '@/lib/redux/api/endpoints/restaurant/notificationsApi';


// Hook options interface
export interface UseNotificationsOptions {
  shopSlug: string;
  branchSlug: string;
  initialFilters?: Partial<NotificationsFilters>;
}

// Enhanced useNotifications hook
export function useNotifications({
  shopSlug,
  branchSlug,
  initialFilters = {}
}: UseNotificationsOptions) {
  const [filters, setFilters] = useState<NotificationsFilters>({
    page: 1,
    limit: 20,
    sort_by: 'timestamp',
    sort_order: 'desc',
    ...initialFilters
  });



  // API query
  const {
    data: notificationsData,
    isLoading,
    isError,
    error,
    refetch
  } = useGetNotificationsQuery({
    shopSlug,
    branchSlug,
    filters
  });

  // Mutations - TODO: Update these to work with slugs (for now using shopId/branchId)
  const [updateNotification, { isLoading: isUpdating }] = useUpdateNotificationMutation();
  const [bulkUpdateNotifications, { isLoading: isBulkUpdating }] = useBulkUpdateNotificationsMutation();
  const [deleteNotification, { isLoading: isDeleting }] = useDeleteNotificationMutation();
  const [markAllAsRead, { isLoading: isMarkingAllAsRead }] = useMarkAllNotificationsAsReadMutation();
  const [clearAll, { isLoading: isClearing }] = useClearAllNotificationsMutation();

  // Data processing
  const notifications = notificationsData?.data || [];
  const pagination = notificationsData ? {
    currentPage: notificationsData.page,
    totalPages: notificationsData.totalPages,
    totalItems: notificationsData.total,
    itemsPerPage: notificationsData.limit,
    hasNextPage: notificationsData.page < notificationsData.totalPages,
    hasPreviousPage: notificationsData.page > 1,
  } : undefined;

  // Statistics
  const stats = useMemo(() => {
    const summary = notificationsData?.summary;
    return {
      totalNotifications: summary?.totalNotifications || 0,
      unreadNotifications: summary?.unreadNotifications || 0,
      readNotifications: summary?.readNotifications || 0,
      urgentNotifications: summary?.urgentNotifications || 0,
      highPriorityNotifications: summary?.highPriorityNotifications || 0,
      byType: summary?.byType || {},
      byPriority: summary?.byPriority || {},
      unreadPercentage: summary?.totalNotifications ?
        Math.round((summary.unreadNotifications / summary.totalNotifications) * 100) : 0,
    };
  }, [notificationsData?.summary]);

  // Filter management functions
  const updateFilters = useCallback((newFilters: Partial<NotificationsFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const resetFilters = useCallback(() => {
    setFilters({
      page: 1,
      limit: 20,
      sort_by: 'timestamp',
      sort_order: 'desc',
    });
  }, []);

  // Search function
  const handleSearch = useCallback((searchTerm: string) => {
    updateFilters({ search: searchTerm, page: 1 });
  }, [updateFilters]);

  // Filter functions
  const handleTypeFilter = useCallback((type: NotificationsFilters['type']) => {
    updateFilters({ type, page: 1 });
  }, [updateFilters]);

  const handlePriorityFilter = useCallback((priority: NotificationsFilters['priority']) => {
    updateFilters({ priority, page: 1 });
  }, [updateFilters]);

  const handleReadStatusFilter = useCallback((isRead: boolean | undefined) => {
    updateFilters({ isRead, page: 1 });
  }, [updateFilters]);

  const handleDateRangeFilter = useCallback((dateRange: NotificationsFilters['dateRange']) => {
    updateFilters({ dateRange, page: 1 });
  }, [updateFilters]);

  // Sorting function
  const handleSort = useCallback((sort_by: NotificationsFilters['sort_by'], sort_order?: NotificationsFilters['sort_order']) => {
    const newSortOrder = sort_order || (filters.sort_by === sort_by && filters.sort_order === 'asc' ? 'desc' : 'asc');
    updateFilters({ sort_by, sort_order: newSortOrder, page: 1 });
  }, [filters.sort_by, filters.sort_order, updateFilters]);

  // Pagination functions
  const handlePageChange = useCallback((page: number) => {
    updateFilters({ page });
  }, [updateFilters]);

  const handleLimitChange = useCallback((limit: number) => {
    updateFilters({ limit, page: 1 });
  }, [updateFilters]);

  // Notification actions
  const handleMarkAsRead = useCallback(async (notificationId: string) => {
    if (!shopSlug || !branchSlug) return;
    try {
      await updateNotification({
        shopSlug,
        branchSlug,
        notificationId,
        updates: { isRead: true }
      }).unwrap();
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  }, [updateNotification, shopSlug, branchSlug]);

  const handleMarkAsUnread = useCallback(async (notificationId: string) => {
    if (!shopSlug || !branchSlug) return;
    try {
      await updateNotification({
        shopSlug,
        branchSlug,
        notificationId,
        updates: { isRead: false }
      }).unwrap();
    } catch (error) {
      console.error('Failed to mark notification as unread:', error);
    }
  }, [updateNotification, shopSlug, branchSlug]);

  const handleDeleteNotification = useCallback(async (notificationId: string) => {
    if (!shopSlug || !branchSlug) return;
    try {
      await deleteNotification({
        shopSlug,
        branchSlug,
        notificationId
      }).unwrap();
    } catch (error) {
      console.error('Failed to delete notification:', error);
    }
  }, [deleteNotification, shopSlug, branchSlug]);

  const handleMarkAllAsRead = useCallback(async () => {
    if (!shopSlug || !branchSlug) return;
    try {
      await markAllAsRead({
        shopSlug,
        branchSlug
      }).unwrap();
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  }, [markAllAsRead, shopSlug, branchSlug]);

  const handleClearAllNotifications = useCallback(async () => {
    if (!shopSlug || !branchSlug) return;
    try {
      await clearAll({
        shopSlug,
        branchSlug
      }).unwrap();
    } catch (error) {
      console.error('Failed to clear all notifications:', error);
    }
  }, [clearAll, shopSlug, branchSlug]);

  const handleBulkMarkAsRead = useCallback(async (notificationIds: string[]) => {
    if (!shopSlug || !branchSlug || notificationIds.length === 0) return;
    try {
      await bulkUpdateNotifications({
        shopSlug,
        branchSlug,
        request: {
          notificationIds,
          updates: { isRead: true }
        }
      }).unwrap();
    } catch (error) {
      console.error('Failed to bulk mark notifications as read:', error);
    }
  }, [bulkUpdateNotifications, shopSlug, branchSlug]);

  // Convenience functions
  const getNotificationById = useCallback((id: string): Notification | undefined => {
    return notifications.find(notification => notification.id === id);
  }, [notifications]);

  const getUnreadNotifications = useCallback((): Notification[] => {
    return notifications.filter(notification => !notification.isRead);
  }, [notifications]);

  const getNotificationsByType = useCallback((type: string): Notification[] => {
    return notifications.filter(notification => notification.type === type);
  }, [notifications]);

  const getNotificationsByPriority = useCallback((priority: string): Notification[] => {
    return notifications.filter(notification => notification.priority === priority);
  }, [notifications]);

  // Utility functions
  const formatTimestamp = useCallback((timestamp: string): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return 'Just now';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes} ${diffInMinutes === 1 ? 'minute' : 'minutes'} ago`;
    } else if (diffInMinutes < 24 * 60) {
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
    } else if (diffInMinutes < 7 * 24 * 60) {
      const days = Math.floor(diffInMinutes / (24 * 60));
      return `${days} ${days === 1 ? 'day' : 'days'} ago`;
    } else {
      return date.toLocaleDateString();
    }
  }, []);

  const getPriorityColor = useCallback((priority: string): string => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-950 dark:border-red-800';
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200 dark:text-orange-400 dark:bg-orange-950 dark:border-orange-800';
      case 'medium': return 'text-blue-600 bg-blue-50 border-blue-200 dark:text-blue-400 dark:bg-blue-950 dark:border-blue-800';
      case 'low': return 'text-muted-foreground bg-muted border-border';
      default: return 'text-muted-foreground bg-muted border-border';
    }
  }, []);

  const getTypeIcon = useCallback((type: string): string => {
    switch (type) {
      case 'order': return '🛒';
      case 'reservation': return '📅';
      case 'review': return '⭐';
      case 'system': return '⚙️';
      case 'staff': return '👥';
      case 'inventory': return '📦';
      case 'payment': return '💳';
      case 'promotion': return '🎉';
      default: return '📢';
    }
  }, []);

  return {
    // Data
    notifications,
    totalCount: notificationsData?.total || 0,
    currentPage: filters.page,
    totalPages: pagination?.totalPages || 0,
    filters,
    stats,
    pagination,

    // Loading states
    isLoading,
    isUpdating,
    isBulkUpdating,
    isDeleting,
    isMarkingAllAsRead,
    isClearing,
    isError,
    error,

    // Filter management
    updateFilters,
    resetFilters,
    handleSearch,
    handleTypeFilter,
    handlePriorityFilter,
    handleReadStatusFilter,
    handleDateRangeFilter,
    handleSort,
    handlePageChange,
    handleLimitChange,

    // Notification actions
    handleMarkAsRead,
    handleMarkAsUnread,
    handleDeleteNotification,
    handleMarkAllAsRead,
    handleClearAllNotifications,
    handleBulkMarkAsRead,

    // Convenience functions
    getNotificationById,
    getUnreadNotifications,
    getNotificationsByType,
    getNotificationsByPriority,

    // Utility functions
    formatTimestamp,
    getPriorityColor,
    getTypeIcon,
    refetch,
  };
}