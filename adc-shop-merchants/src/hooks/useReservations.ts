/**
 * Custom hook for reservation management
 * Separates business logic from UI components
 */

import { useState, useCallback } from 'react';
import {
  useGetReservationsQuery,
  useGetReservationQuery,
  useGetTodayReservationsQuery,
  useCreateReservationMutation,
  useUpdateReservationMutation,
  useUpdateReservationStatusMutation,
  useCancelReservationMutation,
  useCheckInReservationMutation,
  useMarkNoShowMutation,
  useSendConfirmationMutation,
  useGetReservationStatsQuery,
  type ReservationFilters,
} from '@/lib/redux/api/endpoints/restaurant/reservationsApi';
import { errorHandlers } from '@/lib/utils/error-handling';
import { MESSAGES } from '@/lib/constants/messages';

export interface UseReservationsOptions {
  shopSlug: string;
  branchSlug: string;
  initialFilters?: ReservationFilters;
}

export function useReservations({
  shopSlug,
  branchSlug,
  initialFilters = {}
}: UseReservationsOptions) {
  const [filters, setFilters] = useState<ReservationFilters>({
    page: 1,
    limit: 20,
    ...initialFilters
  });

  // RTK Query hooks
  const {
    data: reservationsData,
    isLoading,
    isError,
    error,
    refetch
  } = useGetReservationsQuery({
    shopSlug,
    branchSlug,
    filters
  });

  const {
    data: todayReservations,
    isLoading: isLoadingToday,
    refetch: refetchToday
  } = useGetTodayReservationsQuery({
    shopSlug,
    branchSlug
  });

  const {
    data: reservationStats,
    isLoading: isLoadingStats,
    error: statsError,
    refetch: refetchStats
  } = useGetReservationStatsQuery({
    shopSlug,
    branchSlug
  }, {
    skip: !shopSlug || !branchSlug
  });

  const [createReservation, { isLoading: isCreating }] = useCreateReservationMutation();
  const [updateReservation, { isLoading: isUpdating }] = useUpdateReservationMutation();
  const [cancelReservation, { isLoading: isCancelling }] = useCancelReservationMutation();

  // Filter management
  const updateFilters = useCallback((newFilters: Partial<ReservationFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({ page: 1, limit: 20 });
  }, []);

  // Reservation actions
  const handleCreateReservation = useCallback(async (reservationData: any) => {
    try {
      await createReservation({
        shopSlug,
        branchSlug,
        reservationData
      }).unwrap();

      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.RESERVATION_CREATED);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [shopSlug, branchSlug, createReservation, refetch]);

  const handleUpdateReservation = useCallback(async (reservationSlug: string, updates: any) => {
    try {
      await updateReservation({
        shopSlug,
        branchSlug,
        reservationData: { slug: reservationSlug, ...updates }
      }).unwrap();

      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.RESERVATION_UPDATED);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [shopSlug, branchSlug, updateReservation, refetch]);

  const handleCancelReservation = useCallback(async (reservationSlug: string) => {
    try {
      await cancelReservation({
        shopSlug,
        branchSlug,
        reservationSlug
      }).unwrap();

      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.RESERVATION_CANCELLED);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [shopSlug, branchSlug, cancelReservation, refetch]);

  // Status helpers
  const getStatusColor = useCallback((status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }, []);

  const getStatusText = useCallback((status: string) => {
    return MESSAGES.STATUS[status.toUpperCase() as keyof typeof MESSAGES.STATUS] || status;
  }, []);

  // Data processing
  const reservations = reservationsData?.data || [];
  const pagination = reservationsData?.pagination;
  const totalCount = pagination?.totalItems || 0;

  // Filter statistics
  const statusCounts = reservations.reduce((acc, reservation) => {
    const status = reservation.status;
    acc[status] = (acc[status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return {
    // Data
    reservations,
    pagination,
    totalCount,
    statusCounts,

    // Loading states
    isLoading,
    isCreating,
    isUpdating,
    isCancelling,
    isError,
    error,

    // Filters
    filters,
    updateFilters,
    clearFilters,

    // Actions
    createReservation: handleCreateReservation,
    updateReservation: handleUpdateReservation,
    cancelReservation: handleCancelReservation,
    refetch,

    // Helpers
    getStatusColor,
    getStatusText,
  };
}

// Hook for single reservation
export function useReservation(shopSlug: string, branchSlug: string, reservationSlug: string) {
  const {
    data: reservationData,
    isLoading,
    isError,
    error,
    refetch
  } = useGetReservationQuery({
    shopSlug,
    branchSlug,
    reservationSlug
  });

  const [updateReservation, { isLoading: isUpdating }] = useUpdateReservationMutation();
  const [cancelReservation, { isLoading: isCancelling }] = useCancelReservationMutation();

  const handleUpdate = useCallback(async (updates: any) => {
    try {
      await updateReservation({
        shopSlug,
        branchSlug,
        reservationData: { slug: reservationSlug, ...updates }
      }).unwrap();

      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.RESERVATION_UPDATED);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [shopSlug, branchSlug, reservationSlug, updateReservation, refetch]);

  const handleCancel = useCallback(async () => {
    try {
      await cancelReservation({
        shopSlug,
        branchSlug,
        reservationSlug
      }).unwrap();

      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.RESERVATION_CANCELLED);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [shopSlug, branchSlug, reservationSlug, cancelReservation, refetch]);

  return {
    reservation: reservationData,
    isLoading,
    isUpdating,
    isCancelling,
    isError,
    error,
    updateReservation: handleUpdate,
    cancelReservation: handleCancel,
    refetch,
  };
}
