/**
 * Custom hook for purchase orders with consistent filtering, sorting, and pagination
 * Separates business logic from UI components
 */

import { useState, useCallback, useMemo } from 'react';
import {
  useGetPurchaseOrdersQuery,
  useGetPurchaseOrderQuery,
  useCreatePurchaseOrderMutation,
  useUpdatePurchaseOrderMutation,
  useDeletePurchaseOrderMutation,
  useGetSuppliersQuery,
  type PurchaseOrderFilters,
  type PurchaseOrderStatus,
  type CreatePurchaseOrderRequest,
  type UpdatePurchaseOrderRequest,
} from '@/lib/redux/api/endpoints/restaurant/purchaseOrdersApi';
import { errorHandlers } from '@/lib/utils/error-handling';
import { MESSAGES } from '@/lib/constants/messages';

export interface UsePurchaseOrdersOptions {
  shopId: string;
  branchId: string;
  initialFilters?: PurchaseOrderFilters;
}

export function usePurchaseOrders({
  shopId,
  branchId,
  initialFilters = {}
}: UsePurchaseOrdersOptions) {
  const [filters, setFilters] = useState<PurchaseOrderFilters>({
    page: 1,
    limit: 20,
    sort_by: 'created_at',
    sort_order: 'desc',
    ...initialFilters
  });

  // API queries
  const {
    data: purchaseOrdersData,
    isLoading,
    isError,
    error,
    refetch
  } = useGetPurchaseOrdersQuery({
    shopId,
    branchId,
    filters
  });

  const {
    data: suppliers,
    isLoading: isLoadingSuppliers,
    refetch: refetchSuppliers
  } = useGetSuppliersQuery();

  // API mutations
  const [createPurchaseOrder, {
    isLoading: isCreating,
    error: createError
  }] = useCreatePurchaseOrderMutation();

  const [updatePurchaseOrder, {
    isLoading: isUpdating,
    error: updateError
  }] = useUpdatePurchaseOrderMutation();

  const [deletePurchaseOrder, {
    isLoading: isDeleting,
    error: deleteError
  }] = useDeletePurchaseOrderMutation();

  // Computed values
  const purchaseOrders = useMemo(() => purchaseOrdersData?.data || [], [purchaseOrdersData]);
  const totalCount = useMemo(() => purchaseOrdersData?.total || 0, [purchaseOrdersData]);
  const currentPage = useMemo(() => purchaseOrdersData?.page || 1, [purchaseOrdersData]);
  const totalPages = useMemo(() => purchaseOrdersData?.total_pages || 1, [purchaseOrdersData]);

  // Filter handlers
  const updateFilters = useCallback((newFilters: Partial<PurchaseOrderFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      // Reset to page 1 when filters change (except for page changes)
      page: 'page' in newFilters ? newFilters.page : 1
    }));
  }, []);

  const resetFilters = useCallback(() => {
    setFilters({
      page: 1,
      limit: 20,
      sort_by: 'created_at',
      sort_order: 'desc'
    });
  }, []);

  // Search handler
  const handleSearch = useCallback((searchTerm: string) => {
    updateFilters({ search: searchTerm });
  }, [updateFilters]);

  // Status filter handler
  const handleStatusFilter = useCallback((status: PurchaseOrderStatus | 'all') => {
    updateFilters({ status: status === 'all' ? undefined : status });
  }, [updateFilters]);

  // Supplier filter handler
  const handleSupplierFilter = useCallback((supplierId: string | 'all') => {
    updateFilters({ supplier_id: supplierId === 'all' ? undefined : supplierId });
  }, [updateFilters]);

  // Sorting handlers
  const handleSort = useCallback((sortBy: PurchaseOrderFilters['sort_by'], sortOrder?: 'asc' | 'desc') => {
    updateFilters({
      sort_by: sortBy,
      sort_order: sortOrder || (filters.sort_order === 'asc' ? 'desc' : 'asc')
    });
  }, [filters.sort_order, updateFilters]);

  // Pagination handlers
  const handlePageChange = useCallback((page: number) => {
    updateFilters({ page });
  }, [updateFilters]);

  const handleLimitChange = useCallback((limit: number) => {
    updateFilters({ limit, page: 1 });
  }, [updateFilters]);

  // CRUD operations
  const handleCreatePurchaseOrder = useCallback(async (data: CreatePurchaseOrderRequest) => {
    try {
      await createPurchaseOrder({
        shopId,
        branchId,
        data
      }).unwrap();

      // Refetch data after successful creation
      refetch();

      return { success: true };
    } catch (error) {
      console.error('Failed to create purchase order:', error);
      return {
        success: false,
        error: errorHandlers.getErrorMessage(error) || MESSAGES.ERRORS.GENERIC
      };
    }
  }, [shopId, branchId, createPurchaseOrder, refetch]);

  const handleUpdatePurchaseOrder = useCallback(async (orderId: string, data: UpdatePurchaseOrderRequest) => {
    try {
      await updatePurchaseOrder({
        shopId,
        branchId,
        orderId,
        data
      }).unwrap();

      // Refetch data after successful update
      refetch();

      return { success: true };
    } catch (error) {
      console.error('Failed to update purchase order:', error);
      return {
        success: false,
        error: errorHandlers.getErrorMessage(error) || MESSAGES.ERRORS.GENERIC
      };
    }
  }, [shopId, branchId, updatePurchaseOrder, refetch]);

  const handleDeletePurchaseOrder = useCallback(async (orderId: string) => {
    try {
      await deletePurchaseOrder({
        shopId,
        branchId,
        orderId
      }).unwrap();

      // Refetch data after successful deletion
      refetch();

      return { success: true };
    } catch (error) {
      console.error('Failed to delete purchase order:', error);
      return {
        success: false,
        error: errorHandlers.getErrorMessage(error) || MESSAGES.ERRORS.GENERIC
      };
    }
  }, [shopId, branchId, deletePurchaseOrder, refetch]);

  // Status badge helper
  const getStatusColor = useCallback((status: PurchaseOrderStatus) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300';
      case 'approved':
        return 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300';
      case 'ordered':
        return 'bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-950 dark:text-purple-300';
      case 'received':
        return 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300';
      case 'partial':
        return 'bg-orange-50 text-orange-700 border-orange-200 dark:bg-orange-950 dark:text-orange-300';
      case 'cancelled':
        return 'bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300';
      case 'completed':
        return 'bg-muted text-muted-foreground border-border';
      default:
        return 'bg-muted text-muted-foreground border-border';
    }
  }, []);

  // Format currency helper
  const formatCurrency = useCallback((amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  }, []);

  return {
    // Data
    purchaseOrders,
    suppliers: suppliers || [],
    totalCount,
    currentPage,
    totalPages,
    filters,

    // Loading states
    isLoading,
    isLoadingSuppliers,
    isCreating,
    isUpdating,
    isDeleting,

    // Error states
    isError,
    error,
    createError,
    updateError,
    deleteError,

    // Filter handlers
    updateFilters,
    resetFilters,
    handleSearch,
    handleStatusFilter,
    handleSupplierFilter,
    handleSort,
    handlePageChange,
    handleLimitChange,

    // CRUD operations
    handleCreatePurchaseOrder,
    handleUpdatePurchaseOrder,
    handleDeletePurchaseOrder,

    // Helpers
    getStatusColor,
    formatCurrency,

    // Refetch
    refetch,
    refetchSuppliers,
  };
}

// Hook for single purchase order
export function usePurchaseOrder(shopId: string, branchId: string, orderId: string) {
  const {
    data: purchaseOrder,
    isLoading,
    isError,
    error,
    refetch
  } = useGetPurchaseOrderQuery({
    shopId,
    branchId,
    orderId
  });

  return {
    purchaseOrder,
    isLoading,
    isError,
    error,
    refetch
  };
}
