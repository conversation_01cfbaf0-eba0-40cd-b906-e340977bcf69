import { useState, useEffect, useCallback } from 'react';

export interface ReportData {
  id: string;
  title: string;
  type: 'sales' | 'customers' | 'financial' | 'inventory' | 'staff' | 'operations';
  data: any;
  generatedAt: Date;
  status: 'ready' | 'generating' | 'error';
}

export interface ReportFilters {
  dateRange: string;
  startDate?: Date;
  endDate?: Date;
  branchId?: string;
  category?: string;
}

export interface UseReportsDataOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  onError?: (error: Error) => void;
}

export function useReportsData({
  autoRefresh = false,
  refreshInterval = 300000, // 5 minutes
  onError,
}: UseReportsDataOptions = {}) {
  const [reports, setReports] = useState<ReportData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<ReportFilters>({
    dateRange: '30d',
  });

  // Fetch reports from API
  const fetchReports = useCallback(async () => {
    try {
      setError(null);
      const queryParams = new URLSearchParams();
      
      if (filters.dateRange) queryParams.append('dateRange', filters.dateRange);
      if (filters.startDate) queryParams.append('startDate', filters.startDate.toISOString());
      if (filters.endDate) queryParams.append('endDate', filters.endDate.toISOString());
      if (filters.branchId) queryParams.append('branchId', filters.branchId);
      if (filters.category) queryParams.append('category', filters.category);

      const response = await fetch(`/api/reports?${queryParams.toString()}`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setReports(data.reports || []);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch reports');
      setError(error.message);
      if (onError) {
        onError(error);
      }
    } finally {
      setLoading(false);
    }
  }, [filters, onError]);

  // Generate specific report
  const generateReport = useCallback(async (type: ReportData['type'], options?: any) => {
    try {
      setError(null);
      
      const response = await fetch('/api/reports/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type,
          filters,
          options,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate report');
      }

      const data = await response.json();
      
      // Add the new report to the list
      setReports(prev => [data.report, ...prev]);
      
      return data.report;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to generate report');
      setError(error.message);
      if (onError) {
        onError(error);
      }
      throw error;
    }
  }, [filters, onError]);

  // Export report
  const exportReport = useCallback(async (reportId: string, format: 'pdf' | 'excel' | 'csv') => {
    try {
      const response = await fetch(`/api/reports/${reportId}/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ format }),
      });

      if (!response.ok) {
        throw new Error('Failed to export report');
      }

      // Handle file download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `report-${reportId}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to export report');
      if (onError) {
        onError(error);
      }
      throw error;
    }
  }, [onError]);

  // Delete report
  const deleteReport = useCallback(async (reportId: string) => {
    try {
      const response = await fetch(`/api/reports/${reportId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete report');
      }

      setReports(prev => prev.filter(report => report.id !== reportId));
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to delete report');
      if (onError) {
        onError(error);
      }
      throw error;
    }
  }, [onError]);

  // Update filters
  const updateFilters = useCallback((newFilters: Partial<ReportFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  // Get sales data
  const getSalesData = useCallback(async (dateRange?: string) => {
    try {
      const params = new URLSearchParams();
      if (dateRange) params.append('dateRange', dateRange);
      
      const response = await fetch(`/api/reports/sales/data?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch sales data');
      }

      return await response.json();
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch sales data');
      if (onError) {
        onError(error);
      }
      throw error;
    }
  }, [onError]);

  // Get customer data
  const getCustomerData = useCallback(async (dateRange?: string) => {
    try {
      const params = new URLSearchParams();
      if (dateRange) params.append('dateRange', dateRange);
      
      const response = await fetch(`/api/reports/customers/data?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch customer data');
      }

      return await response.json();
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch customer data');
      if (onError) {
        onError(error);
      }
      throw error;
    }
  }, [onError]);

  // Get financial data
  const getFinancialData = useCallback(async (dateRange?: string) => {
    try {
      const params = new URLSearchParams();
      if (dateRange) params.append('dateRange', dateRange);
      
      const response = await fetch(`/api/reports/financial/data?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch financial data');
      }

      return await response.json();
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch financial data');
      if (onError) {
        onError(error);
      }
      throw error;
    }
  }, [onError]);

  // Auto-refresh setup
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchReports, refreshInterval);
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, fetchReports]);

  // Initial fetch
  useEffect(() => {
    fetchReports();
  }, [fetchReports]);

  // Computed values
  const reportsByType = reports.reduce((acc, report) => {
    if (!acc[report.type]) acc[report.type] = [];
    acc[report.type].push(report);
    return acc;
  }, {} as Record<string, ReportData[]>);

  const recentReports = reports
    .sort((a, b) => b.generatedAt.getTime() - a.generatedAt.getTime())
    .slice(0, 5);

  return {
    reports,
    reportsByType,
    recentReports,
    loading,
    error,
    filters,
    generateReport,
    exportReport,
    deleteReport,
    updateFilters,
    refresh: fetchReports,
    getSalesData,
    getCustomerData,
    getFinancialData,
  };
}
