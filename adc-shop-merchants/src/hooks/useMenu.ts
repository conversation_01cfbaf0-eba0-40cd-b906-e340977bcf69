/**
 * Custom hook for menu management
 * Separates business logic from UI components
 */

import { useState, useCallback, useMemo } from 'react';
import {
  useGetMenuItemsQuery,
  useGetMenuItemQuery,
  useGetMenuItemBySlugQuery,
  useGetMenuCategoriesQuery,
  useCreateMenuItemMutation,
  useUpdateMenuItemMutation,
  useDeleteMenuItemMutation,
  useToggleMenuItemAvailabilityMutation,
  useUploadMenuItemImageMutation,
  useCreateMenuCategoryMutation,
  useUpdateMenuCategoryMutation,
  useDeleteMenuCategoryMutation,
  type MenuItem,
  type MenuCategory,
  type MenuFilters,
} from '@/lib/redux/api/endpoints/restaurant/menuApi';
import { errorHandlers } from '@/lib/utils/error-handling';
import { MESSAGES } from '@/lib/constants/messages';

export interface UseMenuOptions {
  merchantId: string;
  branchId: string;
  initialFilters?: MenuFilters;
}

export function useMenu({
  merchantId,
  branchId,
  initialFilters = {}
}: UseMenuOptions) {
  const [filters, setFilters] = useState<MenuFilters>({
    page: 1,
    limit: 20,
    ...initialFilters
  });

  const [viewMode, setViewMode] = useState<'table' | 'grid'>('table');

  // API queries
  const {
    data: menuItemsData,
    isLoading,
    isError,
    error,
    refetch
  } = useGetMenuItemsQuery({
    merchantId,
    branchId,
    filters
  });

  const {
    data: categories,
    isLoading: isLoadingCategories,
    refetch: refetchCategories
  } = useGetMenuCategoriesQuery({
    merchantId,
    branchId
  });

  // Mutations
  const [createMenuItem, { isLoading: isCreating }] = useCreateMenuItemMutation();
  const [updateMenuItem, { isLoading: isUpdating }] = useUpdateMenuItemMutation();
  const [deleteMenuItem, { isLoading: isDeleting }] = useDeleteMenuItemMutation();
  const [toggleAvailability, { isLoading: isTogglingAvailability }] = useToggleMenuItemAvailabilityMutation();
  const [uploadImage, { isLoading: isUploadingImage }] = useUploadMenuItemImageMutation();
  const [createCategory, { isLoading: isCreatingCategory }] = useCreateMenuCategoryMutation();
  const [updateCategory, { isLoading: isUpdatingCategory }] = useUpdateMenuCategoryMutation();
  const [deleteCategory, { isLoading: isDeletingCategory }] = useDeleteMenuCategoryMutation();

  // Filter management
  const updateFilters = useCallback((newFilters: Partial<MenuFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({ page: 1, limit: 20 });
  }, []);

  // Menu item actions
  const handleCreateMenuItem = useCallback(async (itemData: any) => {
    try {
      await createMenuItem({
        merchantId,
        branchId,
        itemData
      }).unwrap();
      
      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.CREATED);
      refetch();
      refetchCategories();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [createMenuItem, merchantId, branchId, refetch, refetchCategories]);

  const handleUpdateMenuItem = useCallback(async (itemId: string, updates: any) => {
    try {
      await updateMenuItem({
        merchantId,
        branchId,
        itemData: { id: itemId, ...updates }
      }).unwrap();
      
      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.UPDATED);
      refetch();
      refetchCategories();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateMenuItem, merchantId, branchId, refetch, refetchCategories]);

  const handleDeleteMenuItem = useCallback(async (itemId: string) => {
    try {
      await deleteMenuItem({
        merchantId,
        branchId,
        itemId
      }).unwrap();
      
      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.DELETED);
      refetch();
      refetchCategories();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [deleteMenuItem, merchantId, branchId, refetch, refetchCategories]);

  const handleToggleAvailability = useCallback(async (itemId: string, isAvailable: boolean) => {
    try {
      await toggleAvailability({
        merchantId,
        branchId,
        itemId,
        isAvailable
      }).unwrap();
      
      errorHandlers.showSuccessToast(
        isAvailable ? 'Item marked as available' : 'Item marked as unavailable'
      );
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [toggleAvailability, merchantId, branchId, refetch]);

  const handleUploadImage = useCallback(async (itemId: string, file: File) => {
    try {
      const result = await uploadImage({
        merchantId,
        branchId,
        itemId,
        file
      }).unwrap();
      
      errorHandlers.showSuccessToast('Image uploaded successfully');
      refetch();
      return result.imageUrl;
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [uploadImage, merchantId, branchId, refetch]);

  // Category actions
  const handleCreateCategory = useCallback(async (categoryData: any) => {
    try {
      await createCategory({
        merchantId,
        branchId,
        categoryData
      }).unwrap();
      
      errorHandlers.showSuccessToast('Category created successfully');
      refetchCategories();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [createCategory, merchantId, branchId, refetchCategories]);

  const handleUpdateCategory = useCallback(async (categoryId: string, updates: any) => {
    try {
      await updateCategory({
        merchantId,
        branchId,
        categoryId,
        categoryData: updates
      }).unwrap();
      
      errorHandlers.showSuccessToast('Category updated successfully');
      refetchCategories();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateCategory, merchantId, branchId, refetchCategories]);

  const handleDeleteCategory = useCallback(async (categoryId: string) => {
    try {
      await deleteCategory({
        merchantId,
        branchId,
        categoryId
      }).unwrap();
      
      errorHandlers.showSuccessToast('Category deleted successfully');
      refetchCategories();
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [deleteCategory, merchantId, branchId, refetchCategories, refetch]);

  // Format currency
  const formatCurrency = useCallback((amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }, []);

  // Get availability status color
  const getAvailabilityColor = useCallback((isAvailable: boolean) => {
    return isAvailable 
      ? 'bg-green-100 text-green-800 border-green-200'
      : 'bg-red-100 text-red-800 border-red-200';
  }, []);

  // Data processing
  const menuItems = menuItemsData?.data || [];
  const pagination = menuItemsData?.pagination;

  // Category statistics
  const categoryStats = useMemo(() => {
    if (!categories || !menuItems) return {};
    
    return categories.reduce((acc, category) => {
      const categoryItems = menuItems.filter(item => item.categoryId === category.id);
      acc[category.id] = {
        totalItems: categoryItems.length,
        availableItems: categoryItems.filter(item => item.isAvailable).length,
        averagePrice: categoryItems.length > 0 
          ? categoryItems.reduce((sum, item) => sum + item.price, 0) / categoryItems.length 
          : 0
      };
      return acc;
    }, {} as Record<string, { totalItems: number; availableItems: number; averagePrice: number }>);
  }, [categories, menuItems]);

  // Filter statistics
  const filterStats = useMemo(() => {
    return {
      totalItems: menuItems.length,
      availableItems: menuItems.filter(item => item.isAvailable).length,
      vegetarianItems: menuItems.filter(item => item.isVegetarian).length,
      veganItems: menuItems.filter(item => item.isVegan).length,
      glutenFreeItems: menuItems.filter(item => item.isGlutenFree).length,
    };
  }, [menuItems]);

  return {
    // Data
    menuItems,
    categories: categories || [],
    pagination,
    categoryStats,
    filterStats,
    
    // View mode
    viewMode,
    setViewMode,
    
    // Loading states
    isLoading,
    isLoadingCategories,
    isCreating,
    isUpdating,
    isDeleting,
    isTogglingAvailability,
    isUploadingImage,
    isCreatingCategory,
    isUpdatingCategory,
    isDeletingCategory,
    isError,
    error,
    
    // Filters
    filters,
    updateFilters,
    clearFilters,
    
    // Menu item actions
    createMenuItem: handleCreateMenuItem,
    updateMenuItem: handleUpdateMenuItem,
    deleteMenuItem: handleDeleteMenuItem,
    toggleAvailability: handleToggleAvailability,
    uploadImage: handleUploadImage,
    
    // Category actions
    createCategory: handleCreateCategory,
    updateCategory: handleUpdateCategory,
    deleteCategory: handleDeleteCategory,
    
    // Helpers
    formatCurrency,
    getAvailabilityColor,
    refetch,
    refetchCategories,
  };
}

// Hook for single menu item
export function useMenuItem(merchantId: string, branchId: string, itemId?: string, slug?: string) {
  const {
    data: menuItem,
    isLoading,
    isError,
    error,
    refetch
  } = itemId 
    ? useGetMenuItemQuery({ merchantId, branchId, itemId })
    : slug 
    ? useGetMenuItemBySlugQuery({ merchantId, branchId, slug })
    : { data: undefined, isLoading: false, isError: false, error: undefined, refetch: () => {} };

  const [updateMenuItem, { isLoading: isUpdating }] = useUpdateMenuItemMutation();
  const [deleteMenuItem, { isLoading: isDeleting }] = useDeleteMenuItemMutation();
  const [toggleAvailability, { isLoading: isTogglingAvailability }] = useToggleMenuItemAvailabilityMutation();

  const handleUpdate = useCallback(async (updates: any) => {
    if (!menuItem) return;
    
    try {
      await updateMenuItem({
        merchantId,
        branchId,
        itemData: { id: menuItem.id, ...updates }
      }).unwrap();
      
      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.UPDATED);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateMenuItem, merchantId, branchId, menuItem, refetch]);

  const handleDelete = useCallback(async () => {
    if (!menuItem) return;
    
    try {
      await deleteMenuItem({
        merchantId,
        branchId,
        itemId: menuItem.id
      }).unwrap();
      
      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.DELETED);
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [deleteMenuItem, merchantId, branchId, menuItem]);

  const handleToggleAvailability = useCallback(async () => {
    if (!menuItem) return;
    
    try {
      await toggleAvailability({
        merchantId,
        branchId,
        itemId: menuItem.id,
        isAvailable: !menuItem.isAvailable
      }).unwrap();
      
      errorHandlers.showSuccessToast(
        !menuItem.isAvailable ? 'Item marked as available' : 'Item marked as unavailable'
      );
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [toggleAvailability, merchantId, branchId, menuItem, refetch]);

  return {
    menuItem,
    isLoading,
    isUpdating,
    isDeleting,
    isTogglingAvailability,
    isError,
    error,
    updateMenuItem: handleUpdate,
    deleteMenuItem: handleDelete,
    toggleAvailability: handleToggleAvailability,
    refetch,
  };
}
