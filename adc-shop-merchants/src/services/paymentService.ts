import { prisma } from '@/lib/prisma';

/**
 * Interface for payment
 */
export interface Payment {
  id?: string;
  merchantId: string;
  appointmentId?: string;
  userId?: string;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  paymentMethod: 'credit_card' | 'debit_card' | 'bank_transfer' | 'cash' | 'other';
  paymentMethodDetails?: Record<string, any>;
  externalPaymentId?: string;
  refundId?: string;
  metadata?: Record<string, any>;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Interface for payment intent
 */
export interface PaymentIntent {
  id: string;
  merchantId: string;
  appointmentId?: string;
  userId?: string;
  amount: number;
  currency: string;
  status: 'requires_payment_method' | 'requires_confirmation' | 'requires_action' | 'processing' | 'succeeded' | 'canceled';
  clientSecret: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  expiresAt?: Date;
}

/**
 * Service for handling payments
 */
export const paymentService = {
  /**
   * Get payments for a merchant
   * @param merchantId - The ID of the merchant
   * @param limit - The maximum number of payments to return
   * @param offset - The number of payments to skip
   * @returns The payments
   */
  async getPayments(merchantId: string, limit = 10, offset = 0) {
    try {
      const payments = await prisma.payment.findMany({
        where: {
          merchantId,
        },
        include: {
          appointment: {
            include: {
              service: {
                select: {
                  name: true,
                },
              },
              staff: {
                select: {
                  name: true,
                },
              },
            },
          },
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit,
        skip: offset,
      });
      
      const total = await prisma.payment.count({
        where: {
          merchantId,
        },
      });
      
      return {
        payments,
        total,
      };
    } catch (error) {
      console.error('Error fetching payments:', error);
      throw new Error('Failed to fetch payments');
    }
  },
  
  /**
   * Get a payment by ID
   * @param merchantId - The ID of the merchant
   * @param paymentId - The ID of the payment
   * @returns The payment
   */
  async getPaymentById(merchantId: string, paymentId: string) {
    try {
      const payment = await prisma.payment.findUnique({
        where: {
          id: paymentId,
          merchantId,
        },
        include: {
          appointment: {
            include: {
              service: {
                select: {
                  name: true,
                },
              },
              staff: {
                select: {
                  name: true,
                },
              },
            },
          },
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });
      
      if (!payment) {
        throw new Error('Payment not found');
      }
      
      return payment;
    } catch (error) {
      console.error('Error fetching payment:', error);
      throw new Error('Failed to fetch payment');
    }
  },
  
  /**
   * Create a payment
   * @param payment - The payment data
   * @returns The created payment
   */
  async createPayment(payment: Omit<Payment, 'id' | 'createdAt' | 'updatedAt'>) {
    try {
      // If this payment is for an appointment, check if the appointment exists
      if (payment.appointmentId) {
        const appointment = await prisma.appointment.findUnique({
          where: {
            id: payment.appointmentId,
            merchantId: payment.merchantId,
          },
        });
        
        if (!appointment) {
          throw new Error('Appointment not found');
        }
        
        // Update the appointment payment status
        await prisma.appointment.update({
          where: {
            id: payment.appointmentId,
          },
          data: {
            paymentStatus: payment.status === 'completed' ? 'paid' : 'pending',
          },
        });
      }
      
      // Create the payment
      const newPayment = await prisma.payment.create({
        data: payment,
        include: {
          appointment: {
            include: {
              service: {
                select: {
                  name: true,
                },
              },
              staff: {
                select: {
                  name: true,
                },
              },
            },
          },
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });
      
      return newPayment;
    } catch (error) {
      console.error('Error creating payment:', error);
      throw new Error('Failed to create payment');
    }
  },
  
  /**
   * Update a payment
   * @param merchantId - The ID of the merchant
   * @param paymentId - The ID of the payment
   * @param data - The updated payment data
   * @returns The updated payment
   */
  async updatePayment(merchantId: string, paymentId: string, data: Partial<Payment>) {
    try {
      // Check if the payment exists
      const existingPayment = await prisma.payment.findUnique({
        where: {
          id: paymentId,
          merchantId,
        },
      });
      
      if (!existingPayment) {
        throw new Error('Payment not found');
      }
      
      // If the status is changing to 'completed', update the appointment payment status
      if (data.status === 'completed' && existingPayment.status !== 'completed' && existingPayment.appointmentId) {
        await prisma.appointment.update({
          where: {
            id: existingPayment.appointmentId,
          },
          data: {
            paymentStatus: 'paid',
          },
        });
      }
      
      // If the status is changing to 'refunded', update the appointment payment status
      if (data.status === 'refunded' && existingPayment.status !== 'refunded' && existingPayment.appointmentId) {
        await prisma.appointment.update({
          where: {
            id: existingPayment.appointmentId,
          },
          data: {
            paymentStatus: 'refunded',
          },
        });
      }
      
      // Update the payment
      const payment = await prisma.payment.update({
        where: {
          id: paymentId,
        },
        data,
        include: {
          appointment: {
            include: {
              service: {
                select: {
                  name: true,
                },
              },
              staff: {
                select: {
                  name: true,
                },
              },
            },
          },
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });
      
      return payment;
    } catch (error) {
      console.error('Error updating payment:', error);
      throw new Error('Failed to update payment');
    }
  },
  
  /**
   * Create a payment intent
   * @param merchantId - The ID of the merchant
   * @param amount - The payment amount
   * @param currency - The payment currency
   * @param appointmentId - The ID of the appointment (optional)
   * @param userId - The ID of the user (optional)
   * @param metadata - Additional metadata (optional)
   * @returns The payment intent
   */
  async createPaymentIntent(
    merchantId: string,
    amount: number,
    currency: string,
    appointmentId?: string,
    userId?: string,
    metadata?: Record<string, any>
  ) {
    try {
      // In a real implementation, this would call a payment processor API
      // For now, we'll just create a record in the database
      
      // Generate a client secret (in a real implementation, this would come from the payment processor)
      const clientSecret = `pi_${Date.now()}_${Math.random().toString(36).substring(2, 9)}_secret_${Math.random().toString(36).substring(2, 9)}`;
      
      // Create the payment intent
      const paymentIntent = await prisma.paymentIntent.create({
        data: {
          merchantId,
          appointmentId,
          userId,
          amount,
          currency,
          status: 'requires_payment_method',
          clientSecret,
          metadata,
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // Expires in 24 hours
        },
      });
      
      return paymentIntent;
    } catch (error) {
      console.error('Error creating payment intent:', error);
      throw new Error('Failed to create payment intent');
    }
  },
  
  /**
   * Get a payment intent by ID
   * @param merchantId - The ID of the merchant
   * @param intentId - The ID of the payment intent
   * @returns The payment intent
   */
  async getPaymentIntent(merchantId: string, intentId: string) {
    try {
      const paymentIntent = await prisma.paymentIntent.findUnique({
        where: {
          id: intentId,
          merchantId,
        },
      });
      
      if (!paymentIntent) {
        throw new Error('Payment intent not found');
      }
      
      return paymentIntent;
    } catch (error) {
      console.error('Error fetching payment intent:', error);
      throw new Error('Failed to fetch payment intent');
    }
  },
  
  /**
   * Update a payment intent
   * @param merchantId - The ID of the merchant
   * @param intentId - The ID of the payment intent
   * @param status - The new status
   * @returns The updated payment intent
   */
  async updatePaymentIntent(merchantId: string, intentId: string, status: PaymentIntent['status']) {
    try {
      const paymentIntent = await prisma.paymentIntent.update({
        where: {
          id: intentId,
          merchantId,
        },
        data: {
          status,
        },
      });
      
      return paymentIntent;
    } catch (error) {
      console.error('Error updating payment intent:', error);
      throw new Error('Failed to update payment intent');
    }
  },
  
  /**
   * Process a payment for an appointment
   * @param merchantId - The ID of the merchant
   * @param appointmentId - The ID of the appointment
   * @param paymentMethod - The payment method
   * @param paymentMethodDetails - Additional payment method details (optional)
   * @returns The created payment
   */
  async processAppointmentPayment(
    merchantId: string,
    appointmentId: string,
    paymentMethod: Payment['paymentMethod'],
    paymentMethodDetails?: Record<string, any>
  ) {
    try {
      // Get the appointment
      const appointment = await prisma.appointment.findUnique({
        where: {
          id: appointmentId,
          merchantId,
        },
        include: {
          service: true,
        },
      });
      
      if (!appointment) {
        throw new Error('Appointment not found');
      }
      
      // Create a payment
      const payment = await this.createPayment({
        merchantId,
        appointmentId,
        userId: appointment.userId,
        amount: appointment.price,
        currency: 'USD', // In a real app, this would come from merchant settings
        status: 'completed',
        paymentMethod,
        paymentMethodDetails,
        metadata: {
          serviceName: appointment.service.name,
          appointmentDate: appointment.date,
          appointmentTime: appointment.startTime,
        },
      });
      
      return payment;
    } catch (error) {
      console.error('Error processing appointment payment:', error);
      throw new Error('Failed to process appointment payment');
    }
  },
};
