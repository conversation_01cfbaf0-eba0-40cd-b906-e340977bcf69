import { prisma } from '@/lib/prisma';
import crypto from 'crypto';

/**
 * Interface for booking widget
 */
export interface BookingWidget {
  id?: string;
  merchantId: string;
  name: string;
  description?: string;
  apiKey: string;
  allowedOrigins: string[];
  settings: {
    theme: {
      primaryColor: string;
      secondaryColor: string;
      fontFamily: string;
      borderRadius: string;
      buttonStyle: string;
    };
    layout: {
      showHeader: boolean;
      showFooter: boolean;
      showLogo: boolean;
      logoUrl?: string;
      customCss?: string;
    };
    booking: {
      requireCustomerDetails: boolean;
      requireCustomerPhone: boolean;
      requireCustomerEmail: boolean;
      allowGuestBooking: boolean;
      showPrices: boolean;
      showDuration: boolean;
      showAvailableStaff: boolean;
      enableServiceFiltering: boolean;
      enableStaffSelection: boolean;
      maxDaysInAdvance: number;
      minHoursInAdvance: number;
      timeSlotInterval: number;
    };
    services: {
      includedServiceIds: string[];
      excludedServiceIds: string[];
      includedCategoryIds: string[];
      excludedCategoryIds: string[];
    };
    staff: {
      includedStaffIds: string[];
      excludedStaffIds: string[];
    };
  };
  isActive: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Service for handling booking widgets
 */
export const bookingWidgetService = {
  /**
   * Get booking widgets for a merchant
   * @param merchantId - The ID of the merchant
   * @returns The booking widgets
   */
  async getBookingWidgets(merchantId: string) {
    try {
      const widgets = await prisma.bookingWidget.findMany({
        where: {
          merchantId,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
      
      return widgets;
    } catch (error) {
      console.error('Error fetching booking widgets:', error);
      throw new Error('Failed to fetch booking widgets');
    }
  },
  
  /**
   * Get a booking widget by ID
   * @param merchantId - The ID of the merchant
   * @param widgetId - The ID of the booking widget
   * @returns The booking widget
   */
  async getBookingWidgetById(merchantId: string, widgetId: string) {
    try {
      const widget = await prisma.bookingWidget.findUnique({
        where: {
          id: widgetId,
          merchantId,
        },
      });
      
      if (!widget) {
        throw new Error('Booking widget not found');
      }
      
      return widget;
    } catch (error) {
      console.error('Error fetching booking widget:', error);
      throw new Error('Failed to fetch booking widget');
    }
  },
  
  /**
   * Create a booking widget
   * @param widget - The booking widget data
   * @returns The created booking widget
   */
  async createBookingWidget(widget: Omit<BookingWidget, 'id' | 'apiKey' | 'createdAt' | 'updatedAt'>) {
    try {
      // Generate a unique API key
      const apiKey = crypto.randomBytes(16).toString('hex');
      
      // Create the booking widget
      const newWidget = await prisma.bookingWidget.create({
        data: {
          ...widget,
          apiKey,
        },
      });
      
      return newWidget;
    } catch (error) {
      console.error('Error creating booking widget:', error);
      throw new Error('Failed to create booking widget');
    }
  },
  
  /**
   * Update a booking widget
   * @param merchantId - The ID of the merchant
   * @param widgetId - The ID of the booking widget
   * @param data - The updated booking widget data
   * @returns The updated booking widget
   */
  async updateBookingWidget(merchantId: string, widgetId: string, data: Partial<Omit<BookingWidget, 'id' | 'apiKey' | 'merchantId' | 'createdAt' | 'updatedAt'>>) {
    try {
      // Check if the booking widget exists
      const existingWidget = await prisma.bookingWidget.findUnique({
        where: {
          id: widgetId,
          merchantId,
        },
      });
      
      if (!existingWidget) {
        throw new Error('Booking widget not found');
      }
      
      // Update the booking widget
      const widget = await prisma.bookingWidget.update({
        where: {
          id: widgetId,
        },
        data,
      });
      
      return widget;
    } catch (error) {
      console.error('Error updating booking widget:', error);
      throw new Error('Failed to update booking widget');
    }
  },
  
  /**
   * Delete a booking widget
   * @param merchantId - The ID of the merchant
   * @param widgetId - The ID of the booking widget
   * @returns True if the booking widget was deleted
   */
  async deleteBookingWidget(merchantId: string, widgetId: string) {
    try {
      // Check if the booking widget exists
      const widget = await prisma.bookingWidget.findUnique({
        where: {
          id: widgetId,
          merchantId,
        },
      });
      
      if (!widget) {
        throw new Error('Booking widget not found');
      }
      
      // Delete the booking widget
      await prisma.bookingWidget.delete({
        where: {
          id: widgetId,
        },
      });
      
      return { success: true };
    } catch (error) {
      console.error('Error deleting booking widget:', error);
      throw new Error('Failed to delete booking widget');
    }
  },
  
  /**
   * Regenerate API key for a booking widget
   * @param merchantId - The ID of the merchant
   * @param widgetId - The ID of the booking widget
   * @returns The updated booking widget with new API key
   */
  async regenerateApiKey(merchantId: string, widgetId: string) {
    try {
      // Check if the booking widget exists
      const widget = await prisma.bookingWidget.findUnique({
        where: {
          id: widgetId,
          merchantId,
        },
      });
      
      if (!widget) {
        throw new Error('Booking widget not found');
      }
      
      // Generate a new API key
      const apiKey = crypto.randomBytes(16).toString('hex');
      
      // Update the booking widget
      const updatedWidget = await prisma.bookingWidget.update({
        where: {
          id: widgetId,
        },
        data: {
          apiKey,
        },
      });
      
      return updatedWidget;
    } catch (error) {
      console.error('Error regenerating API key:', error);
      throw new Error('Failed to regenerate API key');
    }
  },
  
  /**
   * Validate a booking widget API key
   * @param apiKey - The API key to validate
   * @param origin - The origin of the request
   * @returns The booking widget if valid
   */
  async validateApiKey(apiKey: string, origin: string) {
    try {
      // Find the booking widget with this API key
      const widget = await prisma.bookingWidget.findFirst({
        where: {
          apiKey,
          isActive: true,
        },
        include: {
          merchant: {
            select: {
              id: true,
              name: true,
              type: true,
            },
          },
        },
      });
      
      if (!widget) {
        throw new Error('Invalid API key');
      }
      
      // Check if the origin is allowed
      if (widget.allowedOrigins.length > 0 && !widget.allowedOrigins.includes('*')) {
        const isAllowed = widget.allowedOrigins.some(allowedOrigin => {
          // Convert to regex pattern if it contains wildcards
          if (allowedOrigin.includes('*')) {
            const pattern = allowedOrigin.replace(/\*/g, '.*');
            const regex = new RegExp(`^${pattern}$`);
            return regex.test(origin);
          }
          
          return allowedOrigin === origin;
        });
        
        if (!isAllowed) {
          throw new Error('Origin not allowed');
        }
      }
      
      return widget;
    } catch (error) {
      console.error('Error validating API key:', error);
      throw new Error('Failed to validate API key');
    }
  },
  
  /**
   * Get available services for a booking widget
   * @param widgetId - The ID of the booking widget
   * @returns The available services
   */
  async getAvailableServices(widgetId: string) {
    try {
      // Get the booking widget
      const widget = await prisma.bookingWidget.findUnique({
        where: {
          id: widgetId,
        },
        include: {
          merchant: {
            select: {
              id: true,
            },
          },
        },
      });
      
      if (!widget) {
        throw new Error('Booking widget not found');
      }
      
      // Get the services based on the widget settings
      const { includedServiceIds, excludedServiceIds, includedCategoryIds, excludedCategoryIds } = widget.settings.services;
      
      // Build the query
      const query: any = {
        merchantId: widget.merchant.id,
        isActive: true,
      };
      
      // Apply service filters
      if (includedServiceIds && includedServiceIds.length > 0) {
        query.id = {
          in: includedServiceIds,
        };
      }
      
      if (excludedServiceIds && excludedServiceIds.length > 0) {
        query.id = {
          ...query.id,
          notIn: excludedServiceIds,
        };
      }
      
      // Apply category filters
      if (includedCategoryIds && includedCategoryIds.length > 0) {
        query.categoryId = {
          in: includedCategoryIds,
        };
      }
      
      if (excludedCategoryIds && excludedCategoryIds.length > 0) {
        query.categoryId = {
          ...query.categoryId,
          notIn: excludedCategoryIds,
        };
      }
      
      // Get the services
      const services = await prisma.service.findMany({
        where: query,
        include: {
          category: true,
          staff: widget.settings.booking.showAvailableStaff ? {
            where: {
              isActive: true,
              ...(widget.settings.staff.includedStaffIds && widget.settings.staff.includedStaffIds.length > 0
                ? { id: { in: widget.settings.staff.includedStaffIds } }
                : {}),
              ...(widget.settings.staff.excludedStaffIds && widget.settings.staff.excludedStaffIds.length > 0
                ? { id: { notIn: widget.settings.staff.excludedStaffIds } }
                : {}),
            },
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          } : false,
        },
        orderBy: [
          { category: { order: 'asc' } },
          { name: 'asc' },
        ],
      });
      
      return {
        services,
        widgetSettings: widget.settings,
      };
    } catch (error) {
      console.error('Error fetching available services:', error);
      throw new Error('Failed to fetch available services');
    }
  },
  
  /**
   * Get embed code for a booking widget
   * @param merchantId - The ID of the merchant
   * @param widgetId - The ID of the booking widget
   * @returns The embed code
   */
  async getEmbedCode(merchantId: string, widgetId: string) {
    try {
      // Get the booking widget
      const widget = await prisma.bookingWidget.findUnique({
        where: {
          id: widgetId,
          merchantId,
        },
      });
      
      if (!widget) {
        throw new Error('Booking widget not found');
      }
      
      // Generate the embed code
      const embedCode = `
<!-- Booking Widget -->
<div id="booking-widget-container"></div>
<script src="https://yourdomain.com/booking-widget.js"></script>
<script>
  BookingWidget.initialize({
    container: 'booking-widget-container',
    apiKey: '${widget.apiKey}',
    options: {
      theme: {
        primaryColor: '${widget.settings.theme.primaryColor}',
        secondaryColor: '${widget.settings.theme.secondaryColor}'
      }
    }
  });
</script>
<!-- End Booking Widget -->
      `.trim();
      
      return {
        embedCode,
        apiKey: widget.apiKey,
      };
    } catch (error) {
      console.error('Error generating embed code:', error);
      throw new Error('Failed to generate embed code');
    }
  },
};
