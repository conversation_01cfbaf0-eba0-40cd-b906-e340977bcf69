import { prisma } from '@/lib/prisma';

/**
 * Interface for notification
 */
export interface Notification {
  id?: string;
  merchantId: string;
  userId?: string;
  type: 'appointment_created' | 'appointment_updated' | 'appointment_cancelled' | 'appointment_reminder';
  title: string;
  message: string;
  read: boolean;
  data?: Record<string, any>;
  createdAt?: Date;
}

/**
 * Service for handling notifications
 */
export const notificationService = {
  /**
   * Create a notification
   * @param notification - The notification data
   * @returns The created notification
   */
  async createNotification(notification: Omit<Notification, 'id' | 'read' | 'createdAt'>) {
    try {
      const newNotification = await prisma.notification.create({
        data: {
          ...notification,
          read: false,
        },
      });
      
      return newNotification;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw new Error('Failed to create notification');
    }
  },
  
  /**
   * Get notifications for a user
   * @param userId - The ID of the user
   * @param limit - The maximum number of notifications to return
   * @param offset - The number of notifications to skip
   * @returns The notifications
   */
  async getUserNotifications(userId: string, limit = 10, offset = 0) {
    try {
      const notifications = await prisma.notification.findMany({
        where: {
          userId,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit,
        skip: offset,
      });
      
      const total = await prisma.notification.count({
        where: {
          userId,
        },
      });
      
      const unreadCount = await prisma.notification.count({
        where: {
          userId,
          read: false,
        },
      });
      
      return {
        notifications,
        total,
        unreadCount,
      };
    } catch (error) {
      console.error('Error fetching user notifications:', error);
      throw new Error('Failed to fetch user notifications');
    }
  },
  
  /**
   * Get notifications for a merchant
   * @param merchantId - The ID of the merchant
   * @param limit - The maximum number of notifications to return
   * @param offset - The number of notifications to skip
   * @returns The notifications
   */
  async getMerchantNotifications(merchantId: string, limit = 10, offset = 0) {
    try {
      const notifications = await prisma.notification.findMany({
        where: {
          merchantId,
          userId: null, // Only get merchant-level notifications
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit,
        skip: offset,
      });
      
      const total = await prisma.notification.count({
        where: {
          merchantId,
          userId: null,
        },
      });
      
      const unreadCount = await prisma.notification.count({
        where: {
          merchantId,
          userId: null,
          read: false,
        },
      });
      
      return {
        notifications,
        total,
        unreadCount,
      };
    } catch (error) {
      console.error('Error fetching merchant notifications:', error);
      throw new Error('Failed to fetch merchant notifications');
    }
  },
  
  /**
   * Mark a notification as read
   * @param notificationId - The ID of the notification
   * @returns The updated notification
   */
  async markAsRead(notificationId: string) {
    try {
      const notification = await prisma.notification.update({
        where: {
          id: notificationId,
        },
        data: {
          read: true,
        },
      });
      
      return notification;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw new Error('Failed to mark notification as read');
    }
  },
  
  /**
   * Mark all notifications as read for a user
   * @param userId - The ID of the user
   * @returns The number of notifications marked as read
   */
  async markAllAsRead(userId: string) {
    try {
      const result = await prisma.notification.updateMany({
        where: {
          userId,
          read: false,
        },
        data: {
          read: true,
        },
      });
      
      return result.count;
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw new Error('Failed to mark all notifications as read');
    }
  },
  
  /**
   * Delete a notification
   * @param notificationId - The ID of the notification
   * @returns True if the notification was deleted
   */
  async deleteNotification(notificationId: string) {
    try {
      await prisma.notification.delete({
        where: {
          id: notificationId,
        },
      });
      
      return true;
    } catch (error) {
      console.error('Error deleting notification:', error);
      throw new Error('Failed to delete notification');
    }
  },
  
  /**
   * Create an appointment notification
   * @param merchantId - The ID of the merchant
   * @param appointmentId - The ID of the appointment
   * @param type - The type of notification
   * @returns The created notification
   */
  async createAppointmentNotification(
    merchantId: string,
    appointmentId: string,
    type: 'appointment_created' | 'appointment_updated' | 'appointment_cancelled' | 'appointment_reminder'
  ) {
    try {
      // Get the appointment
      const appointment = await prisma.appointment.findUnique({
        where: {
          id: appointmentId,
          merchantId,
        },
        include: {
          service: true,
          staff: true,
        },
      });
      
      if (!appointment) {
        throw new Error('Appointment not found');
      }
      
      // Create notification for the merchant
      const merchantNotification = await this.createNotification({
        merchantId,
        type,
        title: getNotificationTitle(type, appointment),
        message: getNotificationMessage(type, appointment),
        data: {
          appointmentId: appointment.id,
          serviceId: appointment.serviceId,
          staffId: appointment.staffId,
          date: appointment.date,
          time: appointment.startTime,
        },
      });
      
      // Create notification for the user if there is a userId
      let userNotification = null;
      if (appointment.userId) {
        userNotification = await this.createNotification({
          merchantId,
          userId: appointment.userId,
          type,
          title: getNotificationTitle(type, appointment),
          message: getNotificationMessage(type, appointment),
          data: {
            appointmentId: appointment.id,
            serviceId: appointment.serviceId,
            staffId: appointment.staffId,
            date: appointment.date,
            time: appointment.startTime,
          },
        });
      }
      
      return {
        merchantNotification,
        userNotification,
      };
    } catch (error) {
      console.error('Error creating appointment notification:', error);
      throw new Error('Failed to create appointment notification');
    }
  },
};

/**
 * Helper function to get notification title
 * @param type - The type of notification
 * @param appointment - The appointment
 * @returns The notification title
 */
function getNotificationTitle(
  type: 'appointment_created' | 'appointment_updated' | 'appointment_cancelled' | 'appointment_reminder',
  appointment: any
): string {
  switch (type) {
    case 'appointment_created':
      return 'New Appointment';
    case 'appointment_updated':
      return 'Appointment Updated';
    case 'appointment_cancelled':
      return 'Appointment Cancelled';
    case 'appointment_reminder':
      return 'Appointment Reminder';
    default:
      return 'Appointment Notification';
  }
}

/**
 * Helper function to get notification message
 * @param type - The type of notification
 * @param appointment - The appointment
 * @returns The notification message
 */
function getNotificationMessage(
  type: 'appointment_created' | 'appointment_updated' | 'appointment_cancelled' | 'appointment_reminder',
  appointment: any
): string {
  const serviceName = appointment.service?.name || 'service';
  const staffName = appointment.staff?.name || 'staff member';
  const date = new Date(appointment.date).toLocaleDateString();
  const time = appointment.startTime;
  
  switch (type) {
    case 'appointment_created':
      return `New appointment for ${serviceName} with ${staffName} on ${date} at ${time}.`;
    case 'appointment_updated':
      return `Appointment for ${serviceName} with ${staffName} on ${date} at ${time} has been updated.`;
    case 'appointment_cancelled':
      return `Appointment for ${serviceName} with ${staffName} on ${date} at ${time} has been cancelled.`;
    case 'appointment_reminder':
      return `Reminder: You have an appointment for ${serviceName} with ${staffName} on ${date} at ${time}.`;
    default:
      return `Notification for appointment on ${date} at ${time}.`;
  }
}
