import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';

/**
 * Interface for service category
 */
export interface ServiceCategory {
  id: string;
  merchantId: string;
  name: string;
  description?: string;
  order: number;
}

/**
 * Service for handling service category operations
 */
export const categoryService = {
  /**
   * Get all categories for a merchant
   * @param merchantId - The ID of the merchant
   * @returns A list of categories
   */
  async getCategories(merchantId: string) {
    try {
      const categories = await prisma.serviceCategory.findMany({
        where: {
          merchantId,
        },
        orderBy: {
          order: 'asc',
        },
      });
      
      return categories;
    } catch (error) {
      console.error('Error fetching categories:', error);
      throw new Error('Failed to fetch categories');
    }
  },

  /**
   * Get a category by ID
   * @param merchantId - The ID of the merchant
   * @param categoryId - The ID of the category
   * @returns The category if found
   */
  async getCategoryById(merchantId: string, categoryId: string) {
    try {
      const category = await prisma.serviceCategory.findUnique({
        where: {
          id: categoryId,
          merchantId,
        },
      });
      
      if (!category) {
        throw new Error('Category not found');
      }
      
      return category;
    } catch (error) {
      console.error('Error fetching category:', error);
      throw new Error('Failed to fetch category');
    }
  },

  /**
   * Create a new category
   * @param merchantId - The ID of the merchant
   * @param categoryData - The category data
   * @returns The created category
   */
  async createCategory(merchantId: string, categoryData: Omit<ServiceCategory, 'id'>) {
    try {
      // Get the highest order value to place the new category at the end
      const highestOrder = await prisma.serviceCategory.findFirst({
        where: {
          merchantId,
        },
        orderBy: {
          order: 'desc',
        },
        select: {
          order: true,
        },
      });
      
      const order = highestOrder ? highestOrder.order + 1 : 0;
      
      // Create the category
      const category = await prisma.serviceCategory.create({
        data: {
          ...categoryData,
          merchantId,
          order,
        },
      });
      
      return category;
    } catch (error) {
      console.error('Error creating category:', error);
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new Error('A category with this name already exists');
        }
      }
      throw new Error('Failed to create category');
    }
  },

  /**
   * Update a category
   * @param merchantId - The ID of the merchant
   * @param categoryId - The ID of the category
   * @param categoryData - The updated category data
   * @returns The updated category
   */
  async updateCategory(merchantId: string, categoryId: string, categoryData: Partial<ServiceCategory>) {
    try {
      // Check if the category exists
      const existingCategory = await prisma.serviceCategory.findUnique({
        where: {
          id: categoryId,
          merchantId,
        },
      });
      
      if (!existingCategory) {
        throw new Error('Category not found');
      }
      
      // Update the category
      const category = await prisma.serviceCategory.update({
        where: {
          id: categoryId,
          merchantId,
        },
        data: categoryData,
      });
      
      return category;
    } catch (error) {
      console.error('Error updating category:', error);
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new Error('A category with this name already exists');
        }
      }
      throw new Error('Failed to update category');
    }
  },

  /**
   * Delete a category
   * @param merchantId - The ID of the merchant
   * @param categoryId - The ID of the category
   */
  async deleteCategory(merchantId: string, categoryId: string) {
    try {
      // Check if the category exists
      const category = await prisma.serviceCategory.findUnique({
        where: {
          id: categoryId,
          merchantId,
        },
      });
      
      if (!category) {
        throw new Error('Category not found');
      }
      
      // Check if there are any services using this category
      const servicesCount = await prisma.service.count({
        where: {
          merchantId,
          categoryId,
        },
      });
      
      if (servicesCount > 0) {
        throw new Error('Cannot delete category with associated services');
      }
      
      // Delete the category
      await prisma.serviceCategory.delete({
        where: {
          id: categoryId,
          merchantId,
        },
      });
      
      // Reorder the remaining categories
      const remainingCategories = await prisma.serviceCategory.findMany({
        where: {
          merchantId,
          order: {
            gt: category.order,
          },
        },
        orderBy: {
          order: 'asc',
        },
      });
      
      // Update the order of each remaining category
      for (let i = 0; i < remainingCategories.length; i++) {
        await prisma.serviceCategory.update({
          where: {
            id: remainingCategories[i].id,
          },
          data: {
            order: category.order + i,
          },
        });
      }
      
      return { success: true };
    } catch (error) {
      console.error('Error deleting category:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to delete category');
    }
  },

  /**
   * Reorder categories
   * @param merchantId - The ID of the merchant
   * @param categoryIds - The ordered list of category IDs
   */
  async reorderCategories(merchantId: string, categoryIds: string[]) {
    try {
      // Check if all categories exist and belong to the merchant
      const categories = await prisma.serviceCategory.findMany({
        where: {
          merchantId,
          id: {
            in: categoryIds,
          },
        },
      });
      
      if (categories.length !== categoryIds.length) {
        throw new Error('One or more categories not found');
      }
      
      // Update the order of each category
      for (let i = 0; i < categoryIds.length; i++) {
        await prisma.serviceCategory.update({
          where: {
            id: categoryIds[i],
            merchantId,
          },
          data: {
            order: i,
          },
        });
      }
      
      return { success: true };
    } catch (error) {
      console.error('Error reordering categories:', error);
      throw new Error('Failed to reorder categories');
    }
  },
};
