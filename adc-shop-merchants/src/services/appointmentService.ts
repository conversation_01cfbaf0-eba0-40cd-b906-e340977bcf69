import { prisma } from '@/lib/prisma';
import { Appointment } from '@/lib/validations/appointmentSchema';
import { Prisma } from '@prisma/client';
import { appointmentCommunicationService } from './appointmentCommunicationService';

/**
 * Service for handling appointment-related operations
 */
export const appointmentService = {
  /**
   * Get all appointments for a merchant
   * @param merchantId - The ID of the merchant
   * @returns A list of appointments
   */
  async getAppointments(merchantId: string) {
    try {
      const appointments = await prisma.appointment.findMany({
        where: {
          merchantId,
        },
        include: {
          service: true,
          staff: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          date: 'desc',
        },
      });

      return appointments;
    } catch (error) {
      console.error('Error fetching appointments:', error);
      throw new Error('Failed to fetch appointments');
    }
  },

  /**
   * Get an appointment by ID
   * @param merchantId - The ID of the merchant
   * @param appointmentId - The ID of the appointment
   * @returns The appointment if found
   */
  async getAppointmentById(merchantId: string, appointmentId: string) {
    try {
      const appointment = await prisma.appointment.findUnique({
        where: {
          id: appointmentId,
          merchantId,
        },
        include: {
          service: true,
          staff: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      if (!appointment) {
        throw new Error('Appointment not found');
      }

      return appointment;
    } catch (error) {
      console.error('Error fetching appointment:', error);
      throw new Error('Failed to fetch appointment');
    }
  },

  /**
   * Create a new appointment
   * @param merchantId - The ID of the merchant
   * @param appointmentData - The appointment data
   * @returns The created appointment
   */
  async createAppointment(merchantId: string, appointmentData: Omit<Appointment, 'id'>) {
    try {
      // Check if the service exists
      const service = await prisma.service.findUnique({
        where: {
          id: appointmentData.serviceId,
          merchantId,
        },
      });

      if (!service) {
        throw new Error('Service not found');
      }

      // Check if the staff exists if staffId is provided
      if (appointmentData.staffId) {
        const staff = await prisma.staff.findUnique({
          where: {
            id: appointmentData.staffId,
            merchantId,
          },
        });

        if (!staff) {
          throw new Error('Staff not found');
        }
      }

      // Check if the time slot is available
      const isAvailable = await this.checkAvailability(
        merchantId,
        appointmentData.serviceId,
        appointmentData.date,
        appointmentData.startTime,
        service.duration
      );

      if (!isAvailable) {
        throw new Error('The selected time slot is not available');
      }

      // Create the appointment
      const appointment = await prisma.appointment.create({
        data: {
          ...appointmentData,
          merchantId,
          status: appointmentData.status || 'scheduled',
          // Set the price from the service if not provided
          price: appointmentData.price || service.price,
        },
        include: {
          service: true,
          staff: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
            },
          },
          merchant: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
              address: true,
              website: true,
            },
          },
        },
      });

      try {
        // Send appointment confirmation email
        if (appointment.user?.email) {
          await appointmentCommunicationService.sendAppointmentConfirmation(
            merchantId,
            appointment.id,
            'email'
          );
        }

        // Send appointment confirmation SMS if phone number is available
        if (appointment.user?.phone) {
          await appointmentCommunicationService.sendAppointmentConfirmation(
            merchantId,
            appointment.id,
            'sms'
          );
        }

        // Schedule appointment reminders
        await appointmentCommunicationService.scheduleAppointmentReminders(
          merchantId,
          appointment.id
        );
      } catch (communicationError) {
        // Log the error but don't fail the appointment creation
        console.error('Error sending appointment communications:', communicationError);
      }

      return appointment;
    } catch (error) {
      console.error('Error creating appointment:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to create appointment');
    }
  },

  /**
   * Update an appointment
   * @param merchantId - The ID of the merchant
   * @param appointmentId - The ID of the appointment
   * @param appointmentData - The updated appointment data
   * @returns The updated appointment
   */
  async updateAppointment(merchantId: string, appointmentId: string, appointmentData: Partial<Appointment>) {
    try {
      // Check if the appointment exists
      const existingAppointment = await prisma.appointment.findUnique({
        where: {
          id: appointmentId,
          merchantId,
        },
      });

      if (!existingAppointment) {
        throw new Error('Appointment not found');
      }

      // If changing the service, check if it exists
      if (appointmentData.serviceId && appointmentData.serviceId !== existingAppointment.serviceId) {
        const service = await prisma.service.findUnique({
          where: {
            id: appointmentData.serviceId,
            merchantId,
          },
        });

        if (!service) {
          throw new Error('Service not found');
        }
      }

      // If changing the staff, check if they exist
      if (appointmentData.staffId && appointmentData.staffId !== existingAppointment.staffId) {
        const staff = await prisma.staff.findUnique({
          where: {
            id: appointmentData.staffId,
            merchantId,
          },
        });

        if (!staff) {
          throw new Error('Staff not found');
        }
      }

      // If changing the date or time, check availability
      if (
        (appointmentData.date && appointmentData.date !== existingAppointment.date) ||
        (appointmentData.startTime && appointmentData.startTime !== existingAppointment.startTime)
      ) {
        const service = await prisma.service.findUnique({
          where: {
            id: appointmentData.serviceId || existingAppointment.serviceId,
            merchantId,
          },
        });

        if (!service) {
          throw new Error('Service not found');
        }

        const isAvailable = await this.checkAvailability(
          merchantId,
          appointmentData.serviceId || existingAppointment.serviceId,
          appointmentData.date || existingAppointment.date,
          appointmentData.startTime || existingAppointment.startTime,
          service.duration,
          appointmentId
        );

        if (!isAvailable) {
          throw new Error('The selected time slot is not available');
        }
      }

      // Update the appointment
      const appointment = await prisma.appointment.update({
        where: {
          id: appointmentId,
          merchantId,
        },
        data: appointmentData,
        include: {
          service: true,
          staff: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
            },
          },
          merchant: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
              address: true,
              website: true,
            },
          },
        },
      });

      try {
        // If the status has changed to cancelled, send a cancellation notification
        if (
          appointmentData.status === 'cancelled' &&
          existingAppointment.status !== 'cancelled'
        ) {
          // Send cancellation email
          if (appointment.user?.email) {
            await appointmentCommunicationService.sendAppointmentCancellation(
              merchantId,
              appointment.id,
              'email'
            );
          }

          // Send cancellation SMS if phone number is available
          if (appointment.user?.phone) {
            await appointmentCommunicationService.sendAppointmentCancellation(
              merchantId,
              appointment.id,
              'sms'
            );
          }
        }
        // If the date or time has changed, reschedule reminders
        else if (
          (appointmentData.date && appointmentData.date !== existingAppointment.date) ||
          (appointmentData.startTime && appointmentData.startTime !== existingAppointment.startTime)
        ) {
          // Schedule new appointment reminders
          await appointmentCommunicationService.scheduleAppointmentReminders(
            merchantId,
            appointment.id
          );
        }
      } catch (communicationError) {
        // Log the error but don't fail the appointment update
        console.error('Error sending appointment communications:', communicationError);
      }

      return appointment;
    } catch (error) {
      console.error('Error updating appointment:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to update appointment');
    }
  },

  /**
   * Cancel an appointment
   * @param merchantId - The ID of the merchant
   * @param appointmentId - The ID of the appointment
   * @returns The cancelled appointment
   */
  async cancelAppointment(merchantId: string, appointmentId: string) {
    try {
      // Check if the appointment exists
      const appointment = await prisma.appointment.findUnique({
        where: {
          id: appointmentId,
          merchantId,
        },
      });

      if (!appointment) {
        throw new Error('Appointment not found');
      }

      // Update the appointment status to cancelled
      const cancelledAppointment = await prisma.appointment.update({
        where: {
          id: appointmentId,
          merchantId,
        },
        data: {
          status: 'cancelled',
        },
        include: {
          service: true,
          staff: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
            },
          },
          merchant: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
              address: true,
              website: true,
            },
          },
        },
      });

      try {
        // Send cancellation email
        if (cancelledAppointment.user?.email) {
          await appointmentCommunicationService.sendAppointmentCancellation(
            merchantId,
            cancelledAppointment.id,
            'email'
          );
        }

        // Send cancellation SMS if phone number is available
        if (cancelledAppointment.user?.phone) {
          await appointmentCommunicationService.sendAppointmentCancellation(
            merchantId,
            cancelledAppointment.id,
            'sms'
          );
        }
      } catch (communicationError) {
        // Log the error but don't fail the appointment cancellation
        console.error('Error sending cancellation communications:', communicationError);
      }

      return cancelledAppointment;
    } catch (error) {
      console.error('Error cancelling appointment:', error);
      throw new Error('Failed to cancel appointment');
    }
  },

  /**
   * Check if a time slot is available
   * @param merchantId - The ID of the merchant
   * @param serviceId - The ID of the service
   * @param date - The date to check
   * @param startTime - The start time to check
   * @param duration - The duration of the service
   * @param excludeAppointmentId - Optional ID of an appointment to exclude from the check
   * @returns Whether the time slot is available
   */
  async checkAvailability(
    merchantId: string,
    serviceId: string,
    date: string,
    startTime: string,
    duration: number,
    excludeAppointmentId?: string
  ) {
    try {
      // Calculate the end time
      const [startHour, startMinute] = startTime.split(':').map(Number);
      const startTimeMinutes = startHour * 60 + startMinute;
      const endTimeMinutes = startTimeMinutes + duration;

      // Find overlapping appointments
      const overlappingAppointments = await prisma.appointment.findMany({
        where: {
          merchantId,
          date,
          status: {
            notIn: ['cancelled'],
          },
          id: excludeAppointmentId ? { not: excludeAppointmentId } : undefined,
          OR: [
            // Appointment starts during the requested time slot
            {
              startTime: {
                gte: startTime,
                lt: minutesToTimeString(endTimeMinutes),
              },
            },
            // Appointment ends during the requested time slot
            {
              endTime: {
                gt: startTime,
                lte: minutesToTimeString(endTimeMinutes),
              },
            },
            // Appointment spans the entire requested time slot
            {
              startTime: {
                lte: startTime,
              },
              endTime: {
                gte: minutesToTimeString(endTimeMinutes),
              },
            },
          ],
        },
      });

      return overlappingAppointments.length === 0;
    } catch (error) {
      console.error('Error checking availability:', error);
      throw new Error('Failed to check availability');
    }
  },
};

/**
 * Convert minutes to a time string (HH:MM)
 * @param minutes - The number of minutes
 * @returns A time string in the format HH:MM
 */
function minutesToTimeString(minutes: number): string {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
}
