import { prisma } from '@/lib/prisma';
import { subDays, startOfDay, endOfDay, format, parseISO } from 'date-fns';

/**
 * Interface for communication event
 */
export interface CommunicationEvent {
  id?: string;
  communicationId: string;
  merchantId: string;
  type: 'delivered' | 'opened' | 'clicked' | 'bounced' | 'complained' | 'unsubscribed';
  timestamp: Date;
  metadata?: Record<string, any>;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Interface for communication analytics
 */
export interface CommunicationAnalytics {
  totalSent: number;
  delivered: number;
  opened: number;
  clicked: number;
  bounced: number;
  complained: number;
  unsubscribed: number;
  deliveryRate: number;
  openRate: number;
  clickRate: number;
  clickToOpenRate: number;
  bounceRate: number;
  complaintRate: number;
  unsubscribeRate: number;
}

/**
 * Interface for campaign analytics
 */
export interface CampaignAnalytics extends CommunicationAnalytics {
  campaignId: string;
  campaignName: string;
  campaignType: 'email' | 'sms';
  startDate: Date;
  endDate?: Date;
}

/**
 * Service for handling communication analytics
 */
export const communicationAnalyticsService = {
  /**
   * Track a communication event
   * @param event - The event data
   * @returns The created event
   */
  async trackEvent(event: Omit<CommunicationEvent, 'id' | 'createdAt' | 'updatedAt'>) {
    try {
      // Create the event
      const newEvent = await prisma.communicationEvent.create({
        data: {
          ...event,
          timestamp: event.timestamp || new Date(),
        },
      });
      
      return newEvent;
    } catch (error) {
      console.error('Error tracking communication event:', error);
      throw new Error('Failed to track communication event');
    }
  },
  
  /**
   * Get events for a communication
   * @param merchantId - The ID of the merchant
   * @param communicationId - The ID of the communication
   * @returns The events
   */
  async getCommunicationEvents(merchantId: string, communicationId: string) {
    try {
      const events = await prisma.communicationEvent.findMany({
        where: {
          merchantId,
          communicationId,
        },
        orderBy: {
          timestamp: 'asc',
        },
      });
      
      return events;
    } catch (error) {
      console.error('Error fetching communication events:', error);
      throw new Error('Failed to fetch communication events');
    }
  },
  
  /**
   * Get analytics for a communication
   * @param merchantId - The ID of the merchant
   * @param communicationId - The ID of the communication
   * @returns The analytics
   */
  async getCommunicationAnalytics(merchantId: string, communicationId: string): Promise<CommunicationAnalytics> {
    try {
      // Get the communication
      const communication = await prisma.communication.findUnique({
        where: {
          id: communicationId,
          merchantId,
        },
      });
      
      if (!communication) {
        throw new Error('Communication not found');
      }
      
      // Get events for the communication
      const events = await this.getCommunicationEvents(merchantId, communicationId);
      
      // Calculate analytics
      const delivered = events.filter(event => event.type === 'delivered').length;
      const opened = events.filter(event => event.type === 'opened').length;
      const clicked = events.filter(event => event.type === 'clicked').length;
      const bounced = events.filter(event => event.type === 'bounced').length;
      const complained = events.filter(event => event.type === 'complained').length;
      const unsubscribed = events.filter(event => event.type === 'unsubscribed').length;
      
      // Calculate rates
      const totalSent = 1; // Each communication is sent once
      const deliveryRate = totalSent > 0 ? delivered / totalSent : 0;
      const openRate = delivered > 0 ? opened / delivered : 0;
      const clickRate = delivered > 0 ? clicked / delivered : 0;
      const clickToOpenRate = opened > 0 ? clicked / opened : 0;
      const bounceRate = totalSent > 0 ? bounced / totalSent : 0;
      const complaintRate = delivered > 0 ? complained / delivered : 0;
      const unsubscribeRate = delivered > 0 ? unsubscribed / delivered : 0;
      
      return {
        totalSent,
        delivered,
        opened,
        clicked,
        bounced,
        complained,
        unsubscribed,
        deliveryRate,
        openRate,
        clickRate,
        clickToOpenRate,
        bounceRate,
        complaintRate,
        unsubscribeRate,
      };
    } catch (error) {
      console.error('Error fetching communication analytics:', error);
      throw new Error('Failed to fetch communication analytics');
    }
  },
  
  /**
   * Get analytics for a campaign
   * @param merchantId - The ID of the merchant
   * @param campaignId - The ID of the campaign
   * @returns The analytics
   */
  async getCampaignAnalytics(merchantId: string, campaignId: string): Promise<CampaignAnalytics> {
    try {
      // Get the campaign
      const campaign = await prisma.communicationCampaign.findUnique({
        where: {
          id: campaignId,
          merchantId,
        },
        include: {
          executions: true,
        },
      });
      
      if (!campaign) {
        throw new Error('Campaign not found');
      }
      
      // Get all communications for this campaign
      const communications = await prisma.communication.findMany({
        where: {
          merchantId,
          metadata: {
            path: ['campaignId'],
            equals: campaignId,
          },
        },
      });
      
      // Get events for all communications
      const communicationIds = communications.map(comm => comm.id);
      const events = await prisma.communicationEvent.findMany({
        where: {
          merchantId,
          communicationId: {
            in: communicationIds,
          },
        },
      });
      
      // Calculate analytics
      const totalSent = communications.length;
      const delivered = events.filter(event => event.type === 'delivered').length;
      const opened = events.filter(event => event.type === 'opened').length;
      const clicked = events.filter(event => event.type === 'clicked').length;
      const bounced = events.filter(event => event.type === 'bounced').length;
      const complained = events.filter(event => event.type === 'complained').length;
      const unsubscribed = events.filter(event => event.type === 'unsubscribed').length;
      
      // Calculate rates
      const deliveryRate = totalSent > 0 ? delivered / totalSent : 0;
      const openRate = delivered > 0 ? opened / delivered : 0;
      const clickRate = delivered > 0 ? clicked / delivered : 0;
      const clickToOpenRate = opened > 0 ? clicked / opened : 0;
      const bounceRate = totalSent > 0 ? bounced / totalSent : 0;
      const complaintRate = delivered > 0 ? complained / delivered : 0;
      const unsubscribeRate = delivered > 0 ? unsubscribed / delivered : 0;
      
      // Get campaign dates
      const startDate = campaign.executions.length > 0 
        ? campaign.executions[0].startedAt || campaign.createdAt 
        : campaign.createdAt;
      
      const endDate = campaign.executions.length > 0 
        ? campaign.executions[campaign.executions.length - 1].completedAt 
        : undefined;
      
      return {
        campaignId,
        campaignName: campaign.name,
        campaignType: campaign.type as 'email' | 'sms',
        startDate,
        endDate,
        totalSent,
        delivered,
        opened,
        clicked,
        bounced,
        complained,
        unsubscribed,
        deliveryRate,
        openRate,
        clickRate,
        clickToOpenRate,
        bounceRate,
        complaintRate,
        unsubscribeRate,
      };
    } catch (error) {
      console.error('Error fetching campaign analytics:', error);
      throw new Error('Failed to fetch campaign analytics');
    }
  },
  
  /**
   * Get merchant communication analytics for a time period
   * @param merchantId - The ID of the merchant
   * @param startDate - The start date
   * @param endDate - The end date
   * @returns The analytics
   */
  async getMerchantAnalytics(
    merchantId: string,
    startDate: Date = subDays(new Date(), 30),
    endDate: Date = new Date()
  ) {
    try {
      // Get all communications in the time period
      const communications = await prisma.communication.findMany({
        where: {
          merchantId,
          createdAt: {
            gte: startOfDay(startDate),
            lte: endOfDay(endDate),
          },
        },
      });
      
      // Get all events in the time period
      const events = await prisma.communicationEvent.findMany({
        where: {
          merchantId,
          timestamp: {
            gte: startOfDay(startDate),
            lte: endOfDay(endDate),
          },
        },
      });
      
      // Calculate analytics by type
      const emailCommunications = communications.filter(comm => comm.type === 'email');
      const smsCommunications = communications.filter(comm => comm.type === 'sms');
      
      const emailEvents = events.filter(event => 
        emailCommunications.some(comm => comm.id === event.communicationId)
      );
      
      const smsEvents = events.filter(event => 
        smsCommunications.some(comm => comm.id === event.communicationId)
      );
      
      // Calculate email analytics
      const emailAnalytics = this.calculateAnalytics(emailCommunications.length, emailEvents);
      
      // Calculate SMS analytics
      const smsAnalytics = this.calculateAnalytics(smsCommunications.length, smsEvents);
      
      // Calculate overall analytics
      const overallAnalytics = this.calculateAnalytics(communications.length, events);
      
      // Get daily analytics
      const dailyAnalytics = await this.getDailyAnalytics(merchantId, startDate, endDate);
      
      return {
        overall: overallAnalytics,
        email: emailAnalytics,
        sms: smsAnalytics,
        daily: dailyAnalytics,
        startDate,
        endDate,
      };
    } catch (error) {
      console.error('Error fetching merchant analytics:', error);
      throw new Error('Failed to fetch merchant analytics');
    }
  },
  
  /**
   * Get daily analytics for a merchant
   * @param merchantId - The ID of the merchant
   * @param startDate - The start date
   * @param endDate - The end date
   * @returns The daily analytics
   */
  async getDailyAnalytics(merchantId: string, startDate: Date, endDate: Date) {
    try {
      // Get all communications in the time period
      const communications = await prisma.communication.findMany({
        where: {
          merchantId,
          createdAt: {
            gte: startOfDay(startDate),
            lte: endOfDay(endDate),
          },
        },
      });
      
      // Get all events in the time period
      const events = await prisma.communicationEvent.findMany({
        where: {
          merchantId,
          timestamp: {
            gte: startOfDay(startDate),
            lte: endOfDay(endDate),
          },
        },
      });
      
      // Group communications by day
      const communicationsByDay = communications.reduce((acc, comm) => {
        const day = format(comm.createdAt, 'yyyy-MM-dd');
        if (!acc[day]) acc[day] = [];
        acc[day].push(comm);
        return acc;
      }, {} as Record<string, any[]>);
      
      // Group events by day
      const eventsByDay = events.reduce((acc, event) => {
        const day = format(event.timestamp, 'yyyy-MM-dd');
        if (!acc[day]) acc[day] = [];
        acc[day].push(event);
        return acc;
      }, {} as Record<string, any[]>);
      
      // Calculate analytics for each day
      const dailyAnalytics = [];
      let currentDate = startOfDay(startDate);
      
      while (currentDate <= endOfDay(endDate)) {
        const day = format(currentDate, 'yyyy-MM-dd');
        const dayComms = communicationsByDay[day] || [];
        const dayEvents = eventsByDay[day] || [];
        
        const analytics = this.calculateAnalytics(dayComms.length, dayEvents);
        
        dailyAnalytics.push({
          date: day,
          ...analytics,
        });
        
        // Move to next day
        currentDate = new Date(currentDate.setDate(currentDate.getDate() + 1));
      }
      
      return dailyAnalytics;
    } catch (error) {
      console.error('Error fetching daily analytics:', error);
      throw new Error('Failed to fetch daily analytics');
    }
  },
  
  /**
   * Calculate analytics from events
   * @param totalSent - The total number of communications sent
   * @param events - The events
   * @returns The analytics
   */
  calculateAnalytics(totalSent: number, events: any[]): CommunicationAnalytics {
    const delivered = events.filter(event => event.type === 'delivered').length;
    const opened = events.filter(event => event.type === 'opened').length;
    const clicked = events.filter(event => event.type === 'clicked').length;
    const bounced = events.filter(event => event.type === 'bounced').length;
    const complained = events.filter(event => event.type === 'complained').length;
    const unsubscribed = events.filter(event => event.type === 'unsubscribed').length;
    
    // Calculate rates
    const deliveryRate = totalSent > 0 ? delivered / totalSent : 0;
    const openRate = delivered > 0 ? opened / delivered : 0;
    const clickRate = delivered > 0 ? clicked / delivered : 0;
    const clickToOpenRate = opened > 0 ? clicked / opened : 0;
    const bounceRate = totalSent > 0 ? bounced / totalSent : 0;
    const complaintRate = delivered > 0 ? complained / delivered : 0;
    const unsubscribeRate = delivered > 0 ? unsubscribed / delivered : 0;
    
    return {
      totalSent,
      delivered,
      opened,
      clicked,
      bounced,
      complained,
      unsubscribed,
      deliveryRate,
      openRate,
      clickRate,
      clickToOpenRate,
      bounceRate,
      complaintRate,
      unsubscribeRate,
    };
  },
};
