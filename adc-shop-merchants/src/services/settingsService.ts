import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';

/**
 * Interface for service settings
 */
export interface ServiceSettings {
  appointmentEnabled: boolean;
  serviceLocations: string[];
  cancellationPolicy: string;
  openingHours: Record<string, { open: string; close: string }>;
  bufferTimeBetweenAppointments: number;
  maxAdvanceBookingDays: number;
  minAdvanceBookingHours: number;
  allowCancellationBefore: number; // hours before appointment
  sendReminders: boolean;
  reminderHours: number[];
}

/**
 * Service for handling service settings operations
 */
export const settingsService = {
  /**
   * Get settings for a merchant
   * @param merchantId - The ID of the merchant
   * @returns The settings
   */
  async getSettings(merchantId: string) {
    try {
      // Get the merchant to check if it exists and is a service type
      const merchant = await prisma.merchant.findUnique({
        where: {
          id: merchantId,
        },
        select: {
          id: true,
          type: true,
          settings: true,
        },
      });
      
      if (!merchant) {
        throw new Error('Merchant not found');
      }
      
      if (merchant.type !== 'service') {
        throw new Error('Merchant is not a service type');
      }
      
      // Get or create settings
      let settings = await prisma.serviceSettings.findUnique({
        where: {
          merchantId,
        },
      });
      
      if (!settings) {
        // Create default settings
        settings = await prisma.serviceSettings.create({
          data: {
            merchantId,
            appointmentEnabled: true,
            serviceLocations: [],
            cancellationPolicy: 'Cancellations must be made at least 24 hours in advance.',
            openingHours: {
              monday: { open: '09:00', close: '17:00' },
              tuesday: { open: '09:00', close: '17:00' },
              wednesday: { open: '09:00', close: '17:00' },
              thursday: { open: '09:00', close: '17:00' },
              friday: { open: '09:00', close: '17:00' },
              saturday: { open: '10:00', close: '15:00' },
              sunday: { open: '', close: '' },
            },
            bufferTimeBetweenAppointments: 15,
            maxAdvanceBookingDays: 30,
            minAdvanceBookingHours: 1,
            allowCancellationBefore: 24,
            sendReminders: true,
            reminderHours: [24, 1],
          },
        });
      }
      
      return settings;
    } catch (error) {
      console.error('Error fetching settings:', error);
      throw new Error('Failed to fetch settings');
    }
  },

  /**
   * Update settings for a merchant
   * @param merchantId - The ID of the merchant
   * @param settingsData - The updated settings data
   * @returns The updated settings
   */
  async updateSettings(merchantId: string, settingsData: Partial<ServiceSettings>) {
    try {
      // Get the merchant to check if it exists and is a service type
      const merchant = await prisma.merchant.findUnique({
        where: {
          id: merchantId,
        },
        select: {
          id: true,
          type: true,
        },
      });
      
      if (!merchant) {
        throw new Error('Merchant not found');
      }
      
      if (merchant.type !== 'service') {
        throw new Error('Merchant is not a service type');
      }
      
      // Check if settings exist
      const existingSettings = await prisma.serviceSettings.findUnique({
        where: {
          merchantId,
        },
      });
      
      let settings;
      
      if (existingSettings) {
        // Update existing settings
        settings = await prisma.serviceSettings.update({
          where: {
            merchantId,
          },
          data: settingsData,
        });
      } else {
        // Create settings with provided data and defaults for missing fields
        settings = await prisma.serviceSettings.create({
          data: {
            merchantId,
            appointmentEnabled: settingsData.appointmentEnabled ?? true,
            serviceLocations: settingsData.serviceLocations ?? [],
            cancellationPolicy: settingsData.cancellationPolicy ?? 'Cancellations must be made at least 24 hours in advance.',
            openingHours: settingsData.openingHours ?? {
              monday: { open: '09:00', close: '17:00' },
              tuesday: { open: '09:00', close: '17:00' },
              wednesday: { open: '09:00', close: '17:00' },
              thursday: { open: '09:00', close: '17:00' },
              friday: { open: '09:00', close: '17:00' },
              saturday: { open: '10:00', close: '15:00' },
              sunday: { open: '', close: '' },
            },
            bufferTimeBetweenAppointments: settingsData.bufferTimeBetweenAppointments ?? 15,
            maxAdvanceBookingDays: settingsData.maxAdvanceBookingDays ?? 30,
            minAdvanceBookingHours: settingsData.minAdvanceBookingHours ?? 1,
            allowCancellationBefore: settingsData.allowCancellationBefore ?? 24,
            sendReminders: settingsData.sendReminders ?? true,
            reminderHours: settingsData.reminderHours ?? [24, 1],
          },
        });
      }
      
      return settings;
    } catch (error) {
      console.error('Error updating settings:', error);
      throw new Error('Failed to update settings');
    }
  },
};
