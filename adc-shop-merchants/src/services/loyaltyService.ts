import { prisma } from '@/lib/prisma';

/**
 * Interface for loyalty program
 */
export interface LoyaltyProgram {
  id?: string;
  merchantId: string;
  name: string;
  description?: string;
  pointsPerPurchase: number;
  pointsPerAmount: number;
  amountPerPoint: number;
  minimumPointsRedemption: number;
  pointsValueInCurrency: number;
  expirationMonths?: number;
  isActive: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Interface for loyalty tier
 */
export interface LoyaltyTier {
  id?: string;
  loyaltyProgramId: string;
  name: string;
  description?: string;
  requiredPoints: number;
  benefits: string[];
  multiplier: number;
  order: number;
}

/**
 * Interface for loyalty transaction
 */
export interface LoyaltyTransaction {
  id?: string;
  loyaltyProgramId: string;
  customerId: string;
  appointmentId?: string;
  type: 'earn' | 'redeem' | 'expire' | 'adjust';
  points: number;
  description?: string;
  createdAt?: Date;
}

/**
 * Service for handling loyalty programs
 */
export const loyaltyService = {
  /**
   * Get loyalty programs for a merchant
   * @param merchantId - The ID of the merchant
   * @returns The loyalty programs
   */
  async getLoyaltyPrograms(merchantId: string) {
    try {
      const programs = await prisma.loyaltyProgram.findMany({
        where: {
          merchantId,
        },
        include: {
          tiers: {
            orderBy: {
              order: 'asc',
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
      
      return programs;
    } catch (error) {
      console.error('Error fetching loyalty programs:', error);
      throw new Error('Failed to fetch loyalty programs');
    }
  },
  
  /**
   * Get a loyalty program by ID
   * @param merchantId - The ID of the merchant
   * @param programId - The ID of the loyalty program
   * @returns The loyalty program
   */
  async getLoyaltyProgramById(merchantId: string, programId: string) {
    try {
      const program = await prisma.loyaltyProgram.findUnique({
        where: {
          id: programId,
          merchantId,
        },
        include: {
          tiers: {
            orderBy: {
              order: 'asc',
            },
          },
        },
      });
      
      if (!program) {
        throw new Error('Loyalty program not found');
      }
      
      return program;
    } catch (error) {
      console.error('Error fetching loyalty program:', error);
      throw new Error('Failed to fetch loyalty program');
    }
  },
  
  /**
   * Create a loyalty program
   * @param program - The loyalty program data
   * @returns The created loyalty program
   */
  async createLoyaltyProgram(program: Omit<LoyaltyProgram, 'id' | 'createdAt' | 'updatedAt'>) {
    try {
      // Check if a loyalty program already exists for this merchant
      const existingProgram = await prisma.loyaltyProgram.findFirst({
        where: {
          merchantId: program.merchantId,
          name: program.name,
        },
      });
      
      if (existingProgram) {
        throw new Error('A loyalty program with this name already exists');
      }
      
      // Create the loyalty program
      const newProgram = await prisma.loyaltyProgram.create({
        data: program,
      });
      
      // Create default tiers if not provided
      const defaultTiers = [
        {
          loyaltyProgramId: newProgram.id,
          name: 'Bronze',
          description: 'Entry level tier',
          requiredPoints: 0,
          benefits: ['Basic benefits'],
          multiplier: 1,
          order: 0,
        },
        {
          loyaltyProgramId: newProgram.id,
          name: 'Silver',
          description: 'Mid level tier',
          requiredPoints: 1000,
          benefits: ['Basic benefits', '10% discount on services'],
          multiplier: 1.1,
          order: 1,
        },
        {
          loyaltyProgramId: newProgram.id,
          name: 'Gold',
          description: 'Top level tier',
          requiredPoints: 5000,
          benefits: ['Basic benefits', '20% discount on services', 'Priority booking'],
          multiplier: 1.2,
          order: 2,
        },
      ];
      
      await prisma.loyaltyTier.createMany({
        data: defaultTiers,
      });
      
      // Get the program with tiers
      const programWithTiers = await prisma.loyaltyProgram.findUnique({
        where: {
          id: newProgram.id,
        },
        include: {
          tiers: {
            orderBy: {
              order: 'asc',
            },
          },
        },
      });
      
      return programWithTiers;
    } catch (error) {
      console.error('Error creating loyalty program:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to create loyalty program');
    }
  },
  
  /**
   * Update a loyalty program
   * @param merchantId - The ID of the merchant
   * @param programId - The ID of the loyalty program
   * @param data - The updated loyalty program data
   * @returns The updated loyalty program
   */
  async updateLoyaltyProgram(merchantId: string, programId: string, data: Partial<LoyaltyProgram>) {
    try {
      // Check if the loyalty program exists
      const existingProgram = await prisma.loyaltyProgram.findUnique({
        where: {
          id: programId,
          merchantId,
        },
      });
      
      if (!existingProgram) {
        throw new Error('Loyalty program not found');
      }
      
      // If name is being updated, check if it's already in use
      if (data.name && data.name !== existingProgram.name) {
        const nameInUse = await prisma.loyaltyProgram.findFirst({
          where: {
            merchantId,
            name: data.name,
            id: {
              not: programId,
            },
          },
        });
        
        if (nameInUse) {
          throw new Error('A loyalty program with this name already exists');
        }
      }
      
      // Update the loyalty program
      const program = await prisma.loyaltyProgram.update({
        where: {
          id: programId,
        },
        data,
        include: {
          tiers: {
            orderBy: {
              order: 'asc',
            },
          },
        },
      });
      
      return program;
    } catch (error) {
      console.error('Error updating loyalty program:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to update loyalty program');
    }
  },
  
  /**
   * Delete a loyalty program
   * @param merchantId - The ID of the merchant
   * @param programId - The ID of the loyalty program
   * @returns True if the loyalty program was deleted
   */
  async deleteLoyaltyProgram(merchantId: string, programId: string) {
    try {
      // Check if the loyalty program exists
      const program = await prisma.loyaltyProgram.findUnique({
        where: {
          id: programId,
          merchantId,
        },
        include: {
          tiers: true,
          transactions: true,
        },
      });
      
      if (!program) {
        throw new Error('Loyalty program not found');
      }
      
      // If the program has transactions, don't delete it, just mark it as inactive
      if (program.transactions.length > 0) {
        await prisma.loyaltyProgram.update({
          where: {
            id: programId,
          },
          data: {
            isActive: false,
          },
        });
        
        return { success: true, message: 'Loyalty program has transactions and cannot be deleted. It has been marked as inactive instead.' };
      }
      
      // Delete the tiers
      await prisma.loyaltyTier.deleteMany({
        where: {
          loyaltyProgramId: programId,
        },
      });
      
      // Delete the loyalty program
      await prisma.loyaltyProgram.delete({
        where: {
          id: programId,
        },
      });
      
      return { success: true };
    } catch (error) {
      console.error('Error deleting loyalty program:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to delete loyalty program');
    }
  },
  
  /**
   * Get loyalty tiers for a program
   * @param merchantId - The ID of the merchant
   * @param programId - The ID of the loyalty program
   * @returns The loyalty tiers
   */
  async getLoyaltyTiers(merchantId: string, programId: string) {
    try {
      // Check if the loyalty program exists
      const program = await prisma.loyaltyProgram.findUnique({
        where: {
          id: programId,
          merchantId,
        },
      });
      
      if (!program) {
        throw new Error('Loyalty program not found');
      }
      
      // Get the tiers
      const tiers = await prisma.loyaltyTier.findMany({
        where: {
          loyaltyProgramId: programId,
        },
        orderBy: {
          order: 'asc',
        },
      });
      
      return tiers;
    } catch (error) {
      console.error('Error fetching loyalty tiers:', error);
      throw new Error('Failed to fetch loyalty tiers');
    }
  },
  
  /**
   * Create a loyalty tier
   * @param merchantId - The ID of the merchant
   * @param programId - The ID of the loyalty program
   * @param tier - The loyalty tier data
   * @returns The created loyalty tier
   */
  async createLoyaltyTier(
    merchantId: string,
    programId: string,
    tier: Omit<LoyaltyTier, 'id' | 'loyaltyProgramId'>
  ) {
    try {
      // Check if the loyalty program exists
      const program = await prisma.loyaltyProgram.findUnique({
        where: {
          id: programId,
          merchantId,
        },
      });
      
      if (!program) {
        throw new Error('Loyalty program not found');
      }
      
      // Get the highest order value to place the new tier at the end
      const highestOrder = await prisma.loyaltyTier.findFirst({
        where: {
          loyaltyProgramId: programId,
        },
        orderBy: {
          order: 'desc',
        },
        select: {
          order: true,
        },
      });
      
      const order = highestOrder ? highestOrder.order + 1 : 0;
      
      // Create the tier
      const newTier = await prisma.loyaltyTier.create({
        data: {
          ...tier,
          loyaltyProgramId: programId,
          order,
        },
      });
      
      return newTier;
    } catch (error) {
      console.error('Error creating loyalty tier:', error);
      throw new Error('Failed to create loyalty tier');
    }
  },
  
  /**
   * Update a loyalty tier
   * @param merchantId - The ID of the merchant
   * @param programId - The ID of the loyalty program
   * @param tierId - The ID of the loyalty tier
   * @param data - The updated loyalty tier data
   * @returns The updated loyalty tier
   */
  async updateLoyaltyTier(
    merchantId: string,
    programId: string,
    tierId: string,
    data: Partial<Omit<LoyaltyTier, 'id' | 'loyaltyProgramId'>>
  ) {
    try {
      // Check if the loyalty program exists
      const program = await prisma.loyaltyProgram.findUnique({
        where: {
          id: programId,
          merchantId,
        },
      });
      
      if (!program) {
        throw new Error('Loyalty program not found');
      }
      
      // Check if the tier exists
      const existingTier = await prisma.loyaltyTier.findUnique({
        where: {
          id: tierId,
          loyaltyProgramId: programId,
        },
      });
      
      if (!existingTier) {
        throw new Error('Loyalty tier not found');
      }
      
      // Update the tier
      const tier = await prisma.loyaltyTier.update({
        where: {
          id: tierId,
        },
        data,
      });
      
      return tier;
    } catch (error) {
      console.error('Error updating loyalty tier:', error);
      throw new Error('Failed to update loyalty tier');
    }
  },
  
  /**
   * Delete a loyalty tier
   * @param merchantId - The ID of the merchant
   * @param programId - The ID of the loyalty program
   * @param tierId - The ID of the loyalty tier
   * @returns True if the loyalty tier was deleted
   */
  async deleteLoyaltyTier(merchantId: string, programId: string, tierId: string) {
    try {
      // Check if the loyalty program exists
      const program = await prisma.loyaltyProgram.findUnique({
        where: {
          id: programId,
          merchantId,
        },
      });
      
      if (!program) {
        throw new Error('Loyalty program not found');
      }
      
      // Check if the tier exists
      const tier = await prisma.loyaltyTier.findUnique({
        where: {
          id: tierId,
          loyaltyProgramId: programId,
        },
      });
      
      if (!tier) {
        throw new Error('Loyalty tier not found');
      }
      
      // Delete the tier
      await prisma.loyaltyTier.delete({
        where: {
          id: tierId,
        },
      });
      
      // Reorder the remaining tiers
      const remainingTiers = await prisma.loyaltyTier.findMany({
        where: {
          loyaltyProgramId: programId,
          order: {
            gt: tier.order,
          },
        },
        orderBy: {
          order: 'asc',
        },
      });
      
      // Update the order of each remaining tier
      for (let i = 0; i < remainingTiers.length; i++) {
        await prisma.loyaltyTier.update({
          where: {
            id: remainingTiers[i].id,
          },
          data: {
            order: tier.order + i,
          },
        });
      }
      
      return { success: true };
    } catch (error) {
      console.error('Error deleting loyalty tier:', error);
      throw new Error('Failed to delete loyalty tier');
    }
  },
  
  /**
   * Get customer loyalty points
   * @param merchantId - The ID of the merchant
   * @param customerId - The ID of the customer
   * @returns The customer's loyalty points and tier
   */
  async getCustomerLoyaltyPoints(merchantId: string, customerId: string) {
    try {
      // Check if the customer exists
      const customer = await prisma.customer.findUnique({
        where: {
          id: customerId,
          merchantId,
        },
      });
      
      if (!customer) {
        throw new Error('Customer not found');
      }
      
      // Get the active loyalty program
      const program = await prisma.loyaltyProgram.findFirst({
        where: {
          merchantId,
          isActive: true,
        },
        include: {
          tiers: {
            orderBy: {
              requiredPoints: 'asc',
            },
          },
        },
      });
      
      if (!program) {
        return {
          points: 0,
          tier: null,
          program: null,
          transactions: [],
        };
      }
      
      // Get the customer's loyalty transactions
      const transactions = await prisma.loyaltyTransaction.findMany({
        where: {
          loyaltyProgramId: program.id,
          customerId,
        },
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          appointment: {
            select: {
              id: true,
              date: true,
              startTime: true,
              service: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
      });
      
      // Calculate the total points
      const totalPoints = transactions.reduce((sum, transaction) => {
        if (transaction.type === 'earn' || transaction.type === 'adjust') {
          return sum + transaction.points;
        } else if (transaction.type === 'redeem' || transaction.type === 'expire') {
          return sum - transaction.points;
        }
        return sum;
      }, 0);
      
      // Determine the customer's tier
      let tier = program.tiers[0]; // Default to the lowest tier
      
      for (let i = program.tiers.length - 1; i >= 0; i--) {
        if (totalPoints >= program.tiers[i].requiredPoints) {
          tier = program.tiers[i];
          break;
        }
      }
      
      return {
        points: totalPoints,
        tier,
        program,
        transactions,
      };
    } catch (error) {
      console.error('Error fetching customer loyalty points:', error);
      throw new Error('Failed to fetch customer loyalty points');
    }
  },
  
  /**
   * Add loyalty points for a customer
   * @param merchantId - The ID of the merchant
   * @param customerId - The ID of the customer
   * @param appointmentId - The ID of the appointment (optional)
   * @param amount - The purchase amount
   * @param description - A description of the transaction
   * @returns The loyalty transaction
   */
  async addLoyaltyPoints(
    merchantId: string,
    customerId: string,
    amount: number,
    appointmentId?: string,
    description?: string
  ) {
    try {
      // Check if the customer exists
      const customer = await prisma.customer.findUnique({
        where: {
          id: customerId,
          merchantId,
        },
      });
      
      if (!customer) {
        throw new Error('Customer not found');
      }
      
      // Get the active loyalty program
      const program = await prisma.loyaltyProgram.findFirst({
        where: {
          merchantId,
          isActive: true,
        },
        include: {
          tiers: {
            orderBy: {
              requiredPoints: 'asc',
            },
          },
        },
      });
      
      if (!program) {
        throw new Error('No active loyalty program found');
      }
      
      // Get the customer's current tier
      const { points: currentPoints, tier: currentTier } = await this.getCustomerLoyaltyPoints(merchantId, customerId);
      
      // Calculate points to award
      let pointsToAward = program.pointsPerPurchase;
      
      if (amount > 0) {
        pointsToAward += Math.floor(amount / program.amountPerPoint) * program.pointsPerAmount;
      }
      
      // Apply tier multiplier if available
      if (currentTier && currentTier.multiplier > 1) {
        pointsToAward = Math.floor(pointsToAward * currentTier.multiplier);
      }
      
      // Create the transaction
      const transaction = await prisma.loyaltyTransaction.create({
        data: {
          loyaltyProgramId: program.id,
          customerId,
          appointmentId,
          type: 'earn',
          points: pointsToAward,
          description: description || `Earned ${pointsToAward} points for purchase of $${amount.toFixed(2)}`,
        },
        include: {
          appointment: {
            select: {
              id: true,
              date: true,
              startTime: true,
              service: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
      });
      
      // Check if the customer has moved up a tier
      const newTotalPoints = currentPoints + pointsToAward;
      let newTier = currentTier;
      
      for (let i = program.tiers.length - 1; i >= 0; i--) {
        if (newTotalPoints >= program.tiers[i].requiredPoints) {
          newTier = program.tiers[i];
          break;
        }
      }
      
      // If the customer has moved up a tier, return that information
      const tierUpgrade = newTier && currentTier && newTier.id !== currentTier.id && newTier.order > currentTier.order
        ? {
            previousTier: currentTier,
            newTier,
          }
        : null;
      
      return {
        transaction,
        pointsAwarded: pointsToAward,
        newTotalPoints,
        tierUpgrade,
      };
    } catch (error) {
      console.error('Error adding loyalty points:', error);
      throw new Error('Failed to add loyalty points');
    }
  },
  
  /**
   * Redeem loyalty points for a customer
   * @param merchantId - The ID of the merchant
   * @param customerId - The ID of the customer
   * @param points - The number of points to redeem
   * @param description - A description of the redemption
   * @returns The loyalty transaction
   */
  async redeemLoyaltyPoints(
    merchantId: string,
    customerId: string,
    points: number,
    description?: string
  ) {
    try {
      // Check if the customer exists
      const customer = await prisma.customer.findUnique({
        where: {
          id: customerId,
          merchantId,
        },
      });
      
      if (!customer) {
        throw new Error('Customer not found');
      }
      
      // Get the active loyalty program
      const program = await prisma.loyaltyProgram.findFirst({
        where: {
          merchantId,
          isActive: true,
        },
      });
      
      if (!program) {
        throw new Error('No active loyalty program found');
      }
      
      // Check if the customer has enough points
      const { points: currentPoints } = await this.getCustomerLoyaltyPoints(merchantId, customerId);
      
      if (currentPoints < points) {
        throw new Error('Customer does not have enough points');
      }
      
      // Check if the redemption meets the minimum requirement
      if (points < program.minimumPointsRedemption) {
        throw new Error(`Minimum redemption is ${program.minimumPointsRedemption} points`);
      }
      
      // Calculate the value of the points
      const value = (points * program.pointsValueInCurrency).toFixed(2);
      
      // Create the transaction
      const transaction = await prisma.loyaltyTransaction.create({
        data: {
          loyaltyProgramId: program.id,
          customerId,
          type: 'redeem',
          points,
          description: description || `Redeemed ${points} points for $${value}`,
        },
      });
      
      return {
        transaction,
        pointsRedeemed: points,
        value,
        newTotalPoints: currentPoints - points,
      };
    } catch (error) {
      console.error('Error redeeming loyalty points:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to redeem loyalty points');
    }
  },
};
