import { prisma } from '@/lib/prisma';

/**
 * Interface for search results
 */
export interface SearchResults {
  services: any[];
  appointments: any[];
  staff: any[];
  categories: any[];
}

/**
 * Service for searching across different entities
 */
export const searchService = {
  /**
   * Search across services, appointments, staff, and categories
   * @param merchantId - The ID of the merchant
   * @param query - The search query
   * @returns Search results
   */
  async search(merchantId: string, query: string): Promise<SearchResults> {
    try {
      // Normalize the query
      const normalizedQuery = query.trim().toLowerCase();
      
      if (!normalizedQuery) {
        return {
          services: [],
          appointments: [],
          staff: [],
          categories: [],
        };
      }
      
      // Search for services
      const services = await prisma.service.findMany({
        where: {
          merchantId,
          OR: [
            { name: { contains: normalizedQuery, mode: 'insensitive' } },
            { description: { contains: normalizedQuery, mode: 'insensitive' } },
            { category: { contains: normalizedQuery, mode: 'insensitive' } },
          ],
        },
        include: {
          category: true,
          staff: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        take: 10,
      });
      
      // Search for appointments
      const appointments = await prisma.appointment.findMany({
        where: {
          merchantId,
          OR: [
            { customerName: { contains: normalizedQuery, mode: 'insensitive' } },
            { customerEmail: { contains: normalizedQuery, mode: 'insensitive' } },
            { customerPhone: { contains: normalizedQuery, mode: 'insensitive' } },
            { notes: { contains: normalizedQuery, mode: 'insensitive' } },
          ],
        },
        include: {
          service: {
            select: {
              id: true,
              name: true,
            },
          },
          staff: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        take: 10,
      });
      
      // Search for staff
      const staff = await prisma.staff.findMany({
        where: {
          merchantId,
          OR: [
            { name: { contains: normalizedQuery, mode: 'insensitive' } },
            { email: { contains: normalizedQuery, mode: 'insensitive' } },
            { phone: { contains: normalizedQuery, mode: 'insensitive' } },
            { role: { contains: normalizedQuery, mode: 'insensitive' } },
          ],
        },
        include: {
          services: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        take: 10,
      });
      
      // Search for categories
      const categories = await prisma.serviceCategory.findMany({
        where: {
          merchantId,
          OR: [
            { name: { contains: normalizedQuery, mode: 'insensitive' } },
            { description: { contains: normalizedQuery, mode: 'insensitive' } },
          ],
        },
        include: {
          services: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        take: 10,
      });
      
      return {
        services,
        appointments,
        staff,
        categories,
      };
    } catch (error) {
      console.error('Error searching:', error);
      throw new Error('Failed to search');
    }
  },
  
  /**
   * Search for services
   * @param merchantId - The ID of the merchant
   * @param query - The search query
   * @param categoryId - The category ID to filter by (optional)
   * @returns Services matching the query
   */
  async searchServices(merchantId: string, query: string, categoryId?: string) {
    try {
      // Normalize the query
      const normalizedQuery = query.trim().toLowerCase();
      
      // Build the where clause
      const where: any = {
        merchantId,
      };
      
      // Add category filter if provided
      if (categoryId) {
        where.categoryId = categoryId;
      }
      
      // Add search conditions if query is not empty
      if (normalizedQuery) {
        where.OR = [
          { name: { contains: normalizedQuery, mode: 'insensitive' } },
          { description: { contains: normalizedQuery, mode: 'insensitive' } },
          { category: { contains: normalizedQuery, mode: 'insensitive' } },
        ];
      }
      
      // Search for services
      const services = await prisma.service.findMany({
        where,
        include: {
          category: true,
          staff: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          name: 'asc',
        },
      });
      
      return services;
    } catch (error) {
      console.error('Error searching services:', error);
      throw new Error('Failed to search services');
    }
  },
  
  /**
   * Search for appointments
   * @param merchantId - The ID of the merchant
   * @param query - The search query
   * @param status - The appointment status to filter by (optional)
   * @param startDate - The start date to filter by (optional)
   * @param endDate - The end date to filter by (optional)
   * @returns Appointments matching the query
   */
  async searchAppointments(
    merchantId: string, 
    query: string, 
    status?: string,
    startDate?: string,
    endDate?: string
  ) {
    try {
      // Normalize the query
      const normalizedQuery = query.trim().toLowerCase();
      
      // Build the where clause
      const where: any = {
        merchantId,
      };
      
      // Add status filter if provided
      if (status) {
        where.status = status;
      }
      
      // Add date filter if provided
      if (startDate || endDate) {
        where.date = {};
        if (startDate) {
          where.date.gte = startDate;
        }
        if (endDate) {
          where.date.lte = endDate;
        }
      }
      
      // Add search conditions if query is not empty
      if (normalizedQuery) {
        where.OR = [
          { customerName: { contains: normalizedQuery, mode: 'insensitive' } },
          { customerEmail: { contains: normalizedQuery, mode: 'insensitive' } },
          { customerPhone: { contains: normalizedQuery, mode: 'insensitive' } },
          { notes: { contains: normalizedQuery, mode: 'insensitive' } },
        ];
      }
      
      // Search for appointments
      const appointments = await prisma.appointment.findMany({
        where,
        include: {
          service: {
            select: {
              id: true,
              name: true,
            },
          },
          staff: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: [
          { date: 'desc' },
          { startTime: 'desc' },
        ],
      });
      
      return appointments;
    } catch (error) {
      console.error('Error searching appointments:', error);
      throw new Error('Failed to search appointments');
    }
  },
  
  /**
   * Search for staff
   * @param merchantId - The ID of the merchant
   * @param query - The search query
   * @returns Staff matching the query
   */
  async searchStaff(merchantId: string, query: string) {
    try {
      // Normalize the query
      const normalizedQuery = query.trim().toLowerCase();
      
      // Build the where clause
      const where: any = {
        merchantId,
      };
      
      // Add search conditions if query is not empty
      if (normalizedQuery) {
        where.OR = [
          { name: { contains: normalizedQuery, mode: 'insensitive' } },
          { email: { contains: normalizedQuery, mode: 'insensitive' } },
          { phone: { contains: normalizedQuery, mode: 'insensitive' } },
          { role: { contains: normalizedQuery, mode: 'insensitive' } },
        ];
      }
      
      // Search for staff
      const staff = await prisma.staff.findMany({
        where,
        include: {
          services: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          name: 'asc',
        },
      });
      
      return staff;
    } catch (error) {
      console.error('Error searching staff:', error);
      throw new Error('Failed to search staff');
    }
  },
};
