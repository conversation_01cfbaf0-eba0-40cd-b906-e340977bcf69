import { prisma } from '@/lib/prisma';
import { Service } from '@/lib/validations/serviceSchema';
import { Prisma } from '@prisma/client';

/**
 * Service for handling service-related operations
 */
export const serviceService = {
  /**
   * Get all services for a merchant
   * @param merchantId - The ID of the merchant
   * @returns A list of services
   */
  async getServices(merchantId: string, categoryId?: string) {
    try {
      const services = await prisma.service.findMany({
        where: {
          merchantId,
          ...(categoryId ? { categoryId } : {}),
        },
        include: {
          staff: true,
          category: true,
        },
        orderBy: {
          name: 'asc',
        },
      });

      return services;
    } catch (error) {
      console.error('Error fetching services:', error);
      throw new Error('Failed to fetch services');
    }
  },

  /**
   * Get a service by ID
   * @param merchantId - The ID of the merchant
   * @param serviceId - The ID of the service
   * @returns The service if found
   */
  async getServiceById(merchantId: string, serviceId: string) {
    try {
      const service = await prisma.service.findUnique({
        where: {
          id: serviceId,
          merchantId,
        },
        include: {
          staff: true,
          category: true,
        },
      });

      if (!service) {
        throw new Error('Service not found');
      }

      return service;
    } catch (error) {
      console.error('Error fetching service:', error);
      throw new Error('Failed to fetch service');
    }
  },

  /**
   * Create a new service
   * @param merchantId - The ID of the merchant
   * @param serviceData - The service data
   * @returns The created service
   */
  async createService(merchantId: string, serviceData: Omit<Service, 'id'>) {
    try {
      // Extract staffIds and categoryId from the service data
      const { staffIds, categoryId, ...serviceInfo } = serviceData;

      // Create the service
      const service = await prisma.service.create({
        data: {
          ...serviceInfo,
          merchantId,
          // Connect category if provided
          category: categoryId
            ? {
                connect: { id: categoryId },
              }
            : undefined,
          // Connect staff if provided
          staff: staffIds && staffIds.length > 0
            ? {
                connect: staffIds.map(id => ({ id })),
              }
            : undefined,
        },
        include: {
          staff: true,
          category: true,
        },
      });

      return service;
    } catch (error) {
      console.error('Error creating service:', error);
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new Error('A service with this name already exists');
        }
      }
      throw new Error('Failed to create service');
    }
  },

  /**
   * Update a service
   * @param merchantId - The ID of the merchant
   * @param serviceId - The ID of the service
   * @param serviceData - The updated service data
   * @returns The updated service
   */
  async updateService(merchantId: string, serviceId: string, serviceData: Partial<Service>) {
    try {
      // Extract staffIds and categoryId from the service data
      const { staffIds, categoryId, ...serviceInfo } = serviceData;

      // Get the current service to check if it exists
      const existingService = await prisma.service.findUnique({
        where: {
          id: serviceId,
          merchantId,
        },
        include: {
          staff: true,
          category: true,
        },
      });

      if (!existingService) {
        throw new Error('Service not found');
      }

      // Update the service
      const service = await prisma.service.update({
        where: {
          id: serviceId,
          merchantId,
        },
        data: {
          ...serviceInfo,
          // Update category connection if provided
          category: categoryId !== undefined
            ? categoryId
              ? { connect: { id: categoryId } }
              : { disconnect: true }
            : undefined,
          // Update staff connections if provided
          staff: staffIds
            ? {
                set: staffIds.map(id => ({ id })),
              }
            : undefined,
        },
        include: {
          staff: true,
          category: true,
        },
      });

      return service;
    } catch (error) {
      console.error('Error updating service:', error);
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new Error('A service with this name already exists');
        }
      }
      throw new Error('Failed to update service');
    }
  },

  /**
   * Delete a service
   * @param merchantId - The ID of the merchant
   * @param serviceId - The ID of the service
   */
  async deleteService(merchantId: string, serviceId: string) {
    try {
      // Check if the service exists
      const service = await prisma.service.findUnique({
        where: {
          id: serviceId,
          merchantId,
        },
      });

      if (!service) {
        throw new Error('Service not found');
      }

      // Delete the service
      await prisma.service.delete({
        where: {
          id: serviceId,
          merchantId,
        },
      });

      return { success: true };
    } catch (error) {
      console.error('Error deleting service:', error);
      throw new Error('Failed to delete service');
    }
  },

  /**
   * Check availability for a service on a specific date
   * @param merchantId - The ID of the merchant
   * @param serviceId - The ID of the service
   * @param date - The date to check availability for
   * @returns Available time slots
   */
  async getAvailability(merchantId: string, serviceId: string, date: string) {
    try {
      // Get the service to check duration
      const service = await prisma.service.findUnique({
        where: {
          id: serviceId,
          merchantId,
        },
        include: {
          staff: true,
        },
      });

      if (!service) {
        throw new Error('Service not found');
      }

      // Get existing appointments for this service on this date
      const appointments = await prisma.appointment.findMany({
        where: {
          serviceId,
          merchantId,
          date,
          status: {
            notIn: ['cancelled'],
          },
        },
      });

      // Calculate available time slots based on service duration and existing appointments
      // This is a simplified implementation - in a real app, you would need more complex logic
      const timeSlots = generateTimeSlots(service.duration);
      const bookedSlots = appointments.map(appointment => appointment.time);

      // Filter out booked slots
      const availableSlots = timeSlots.filter(slot => !bookedSlots.includes(slot));

      return {
        date,
        availableSlots,
      };
    } catch (error) {
      console.error('Error checking availability:', error);
      throw new Error('Failed to check availability');
    }
  },
};

/**
 * Generate time slots based on service duration
 * @param duration - The duration of the service in minutes
 * @returns An array of time slots
 */
function generateTimeSlots(duration: number) {
  // This is a simplified implementation
  // In a real app, you would need to consider business hours, breaks, etc.
  const slots = [];
  const startHour = 9; // 9 AM
  const endHour = 17; // 5 PM

  for (let hour = startHour; hour < endHour; hour++) {
    for (let minute = 0; minute < 60; minute += duration) {
      if (hour === endHour - 1 && minute + duration > 60) {
        // Skip slots that would go past end time
        continue;
      }

      const formattedHour = hour.toString().padStart(2, '0');
      const formattedMinute = minute.toString().padStart(2, '0');
      slots.push(`${formattedHour}:${formattedMinute}`);
    }
  }

  return slots;
}
