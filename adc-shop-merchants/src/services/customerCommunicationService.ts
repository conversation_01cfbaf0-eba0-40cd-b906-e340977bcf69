import { prisma } from '@/lib/prisma';
import { communicationService } from './communicationService';
import { customerService } from './customerService';
import { addDays, format, isAfter, isBefore, parseISO, subDays } from 'date-fns';

/**
 * Service for handling customer-related communications
 */
export const customerCommunicationService = {
  /**
   * Send a birthday greeting to a customer
   * @param merchantId - The ID of the merchant
   * @param customerId - The ID of the customer
   * @param type - The type of message to send (email or sms)
   * @returns The sent communication
   */
  async sendBirthdayGreeting(
    merchantId: string,
    customerId: string,
    type: 'email' | 'sms' = 'email'
  ) {
    try {
      // Get the customer details
      const customer = await customerService.getCustomerById(merchantId, customerId);
      
      if (!customer) {
        throw new Error('Customer not found');
      }
      
      // Check if the customer has a birthday in their metadata
      if (!customer.metadata?.birthday) {
        throw new Error('Customer does not have a birthday set');
      }
      
      // Get the default template for birthday greetings
      const templates = await communicationService.getMerchantTemplates(
        merchantId,
        type,
        'marketing'
      );
      
      const birthdayTemplate = templates.find(template => 
        template.metadata?.purpose === 'birthday_greeting' && template.isDefault
      );
      
      if (!birthdayTemplate) {
        throw new Error(`No default ${type} template found for birthday greetings`);
      }
      
      // Prepare variables for the template
      const variables = this.getCustomerVariables(customer);
      
      // Get the recipient
      const recipient = type === 'email' 
        ? customer.email 
        : customer.phone;
      
      if (!recipient) {
        throw new Error(`Customer ${type === 'email' ? 'email' : 'phone'} not found`);
      }
      
      // Send the communication
      const communication = await communicationService.sendWithTemplate({
        merchantId,
        templateId: birthdayTemplate.id!,
        userId: customer.userId,
        recipient,
        variables,
      });
      
      // Record that we sent a birthday greeting this year
      await prisma.customer.update({
        where: {
          id: customerId,
        },
        data: {
          metadata: {
            ...customer.metadata,
            lastBirthdayGreeting: new Date().toISOString().split('T')[0],
          },
        },
      });
      
      return communication;
    } catch (error) {
      console.error('Error sending birthday greeting:', error);
      throw new Error('Failed to send birthday greeting');
    }
  },
  
  /**
   * Send a special offer to a loyal customer
   * @param merchantId - The ID of the merchant
   * @param customerId - The ID of the customer
   * @param type - The type of message to send (email or sms)
   * @param offerType - The type of offer to send
   * @returns The sent communication
   */
  async sendLoyaltyOffer(
    merchantId: string,
    customerId: string,
    type: 'email' | 'sms' = 'email',
    offerType: 'discount' | 'free_service' | 'gift' = 'discount'
  ) {
    try {
      // Get the customer details
      const customer = await customerService.getCustomerById(merchantId, customerId);
      
      if (!customer) {
        throw new Error('Customer not found');
      }
      
      // Get the default template for loyalty offers
      const templates = await communicationService.getMerchantTemplates(
        merchantId,
        type,
        'marketing'
      );
      
      const loyaltyTemplate = templates.find(template => 
        template.metadata?.purpose === 'loyalty_offer' && 
        template.metadata?.offerType === offerType &&
        template.isDefault
      );
      
      if (!loyaltyTemplate) {
        throw new Error(`No default ${type} template found for loyalty offers of type ${offerType}`);
      }
      
      // Prepare variables for the template
      const variables = this.getCustomerVariables(customer);
      
      // Add offer-specific variables
      const offerVariables = this.getLoyaltyOfferVariables(offerType);
      
      // Get the recipient
      const recipient = type === 'email' 
        ? customer.email 
        : customer.phone;
      
      if (!recipient) {
        throw new Error(`Customer ${type === 'email' ? 'email' : 'phone'} not found`);
      }
      
      // Send the communication
      const communication = await communicationService.sendWithTemplate({
        merchantId,
        templateId: loyaltyTemplate.id!,
        userId: customer.userId,
        recipient,
        variables: { ...variables, ...offerVariables },
      });
      
      // Record that we sent a loyalty offer
      await prisma.customer.update({
        where: {
          id: customerId,
        },
        data: {
          metadata: {
            ...customer.metadata,
            lastLoyaltyOffer: {
              date: new Date().toISOString().split('T')[0],
              type: offerType,
            },
          },
        },
      });
      
      return communication;
    } catch (error) {
      console.error('Error sending loyalty offer:', error);
      throw new Error('Failed to send loyalty offer');
    }
  },
  
  /**
   * Send a re-engagement message to inactive customers
   * @param merchantId - The ID of the merchant
   * @param daysSinceLastAppointment - Number of days since last appointment to consider a customer inactive
   * @param type - The type of message to send (email or sms)
   * @returns The number of communications sent
   */
  async sendReengagementCampaign(
    merchantId: string,
    daysSinceLastAppointment: number = 90,
    type: 'email' | 'sms' = 'email'
  ) {
    try {
      // Get the cutoff date
      const cutoffDate = subDays(new Date(), daysSinceLastAppointment);
      
      // Find inactive customers
      const inactiveCustomers = await prisma.customer.findMany({
        where: {
          merchantId,
          status: 'active',
          appointments: {
            some: {
              date: {
                lt: cutoffDate.toISOString().split('T')[0],
              },
            },
            none: {
              date: {
                gte: cutoffDate.toISOString().split('T')[0],
              },
            },
          },
        },
        include: {
          appointments: {
            orderBy: {
              date: 'desc',
            },
            take: 1,
          },
        },
      });
      
      if (inactiveCustomers.length === 0) {
        return { sent: 0, message: 'No inactive customers found' };
      }
      
      // Get the default template for re-engagement
      const templates = await communicationService.getMerchantTemplates(
        merchantId,
        type,
        'marketing'
      );
      
      const reengagementTemplate = templates.find(template => 
        template.metadata?.purpose === 'reengagement' && template.isDefault
      );
      
      if (!reengagementTemplate) {
        throw new Error(`No default ${type} template found for re-engagement`);
      }
      
      // Send communications to each inactive customer
      let sentCount = 0;
      
      for (const customer of inactiveCustomers) {
        try {
          // Skip customers who don't have the required contact info
          const recipient = type === 'email' ? customer.email : customer.phone;
          if (!recipient) continue;
          
          // Skip customers who have received a re-engagement message in the last 30 days
          const lastReengagement = customer.metadata?.lastReengagement;
          if (lastReengagement) {
            const lastDate = parseISO(lastReengagement);
            if (isAfter(lastDate, subDays(new Date(), 30))) {
              continue;
            }
          }
          
          // Prepare variables for the template
          const variables = this.getCustomerVariables(customer);
          
          // Add last appointment date
          if (customer.appointments.length > 0) {
            const lastAppointment = customer.appointments[0];
            variables.lastAppointmentDate = format(parseISO(lastAppointment.date), 'MMMM d, yyyy');
          }
          
          // Send the communication
          await communicationService.sendWithTemplate({
            merchantId,
            templateId: reengagementTemplate.id!,
            userId: customer.userId,
            recipient,
            variables,
          });
          
          // Record that we sent a re-engagement message
          await prisma.customer.update({
            where: {
              id: customer.id,
            },
            data: {
              metadata: {
                ...customer.metadata,
                lastReengagement: new Date().toISOString().split('T')[0],
              },
            },
          });
          
          sentCount++;
        } catch (error) {
          console.error(`Error sending re-engagement to customer ${customer.id}:`, error);
          // Continue with the next customer
        }
      }
      
      return { sent: sentCount };
    } catch (error) {
      console.error('Error sending re-engagement campaign:', error);
      throw new Error('Failed to send re-engagement campaign');
    }
  },
  
  /**
   * Get variables for a customer template
   * @param customer - The customer
   * @returns The variables
   */
  getCustomerVariables(customer: any) {
    return {
      customerName: customer.name,
      customerFirstName: customer.name.split(' ')[0],
      customerLastName: customer.name.split(' ').slice(1).join(' '),
      customerEmail: customer.email,
      customerPhone: customer.phone || '',
      customerAddress: customer.address || '',
      customerCity: customer.city || '',
      customerState: customer.state || '',
      customerPostalCode: customer.postalCode || '',
      customerCountry: customer.country || '',
      customerBirthday: customer.metadata?.birthday || '',
    };
  },
  
  /**
   * Get variables for a loyalty offer
   * @param offerType - The type of offer
   * @returns The variables
   */
  getLoyaltyOfferVariables(offerType: 'discount' | 'free_service' | 'gift') {
    switch (offerType) {
      case 'discount':
        return {
          offerType: 'discount',
          offerValue: '20%',
          offerCode: `LOYAL${Math.floor(Math.random() * 10000)}`,
          offerExpiry: format(addDays(new Date(), 30), 'MMMM d, yyyy'),
        };
      case 'free_service':
        return {
          offerType: 'free service',
          offerValue: 'One complimentary service',
          offerCode: `FREE${Math.floor(Math.random() * 10000)}`,
          offerExpiry: format(addDays(new Date(), 30), 'MMMM d, yyyy'),
        };
      case 'gift':
        return {
          offerType: 'gift',
          offerValue: 'A special gift',
          offerCode: `GIFT${Math.floor(Math.random() * 10000)}`,
          offerExpiry: format(addDays(new Date(), 30), 'MMMM d, yyyy'),
        };
      default:
        return {};
    }
  },
};
