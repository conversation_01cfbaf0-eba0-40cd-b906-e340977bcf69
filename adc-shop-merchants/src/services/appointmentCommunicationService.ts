import { communicationService } from './communicationService';
import { appointmentService } from './appointmentService';
import { settingsService } from './settingsService';
import { format, addHours, addDays, parseISO } from 'date-fns';

/**
 * Service for handling appointment-related communications
 */
export const appointmentCommunicationService = {
  /**
   * Send an appointment confirmation message
   * @param merchantId - The ID of the merchant
   * @param appointmentId - The ID of the appointment
   * @param type - The type of message to send (email or sms)
   * @returns The sent communication
   */
  async sendAppointmentConfirmation(
    merchantId: string,
    appointmentId: string,
    type: 'email' | 'sms' = 'email'
  ) {
    try {
      // Get the appointment details
      const appointment = await appointmentService.getAppointmentById(merchantId, appointmentId);
      
      if (!appointment) {
        throw new Error('Appointment not found');
      }
      
      // Get the default template for appointment confirmation
      const templates = await communicationService.getMerchantTemplates(
        merchantId,
        type,
        'appointment_confirmation'
      );
      
      const defaultTemplate = templates.find(template => template.isDefault);
      
      if (!defaultTemplate) {
        throw new Error(`No default ${type} template found for appointment confirmation`);
      }
      
      // Prepare variables for the template
      const variables = this.getAppointmentVariables(appointment);
      
      // Get the recipient
      const recipient = type === 'email' 
        ? appointment.user?.email 
        : appointment.user?.phone;
      
      if (!recipient) {
        throw new Error(`User ${type === 'email' ? 'email' : 'phone'} not found`);
      }
      
      // Send the communication
      const communication = await communicationService.sendWithTemplate({
        merchantId,
        templateId: defaultTemplate.id!,
        userId: appointment.userId,
        recipient,
        variables,
      });
      
      return communication;
    } catch (error) {
      console.error('Error sending appointment confirmation:', error);
      throw new Error('Failed to send appointment confirmation');
    }
  },
  
  /**
   * Send an appointment reminder message
   * @param merchantId - The ID of the merchant
   * @param appointmentId - The ID of the appointment
   * @param type - The type of message to send (email or sms)
   * @returns The sent communication
   */
  async sendAppointmentReminder(
    merchantId: string,
    appointmentId: string,
    type: 'email' | 'sms' = 'email'
  ) {
    try {
      // Get the appointment details
      const appointment = await appointmentService.getAppointmentById(merchantId, appointmentId);
      
      if (!appointment) {
        throw new Error('Appointment not found');
      }
      
      // Get the default template for appointment reminder
      const templates = await communicationService.getMerchantTemplates(
        merchantId,
        type,
        'appointment_reminder'
      );
      
      const defaultTemplate = templates.find(template => template.isDefault);
      
      if (!defaultTemplate) {
        throw new Error(`No default ${type} template found for appointment reminder`);
      }
      
      // Prepare variables for the template
      const variables = this.getAppointmentVariables(appointment);
      
      // Get the recipient
      const recipient = type === 'email' 
        ? appointment.user?.email 
        : appointment.user?.phone;
      
      if (!recipient) {
        throw new Error(`User ${type === 'email' ? 'email' : 'phone'} not found`);
      }
      
      // Send the communication
      const communication = await communicationService.sendWithTemplate({
        merchantId,
        templateId: defaultTemplate.id!,
        userId: appointment.userId,
        recipient,
        variables,
      });
      
      return communication;
    } catch (error) {
      console.error('Error sending appointment reminder:', error);
      throw new Error('Failed to send appointment reminder');
    }
  },
  
  /**
   * Send an appointment cancellation message
   * @param merchantId - The ID of the merchant
   * @param appointmentId - The ID of the appointment
   * @param type - The type of message to send (email or sms)
   * @returns The sent communication
   */
  async sendAppointmentCancellation(
    merchantId: string,
    appointmentId: string,
    type: 'email' | 'sms' = 'email'
  ) {
    try {
      // Get the appointment details
      const appointment = await appointmentService.getAppointmentById(merchantId, appointmentId);
      
      if (!appointment) {
        throw new Error('Appointment not found');
      }
      
      // Get the default template for appointment cancellation
      const templates = await communicationService.getMerchantTemplates(
        merchantId,
        type,
        'appointment_cancellation'
      );
      
      const defaultTemplate = templates.find(template => template.isDefault);
      
      if (!defaultTemplate) {
        throw new Error(`No default ${type} template found for appointment cancellation`);
      }
      
      // Prepare variables for the template
      const variables = this.getAppointmentVariables(appointment);
      
      // Get the recipient
      const recipient = type === 'email' 
        ? appointment.user?.email 
        : appointment.user?.phone;
      
      if (!recipient) {
        throw new Error(`User ${type === 'email' ? 'email' : 'phone'} not found`);
      }
      
      // Send the communication
      const communication = await communicationService.sendWithTemplate({
        merchantId,
        templateId: defaultTemplate.id!,
        userId: appointment.userId,
        recipient,
        variables,
      });
      
      return communication;
    } catch (error) {
      console.error('Error sending appointment cancellation:', error);
      throw new Error('Failed to send appointment cancellation');
    }
  },
  
  /**
   * Schedule appointment reminders based on merchant settings
   * @param merchantId - The ID of the merchant
   * @param appointmentId - The ID of the appointment
   * @returns The scheduled communications
   */
  async scheduleAppointmentReminders(merchantId: string, appointmentId: string) {
    try {
      // Get the appointment details
      const appointment = await appointmentService.getAppointmentById(merchantId, appointmentId);
      
      if (!appointment) {
        throw new Error('Appointment not found');
      }
      
      // Get the merchant's reminder settings
      const settings = await settingsService.getSettings(merchantId);
      const reminderSettings = settings?.appointmentReminders || {
        emailEnabled: true,
        emailReminderHours: 24,
        smsEnabled: false,
        smsReminderHours: 2,
      };
      
      const scheduledCommunications = [];
      
      // Schedule email reminder if enabled
      if (reminderSettings.emailEnabled && appointment.user?.email) {
        // Calculate when to send the reminder
        const appointmentDate = parseISO(`${appointment.date}T${appointment.startTime}`);
        const reminderDate = addHours(appointmentDate, -reminderSettings.emailReminderHours);
        
        // Get the default template for appointment reminder
        const templates = await communicationService.getMerchantTemplates(
          merchantId,
          'email',
          'appointment_reminder'
        );
        
        const defaultTemplate = templates.find(template => template.isDefault);
        
        if (defaultTemplate) {
          // Prepare variables for the template
          const variables = this.getAppointmentVariables(appointment);
          
          // Schedule the email reminder
          const communication = await communicationService.sendWithTemplate({
            merchantId,
            templateId: defaultTemplate.id!,
            userId: appointment.userId,
            recipient: appointment.user.email,
            variables,
            scheduledFor: reminderDate,
          });
          
          scheduledCommunications.push(communication);
        }
      }
      
      // Schedule SMS reminder if enabled
      if (reminderSettings.smsEnabled && appointment.user?.phone) {
        // Calculate when to send the reminder
        const appointmentDate = parseISO(`${appointment.date}T${appointment.startTime}`);
        const reminderDate = addHours(appointmentDate, -reminderSettings.smsReminderHours);
        
        // Get the default template for appointment reminder
        const templates = await communicationService.getMerchantTemplates(
          merchantId,
          'sms',
          'appointment_reminder'
        );
        
        const defaultTemplate = templates.find(template => template.isDefault);
        
        if (defaultTemplate) {
          // Prepare variables for the template
          const variables = this.getAppointmentVariables(appointment);
          
          // Schedule the SMS reminder
          const communication = await communicationService.sendWithTemplate({
            merchantId,
            templateId: defaultTemplate.id!,
            userId: appointment.userId,
            recipient: appointment.user.phone,
            variables,
            scheduledFor: reminderDate,
          });
          
          scheduledCommunications.push(communication);
        }
      }
      
      return scheduledCommunications;
    } catch (error) {
      console.error('Error scheduling appointment reminders:', error);
      throw new Error('Failed to schedule appointment reminders');
    }
  },
  
  /**
   * Get variables for an appointment template
   * @param appointment - The appointment
   * @returns The variables
   */
  getAppointmentVariables(appointment: any) {
    const appointmentDate = parseISO(`${appointment.date}T${appointment.startTime}`);
    
    return {
      appointmentId: appointment.id,
      appointmentDate: format(appointmentDate, 'MMMM d, yyyy'),
      appointmentTime: format(appointmentDate, 'h:mm a'),
      appointmentDuration: `${appointment.service.duration} minutes`,
      appointmentStatus: appointment.status,
      appointmentNotes: appointment.notes || '',
      serviceName: appointment.service.name,
      serviceDescription: appointment.service.description || '',
      servicePrice: `$${appointment.service.price.toFixed(2)}`,
      serviceDuration: `${appointment.service.duration} minutes`,
      serviceCategory: appointment.service.category?.name || '',
      staffName: appointment.staff?.name || '',
      staffEmail: appointment.staff?.email || '',
      staffPhone: appointment.staff?.phone || '',
      staffTitle: appointment.staff?.title || '',
      customerName: appointment.user?.name || '',
      customerFirstName: appointment.user?.name?.split(' ')[0] || '',
      customerLastName: appointment.user?.name?.split(' ').slice(1).join(' ') || '',
      customerEmail: appointment.user?.email || '',
      customerPhone: appointment.user?.phone || '',
      merchantName: appointment.merchant?.name || '',
      merchantAddress: appointment.merchant?.address || '',
      merchantPhone: appointment.merchant?.phone || '',
      merchantEmail: appointment.merchant?.email || '',
      merchantWebsite: appointment.merchant?.website || '',
    };
  },
};
