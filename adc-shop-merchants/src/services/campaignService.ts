import { prisma } from '@/lib/prisma';
import { communicationService, CommunicationCampaign } from './communicationService';
import { customerService } from './customerService';

/**
 * Interface for campaign segment
 */
export interface CampaignSegment {
  id?: string;
  merchantId: string;
  name: string;
  description?: string;
  filters: CampaignSegmentFilter[];
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Interface for campaign segment filter
 */
export interface CampaignSegmentFilter {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than' | 'in' | 'not_in';
  value: any;
}

/**
 * Interface for campaign execution
 */
export interface CampaignExecution {
  id?: string;
  campaignId: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  startedAt?: Date;
  completedAt?: Date;
  totalRecipients: number;
  successCount: number;
  failureCount: number;
  logs?: string[];
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Service for handling campaign management
 */
export const campaignService = {
  /**
   * Create a campaign
   * @param campaign - The campaign data
   * @returns The created campaign
   */
  async createCampaign(campaign: Omit<CommunicationCampaign, 'id' | 'createdAt' | 'updatedAt'>) {
    try {
      // Validate the template exists
      const template = await communicationService.getTemplateById(campaign.templateId);
      
      if (!template) {
        throw new Error('Template not found');
      }
      
      // Create the campaign
      const newCampaign = await prisma.communicationCampaign.create({
        data: campaign,
      });
      
      return newCampaign;
    } catch (error) {
      console.error('Error creating campaign:', error);
      throw new Error('Failed to create campaign');
    }
  },
  
  /**
   * Get a campaign by ID
   * @param merchantId - The ID of the merchant
   * @param campaignId - The ID of the campaign
   * @returns The campaign
   */
  async getCampaignById(merchantId: string, campaignId: string) {
    try {
      const campaign = await prisma.communicationCampaign.findUnique({
        where: {
          id: campaignId,
          merchantId,
        },
        include: {
          template: true,
          executions: {
            orderBy: {
              createdAt: 'desc',
            },
          },
        },
      });
      
      if (!campaign) {
        throw new Error('Campaign not found');
      }
      
      return campaign;
    } catch (error) {
      console.error('Error fetching campaign:', error);
      throw new Error('Failed to fetch campaign');
    }
  },
  
  /**
   * Get campaigns for a merchant
   * @param merchantId - The ID of the merchant
   * @param type - Optional filter by campaign type
   * @returns The campaigns
   */
  async getCampaigns(merchantId: string, type?: 'email' | 'sms') {
    try {
      const campaigns = await prisma.communicationCampaign.findMany({
        where: {
          merchantId,
          ...(type && { type }),
        },
        include: {
          template: true,
          executions: {
            orderBy: {
              createdAt: 'desc',
            },
            take: 1,
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
      
      return campaigns;
    } catch (error) {
      console.error('Error fetching campaigns:', error);
      throw new Error('Failed to fetch campaigns');
    }
  },
  
  /**
   * Update a campaign
   * @param merchantId - The ID of the merchant
   * @param campaignId - The ID of the campaign
   * @param data - The updated campaign data
   * @returns The updated campaign
   */
  async updateCampaign(
    merchantId: string,
    campaignId: string,
    data: Partial<Omit<CommunicationCampaign, 'id' | 'merchantId' | 'createdAt' | 'updatedAt'>>
  ) {
    try {
      // Check if the campaign exists
      const campaign = await prisma.communicationCampaign.findUnique({
        where: {
          id: campaignId,
          merchantId,
        },
      });
      
      if (!campaign) {
        throw new Error('Campaign not found');
      }
      
      // Validate the template if it's being updated
      if (data.templateId && data.templateId !== campaign.templateId) {
        const template = await communicationService.getTemplateById(data.templateId);
        
        if (!template) {
          throw new Error('Template not found');
        }
      }
      
      // Update the campaign
      const updatedCampaign = await prisma.communicationCampaign.update({
        where: {
          id: campaignId,
        },
        data,
        include: {
          template: true,
        },
      });
      
      return updatedCampaign;
    } catch (error) {
      console.error('Error updating campaign:', error);
      throw new Error('Failed to update campaign');
    }
  },
  
  /**
   * Delete a campaign
   * @param merchantId - The ID of the merchant
   * @param campaignId - The ID of the campaign
   * @returns True if the campaign was deleted
   */
  async deleteCampaign(merchantId: string, campaignId: string) {
    try {
      // Check if the campaign exists
      const campaign = await prisma.communicationCampaign.findUnique({
        where: {
          id: campaignId,
          merchantId,
        },
      });
      
      if (!campaign) {
        throw new Error('Campaign not found');
      }
      
      // Delete the campaign
      await prisma.communicationCampaign.delete({
        where: {
          id: campaignId,
        },
      });
      
      return true;
    } catch (error) {
      console.error('Error deleting campaign:', error);
      throw new Error('Failed to delete campaign');
    }
  },
  
  /**
   * Execute a campaign
   * @param merchantId - The ID of the merchant
   * @param campaignId - The ID of the campaign
   * @returns The campaign execution
   */
  async executeCampaign(merchantId: string, campaignId: string) {
    try {
      // Get the campaign
      const campaign = await this.getCampaignById(merchantId, campaignId);
      
      if (campaign.status !== 'draft' && campaign.status !== 'scheduled') {
        throw new Error(`Cannot execute campaign with status: ${campaign.status}`);
      }
      
      // Create an execution record
      const execution = await prisma.campaignExecution.create({
        data: {
          campaignId,
          status: 'in_progress',
          startedAt: new Date(),
          totalRecipients: campaign.recipients.length,
          successCount: 0,
          failureCount: 0,
          logs: [],
        },
      });
      
      // Update campaign status
      await prisma.communicationCampaign.update({
        where: {
          id: campaignId,
        },
        data: {
          status: 'in_progress',
        },
      });
      
      // Process the campaign in the background
      this.processCampaign(merchantId, campaignId, execution.id)
        .catch(error => {
          console.error('Error processing campaign:', error);
        });
      
      return execution;
    } catch (error) {
      console.error('Error executing campaign:', error);
      throw new Error('Failed to execute campaign');
    }
  },
  
  /**
   * Process a campaign (send communications to recipients)
   * @param merchantId - The ID of the merchant
   * @param campaignId - The ID of the campaign
   * @param executionId - The ID of the campaign execution
   */
  async processCampaign(merchantId: string, campaignId: string, executionId: string) {
    try {
      // Get the campaign
      const campaign = await this.getCampaignById(merchantId, campaignId);
      
      // Get the template
      const template = await communicationService.getTemplateById(campaign.templateId);
      
      if (!template) {
        throw new Error('Template not found');
      }
      
      let successCount = 0;
      let failureCount = 0;
      const logs: string[] = [];
      
      // Process each recipient
      for (const recipient of campaign.recipients) {
        try {
          // Send the communication
          await communicationService.sendWithTemplate({
            merchantId,
            templateId: campaign.templateId,
            recipient,
            variables: {},
            scheduledFor: campaign.scheduledFor,
          });
          
          successCount++;
          logs.push(`Successfully sent to ${recipient}`);
        } catch (error) {
          failureCount++;
          logs.push(`Failed to send to ${recipient}: ${error.message}`);
        }
        
        // Update the execution record periodically
        if ((successCount + failureCount) % 10 === 0 || (successCount + failureCount) === campaign.recipients.length) {
          await prisma.campaignExecution.update({
            where: {
              id: executionId,
            },
            data: {
              successCount,
              failureCount,
              logs,
            },
          });
        }
      }
      
      // Update the execution record as completed
      await prisma.campaignExecution.update({
        where: {
          id: executionId,
        },
        data: {
          status: 'completed',
          completedAt: new Date(),
          successCount,
          failureCount,
          logs,
        },
      });
      
      // Update the campaign status
      await prisma.communicationCampaign.update({
        where: {
          id: campaignId,
        },
        data: {
          status: 'completed',
        },
      });
    } catch (error) {
      console.error('Error processing campaign:', error);
      
      // Update the execution record as failed
      await prisma.campaignExecution.update({
        where: {
          id: executionId,
        },
        data: {
          status: 'failed',
          completedAt: new Date(),
          logs: [`Campaign processing failed: ${error.message}`],
        },
      });
      
      // Update the campaign status
      await prisma.communicationCampaign.update({
        where: {
          id: campaignId,
        },
        data: {
          status: 'failed',
        },
      });
      
      throw error;
    }
  },
};
