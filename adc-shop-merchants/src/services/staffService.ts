import { prisma } from '@/lib/prisma';
import { Staff } from '@/lib/validations/serviceSchema';
import { Prisma } from '@prisma/client';

/**
 * Service for handling staff-related operations
 */
export const staffService = {
  /**
   * Get all staff for a merchant
   * @param merchantId - The ID of the merchant
   * @returns A list of staff
   */
  async getStaff(merchantId: string) {
    try {
      const staff = await prisma.staff.findMany({
        where: {
          merchantId,
        },
        include: {
          services: true,
        },
      });
      
      return staff;
    } catch (error) {
      console.error('Error fetching staff:', error);
      throw new Error('Failed to fetch staff');
    }
  },

  /**
   * Get a staff member by ID
   * @param merchantId - The ID of the merchant
   * @param staffId - The ID of the staff member
   * @returns The staff member if found
   */
  async getStaffById(merchantId: string, staffId: string) {
    try {
      const staff = await prisma.staff.findUnique({
        where: {
          id: staffId,
          merchantId,
        },
        include: {
          services: true,
        },
      });
      
      if (!staff) {
        throw new Error('Staff not found');
      }
      
      return staff;
    } catch (error) {
      console.error('Error fetching staff:', error);
      throw new Error('Failed to fetch staff');
    }
  },

  /**
   * Create a new staff member
   * @param merchantId - The ID of the merchant
   * @param staffData - The staff data
   * @returns The created staff member
   */
  async createStaff(merchantId: string, staffData: Omit<Staff, 'id'>) {
    try {
      // Create the staff member
      const staff = await prisma.staff.create({
        data: {
          ...staffData,
          merchantId,
        },
        include: {
          services: true,
        },
      });
      
      return staff;
    } catch (error) {
      console.error('Error creating staff:', error);
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new Error('A staff member with this email already exists');
        }
      }
      throw new Error('Failed to create staff');
    }
  },

  /**
   * Update a staff member
   * @param merchantId - The ID of the merchant
   * @param staffId - The ID of the staff member
   * @param staffData - The updated staff data
   * @returns The updated staff member
   */
  async updateStaff(merchantId: string, staffId: string, staffData: Partial<Staff>) {
    try {
      // Check if the staff member exists
      const existingStaff = await prisma.staff.findUnique({
        where: {
          id: staffId,
          merchantId,
        },
      });
      
      if (!existingStaff) {
        throw new Error('Staff not found');
      }
      
      // Update the staff member
      const staff = await prisma.staff.update({
        where: {
          id: staffId,
          merchantId,
        },
        data: staffData,
        include: {
          services: true,
        },
      });
      
      return staff;
    } catch (error) {
      console.error('Error updating staff:', error);
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new Error('A staff member with this email already exists');
        }
      }
      throw new Error('Failed to update staff');
    }
  },

  /**
   * Delete a staff member
   * @param merchantId - The ID of the merchant
   * @param staffId - The ID of the staff member
   */
  async deleteStaff(merchantId: string, staffId: string) {
    try {
      // Check if the staff member exists
      const staff = await prisma.staff.findUnique({
        where: {
          id: staffId,
          merchantId,
        },
      });
      
      if (!staff) {
        throw new Error('Staff not found');
      }
      
      // Delete the staff member
      await prisma.staff.delete({
        where: {
          id: staffId,
          merchantId,
        },
      });
      
      return { success: true };
    } catch (error) {
      console.error('Error deleting staff:', error);
      throw new Error('Failed to delete staff');
    }
  },

  /**
   * Get staff availability
   * @param merchantId - The ID of the merchant
   * @param staffId - The ID of the staff member
   * @param date - The date to check availability for
   * @returns Available time slots
   */
  async getStaffAvailability(merchantId: string, staffId: string, date: string) {
    try {
      // Check if the staff member exists
      const staff = await prisma.staff.findUnique({
        where: {
          id: staffId,
          merchantId,
        },
      });
      
      if (!staff) {
        throw new Error('Staff not found');
      }
      
      // Get the day of the week
      const dayOfWeek = new Date(date).toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
      
      // Check if the staff member has availability for this day
      const dayAvailability = staff.availability?.[dayOfWeek];
      
      if (!dayAvailability || dayAvailability.length === 0) {
        return {
          date,
          availableSlots: [],
        };
      }
      
      // Get existing appointments for this staff member on this date
      const appointments = await prisma.appointment.findMany({
        where: {
          staffId,
          merchantId,
          date,
          status: {
            notIn: ['cancelled'],
          },
        },
        include: {
          service: true,
        },
      });
      
      // Calculate available time slots based on staff availability and existing appointments
      const availableSlots = [];
      
      for (const slot of dayAvailability) {
        const [startHour, startMinute] = slot.start.split(':').map(Number);
        const [endHour, endMinute] = slot.end.split(':').map(Number);
        
        const startTimeMinutes = startHour * 60 + startMinute;
        const endTimeMinutes = endHour * 60 + endMinute;
        
        // Generate 30-minute slots within the availability window
        for (let time = startTimeMinutes; time < endTimeMinutes; time += 30) {
          const timeSlot = minutesToTimeString(time);
          
          // Check if this time slot overlaps with any appointments
          const isAvailable = !appointments.some(appointment => {
            const appointmentStartTime = appointment.startTime;
            const appointmentEndTime = appointment.endTime;
            
            const [appStartHour, appStartMinute] = appointmentStartTime.split(':').map(Number);
            const [appEndHour, appEndMinute] = appointmentEndTime.split(':').map(Number);
            
            const appStartTimeMinutes = appStartHour * 60 + appStartMinute;
            const appEndTimeMinutes = appEndHour * 60 + appEndMinute;
            
            // Check if the time slot overlaps with the appointment
            return (
              (time >= appStartTimeMinutes && time < appEndTimeMinutes) ||
              (time + 30 > appStartTimeMinutes && time + 30 <= appEndTimeMinutes) ||
              (time <= appStartTimeMinutes && time + 30 >= appEndTimeMinutes)
            );
          });
          
          if (isAvailable) {
            availableSlots.push(timeSlot);
          }
        }
      }
      
      return {
        date,
        availableSlots,
      };
    } catch (error) {
      console.error('Error checking staff availability:', error);
      throw new Error('Failed to check staff availability');
    }
  },

  /**
   * Assign services to a staff member
   * @param merchantId - The ID of the merchant
   * @param staffId - The ID of the staff member
   * @param serviceIds - The IDs of the services to assign
   * @returns The updated staff member
   */
  async assignServices(merchantId: string, staffId: string, serviceIds: string[]) {
    try {
      // Check if the staff member exists
      const staff = await prisma.staff.findUnique({
        where: {
          id: staffId,
          merchantId,
        },
      });
      
      if (!staff) {
        throw new Error('Staff not found');
      }
      
      // Check if all services exist
      const services = await prisma.service.findMany({
        where: {
          id: {
            in: serviceIds,
          },
          merchantId,
        },
      });
      
      if (services.length !== serviceIds.length) {
        throw new Error('One or more services not found');
      }
      
      // Update the staff member's services
      const updatedStaff = await prisma.staff.update({
        where: {
          id: staffId,
          merchantId,
        },
        data: {
          services: {
            set: serviceIds.map(id => ({ id })),
          },
        },
        include: {
          services: true,
        },
      });
      
      return updatedStaff;
    } catch (error) {
      console.error('Error assigning services:', error);
      throw new Error('Failed to assign services');
    }
  },
};

/**
 * Convert minutes to a time string (HH:MM)
 * @param minutes - The number of minutes
 * @returns A time string in the format HH:MM
 */
function minutesToTimeString(minutes: number): string {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
}
