import { prisma } from '@/lib/prisma';

/**
 * Interface for voucher
 */
export interface Voucher {
  id?: string;
  merchantId: string;
  code: string;
  type: 'percentage' | 'fixed_amount' | 'free_service';
  value: number;
  minSpend?: number;
  maxDiscount?: number;
  serviceId?: string;
  categoryId?: string;
  startDate: Date;
  endDate: Date;
  usageLimit?: number;
  usageCount: number;
  isActive: boolean;
  isGiftCard: boolean;
  recipientEmail?: string;
  recipientName?: string;
  senderName?: string;
  message?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Interface for voucher usage
 */
export interface VoucherUsage {
  id?: string;
  voucherId: string;
  appointmentId?: string;
  customerId?: string;
  userId?: string;
  discountAmount: number;
  usedAt: Date;
}

/**
 * Service for handling vouchers and gift cards
 */
export const voucherService = {
  /**
   * Get vouchers for a merchant
   * @param merchantId - The ID of the merchant
   * @param includeInactive - Whether to include inactive vouchers
   * @returns The vouchers
   */
  async getVouchers(merchantId: string, includeInactive = false) {
    try {
      const vouchers = await prisma.voucher.findMany({
        where: {
          merchantId,
          ...(includeInactive ? {} : { isActive: true }),
        },
        include: {
          service: {
            select: {
              id: true,
              name: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          usages: {
            include: {
              appointment: {
                select: {
                  id: true,
                  date: true,
                  startTime: true,
                  customerName: true,
                },
              },
              customer: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
      
      return vouchers;
    } catch (error) {
      console.error('Error fetching vouchers:', error);
      throw new Error('Failed to fetch vouchers');
    }
  },
  
  /**
   * Get a voucher by ID
   * @param merchantId - The ID of the merchant
   * @param voucherId - The ID of the voucher
   * @returns The voucher
   */
  async getVoucherById(merchantId: string, voucherId: string) {
    try {
      const voucher = await prisma.voucher.findUnique({
        where: {
          id: voucherId,
          merchantId,
        },
        include: {
          service: {
            select: {
              id: true,
              name: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          usages: {
            include: {
              appointment: {
                select: {
                  id: true,
                  date: true,
                  startTime: true,
                  customerName: true,
                  service: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
              customer: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
      });
      
      if (!voucher) {
        throw new Error('Voucher not found');
      }
      
      return voucher;
    } catch (error) {
      console.error('Error fetching voucher:', error);
      throw new Error('Failed to fetch voucher');
    }
  },
  
  /**
   * Get a voucher by code
   * @param merchantId - The ID of the merchant
   * @param code - The voucher code
   * @returns The voucher
   */
  async getVoucherByCode(merchantId: string, code: string) {
    try {
      const voucher = await prisma.voucher.findFirst({
        where: {
          merchantId,
          code: {
            equals: code,
            mode: 'insensitive',
          },
        },
        include: {
          service: {
            select: {
              id: true,
              name: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          usages: true,
        },
      });
      
      if (!voucher) {
        throw new Error('Voucher not found');
      }
      
      return voucher;
    } catch (error) {
      console.error('Error fetching voucher by code:', error);
      throw new Error('Failed to fetch voucher by code');
    }
  },
  
  /**
   * Create a voucher
   * @param voucher - The voucher data
   * @returns The created voucher
   */
  async createVoucher(voucher: Omit<Voucher, 'id' | 'usageCount' | 'createdAt' | 'updatedAt'>) {
    try {
      // Check if a voucher with this code already exists
      const existingVoucher = await prisma.voucher.findFirst({
        where: {
          merchantId: voucher.merchantId,
          code: {
            equals: voucher.code,
            mode: 'insensitive',
          },
        },
      });
      
      if (existingVoucher) {
        throw new Error('A voucher with this code already exists');
      }
      
      // If this is for a specific service, check if the service exists
      if (voucher.serviceId) {
        const service = await prisma.service.findUnique({
          where: {
            id: voucher.serviceId,
            merchantId: voucher.merchantId,
          },
        });
        
        if (!service) {
          throw new Error('Service not found');
        }
      }
      
      // If this is for a specific category, check if the category exists
      if (voucher.categoryId) {
        const category = await prisma.serviceCategory.findUnique({
          where: {
            id: voucher.categoryId,
            merchantId: voucher.merchantId,
          },
        });
        
        if (!category) {
          throw new Error('Category not found');
        }
      }
      
      // Create the voucher
      const newVoucher = await prisma.voucher.create({
        data: {
          ...voucher,
          usageCount: 0,
        },
        include: {
          service: {
            select: {
              id: true,
              name: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });
      
      return newVoucher;
    } catch (error) {
      console.error('Error creating voucher:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to create voucher');
    }
  },
  
  /**
   * Update a voucher
   * @param merchantId - The ID of the merchant
   * @param voucherId - The ID of the voucher
   * @param data - The updated voucher data
   * @returns The updated voucher
   */
  async updateVoucher(merchantId: string, voucherId: string, data: Partial<Voucher>) {
    try {
      // Check if the voucher exists
      const existingVoucher = await prisma.voucher.findUnique({
        where: {
          id: voucherId,
          merchantId,
        },
      });
      
      if (!existingVoucher) {
        throw new Error('Voucher not found');
      }
      
      // If code is being updated, check if it's already in use
      if (data.code && data.code !== existingVoucher.code) {
        const codeInUse = await prisma.voucher.findFirst({
          where: {
            merchantId,
            code: {
              equals: data.code,
              mode: 'insensitive',
            },
            id: {
              not: voucherId,
            },
          },
        });
        
        if (codeInUse) {
          throw new Error('A voucher with this code already exists');
        }
      }
      
      // If service is being updated, check if it exists
      if (data.serviceId && data.serviceId !== existingVoucher.serviceId) {
        const service = await prisma.service.findUnique({
          where: {
            id: data.serviceId,
            merchantId,
          },
        });
        
        if (!service) {
          throw new Error('Service not found');
        }
      }
      
      // If category is being updated, check if it exists
      if (data.categoryId && data.categoryId !== existingVoucher.categoryId) {
        const category = await prisma.serviceCategory.findUnique({
          where: {
            id: data.categoryId,
            merchantId,
          },
        });
        
        if (!category) {
          throw new Error('Category not found');
        }
      }
      
      // Update the voucher
      const voucher = await prisma.voucher.update({
        where: {
          id: voucherId,
        },
        data,
        include: {
          service: {
            select: {
              id: true,
              name: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });
      
      return voucher;
    } catch (error) {
      console.error('Error updating voucher:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to update voucher');
    }
  },
  
  /**
   * Delete a voucher
   * @param merchantId - The ID of the merchant
   * @param voucherId - The ID of the voucher
   * @returns True if the voucher was deleted
   */
  async deleteVoucher(merchantId: string, voucherId: string) {
    try {
      // Check if the voucher exists
      const voucher = await prisma.voucher.findUnique({
        where: {
          id: voucherId,
          merchantId,
        },
        include: {
          usages: true,
        },
      });
      
      if (!voucher) {
        throw new Error('Voucher not found');
      }
      
      // If the voucher has been used, don't delete it, just mark it as inactive
      if (voucher.usages.length > 0) {
        await prisma.voucher.update({
          where: {
            id: voucherId,
          },
          data: {
            isActive: false,
          },
        });
        
        return { success: true, message: 'Voucher has been used and cannot be deleted. It has been marked as inactive instead.' };
      }
      
      // Delete the voucher
      await prisma.voucher.delete({
        where: {
          id: voucherId,
        },
      });
      
      return { success: true };
    } catch (error) {
      console.error('Error deleting voucher:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to delete voucher');
    }
  },
  
  /**
   * Validate a voucher for use
   * @param merchantId - The ID of the merchant
   * @param code - The voucher code
   * @param amount - The order amount
   * @param serviceId - The ID of the service (optional)
   * @returns The validated voucher and discount amount
   */
  async validateVoucher(merchantId: string, code: string, amount: number, serviceId?: string) {
    try {
      // Get the voucher
      const voucher = await this.getVoucherByCode(merchantId, code);
      
      // Check if the voucher is active
      if (!voucher.isActive) {
        throw new Error('This voucher is not active');
      }
      
      // Check if the voucher has expired
      const now = new Date();
      if (now < voucher.startDate || now > voucher.endDate) {
        throw new Error('This voucher has expired or is not yet valid');
      }
      
      // Check if the voucher has reached its usage limit
      if (voucher.usageLimit && voucher.usageCount >= voucher.usageLimit) {
        throw new Error('This voucher has reached its usage limit');
      }
      
      // Check if the order meets the minimum spend requirement
      if (voucher.minSpend && amount < voucher.minSpend) {
        throw new Error(`This voucher requires a minimum spend of ${voucher.minSpend}`);
      }
      
      // Check if the voucher is for a specific service
      if (voucher.serviceId && serviceId && voucher.serviceId !== serviceId) {
        throw new Error('This voucher is only valid for a specific service');
      }
      
      // Calculate the discount amount
      let discountAmount = 0;
      
      if (voucher.type === 'percentage') {
        discountAmount = (amount * voucher.value) / 100;
        
        // Apply maximum discount if specified
        if (voucher.maxDiscount && discountAmount > voucher.maxDiscount) {
          discountAmount = voucher.maxDiscount;
        }
      } else if (voucher.type === 'fixed_amount') {
        discountAmount = voucher.value;
        
        // Ensure discount doesn't exceed order amount
        if (discountAmount > amount) {
          discountAmount = amount;
        }
      } else if (voucher.type === 'free_service') {
        // For free service, the discount is the full amount if the service matches
        if (voucher.serviceId === serviceId) {
          discountAmount = amount;
        } else {
          throw new Error('This voucher is only valid for a specific service');
        }
      }
      
      return {
        voucher,
        discountAmount,
      };
    } catch (error) {
      console.error('Error validating voucher:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to validate voucher');
    }
  },
  
  /**
   * Apply a voucher to an appointment
   * @param merchantId - The ID of the merchant
   * @param appointmentId - The ID of the appointment
   * @param code - The voucher code
   * @param customerId - The ID of the customer (optional)
   * @param userId - The ID of the user (optional)
   * @returns The updated appointment and voucher usage
   */
  async applyVoucherToAppointment(
    merchantId: string,
    appointmentId: string,
    code: string,
    customerId?: string,
    userId?: string
  ) {
    try {
      // Get the appointment
      const appointment = await prisma.appointment.findUnique({
        where: {
          id: appointmentId,
          merchantId,
        },
        include: {
          service: true,
        },
      });
      
      if (!appointment) {
        throw new Error('Appointment not found');
      }
      
      // Validate the voucher
      const { voucher, discountAmount } = await this.validateVoucher(
        merchantId,
        code,
        appointment.price,
        appointment.serviceId
      );
      
      // Create a voucher usage record
      const usage = await prisma.voucherUsage.create({
        data: {
          voucherId: voucher.id,
          appointmentId,
          customerId,
          userId,
          discountAmount,
          usedAt: new Date(),
        },
      });
      
      // Update the voucher usage count
      await prisma.voucher.update({
        where: {
          id: voucher.id,
        },
        data: {
          usageCount: {
            increment: 1,
          },
        },
      });
      
      // Update the appointment with the discount
      const updatedAppointment = await prisma.appointment.update({
        where: {
          id: appointmentId,
        },
        data: {
          discountAmount,
          finalPrice: appointment.price - discountAmount,
        },
        include: {
          service: true,
          staff: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });
      
      return {
        appointment: updatedAppointment,
        usage,
        voucher,
      };
    } catch (error) {
      console.error('Error applying voucher to appointment:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to apply voucher to appointment');
    }
  },
  
  /**
   * Generate a gift card
   * @param merchantId - The ID of the merchant
   * @param amount - The gift card amount
   * @param recipientEmail - The recipient's email
   * @param recipientName - The recipient's name
   * @param senderName - The sender's name
   * @param message - A personal message
   * @returns The created gift card
   */
  async generateGiftCard(
    merchantId: string,
    amount: number,
    recipientEmail: string,
    recipientName: string,
    senderName: string,
    message?: string
  ) {
    try {
      // Generate a unique code
      const code = `GIFT-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
      
      // Create the gift card voucher
      const giftCard = await this.createVoucher({
        merchantId,
        code,
        type: 'fixed_amount',
        value: amount,
        startDate: new Date(),
        endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // Valid for 1 year
        isActive: true,
        isGiftCard: true,
        recipientEmail,
        recipientName,
        senderName,
        message,
      });
      
      // In a real application, you would send an email to the recipient here
      
      return giftCard;
    } catch (error) {
      console.error('Error generating gift card:', error);
      throw new Error('Failed to generate gift card');
    }
  },
};
