import { prisma } from '@/lib/prisma';
import { Reservation, Table, TableTimeSlot } from '@/lib/validations/reservationSchema';
import { addMinutes, format, parse, isAfter, isBefore, isEqual } from 'date-fns';

/**
 * Reservation service for managing restaurant reservations
 */
class ReservationService {
  /**
   * Get all reservations for a merchant
   */
  async getReservations(merchantId: string) {
    return prisma.reservation.findMany({
      where: {
        merchantId,
      },
      orderBy: {
        date: 'asc',
      },
    });
  }

  /**
   * Get a reservation by ID
   */
  async getReservationById(merchantId: string, reservationId: string) {
    return prisma.reservation.findFirst({
      where: {
        id: reservationId,
        merchantId,
      },
    });
  }

  /**
   * Create a new reservation
   */
  async createReservation(merchantId: string, reservationData: Omit<Reservation, 'id'>) {
    // Check if the table is available
    const isAvailable = await this.checkTableAvailability(
      merchantId,
      reservationData.tableId,
      reservationData.date,
      reservationData.time,
      reservationData.duration
    );

    if (!isAvailable) {
      throw new Error('Table is not available for the selected time');
    }

    return prisma.reservation.create({
      data: {
        ...reservationData,
        merchantId,
      },
    });
  }

  /**
   * Update a reservation
   */
  async updateReservation(merchantId: string, reservationId: string, reservationData: Partial<Reservation>) {
    // If changing table, date, or time, check availability
    const currentReservation = await this.getReservationById(merchantId, reservationId);

    if (!currentReservation) {
      throw new Error('Reservation not found');
    }

    // Check if table, date, or time is being changed
    if (
      (reservationData.tableId && reservationData.tableId !== currentReservation.tableId) ||
      (reservationData.date && reservationData.date !== currentReservation.date) ||
      (reservationData.time && reservationData.time !== currentReservation.time) ||
      (reservationData.duration && reservationData.duration !== currentReservation.duration)
    ) {
      // Check availability with new data
      const isAvailable = await this.checkTableAvailability(
        merchantId,
        reservationData.tableId || currentReservation.tableId,
        reservationData.date || currentReservation.date,
        reservationData.time || currentReservation.time,
        reservationData.duration || currentReservation.duration,
        reservationId
      );

      if (!isAvailable) {
        throw new Error('Table is not available for the selected time');
      }
    }

    return prisma.reservation.update({
      where: {
        id: reservationId,
      },
      data: reservationData,
    });
  }

  /**
   * Cancel a reservation
   */
  async cancelReservation(merchantId: string, reservationId: string) {
    return prisma.reservation.update({
      where: {
        id: reservationId,
        merchantId,
      },
      data: {
        status: 'cancelled',
        updatedAt: new Date(),
      },
    });
  }

  /**
   * Get reservations with filters
   */
  async getReservationsWithFilters(merchantId: string, filters: {
    status?: string;
    date?: string;
    startDate?: string;
    endDate?: string;
    search?: string;
    tableId?: string;
    page?: number;
    limit?: number;
  }) {
    const {
      status,
      date,
      startDate,
      endDate,
      search,
      tableId,
      page = 1,
      limit = 20
    } = filters;

    const where: any = {
      merchantId,
    };

    // Status filter
    if (status) {
      where.status = status;
    }

    // Date filters
    if (date) {
      where.date = date;
    } else if (startDate && endDate) {
      where.date = {
        gte: startDate,
        lte: endDate,
      };
    } else if (startDate) {
      where.date = {
        gte: startDate,
      };
    } else if (endDate) {
      where.date = {
        lte: endDate,
      };
    }

    // Table filter
    if (tableId) {
      where.tableId = tableId;
    }

    // Search filter
    if (search) {
      where.OR = [
        {
          customerName: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          customerEmail: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          customerPhone: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get total count
    const totalItems = await prisma.reservation.count({ where });

    // Get reservations
    const reservations = await prisma.reservation.findMany({
      where,
      orderBy: [
        { date: 'asc' },
        { time: 'asc' },
      ],
      skip,
      take: limit,
    });

    // Calculate pagination info
    const totalPages = Math.ceil(totalItems / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    return {
      data: reservations,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems,
        itemsPerPage: limit,
        hasNextPage,
        hasPreviousPage,
      },
    };
  }

  /**
   * Delete a reservation
   */
  async deleteReservation(merchantId: string, reservationId: string) {
    return prisma.reservation.delete({
      where: {
        id: reservationId,
        merchantId,
      },
    });
  }

  /**
   * Get all tables for a merchant
   */
  async getTables(merchantId: string) {
    return prisma.table.findMany({
      where: {
        merchantId,
      },
      orderBy: {
        number: 'asc',
      },
    });
  }

  /**
   * Create a new table
   */
  async createTable(merchantId: string, tableData: Omit<Table, 'id'>) {
    return prisma.table.create({
      data: {
        ...tableData,
        merchantId,
      },
    });
  }

  /**
   * Update a table
   */
  async updateTable(merchantId: string, tableId: string, tableData: Partial<Table>) {
    return prisma.table.update({
      where: {
        id: tableId,
        merchantId,
      },
      data: tableData,
    });
  }

  /**
   * Delete a table
   */
  async deleteTable(merchantId: string, tableId: string) {
    // Check if table has any reservations
    const reservations = await prisma.reservation.findMany({
      where: {
        tableId,
        merchantId,
        status: {
          in: ['pending', 'confirmed'],
        },
      },
    });

    if (reservations.length > 0) {
      throw new Error('Cannot delete table with active reservations');
    }

    return prisma.table.delete({
      where: {
        id: tableId,
        merchantId,
      },
    });
  }

  /**
   * Check if a table is available for a specific time
   */
  async checkTableAvailability(
    merchantId: string,
    tableId: string,
    date: string,
    time: string,
    duration: number,
    excludeReservationId?: string
  ): Promise<boolean> {
    // Get the table
    const table = await prisma.table.findFirst({
      where: {
        id: tableId,
        merchantId,
      },
    });

    if (!table) {
      throw new Error('Table not found');
    }

    if (table.status === 'unavailable') {
      return false;
    }

    // Get all reservations for this table on the given date
    const reservations = await prisma.reservation.findMany({
      where: {
        tableId,
        merchantId,
        date,
        status: {
          in: ['pending', 'confirmed'],
        },
        id: {
          not: excludeReservationId,
        },
      },
    });

    // Parse the requested time
    const requestedStartTime = parse(`${date} ${time}`, 'yyyy-MM-dd HH:mm', new Date());
    const requestedEndTime = addMinutes(requestedStartTime, duration);

    // Check if the requested time overlaps with any existing reservation
    for (const reservation of reservations) {
      const reservationStartTime = parse(
        `${reservation.date} ${reservation.time}`,
        'yyyy-MM-dd HH:mm',
        new Date()
      );
      const reservationEndTime = addMinutes(reservationStartTime, reservation.duration);

      // Check for overlap
      if (
        (isAfter(requestedStartTime, reservationStartTime) && isBefore(requestedStartTime, reservationEndTime)) ||
        (isAfter(requestedEndTime, reservationStartTime) && isBefore(requestedEndTime, reservationEndTime)) ||
        (isBefore(requestedStartTime, reservationStartTime) && isAfter(requestedEndTime, reservationEndTime)) ||
        isEqual(requestedStartTime, reservationStartTime)
      ) {
        return false;
      }
    }

    return true;
  }

  /**
   * Get available time slots for a specific date
   */
  async getAvailableTimeSlots(
    merchantId: string,
    date: string,
    partySize: number
  ): Promise<TableTimeSlot[]> {
    // Get all tables for the merchant
    const tables = await this.getTables(merchantId);

    // Get all reservations for the date
    const reservations = await prisma.reservation.findMany({
      where: {
        merchantId,
        date,
        status: {
          in: ['pending', 'confirmed'],
        },
      },
    });

    // Get merchant settings for opening hours
    const merchant = await prisma.merchant.findUnique({
      where: {
        id: merchantId,
      },
      select: {
        settings: true,
      },
    });

    if (!merchant || !merchant.settings) {
      throw new Error('Merchant settings not found');
    }

    // Get the day of the week
    const dayOfWeek = format(new Date(date), 'EEEE');

    // Get opening hours for the day
    const openingHours = merchant.settings.openingHours?.[dayOfWeek];

    if (!openingHours) {
      return [];
    }

    // Generate time slots every 30 minutes from opening to closing
    const timeSlots: TableTimeSlot[] = [];
    const openTime = parse(openingHours.open, 'HH:mm', new Date());
    const closeTime = parse(openingHours.close, 'HH:mm', new Date());

    let currentTime = openTime;
    while (isBefore(currentTime, closeTime)) {
      const timeString = format(currentTime, 'HH:mm');

      // Check each table for availability at this time
      for (const table of tables) {
        if (table.capacity >= partySize && table.status !== 'unavailable') {
          const isAvailable = await this.checkTableAvailability(
            merchantId,
            table.id,
            date,
            timeString,
            60 // Default duration of 1 hour
          );

          if (isAvailable) {
            timeSlots.push({
              date,
              time: timeString,
              available: true,
              tableId: table.id,
              capacity: table.capacity,
            });
          }
        }
      }

      // Move to next 30-minute slot
      currentTime = addMinutes(currentTime, 30);
    }

    return timeSlots;
  }
}

export const reservationService = new ReservationService();
