import { prisma } from '@/lib/prisma';

/**
 * Interface for customer
 */
export interface Customer {
  id?: string;
  merchantId: string;
  userId?: string;
  name: string;
  email: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  notes?: string;
  tags?: string[];
  source?: string;
  status: 'active' | 'inactive' | 'blocked';
  metadata?: Record<string, any>;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Service for handling customers
 */
export const customerService = {
  /**
   * Get customers for a merchant
   * @param merchantId - The ID of the merchant
   * @param limit - The maximum number of customers to return
   * @param offset - The number of customers to skip
   * @returns The customers
   */
  async getCustomers(merchantId: string, limit = 10, offset = 0) {
    try {
      const customers = await prisma.customer.findMany({
        where: {
          merchantId,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          appointments: {
            select: {
              id: true,
              date: true,
              startTime: true,
              status: true,
              service: {
                select: {
                  name: true,
                },
              },
            },
            orderBy: {
              date: 'desc',
            },
            take: 5,
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit,
        skip: offset,
      });
      
      const total = await prisma.customer.count({
        where: {
          merchantId,
        },
      });
      
      return {
        customers,
        total,
      };
    } catch (error) {
      console.error('Error fetching customers:', error);
      throw new Error('Failed to fetch customers');
    }
  },
  
  /**
   * Get a customer by ID
   * @param merchantId - The ID of the merchant
   * @param customerId - The ID of the customer
   * @returns The customer
   */
  async getCustomerById(merchantId: string, customerId: string) {
    try {
      const customer = await prisma.customer.findUnique({
        where: {
          id: customerId,
          merchantId,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          appointments: {
            include: {
              service: {
                select: {
                  name: true,
                },
              },
              staff: {
                select: {
                  name: true,
                },
              },
              payments: {
                select: {
                  id: true,
                  amount: true,
                  status: true,
                  paymentMethod: true,
                  createdAt: true,
                },
              },
            },
            orderBy: {
              date: 'desc',
            },
          },
        },
      });
      
      if (!customer) {
        throw new Error('Customer not found');
      }
      
      return customer;
    } catch (error) {
      console.error('Error fetching customer:', error);
      throw new Error('Failed to fetch customer');
    }
  },
  
  /**
   * Create a customer
   * @param customer - The customer data
   * @returns The created customer
   */
  async createCustomer(customer: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>) {
    try {
      // Check if a customer with this email already exists for this merchant
      const existingCustomer = await prisma.customer.findFirst({
        where: {
          merchantId: customer.merchantId,
          email: customer.email,
        },
      });
      
      if (existingCustomer) {
        throw new Error('A customer with this email already exists');
      }
      
      // Create the customer
      const newCustomer = await prisma.customer.create({
        data: {
          ...customer,
          status: customer.status || 'active',
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });
      
      return newCustomer;
    } catch (error) {
      console.error('Error creating customer:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to create customer');
    }
  },
  
  /**
   * Update a customer
   * @param merchantId - The ID of the merchant
   * @param customerId - The ID of the customer
   * @param data - The updated customer data
   * @returns The updated customer
   */
  async updateCustomer(merchantId: string, customerId: string, data: Partial<Customer>) {
    try {
      // Check if the customer exists
      const existingCustomer = await prisma.customer.findUnique({
        where: {
          id: customerId,
          merchantId,
        },
      });
      
      if (!existingCustomer) {
        throw new Error('Customer not found');
      }
      
      // If email is being updated, check if it's already in use
      if (data.email && data.email !== existingCustomer.email) {
        const emailInUse = await prisma.customer.findFirst({
          where: {
            merchantId,
            email: data.email,
            id: {
              not: customerId,
            },
          },
        });
        
        if (emailInUse) {
          throw new Error('A customer with this email already exists');
        }
      }
      
      // Update the customer
      const customer = await prisma.customer.update({
        where: {
          id: customerId,
        },
        data,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });
      
      return customer;
    } catch (error) {
      console.error('Error updating customer:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to update customer');
    }
  },
  
  /**
   * Delete a customer
   * @param merchantId - The ID of the merchant
   * @param customerId - The ID of the customer
   * @returns True if the customer was deleted
   */
  async deleteCustomer(merchantId: string, customerId: string) {
    try {
      // Check if the customer exists
      const customer = await prisma.customer.findUnique({
        where: {
          id: customerId,
          merchantId,
        },
      });
      
      if (!customer) {
        throw new Error('Customer not found');
      }
      
      // Check if the customer has any appointments
      const appointmentsCount = await prisma.appointment.count({
        where: {
          merchantId,
          customerId,
        },
      });
      
      if (appointmentsCount > 0) {
        // Instead of deleting, mark as inactive
        await prisma.customer.update({
          where: {
            id: customerId,
          },
          data: {
            status: 'inactive',
          },
        });
        
        return { success: true, message: 'Customer marked as inactive due to existing appointments' };
      }
      
      // Delete the customer
      await prisma.customer.delete({
        where: {
          id: customerId,
        },
      });
      
      return { success: true };
    } catch (error) {
      console.error('Error deleting customer:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to delete customer');
    }
  },
  
  /**
   * Get customer appointments
   * @param merchantId - The ID of the merchant
   * @param customerId - The ID of the customer
   * @param limit - The maximum number of appointments to return
   * @param offset - The number of appointments to skip
   * @returns The customer's appointments
   */
  async getCustomerAppointments(merchantId: string, customerId: string, limit = 10, offset = 0) {
    try {
      // Check if the customer exists
      const customer = await prisma.customer.findUnique({
        where: {
          id: customerId,
          merchantId,
        },
      });
      
      if (!customer) {
        throw new Error('Customer not found');
      }
      
      // Get the customer's appointments
      const appointments = await prisma.appointment.findMany({
        where: {
          merchantId,
          customerId,
        },
        include: {
          service: true,
          staff: {
            select: {
              id: true,
              name: true,
            },
          },
          payments: {
            select: {
              id: true,
              amount: true,
              status: true,
              paymentMethod: true,
              createdAt: true,
            },
          },
        },
        orderBy: {
          date: 'desc',
        },
        take: limit,
        skip: offset,
      });
      
      const total = await prisma.appointment.count({
        where: {
          merchantId,
          customerId,
        },
      });
      
      return {
        appointments,
        total,
      };
    } catch (error) {
      console.error('Error fetching customer appointments:', error);
      throw new Error('Failed to fetch customer appointments');
    }
  },
  
  /**
   * Get customer by user ID
   * @param merchantId - The ID of the merchant
   * @param userId - The ID of the user
   * @returns The customer
   */
  async getCustomerByUserId(merchantId: string, userId: string) {
    try {
      const customer = await prisma.customer.findFirst({
        where: {
          merchantId,
          userId,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });
      
      if (!customer) {
        throw new Error('Customer not found');
      }
      
      return customer;
    } catch (error) {
      console.error('Error fetching customer by user ID:', error);
      throw new Error('Failed to fetch customer by user ID');
    }
  },
  
  /**
   * Search for customers
   * @param merchantId - The ID of the merchant
   * @param query - The search query
   * @param limit - The maximum number of customers to return
   * @param offset - The number of customers to skip
   * @returns The customers matching the query
   */
  async searchCustomers(merchantId: string, query: string, limit = 10, offset = 0) {
    try {
      // Normalize the query
      const normalizedQuery = query.trim().toLowerCase();
      
      if (!normalizedQuery) {
        return this.getCustomers(merchantId, limit, offset);
      }
      
      // Search for customers
      const customers = await prisma.customer.findMany({
        where: {
          merchantId,
          OR: [
            { name: { contains: normalizedQuery, mode: 'insensitive' } },
            { email: { contains: normalizedQuery, mode: 'insensitive' } },
            { phone: { contains: normalizedQuery, mode: 'insensitive' } },
            { address: { contains: normalizedQuery, mode: 'insensitive' } },
            { city: { contains: normalizedQuery, mode: 'insensitive' } },
            { state: { contains: normalizedQuery, mode: 'insensitive' } },
            { postalCode: { contains: normalizedQuery, mode: 'insensitive' } },
            { notes: { contains: normalizedQuery, mode: 'insensitive' } },
          ],
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          appointments: {
            select: {
              id: true,
              date: true,
              startTime: true,
              status: true,
              service: {
                select: {
                  name: true,
                },
              },
            },
            orderBy: {
              date: 'desc',
            },
            take: 5,
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit,
        skip: offset,
      });
      
      const total = await prisma.customer.count({
        where: {
          merchantId,
          OR: [
            { name: { contains: normalizedQuery, mode: 'insensitive' } },
            { email: { contains: normalizedQuery, mode: 'insensitive' } },
            { phone: { contains: normalizedQuery, mode: 'insensitive' } },
            { address: { contains: normalizedQuery, mode: 'insensitive' } },
            { city: { contains: normalizedQuery, mode: 'insensitive' } },
            { state: { contains: normalizedQuery, mode: 'insensitive' } },
            { postalCode: { contains: normalizedQuery, mode: 'insensitive' } },
            { notes: { contains: normalizedQuery, mode: 'insensitive' } },
          ],
        },
      });
      
      return {
        customers,
        total,
      };
    } catch (error) {
      console.error('Error searching customers:', error);
      throw new Error('Failed to search customers');
    }
  },
};
