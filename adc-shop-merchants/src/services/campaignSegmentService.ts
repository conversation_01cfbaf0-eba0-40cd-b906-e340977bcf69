import { prisma } from '@/lib/prisma';
import { CampaignSegment, CampaignSegmentFilter } from './campaignService';
import { customerService } from './customerService';
import { Prisma } from '@prisma/client';

/**
 * Service for handling campaign segments
 */
export const campaignSegmentService = {
  /**
   * Create a campaign segment
   * @param segment - The segment data
   * @returns The created segment
   */
  async createSegment(segment: Omit<CampaignSegment, 'id' | 'createdAt' | 'updatedAt'>) {
    try {
      // Create the segment
      const newSegment = await prisma.campaignSegment.create({
        data: {
          merchantId: segment.merchantId,
          name: segment.name,
          description: segment.description,
          filters: segment.filters as any,
        },
      });
      
      return newSegment;
    } catch (error) {
      console.error('Error creating campaign segment:', error);
      throw new Error('Failed to create campaign segment');
    }
  },
  
  /**
   * Get a campaign segment by ID
   * @param merchantId - The ID of the merchant
   * @param segmentId - The ID of the segment
   * @returns The segment
   */
  async getSegmentById(merchantId: string, segmentId: string) {
    try {
      const segment = await prisma.campaignSegment.findUnique({
        where: {
          id: segmentId,
          merchantId,
        },
      });
      
      if (!segment) {
        throw new Error('Segment not found');
      }
      
      return segment;
    } catch (error) {
      console.error('Error fetching campaign segment:', error);
      throw new Error('Failed to fetch campaign segment');
    }
  },
  
  /**
   * Get campaign segments for a merchant
   * @param merchantId - The ID of the merchant
   * @returns The segments
   */
  async getSegments(merchantId: string) {
    try {
      const segments = await prisma.campaignSegment.findMany({
        where: {
          merchantId,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
      
      return segments;
    } catch (error) {
      console.error('Error fetching campaign segments:', error);
      throw new Error('Failed to fetch campaign segments');
    }
  },
  
  /**
   * Update a campaign segment
   * @param merchantId - The ID of the merchant
   * @param segmentId - The ID of the segment
   * @param data - The updated segment data
   * @returns The updated segment
   */
  async updateSegment(
    merchantId: string,
    segmentId: string,
    data: Partial<Omit<CampaignSegment, 'id' | 'merchantId' | 'createdAt' | 'updatedAt'>>
  ) {
    try {
      // Check if the segment exists
      const segment = await prisma.campaignSegment.findUnique({
        where: {
          id: segmentId,
          merchantId,
        },
      });
      
      if (!segment) {
        throw new Error('Segment not found');
      }
      
      // Update the segment
      const updatedSegment = await prisma.campaignSegment.update({
        where: {
          id: segmentId,
        },
        data: {
          name: data.name,
          description: data.description,
          filters: data.filters as any,
        },
      });
      
      return updatedSegment;
    } catch (error) {
      console.error('Error updating campaign segment:', error);
      throw new Error('Failed to update campaign segment');
    }
  },
  
  /**
   * Delete a campaign segment
   * @param merchantId - The ID of the merchant
   * @param segmentId - The ID of the segment
   * @returns True if the segment was deleted
   */
  async deleteSegment(merchantId: string, segmentId: string) {
    try {
      // Check if the segment exists
      const segment = await prisma.campaignSegment.findUnique({
        where: {
          id: segmentId,
          merchantId,
        },
      });
      
      if (!segment) {
        throw new Error('Segment not found');
      }
      
      // Delete the segment
      await prisma.campaignSegment.delete({
        where: {
          id: segmentId,
        },
      });
      
      return true;
    } catch (error) {
      console.error('Error deleting campaign segment:', error);
      throw new Error('Failed to delete campaign segment');
    }
  },
  
  /**
   * Get customers in a segment
   * @param merchantId - The ID of the merchant
   * @param segmentId - The ID of the segment
   * @param limit - The maximum number of customers to return
   * @param offset - The number of customers to skip
   * @returns The customers in the segment
   */
  async getSegmentCustomers(merchantId: string, segmentId: string, limit = 100, offset = 0) {
    try {
      // Get the segment
      const segment = await this.getSegmentById(merchantId, segmentId);
      
      // Build the query based on the segment filters
      const whereClause = this.buildWhereClause(segment.filters as any);
      
      // Get the customers
      const customers = await prisma.customer.findMany({
        where: {
          merchantId,
          ...whereClause,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit,
        skip: offset,
      });
      
      const total = await prisma.customer.count({
        where: {
          merchantId,
          ...whereClause,
        },
      });
      
      return {
        customers,
        total,
      };
    } catch (error) {
      console.error('Error fetching segment customers:', error);
      throw new Error('Failed to fetch segment customers');
    }
  },
  
  /**
   * Get customer emails in a segment
   * @param merchantId - The ID of the merchant
   * @param segmentId - The ID of the segment
   * @returns The customer emails in the segment
   */
  async getSegmentEmails(merchantId: string, segmentId: string) {
    try {
      // Get the segment
      const segment = await this.getSegmentById(merchantId, segmentId);
      
      // Build the query based on the segment filters
      const whereClause = this.buildWhereClause(segment.filters as any);
      
      // Get the customers
      const customers = await prisma.customer.findMany({
        where: {
          merchantId,
          ...whereClause,
          email: {
            not: null,
          },
        },
        select: {
          email: true,
        },
      });
      
      return customers.map(customer => customer.email);
    } catch (error) {
      console.error('Error fetching segment emails:', error);
      throw new Error('Failed to fetch segment emails');
    }
  },
  
  /**
   * Get customer phone numbers in a segment
   * @param merchantId - The ID of the merchant
   * @param segmentId - The ID of the segment
   * @returns The customer phone numbers in the segment
   */
  async getSegmentPhones(merchantId: string, segmentId: string) {
    try {
      // Get the segment
      const segment = await this.getSegmentById(merchantId, segmentId);
      
      // Build the query based on the segment filters
      const whereClause = this.buildWhereClause(segment.filters as any);
      
      // Get the customers
      const customers = await prisma.customer.findMany({
        where: {
          merchantId,
          ...whereClause,
          phone: {
            not: null,
          },
        },
        select: {
          phone: true,
        },
      });
      
      return customers.map(customer => customer.phone).filter(Boolean);
    } catch (error) {
      console.error('Error fetching segment phone numbers:', error);
      throw new Error('Failed to fetch segment phone numbers');
    }
  },
  
  /**
   * Build a Prisma where clause from segment filters
   * @param filters - The segment filters
   * @returns The Prisma where clause
   */
  buildWhereClause(filters: CampaignSegmentFilter[]) {
    if (!filters || !filters.length) {
      return {};
    }
    
    const whereConditions = filters.map(filter => {
      const { field, operator, value } = filter;
      
      switch (operator) {
        case 'equals':
          return { [field]: { equals: value } };
        case 'not_equals':
          return { [field]: { not: value } };
        case 'contains':
          return { [field]: { contains: value, mode: 'insensitive' } };
        case 'not_contains':
          return { [field]: { not: { contains: value, mode: 'insensitive' } } };
        case 'greater_than':
          return { [field]: { gt: value } };
        case 'less_than':
          return { [field]: { lt: value } };
        case 'in':
          return { [field]: { in: Array.isArray(value) ? value : [value] } };
        case 'not_in':
          return { [field]: { notIn: Array.isArray(value) ? value : [value] } };
        default:
          return {};
      }
    });
    
    return { AND: whereConditions };
  },
};
