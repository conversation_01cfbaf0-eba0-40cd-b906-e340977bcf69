import { Shop, Branch } from '@/lib/types/shop';

// Mock shops data
export const mockShops: Shop[] = [
  {
    id: 'shop-1',
    slug: 'thai-delight',
    name: 'Thai Delight',
    description: 'Authentic Thai cuisine in a cozy atmosphere',
    logo: 'https://images.unsplash.com/photo-1559339352-11d035aa65de?q=80&w=1974&auto=format&fit=crop',
    type: 'restaurant',
    ownerId: 'user-1',
    status: 'active',
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
    branches: [],
    settings: {
      cuisineType: 'Thai',
      priceRange: '$$',
      seatingCapacity: 80,
      reservationEnabled: true,
      deliveryEnabled: true,
      takeoutEnabled: true,
      openingHours: {
        monday: { open: '10:00', close: '22:00' },
        tuesday: { open: '10:00', close: '22:00' },
        wednesday: { open: '10:00', close: '22:00' },
        thursday: { open: '10:00', close: '22:00' },
        friday: { open: '10:00', close: '23:00' },
        saturday: { open: '11:00', close: '23:00' },
        sunday: { open: '11:00', close: '22:00' },
      },
    },
  },
  {
    id: 'shop-2',
    slug: 'urban-cafe',
    name: 'Urban Cafe',
    description: 'Modern cafe with specialty coffee and pastries',
    logo: 'https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?q=80&w=1978&auto=format&fit=crop',
    type: 'restaurant',
    ownerId: 'user-1',
    status: 'active',
    createdAt: '2023-02-01T00:00:00Z',
    updatedAt: '2023-02-01T00:00:00Z',
    branches: [],
    settings: {
      cuisineType: 'Cafe',
      priceRange: '$$',
      seatingCapacity: 40,
      reservationEnabled: false,
      deliveryEnabled: true,
      takeoutEnabled: true,
      openingHours: {
        monday: { open: '07:00', close: '20:00' },
        tuesday: { open: '07:00', close: '20:00' },
        wednesday: { open: '07:00', close: '20:00' },
        thursday: { open: '07:00', close: '20:00' },
        friday: { open: '07:00', close: '22:00' },
        saturday: { open: '08:00', close: '22:00' },
        sunday: { open: '08:00', close: '18:00' },
      },
    },
  },
];

// Mock branches data
export const mockBranches: Branch[] = [
  {
    id: 'branch-1',
    slug: 'downtown',
    shopId: 'shop-1',
    name: 'Downtown',
    description: 'Our flagship restaurant in the heart of downtown',
    address: '123 Main Street, Bangkok, Thailand',
    phoneNumber: '+66 2 123 4567',
    email: '<EMAIL>',
    status: 'active',
    isMainBranch: true,
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
    settings: {
      seatingCapacity: 80,
      hasParking: true,
      hasOutdoorSeating: true,
      hasPrivateRooms: true,
    },
  },
  {
    id: 'branch-2',
    slug: 'riverside',
    shopId: 'shop-1',
    name: 'Riverside',
    description: 'Scenic dining with river views',
    address: '456 River Road, Bangkok, Thailand',
    phoneNumber: '+66 2 234 5678',
    email: '<EMAIL>',
    status: 'active',
    isMainBranch: false,
    createdAt: '2023-03-01T00:00:00Z',
    updatedAt: '2023-03-01T00:00:00Z',
    settings: {
      seatingCapacity: 60,
      hasParking: true,
      hasOutdoorSeating: true,
      hasPrivateRooms: false,
    },
  },
  {
    id: 'branch-3',
    slug: 'city-center',
    shopId: 'shop-2',
    name: 'City Center',
    description: 'Cozy cafe in the shopping district',
    address: '789 Shopping Street, Bangkok, Thailand',
    phoneNumber: '+66 2 345 6789',
    email: '<EMAIL>',
    status: 'active',
    isMainBranch: true,
    createdAt: '2023-02-01T00:00:00Z',
    updatedAt: '2023-02-01T00:00:00Z',
    settings: {
      seatingCapacity: 40,
      hasParking: false,
      hasOutdoorSeating: true,
      hasPrivateRooms: false,
    },
  },
  {
    id: 'branch-4',
    slug: 'mall-branch',
    shopId: 'shop-2',
    name: 'Mall Branch',
    description: 'Convenient location in the central mall',
    address: '101 Mall Avenue, Bangkok, Thailand',
    phoneNumber: '+66 2 456 7890',
    email: '<EMAIL>',
    status: 'active',
    isMainBranch: false,
    createdAt: '2023-04-01T00:00:00Z',
    updatedAt: '2023-04-01T00:00:00Z',
    settings: {
      seatingCapacity: 30,
      hasParking: true,
      hasOutdoorSeating: false,
      hasPrivateRooms: false,
    },
  },
];

// Combine shops with their branches
export const mockShopsWithBranches = mockShops.map(shop => ({
  ...shop,
  branches: mockBranches.filter(branch => branch.shopId === shop.id),
}));

// Helper function to get a shop by slug
export const getShopBySlug = (slug: string) => {
  const shop = mockShopsWithBranches.find(shop => shop.slug === slug);
  return shop || null;
};

// Helper function to get a branch by shop slug and branch slug
export const getBranchBySlug = (shopSlug: string, branchSlug: string) => {
  const shop = getShopBySlug(shopSlug);
  if (!shop) return null;

  const branch = shop.branches.find(branch => branch.slug === branchSlug);
  return branch || null;
};

// Helper function to get a branch with its shop
export const getBranchWithShop = (shopSlug: string, branchSlug: string) => {
  const shop = getShopBySlug(shopSlug);
  if (!shop) return null;

  const branch = shop.branches.find(branch => branch.slug === branchSlug);
  if (!branch) return null;

  return {
    branch,
    shop: {
      ...shop,
      branches: [], // Avoid circular references
    },
  };
};
