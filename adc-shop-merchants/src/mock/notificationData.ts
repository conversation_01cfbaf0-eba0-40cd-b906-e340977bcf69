import { addMinutes, subHours, subDays, format } from 'date-fns';

export interface Notification {
  id: string;
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  type: 'order' | 'reservation' | 'review' | 'system' | 'staff';
  link?: string;
  data?: Record<string, any>;
}

// Helper function to generate timestamps
const getTimestamp = (hoursAgo: number) => {
  const date = subHours(new Date(), hoursAgo);
  return date.toISOString();
};

// Generate relative time string
export const getRelativeTime = (timestamp: string) => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
  
  if (diffInMinutes < 1) {
    return 'Just now';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes} ${diffInMinutes === 1 ? 'minute' : 'minutes'} ago`;
  } else if (diffInMinutes < 24 * 60) {
    const hours = Math.floor(diffInMinutes / 60);
    return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
  } else if (diffInMinutes < 7 * 24 * 60) {
    const days = Math.floor(diffInMinutes / (24 * 60));
    return `${days} ${days === 1 ? 'day' : 'days'} ago`;
  } else {
    return format(date, 'MMM d, yyyy');
  }
};

// Mock notifications data
export const mockNotifications: Notification[] = [
  {
    id: '1',
    title: 'New Order Received',
    message: 'Order #1234 has been placed for $78.50',
    timestamp: getTimestamp(0.1),
    isRead: false,
    type: 'order',
    link: '/app/restaurant/orders/1234',
    data: {
      orderId: '1234',
      amount: 78.50,
      items: 4
    }
  },
  {
    id: '2',
    title: 'New Reservation',
    message: 'John Smith made a reservation for 4 people at 7:00 PM today',
    timestamp: getTimestamp(1.5),
    isRead: false,
    type: 'reservation',
    link: '/app/restaurant/tables/reservations/5678',
    data: {
      reservationId: '5678',
      customerName: 'John Smith',
      partySize: 4,
      time: '7:00 PM'
    }
  },
  {
    id: '3',
    title: 'New Review',
    message: 'Emma Thompson left a 5-star review',
    timestamp: getTimestamp(3),
    isRead: true,
    type: 'review',
    link: '/app/restaurant/reviews/9012',
    data: {
      reviewId: '9012',
      customerName: 'Emma Thompson',
      rating: 5
    }
  },
  {
    id: '4',
    title: 'Inventory Alert',
    message: 'Red Wine (Cabernet) is running low. Current stock: 3 bottles',
    timestamp: getTimestamp(5),
    isRead: false,
    type: 'system',
    link: '/app/restaurant/inventory',
    data: {
      itemId: 'wine-cab-123',
      currentStock: 3,
      threshold: 5
    }
  },
  {
    id: '5',
    title: 'Staff Schedule Updated',
    message: 'The schedule for next week has been updated',
    timestamp: getTimestamp(8),
    isRead: true,
    type: 'staff',
    link: '/app/restaurant/staff/schedule',
  },
  {
    id: '6',
    title: 'Payment Processed',
    message: 'Payment for Order #1122 has been successfully processed',
    timestamp: getTimestamp(12),
    isRead: true,
    type: 'order',
    link: '/app/restaurant/orders/1122',
    data: {
      orderId: '1122',
      amount: 145.75
    }
  },
  {
    id: '7',
    title: 'Reservation Canceled',
    message: 'Reservation #4455 for Michael Brown has been canceled',
    timestamp: getTimestamp(24),
    isRead: true,
    type: 'reservation',
    link: '/app/restaurant/tables/reservations/4455',
    data: {
      reservationId: '4455',
      customerName: 'Michael Brown'
    }
  },
  {
    id: '8',
    title: 'New Review',
    message: 'Sarah Johnson left a 4-star review',
    timestamp: getTimestamp(36),
    isRead: true,
    type: 'review',
    link: '/app/restaurant/reviews/7788',
    data: {
      reviewId: '7788',
      customerName: 'Sarah Johnson',
      rating: 4
    }
  },
  {
    id: '9',
    title: 'System Update',
    message: 'The system will undergo maintenance tonight at 2:00 AM',
    timestamp: getTimestamp(48),
    isRead: true,
    type: 'system',
    link: '/app/restaurant/settings/system',
  },
  {
    id: '10',
    title: 'Staff Request',
    message: 'David Wilson requested time off for next Friday',
    timestamp: getTimestamp(72),
    isRead: true,
    type: 'staff',
    link: '/app/restaurant/staff/requests',
    data: {
      staffId: 'staff-123',
      staffName: 'David Wilson',
      requestType: 'time-off',
      date: format(addMinutes(subDays(new Date(), 3), 45), 'yyyy-MM-dd')
    }
  },
];

export default mockNotifications;
