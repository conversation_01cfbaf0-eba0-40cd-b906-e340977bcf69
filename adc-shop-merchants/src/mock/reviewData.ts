import { Review } from '@/lib/redux/api/endpoints/restaurant/restaurantApi';
import { addDays, subDays, format } from 'date-fns';

// Generate a date string in ISO format
const getDateString = (daysFromNow: number) => {
  const date = daysFromNow >= 0
    ? addDays(new Date(), daysFromNow)
    : subDays(new Date(), Math.abs(daysFromNow));
  return format(date, 'yyyy-MM-dd');
};

// <PERSON><PERSON> reviews data
export const mockReviews: Review[] = [
  {
    id: '1',
    merchantId: 'current-merchant-id',
    reviewer: {
      id: 'user1',
      name: '<PERSON>',
      email: '<EMAIL>',
      avatar: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDaJUYYfPWH5k7uFs5aCBY5XAUyDh3hlLyRmXK-0W78ZX40eO4jclUIEzGKCQB58pn20ZhnBXDM5CxCa0QCKBBLixsKj1B-DUAljhQwuTLR2B5ZmiF3TP2mC0AiHg_RZJ_v2yKxEAP1ZTdJ_YGwedQIWtH1ydRapc_jvfEnkyojTjOfqPTofeJcnBQ48FPw-gRuTtoyQuT6Tjfauk5dcCEJ760e4vr9EPM4Iv8Qx4ajS_97NyJxsM4agn6pqRy26-DAzQIBguszqpx6',
    },
    rating: 4.5,
    date: getDateString(-2),
    content: "The ambiance was lovely, and the service was excellent. I particularly enjoyed the pasta dish, which was cooked to perfection. Will definitely be back!",
    reviewedItems: [
      {
        id: 'item1',
        type: 'menu_item',
        name: 'Pasta Carbonara',
      },
      {
        id: 'item2',
        type: 'service',
        name: 'Service',
      },
      {
        id: 'item3',
        type: 'ambiance',
        name: 'Ambiance',
      },
    ],
    status: 'published',
  },
  {
    id: '2',
    merchantId: 'current-merchant-id',
    reviewer: {
      id: 'user2',
      name: 'James Wilson',
      email: '<EMAIL>',
    },
    rating: 5,
    date: getDateString(-5),
    content: "Absolutely fantastic experience! The food was delicious, especially the steak which was cooked to perfection. The staff was attentive and friendly. The atmosphere was cozy and elegant. Will definitely return!",
    reviewedItems: [
      {
        id: 'item4',
        type: 'menu_item',
        name: 'Filet Mignon',
      },
      {
        id: 'item5',
        type: 'service',
        name: 'Service',
      },
    ],
    status: 'published',
    response: "Thank you for your wonderful review, James! We're thrilled to hear you enjoyed your dining experience with us. Our chef will be delighted to know you loved the steak. We look forward to welcoming you back soon!",
  },
  {
    id: '3',
    merchantId: 'current-merchant-id',
    reviewer: {
      id: 'user3',
      name: 'Emma Thompson',
      email: '<EMAIL>',
      avatar: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCxJXGDRNWSmAMcDTIQUrKT6Fgzs9jb6Ifo5Plcrk2rYIc2aX8lpKfzJ-yoT9WW78nhIqhJTY30PT7xYHi2iIL1_ljkeITv1E-R78_0vuNDzkMkJZcYehxP39CP3wPqQNFrrbBwW46EN9eSzpoY6eb3Scw6tLoqDiLBhxFHIHsY8cwHGG67VGmPxD_587BcUqXrNbNc-sdXAwWxaS42j1EUltnUsAoRxR-ckTDGVLeQDSOrYGrrJiWE4cXizZlrOPM-S9v9IaOcYfqW',
    },
    rating: 3,
    date: getDateString(-10),
    content: "The food was good but the service was a bit slow. Had to wait almost 30 minutes for our main course. The dessert was amazing though!",
    reviewedItems: [
      {
        id: 'item6',
        type: 'menu_item',
        name: 'Chocolate Lava Cake',
      },
      {
        id: 'item7',
        type: 'service',
        name: 'Service',
      },
    ],
    status: 'published',
    response: "Thank you for your feedback, Emma. We apologize for the slow service during your visit. We're working on improving our kitchen efficiency. We're glad you enjoyed the dessert and hope you'll give us another chance to provide a better experience.",
  },
  {
    id: '4',
    merchantId: 'current-merchant-id',
    reviewer: {
      id: 'user4',
      name: 'Michael Brown',
      email: '<EMAIL>',
    },
    rating: 2,
    date: getDateString(-15),
    content: "Disappointing experience. The food was cold when served and overpriced for the quality. The waiter seemed disinterested and barely checked on us.",
    reviewedItems: [
      {
        id: 'item8',
        type: 'menu_item',
        name: 'Seafood Platter',
      },
      {
        id: 'item9',
        type: 'service',
        name: 'Service',
      },
    ],
    status: 'flagged',
  },
  {
    id: '5',
    merchantId: 'current-merchant-id',
    reviewer: {
      id: 'user5',
      name: 'Olivia Davis',
      email: '<EMAIL>',
      avatar: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAaOf-uLDLFdmM7k5sgNL85tdu1XJeYVbxZ0Vs6nTrpxqoRdX6fFJmgNv7xC7man1vVyo8Jgiz8wOuEKF7F4jCpHy6ALMT9t6Z6gF-CtShu7ZSgb49wpD172RKDdLrTmQXM7kgYzBsXPy8kWqgNRIhraorA86YuGCAv73IfrzLAt-rn6lqpEHoh5FocvmbYsQLgw_Y75-MsM-m1_Vq7fPSIYEjjrNCRODfp590hTUACJkG0F77t8zo1MFHzFQO25stOeUdLdWlHRm9c',
    },
    rating: 5,
    date: getDateString(-20),
    content: "Incredible dining experience! The chef's special was out of this world. The staff went above and beyond to make our anniversary dinner special. The wine pairing recommendation was perfect.",
    reviewedItems: [
      {
        id: 'item10',
        type: 'menu_item',
        name: 'Chef\'s Special',
      },
      {
        id: 'item11',
        type: 'service',
        name: 'Service',
      },
      {
        id: 'item12',
        type: 'ambiance',
        name: 'Ambiance',
      },
    ],
    status: 'published',
    response: "Thank you for choosing us to celebrate your special occasion, Olivia! We're delighted to hear that you enjoyed the chef's special and our wine recommendations. We strive to create memorable experiences for our guests, and we're thrilled we could make your anniversary dinner special. We hope to welcome you back soon!",
  },
  {
    id: '6',
    merchantId: 'current-merchant-id',
    reviewer: {
      id: 'user6',
      name: 'Daniel Miller',
      email: '<EMAIL>',
    },
    rating: 4,
    date: getDateString(-25),
    content: "Great food and atmosphere. The cocktails were creative and delicious. Only giving 4 stars because the restaurant was a bit too noisy for a business dinner.",
    reviewedItems: [
      {
        id: 'item13',
        type: 'menu_item',
        name: 'Signature Cocktails',
      },
      {
        id: 'item14',
        type: 'ambiance',
        name: 'Ambiance',
      },
    ],
    status: 'published',
  },
  {
    id: '7',
    merchantId: 'current-merchant-id',
    reviewer: {
      id: 'user7',
      name: 'Sophia Martinez',
      email: '<EMAIL>',
      avatar: 'https://lh3.googleusercontent.com/aida-public/AB6AXuD7d5Znk1l8_psvU22vHDROcr1DP66W1nu5Kj7CeslrrJhAj8sPkaeOjerGbVUAm7HhPyAa5lW7zhylwEH3059eKWJ0MyUJ1KV196sXg4I11yxV95t6raA70uHmDp1Lds0Xd75RAhg9W37q7Ym7nqugAc5e__XZKSSidy0Z8lENBMDr5c_jTL0Z-pLnlXU6qLoGrtsTu-Cc2S4s7p1_inx8dD2FP0RwTdpHBRd4uPzI9FLoSMT8EEXYKi69u_LY_dqu-vpyv6Qm4y2c',
    },
    rating: 1,
    date: getDateString(-30),
    content: "Terrible experience. Found a hair in my soup and the manager was unapologetic. Will never return.",
    reviewedItems: [
      {
        id: 'item15',
        type: 'menu_item',
        name: 'Tomato Soup',
      },
      {
        id: 'item16',
        type: 'service',
        name: 'Service',
      },
    ],
    status: 'archived',
  },
];

export default mockReviews;
