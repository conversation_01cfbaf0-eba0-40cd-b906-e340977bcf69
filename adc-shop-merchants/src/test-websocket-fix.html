<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Infinite Loop Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .counter {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .metric-label {
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 WebSocket Infinite Loop Fix Test</h1>
        <p>This test simulates the React useWebSocket hook behavior to verify the infinite loop fix.</p>

        <div class="metrics">
            <div class="metric">
                <div class="metric-value" id="connectionCount">0</div>
                <div class="metric-label">Connection Attempts</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="reconnectCount">0</div>
                <div class="metric-label">Reconnections</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="messageCount">0</div>
                <div class="metric-label">Messages Received</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="errorCount">0</div>
                <div class="metric-label">Errors</div>
            </div>
        </div>

        <div class="status info" id="status">
            Ready to test WebSocket connection stability
        </div>

        <div>
            <button onclick="startTest()" id="startBtn">Start Test</button>
            <button onclick="stopTest()" id="stopBtn" disabled>Stop Test</button>
            <button onclick="clearLog()" id="clearBtn">Clear Log</button>
            <button onclick="simulateReactRerender()" id="rerenderBtn">Simulate React Re-render</button>
        </div>

        <div class="log" id="log"></div>

        <div style="margin-top: 20px;">
            <h3>Test Scenarios:</h3>
            <ul>
                <li><strong>✅ Fixed Behavior:</strong> Connection attempts should be stable, no infinite loops</li>
                <li><strong>❌ Broken Behavior:</strong> Rapid connection attempts, infinite reconnection loops</li>
                <li><strong>🔄 Re-render Test:</strong> Simulates React component re-renders that previously caused loops</li>
            </ul>
        </div>
    </div>

    <script>
        let ws = null;
        let connectionCount = 0;
        let reconnectCount = 0;
        let messageCount = 0;
        let errorCount = 0;
        let isTestRunning = false;
        let reconnectTimer = null;
        let connectionAttempts = [];

        // Simulate the fixed useWebSocket hook behavior
        class WebSocketManager {
            constructor() {
                this.ws = null;
                this.isConnected = false;
                this.isConnecting = false;
                this.mounted = true;
                this.reconnectAttempts = 0;
                this.maxReconnectAttempts = 5;
                this.reconnectInterval = 3000;
                this.lastConnectionTime = 0;
            }

            connect() {
                // Prevent rapid reconnections (simulating the fix)
                const now = Date.now();
                if (now - this.lastConnectionTime < 1000) {
                    this.log('⚠️ Prevented rapid reconnection attempt', 'warning');
                    return;
                }

                if (this.ws?.readyState === WebSocket.OPEN) {
                    this.log('✅ Already connected, skipping', 'info');
                    return;
                }

                if (this.isConnecting) {
                    this.log('⏳ Already connecting, skipping', 'info');
                    return;
                }

                this.lastConnectionTime = now;
                this.isConnecting = true;
                connectionCount++;
                this.updateMetrics();

                this.log(`🔌 Attempting connection #${connectionCount}`, 'info');

                try {
                    // Use a test WebSocket server or simulate connection
                    this.ws = new WebSocket('ws://localhost:8200/ws');

                    this.ws.onopen = () => {
                        this.log('✅ WebSocket connected successfully', 'success');
                        this.isConnected = true;
                        this.isConnecting = false;
                        this.reconnectAttempts = 0;
                        this.updateStatus('Connected', 'success');
                    };

                    this.ws.onmessage = (event) => {
                        messageCount++;
                        this.updateMetrics();
                        this.log(`📨 Message received: ${event.data}`, 'info');
                    };

                    this.ws.onclose = (event) => {
                        this.log(`🔌 Connection closed: ${event.code} - ${event.reason}`, 'warning');
                        this.isConnected = false;
                        this.isConnecting = false;
                        this.ws = null;

                        // Only reconnect if not manually closed and within limits
                        if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts && this.mounted) {
                            this.reconnectAttempts++;
                            reconnectCount++;
                            this.updateMetrics();
                            
                            this.log(`🔄 Scheduling reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}`, 'warning');
                            
                            setTimeout(() => {
                                if (this.mounted && !this.isConnected) {
                                    this.connect();
                                }
                            }, this.reconnectInterval);
                        }
                    };

                    this.ws.onerror = (error) => {
                        errorCount++;
                        this.updateMetrics();
                        this.log(`❌ WebSocket error: ${error}`, 'error');
                        this.isConnecting = false;
                    };

                } catch (error) {
                    errorCount++;
                    this.updateMetrics();
                    this.log(`❌ Failed to create WebSocket: ${error.message}`, 'error');
                    this.isConnecting = false;
                }
            }

            disconnect() {
                this.mounted = false;
                if (this.ws) {
                    this.ws.close(1000, 'Manual disconnect');
                    this.ws = null;
                }
                this.isConnected = false;
                this.isConnecting = false;
                this.reconnectAttempts = 0;
                this.updateStatus('Disconnected', 'error');
                this.log('🔌 Manually disconnected', 'info');
            }

            // Simulate React component re-render that previously caused infinite loops
            simulateRerender() {
                this.log('🔄 Simulating React re-render (this should NOT cause reconnection)', 'info');
                // In the broken version, this would trigger a new connection
                // In the fixed version, this should do nothing if already connected
                if (!this.isConnected && !this.isConnecting) {
                    this.log('📝 Re-render triggered connection (expected when disconnected)', 'info');
                    this.connect();
                } else {
                    this.log('✅ Re-render did NOT trigger unnecessary reconnection (GOOD!)', 'success');
                }
            }

            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const logElement = document.getElementById('log');
                const logEntry = document.createElement('div');
                logEntry.innerHTML = `[${timestamp}] ${message}`;
                logEntry.className = type;
                logElement.appendChild(logEntry);
                logElement.scrollTop = logElement.scrollHeight;
            }

            updateStatus(message, type) {
                const statusElement = document.getElementById('status');
                statusElement.textContent = message;
                statusElement.className = `status ${type}`;
            }

            updateMetrics() {
                document.getElementById('connectionCount').textContent = connectionCount;
                document.getElementById('reconnectCount').textContent = reconnectCount;
                document.getElementById('messageCount').textContent = messageCount;
                document.getElementById('errorCount').textContent = errorCount;
            }
        }

        const wsManager = new WebSocketManager();

        function startTest() {
            isTestRunning = true;
            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            
            wsManager.log('🚀 Starting WebSocket stability test', 'info');
            wsManager.updateStatus('Testing...', 'info');
            wsManager.connect();
        }

        function stopTest() {
            isTestRunning = false;
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            
            wsManager.disconnect();
            wsManager.log('🛑 Test stopped', 'warning');
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            connectionCount = 0;
            reconnectCount = 0;
            messageCount = 0;
            errorCount = 0;
            wsManager.updateMetrics();
            wsManager.log('🧹 Log cleared', 'info');
        }

        function simulateReactRerender() {
            wsManager.simulateRerender();
        }

        // Initialize
        wsManager.updateMetrics();
        wsManager.log('🔧 WebSocket Infinite Loop Fix Test initialized', 'success');
        wsManager.log('💡 This test verifies that WebSocket connections remain stable during React re-renders', 'info');
    </script>
</body>
</html>
