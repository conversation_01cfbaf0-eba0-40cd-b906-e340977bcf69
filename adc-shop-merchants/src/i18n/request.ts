import { getRequestConfig } from 'next-intl/server';
import { hasLocale } from 'next-intl';
import { routing } from './routing';

export default getRequestConfig(async ({ requestLocale }) => {
  // Typically corresponds to the `[locale]` segment
  const requested = await requestLocale;
  const locale = hasLocale(routing.locales, requested)
    ? requested
    : routing.defaultLocale;

  try {
    // Try to load messages from the messages directory
    const messages = (await import(`../../messages/${locale}/common.json`)).default;
    return {
      locale,
      messages,
      timeZone: 'Asia/Bangkok',
      now: new Date()
    };
  } catch (error) {
    console.error(`Failed to load messages for locale ${locale}:`, error);

    // Fallback to default locale if the requested locale's messages can't be loaded
    if (locale !== routing.defaultLocale) {
      try {
        const defaultMessages = (await import(`../../messages/${routing.defaultLocale}/common.json`)).default;
        return {
          locale,
          messages: defaultMessages,
          timeZone: 'Asia/Bangkok',
          now: new Date()
        };
      } catch (fallbackError) {
        console.error(`Failed to load fallback messages:`, fallbackError);
      }
    }

    // Return empty messages as a last resort
    return {
      locale,
      messages: {},
      timeZone: 'Asia/Bangkok',
      now: new Date()
    };
  }
});