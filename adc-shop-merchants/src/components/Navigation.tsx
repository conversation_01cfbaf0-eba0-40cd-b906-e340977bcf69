'use client';

import { useTranslations } from 'next-intl';
import { Link } from '@/navigation';
import LanguageSwitcher from './LanguageSwitcher';

export default function Navigation() {
  const t = useTranslations('navigation');

  return (
    <nav className="bg-gray-800 text-white p-4">
      <div className="container mx-auto flex justify-between items-center">
        <div className="flex space-x-4">
          <Link href="/" className="hover:text-gray-300">
            {t('home')}
          </Link>
          <Link href="/dashboard" className="hover:text-gray-300">
            {t('dashboard')}
          </Link>
          <Link href="/products" className="hover:text-gray-300">
            {t('products')}
          </Link>
          <Link href="/orders" className="hover:text-gray-300">
            {t('orders')}
          </Link>
        </div>
        <LanguageSwitcher />
      </div>
    </nav>
  );
}
