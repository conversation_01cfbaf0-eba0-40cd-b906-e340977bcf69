'use client';

import { useState } from 'react';
import { useF<PERSON>, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Table, tableSchema } from '@/lib/validations/reservationSchema';
import {
  useGetTablesQuery,
  useCreateTableMutation,
  useUpdateTableMutation,
  useDeleteTableMutation
} from '@/lib/redux/api/endpoints/restaurant/restaurantApi';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { TableDeleteDialog } from '@/components/ui/delete-confirmation-dialog';

interface TableManagementProps {
  merchantId: string;
}

export default function TableManagement({ merchantId }: TableManagementProps) {
  const { data: tables, isLoading, refetch } = useGetTablesQuery(merchantId);
  const [createTable, { isLoading: isCreating }] = useCreateTableMutation();
  const [updateTable, { isLoading: isUpdating }] = useUpdateTableMutation();
  const [deleteTable, { isLoading: isDeleting }] = useDeleteTableMutation();

  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [tableToDelete, setTableToDelete] = useState<Table | null>(null);

  // Set up react-hook-form with zod validation for adding a table
  const {
    register: registerAdd,
    handleSubmit: handleSubmitAdd,
    formState: { errors: errorsAdd },
    reset: resetAdd
  } = useForm<Table>({
    resolver: zodResolver(tableSchema),
    defaultValues: {
      number: 1,
      capacity: 2,
      status: 'available',
      location: '',
    }
  });

  // Set up react-hook-form with zod validation for editing a table
  const {
    register: registerEdit,
    handleSubmit: handleSubmitEdit,
    formState: { errors: errorsEdit },
    reset: resetEdit
  } = useForm<Table>({
    resolver: zodResolver(tableSchema),
    defaultValues: {
      number: 1,
      capacity: 2,
      status: 'available',
      location: '',
    }
  });

  // Handle adding a new table
  const onAddTable: SubmitHandler<Table> = async (data) => {
    try {
      await createTable({
        merchantId,
        ...data,
      }).unwrap();

      toast.success('Table added successfully');
      setIsAddDialogOpen(false);
      resetAdd();
      refetch();
    } catch (err) {
      console.error('Error adding table:', err);
      toast.error('Failed to add table. Please try again.');
    }
  };

  // Handle editing a table
  const onEditTable: SubmitHandler<Table> = async (data) => {
    if (!selectedTable?.id) return;

    try {
      await updateTable({
        merchantId,
        tableId: selectedTable.id,
        data,
      }).unwrap();

      toast.success('Table updated successfully');
      setIsEditDialogOpen(false);
      setSelectedTable(null);
      refetch();
    } catch (err) {
      console.error('Error updating table:', err);
      toast.error('Failed to update table. Please try again.');
    }
  };

  // Handle opening delete dialog
  const handleDeleteTable = (table: Table) => {
    setTableToDelete(table);
    setIsDeleteDialogOpen(true);
  };

  // Handle confirming delete
  const confirmDeleteTable = async () => {
    if (!tableToDelete?.id) return;

    try {
      await deleteTable({
        merchantId,
        tableId: tableToDelete.id,
      }).unwrap();

      toast.success('Table deleted successfully');
      setIsDeleteDialogOpen(false);
      setTableToDelete(null);
      refetch();
    } catch (err) {
      console.error('Error deleting table:', err);
      toast.error('Failed to delete table. Please try again.');
    }
  };

  // Open edit dialog and set selected table
  const openEditDialog = (table: Table) => {
    setSelectedTable(table);
    resetEdit(table);
    setIsEditDialogOpen(true);
  };

  if (isLoading) {
    return <div className="p-4">Loading tables...</div>;
  }

  return (
    <div className="bg-white rounded-lg shadow-md">
      <div className="p-4 border-b flex justify-between items-center">
        <h2 className="text-lg font-semibold">Table Management</h2>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="default">Add Table</Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Table</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmitAdd(onAddTable)} className="space-y-4">
              <div>
                <label htmlFor="number" className="block text-sm font-medium text-gray-700 mb-1">
                  Table Number *
                </label>
                <input
                  id="number"
                  type="number"
                  min="1"
                  {...registerAdd('number', { valueAsNumber: true })}
                  className={`w-full p-2 border rounded-md ${errorsAdd.number ? 'border-red-500' : ''}`}
                />
                {errorsAdd.number && (
                  <p className="mt-1 text-sm text-red-600">{errorsAdd.number.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="capacity" className="block text-sm font-medium text-gray-700 mb-1">
                  Capacity *
                </label>
                <input
                  id="capacity"
                  type="number"
                  min="1"
                  {...registerAdd('capacity', { valueAsNumber: true })}
                  className={`w-full p-2 border rounded-md ${errorsAdd.capacity ? 'border-red-500' : ''}`}
                />
                {errorsAdd.capacity && (
                  <p className="mt-1 text-sm text-red-600">{errorsAdd.capacity.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                  Status *
                </label>
                <select
                  id="status"
                  {...registerAdd('status')}
                  className={`w-full p-2 border rounded-md ${errorsAdd.status ? 'border-red-500' : ''}`}
                >
                  <option value="available">Available</option>
                  <option value="occupied">Occupied</option>
                  <option value="reserved">Reserved</option>
                  <option value="unavailable">Unavailable</option>
                </select>
                {errorsAdd.status && (
                  <p className="mt-1 text-sm text-red-600">{errorsAdd.status.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-1">
                  Location
                </label>
                <input
                  id="location"
                  type="text"
                  {...registerAdd('location')}
                  className={`w-full p-2 border rounded-md ${errorsAdd.location ? 'border-red-500' : ''}`}
                  placeholder="e.g., Window, Patio, Main Floor"
                />
                {errorsAdd.location && (
                  <p className="mt-1 text-sm text-red-600">{errorsAdd.location.message}</p>
                )}
              </div>

              <div className="flex justify-end space-x-2">
                <DialogClose asChild>
                  <Button variant="outline" type="button">Cancel</Button>
                </DialogClose>
                <Button type="submit" disabled={isCreating}>
                  {isCreating ? 'Adding...' : 'Add Table'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="p-4">
        {tables && tables.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Table Number
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Capacity
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Location
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-card divide-y divide-border">
                {tables.map((table) => (
                  <tr key={table.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-foreground">
                      {table.number}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-foreground">
                      {table.capacity}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                        ${table.status === 'available' ? 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300' :
                          table.status === 'occupied' ? 'bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300' :
                          table.status === 'reserved' ? 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300' :
                          'bg-muted text-muted-foreground border-border'}`}>
                        {table.status.charAt(0).toUpperCase() + table.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {table.location || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => openEditDialog(table)}
                        className="text-indigo-600 hover:text-indigo-900 mr-4"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleDeleteTable(table)}
                        className="text-red-600 hover:text-red-900"
                        disabled={isDeleting}
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <p>No tables found. Add your first table to get started.</p>
          </div>
        )}
      </div>

      {/* Edit Table Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Table</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmitEdit(onEditTable)} className="space-y-4">
            <div>
              <label htmlFor="edit-number" className="block text-sm font-medium text-gray-700 mb-1">
                Table Number *
              </label>
              <input
                id="edit-number"
                type="number"
                min="1"
                {...registerEdit('number', { valueAsNumber: true })}
                className={`w-full p-2 border rounded-md ${errorsEdit.number ? 'border-red-500' : ''}`}
              />
              {errorsEdit.number && (
                <p className="mt-1 text-sm text-red-600">{errorsEdit.number.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="edit-capacity" className="block text-sm font-medium text-gray-700 mb-1">
                Capacity *
              </label>
              <input
                id="edit-capacity"
                type="number"
                min="1"
                {...registerEdit('capacity', { valueAsNumber: true })}
                className={`w-full p-2 border rounded-md ${errorsEdit.capacity ? 'border-red-500' : ''}`}
              />
              {errorsEdit.capacity && (
                <p className="mt-1 text-sm text-red-600">{errorsEdit.capacity.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="edit-status" className="block text-sm font-medium text-gray-700 mb-1">
                Status *
              </label>
              <select
                id="edit-status"
                {...registerEdit('status')}
                className={`w-full p-2 border rounded-md ${errorsEdit.status ? 'border-red-500' : ''}`}
              >
                <option value="available">Available</option>
                <option value="occupied">Occupied</option>
                <option value="reserved">Reserved</option>
                <option value="unavailable">Unavailable</option>
              </select>
              {errorsEdit.status && (
                <p className="mt-1 text-sm text-red-600">{errorsEdit.status.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="edit-location" className="block text-sm font-medium text-gray-700 mb-1">
                Location
              </label>
              <input
                id="edit-location"
                type="text"
                {...registerEdit('location')}
                className={`w-full p-2 border rounded-md ${errorsEdit.location ? 'border-red-500' : ''}`}
                placeholder="e.g., Window, Patio, Main Floor"
              />
              {errorsEdit.location && (
                <p className="mt-1 text-sm text-red-600">{errorsEdit.location.message}</p>
              )}
            </div>

            <div className="flex justify-end space-x-2">
              <DialogClose asChild>
                <Button variant="outline" type="button">Cancel</Button>
              </DialogClose>
              <Button type="submit" disabled={isUpdating}>
                {isUpdating ? 'Saving...' : 'Save Changes'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <TableDeleteDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={confirmDeleteTable}
        tableName={tableToDelete ? `Table ${tableToDelete.number}` : undefined}
        isLoading={isDeleting}
      />
    </div>
  );
}
