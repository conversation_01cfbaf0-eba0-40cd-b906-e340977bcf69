'use client';

import { useState } from 'react';
import { useF<PERSON>, SubmitHandler, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Service, serviceSchema } from '@/lib/validations/serviceSchema';
import {
  useCreateServiceMutation,
  useUpdateServiceMutation,
  useGetStaffQuery
} from '@/lib/redux/api/endpoints/serviceApi';

interface ServiceFormProps {
  merchantId: string;
  initialData?: Service & { id?: string };
  onSuccess?: (data: any) => void;
  mode: 'create' | 'edit';
}

export default function ServiceForm({
  merchantId,
  initialData,
  onSuccess,
  mode
}: ServiceFormProps) {
  const [createService, { isLoading: isCreating }] = useCreateServiceMutation();
  const [updateService, { isLoading: isUpdating }] = useUpdateServiceMutation();
  const { data: staff } = useGetStaffQuery(merchantId);

  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const isLoading = isCreating || isUpdating;

  // Set up react-hook-form with zod validation
  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isDirty }
  } = useForm<Service>({
    resolver: zodResolver(serviceSchema),
    defaultValues: initialData || {
      name: '',
      description: '',
      price: 0,
      duration: 60, // Default to 60 minutes
      category: '',
      image: '',
      available: true,
      maxCapacity: 1,
      requiresStaff: true,
      staffIds: [],
      preparationTime: 0,
      cleanupTime: 0,
    }
  });

  // Set up field array for staff IDs
  const {
    fields: staffFields,
    append: appendStaff,
    remove: removeStaff
  } = useFieldArray({
    control,
    name: 'staffIds',
  });

  // Form submission handler
  const onSubmit: SubmitHandler<Service> = async (data) => {
    try {
      let result;

      if (mode === 'create') {
        // Create new service
        result = await createService({
          merchantId,
          ...data,
        }).unwrap();

        setSuccess('Service created successfully');
      } else {
        // Update existing service
        if (!initialData?.id) {
          throw new Error('Service ID is required for updates');
        }

        result = await updateService({
          merchantId,
          serviceId: initialData.id,
          data,
        }).unwrap();

        setSuccess('Service updated successfully');
      }

      setError(null);

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess(result);
      }

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      setError(`Failed to ${mode} service. Please try again.`);
      setSuccess(null);
      console.error(`Error ${mode}ing service:`, err);
    }
  };

  return (
    <div className="bg-card p-6 rounded-lg shadow-sm border border-border">
      <h2 className="text-lg font-semibold mb-4 text-foreground">
        {mode === 'create' ? 'Add Service' : 'Edit Service'}
      </h2>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6 dark:bg-red-950 dark:border-red-800">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-red-700 dark:text-red-300">❌</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-6 dark:bg-green-950 dark:border-green-800">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-green-700 dark:text-green-300">✓</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700 dark:text-green-300">{success}</p>
            </div>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-foreground mb-1">
              Service Name *
            </label>
            <input
              id="name"
              {...register('name')}
              className={`w-full p-2 border border-border rounded-md bg-background text-foreground ${errors.name ? 'border-destructive' : ''}`}
            />
            {errors.name && (
              <p className="mt-1 text-sm text-destructive">{errors.name.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="category" className="block text-sm font-medium text-foreground mb-1">
              Category *
            </label>
            <input
              id="category"
              {...register('category')}
              className={`w-full p-2 border border-border rounded-md bg-background text-foreground ${errors.category ? 'border-destructive' : ''}`}
              placeholder="e.g., Haircut, Massage, Consultation"
            />
            {errors.category && (
              <p className="mt-1 text-sm text-destructive">{errors.category.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-1">
              Price *
            </label>
            <input
              id="price"
              type="number"
              step="0.01"
              {...register('price', { valueAsNumber: true })}
              className={`w-full p-2 border rounded-md ${errors.price ? 'border-red-500' : ''}`}
              min="0"
            />
            {errors.price && (
              <p className="mt-1 text-sm text-red-600">{errors.price.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="duration" className="block text-sm font-medium text-gray-700 mb-1">
              Duration (minutes) *
            </label>
            <input
              id="duration"
              type="number"
              {...register('duration', { valueAsNumber: true })}
              className={`w-full p-2 border rounded-md ${errors.duration ? 'border-red-500' : ''}`}
              min="1"
            />
            {errors.duration && (
              <p className="mt-1 text-sm text-red-600">{errors.duration.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="maxCapacity" className="block text-sm font-medium text-gray-700 mb-1">
              Maximum Capacity
            </label>
            <input
              id="maxCapacity"
              type="number"
              {...register('maxCapacity', { valueAsNumber: true })}
              className={`w-full p-2 border rounded-md ${errors.maxCapacity ? 'border-red-500' : ''}`}
              min="1"
            />
            {errors.maxCapacity && (
              <p className="mt-1 text-sm text-red-600">{errors.maxCapacity.message}</p>
            )}
          </div>

          <div className="flex flex-col space-y-4">
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  {...register('requiresStaff')}
                  className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Requires Staff</span>
              </label>
            </div>

            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  {...register('available')}
                  className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Available</span>
              </label>
            </div>
          </div>

          <div className="md:col-span-2">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              id="description"
              {...register('description')}
              rows={3}
              className={`w-full p-2 border rounded-md ${errors.description ? 'border-red-500' : ''}`}
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="image" className="block text-sm font-medium text-gray-700 mb-1">
              Image URL
            </label>
            <input
              id="image"
              type="url"
              {...register('image')}
              className={`w-full p-2 border rounded-md ${errors.image ? 'border-red-500' : ''}`}
              placeholder="https://example.com/image.jpg"
            />
            {errors.image && (
              <p className="mt-1 text-sm text-red-600">{errors.image.message}</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label htmlFor="preparationTime" className="block text-sm font-medium text-gray-700 mb-1">
                Preparation Time (min)
              </label>
              <input
                id="preparationTime"
                type="number"
                {...register('preparationTime', { valueAsNumber: true })}
                className={`w-full p-2 border rounded-md ${errors.preparationTime ? 'border-red-500' : ''}`}
                min="0"
              />
              {errors.preparationTime && (
                <p className="mt-1 text-sm text-red-600">{errors.preparationTime.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="cleanupTime" className="block text-sm font-medium text-gray-700 mb-1">
                Cleanup Time (min)
              </label>
              <input
                id="cleanupTime"
                type="number"
                {...register('cleanupTime', { valueAsNumber: true })}
                className={`w-full p-2 border rounded-md ${errors.cleanupTime ? 'border-red-500' : ''}`}
                min="0"
              />
              {errors.cleanupTime && (
                <p className="mt-1 text-sm text-red-600">{errors.cleanupTime.message}</p>
              )}
            </div>
          </div>
        </div>

        {staff && staff.length > 0 && (
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Assign Staff
            </label>

            <div className="space-y-2">
              {staff.map((staffMember) => (
                <label key={staffMember.id} className="flex items-center p-2 border rounded-md">
                  <input
                    type="checkbox"
                    value={staffMember.id}
                    {...register('staffIds')}
                    className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                  />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-700">{staffMember.name}</p>
                    <p className="text-xs text-gray-500">{staffMember.role}</p>
                  </div>
                </label>
              ))}
            </div>
          </div>
        )}

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isLoading || (mode === 'edit' && !isDirty)}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:bg-primary/50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Saving...' : mode === 'create' ? 'Create Service' : 'Save Changes'}
          </button>
        </div>
      </form>
    </div>
  );
}
