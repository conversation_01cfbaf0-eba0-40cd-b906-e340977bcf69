'use client';

import { useState } from 'react';
import { use<PERSON><PERSON>, SubmitHandler, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Staff, staffSchema } from '@/lib/validations/serviceSchema';
import { 
  useCreateStaffMutation,
  useUpdateStaffMutation
} from '@/lib/redux/api/endpoints/serviceApi';

interface StaffFormProps {
  merchantId: string;
  initialData?: Staff & { id?: string };
  onSuccess?: (data: any) => void;
  mode: 'create' | 'edit';
}

export default function StaffForm({ 
  merchantId, 
  initialData, 
  onSuccess,
  mode
}: StaffFormProps) {
  const [createStaff, { isLoading: isCreating }] = useCreateStaffMutation();
  const [updateStaff, { isLoading: isUpdating }] = useUpdateStaffMutation();
  
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  const isLoading = isCreating || isUpdating;
  
  // Set up react-hook-form with zod validation
  const { 
    register, 
    handleSubmit, 
    control,
    formState: { errors, isDirty }
  } = useForm<Staff>({
    resolver: zodResolver(staffSchema),
    defaultValues: initialData || {
      name: '',
      email: '',
      phone: '',
      role: '',
      specialties: [],
      image: '',
      availability: {},
    }
  });
  
  // Set up field array for specialties
  const { 
    fields: specialtyFields, 
    append: appendSpecialty, 
    remove: removeSpecialty 
  } = useFieldArray({
    control,
    name: 'specialties',
  });
  
  // Form submission handler
  const onSubmit: SubmitHandler<Staff> = async (data) => {
    try {
      let result;
      
      if (mode === 'create') {
        // Create new staff member
        result = await createStaff({
          merchantId,
          ...data,
        }).unwrap();
        
        setSuccess('Staff member created successfully');
      } else {
        // Update existing staff member
        if (!initialData?.id) {
          throw new Error('Staff ID is required for updates');
        }
        
        result = await updateStaff({
          merchantId,
          staffId: initialData.id,
          data,
        }).unwrap();
        
        setSuccess('Staff member updated successfully');
      }
      
      setError(null);
      
      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess(result);
      }
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      setError(`Failed to ${mode} staff member. Please try again.`);
      setSuccess(null);
      console.error(`Error ${mode}ing staff member:`, err);
    }
  };
  
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h2 className="text-lg font-semibold mb-4">
        {mode === 'create' ? 'Add Staff Member' : 'Edit Staff Member'}
      </h2>
      
      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-red-400">❌</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}
      
      {success && (
        <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-green-400">✓</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700">{success}</p>
            </div>
          </div>
        </div>
      )}
      
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              Full Name *
            </label>
            <input
              id="name"
              {...register('name')}
              className={`w-full p-2 border rounded-md ${errors.name ? 'border-red-500' : ''}`}
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
              Role *
            </label>
            <input
              id="role"
              {...register('role')}
              className={`w-full p-2 border rounded-md ${errors.role ? 'border-red-500' : ''}`}
              placeholder="e.g., Stylist, Therapist, Consultant"
            />
            {errors.role && (
              <p className="mt-1 text-sm text-red-600">{errors.role.message}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email *
            </label>
            <input
              id="email"
              type="email"
              {...register('email')}
              className={`w-full p-2 border rounded-md ${errors.email ? 'border-red-500' : ''}`}
            />
            {errors.email && (
              <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
              Phone *
            </label>
            <input
              id="phone"
              {...register('phone')}
              className={`w-full p-2 border rounded-md ${errors.phone ? 'border-red-500' : ''}`}
            />
            {errors.phone && (
              <p className="mt-1 text-sm text-red-600">{errors.phone.message}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="image" className="block text-sm font-medium text-gray-700 mb-1">
              Profile Image URL
            </label>
            <input
              id="image"
              type="url"
              {...register('image')}
              className={`w-full p-2 border rounded-md ${errors.image ? 'border-red-500' : ''}`}
              placeholder="https://example.com/image.jpg"
            />
            {errors.image && (
              <p className="mt-1 text-sm text-red-600">{errors.image.message}</p>
            )}
          </div>
        </div>
        
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Specialties
          </label>
          
          <div className="space-y-2 mb-2">
            {specialtyFields.map((field, index) => (
              <div key={field.id} className="flex items-center">
                <input
                  {...register(`specialties.${index}` as const)}
                  className="flex-1 p-2 border rounded-md"
                  placeholder="e.g., Hair Coloring, Deep Tissue Massage"
                />
                <button
                  type="button"
                  onClick={() => removeSpecialty(index)}
                  className="ml-2 p-2 text-red-600 hover:text-red-800"
                >
                  ✕
                </button>
              </div>
            ))}
          </div>
          
          <button
            type="button"
            onClick={() => appendSpecialty('')}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            + Add Specialty
          </button>
        </div>
        
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isLoading || (mode === 'edit' && !isDirty)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Saving...' : mode === 'create' ? 'Create Staff Member' : 'Save Changes'}
          </button>
        </div>
      </form>
    </div>
  );
}
