'use client';

import { useState } from 'react';
import { 
  useSendBirthdayGreetingMutation,
  useSendLoyaltyOfferMutation
} from '@/lib/redux/api/endpoints/customerCommunicationApi';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { 
  Mail, 
  MessageSquare, 
  Gift, 
  Cake, 
  Tag, 
  ShoppingBag, 
  Award, 
  AlertCircle, 
  CheckCircle, 
  Loader2 
} from 'lucide-react';
import { toast } from 'sonner';

interface CustomerCommunicationsProps {
  merchantId: string;
  customerId: string;
  customer: any;
}

export default function CustomerCommunications({ 
  merchantId, 
  customerId,
  customer
}: CustomerCommunicationsProps) {
  const [communicationType, setCommunicationType] = useState<'email' | 'sms'>('email');
  const [offerType, setOfferType] = useState<'discount' | 'free_service' | 'gift'>('discount');
  
  // Mutations
  const [sendBirthdayGreeting, { isLoading: isSendingBirthday }] = useSendBirthdayGreetingMutation();
  const [sendLoyaltyOffer, { isLoading: isSendingOffer }] = useSendLoyaltyOfferMutation();
  
  // Handle sending a birthday greeting
  const handleSendBirthdayGreeting = async () => {
    try {
      await sendBirthdayGreeting({
        merchantId,
        customerId,
        type: communicationType,
      }).unwrap();
      
      toast.success('Birthday greeting sent', {
        description: `The birthday greeting has been sent via ${communicationType}.`,
      });
    } catch (error) {
      toast.error('Error', {
        description: error.message || 'Failed to send birthday greeting. Please try again.',
      });
    }
  };
  
  // Handle sending a loyalty offer
  const handleSendLoyaltyOffer = async () => {
    try {
      await sendLoyaltyOffer({
        merchantId,
        customerId,
        type: communicationType,
        offerType,
      }).unwrap();
      
      toast.success('Loyalty offer sent', {
        description: `The ${offerType} offer has been sent via ${communicationType}.`,
      });
    } catch (error) {
      toast.error('Error', {
        description: error.message || 'Failed to send loyalty offer. Please try again.',
      });
    }
  };
  
  // Check if the user has an email or phone
  const hasEmail = !!customer?.email;
  const hasPhone = !!customer?.phone;
  
  // Check if the customer has a birthday
  const hasBirthday = !!customer?.metadata?.birthday;
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Customer Communications</CardTitle>
        <CardDescription>
          Send personalized communications to this customer
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="birthday" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="birthday">
              <Cake className="h-4 w-4 mr-2" />
              Birthday
            </TabsTrigger>
            <TabsTrigger value="loyalty">
              <Award className="h-4 w-4 mr-2" />
              Loyalty Offer
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="birthday" className="space-y-4 pt-4">
            <div className="space-y-4">
              {!hasBirthday && (
                <div className="flex items-center p-3 text-sm bg-yellow-50 text-yellow-800 rounded-md">
                  <AlertCircle className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span>Customer does not have a birthday set in their profile.</span>
                </div>
              )}
              
              <div className="space-y-2">
                <Label htmlFor="communication-method-birthday">Send Via</Label>
                <Select
                  value={communicationType}
                  onValueChange={(value) => setCommunicationType(value as 'email' | 'sms')}
                >
                  <SelectTrigger id="communication-method-birthday">
                    <SelectValue placeholder="Select method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="email" disabled={!hasEmail}>
                      <div className="flex items-center">
                        <Mail className="mr-2 h-4 w-4" />
                        <span>Email</span>
                        {!hasEmail && <span className="ml-2 text-xs text-red-500">(No email)</span>}
                      </div>
                    </SelectItem>
                    <SelectItem value="sms" disabled={!hasPhone}>
                      <div className="flex items-center">
                        <MessageSquare className="mr-2 h-4 w-4" />
                        <span>SMS</span>
                        {!hasPhone && <span className="ml-2 text-xs text-red-500">(No phone)</span>}
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="pt-2">
                <Button 
                  onClick={handleSendBirthdayGreeting} 
                  disabled={isSendingBirthday || !hasBirthday || (!hasEmail && communicationType === 'email') || (!hasPhone && communicationType === 'sms')}
                  className="w-full"
                >
                  {isSendingBirthday ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Cake className="mr-2 h-4 w-4" />
                      Send Birthday Greeting
                    </>
                  )}
                </Button>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="loyalty" className="space-y-4 pt-4">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="communication-method-loyalty">Send Via</Label>
                  <Select
                    value={communicationType}
                    onValueChange={(value) => setCommunicationType(value as 'email' | 'sms')}
                  >
                    <SelectTrigger id="communication-method-loyalty">
                      <SelectValue placeholder="Select method" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="email" disabled={!hasEmail}>
                        <div className="flex items-center">
                          <Mail className="mr-2 h-4 w-4" />
                          <span>Email</span>
                          {!hasEmail && <span className="ml-2 text-xs text-red-500">(No email)</span>}
                        </div>
                      </SelectItem>
                      <SelectItem value="sms" disabled={!hasPhone}>
                        <div className="flex items-center">
                          <MessageSquare className="mr-2 h-4 w-4" />
                          <span>SMS</span>
                          {!hasPhone && <span className="ml-2 text-xs text-red-500">(No phone)</span>}
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="offer-type">Offer Type</Label>
                  <Select
                    value={offerType}
                    onValueChange={(value) => setOfferType(value as 'discount' | 'free_service' | 'gift')}
                  >
                    <SelectTrigger id="offer-type">
                      <SelectValue placeholder="Select offer type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="discount">
                        <div className="flex items-center">
                          <Tag className="mr-2 h-4 w-4" />
                          <span>Discount</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="free_service">
                        <div className="flex items-center">
                          <ShoppingBag className="mr-2 h-4 w-4" />
                          <span>Free Service</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="gift">
                        <div className="flex items-center">
                          <Gift className="mr-2 h-4 w-4" />
                          <span>Gift</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="pt-2">
                <Button 
                  onClick={handleSendLoyaltyOffer} 
                  disabled={isSendingOffer || (!hasEmail && communicationType === 'email') || (!hasPhone && communicationType === 'sms')}
                  className="w-full"
                >
                  {isSendingOffer ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Award className="mr-2 h-4 w-4" />
                      Send Loyalty Offer
                    </>
                  )}
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="border-t pt-4">
        <div className="w-full flex flex-col space-y-2">
          <div className="flex items-center text-xs text-muted-foreground">
            <CheckCircle className="h-3 w-3 mr-1" />
            <span>
              {hasEmail ? 'Email available' : 'No email available'} 
              {hasEmail && hasPhone ? ' • ' : ''}
              {hasPhone ? 'Phone available' : 'No phone available'}
            </span>
          </div>
          {hasBirthday && (
            <div className="flex items-center text-xs text-muted-foreground">
              <Cake className="h-3 w-3 mr-1" />
              <span>
                Birthday: {customer.metadata.birthday}
              </span>
            </div>
          )}
          {customer.metadata?.lastBirthdayGreeting && (
            <div className="flex items-center text-xs text-muted-foreground">
              <Mail className="h-3 w-3 mr-1" />
              <span>
                Last birthday greeting sent: {customer.metadata.lastBirthdayGreeting}
              </span>
            </div>
          )}
          {customer.metadata?.lastLoyaltyOffer && (
            <div className="flex items-center text-xs text-muted-foreground">
              <Award className="h-3 w-3 mr-1" />
              <span>
                Last loyalty offer sent: {customer.metadata.lastLoyaltyOffer.date} ({customer.metadata.lastLoyaltyOffer.type})
              </span>
            </div>
          )}
        </div>
      </CardFooter>
    </Card>
  );
}
