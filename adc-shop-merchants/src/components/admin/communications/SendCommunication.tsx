'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  useGetTemplatesQuery,
  useSendCommunicationMutation,
  useSendWithTemplateMutation
} from '@/lib/redux/api/endpoints/communicationApi';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2, Mail, MessageSquare, Send } from 'lucide-react';
import { toast } from 'sonner';
import TemplatePreview from './TemplatePreview';

// Form schema for direct send
const directSendSchema = z.object({
  type: z.enum(['email', 'sms']),
  recipient: z.string().min(1, 'Recipient is required'),
  subject: z.string().optional(),
  content: z.string().min(1, 'Content is required'),
});

// Form schema for template send
const templateSendSchema = z.object({
  templateId: z.string().min(1, 'Template is required'),
  recipient: z.string().min(1, 'Recipient is required'),
  variables: z.record(z.string()).optional(),
});

type DirectSendFormValues = z.infer<typeof directSendSchema>;
type TemplateSendFormValues = z.infer<typeof templateSendSchema>;

interface SendCommunicationProps {
  merchantId: string;
}

export default function SendCommunication({ merchantId }: SendCommunicationProps) {
  // Using sonner toast
  const [sendMode, setSendMode] = useState<'direct' | 'template'>('template');
  const [selectedTemplateId, setSelectedTemplateId] = useState<string>('');
  const [previewData, setPreviewData] = useState<Record<string, string>>({});

  // Fetch templates
  const { data: templates, isLoading: isLoadingTemplates } = useGetTemplatesQuery({
    merchantId,
  });

  // Get selected template
  const selectedTemplate = templates?.find(template => template.id === selectedTemplateId);

  // Mutations
  const [sendCommunication, { isLoading: isSendingDirect }] = useSendCommunicationMutation();
  const [sendWithTemplate, { isLoading: isSendingTemplate }] = useSendWithTemplateMutation();

  // Direct send form
  const directSendForm = useForm<DirectSendFormValues>({
    resolver: zodResolver(directSendSchema),
    defaultValues: {
      type: 'email',
      recipient: '',
      subject: '',
      content: '',
    },
  });

  // Template send form
  const templateSendForm = useForm<TemplateSendFormValues>({
    resolver: zodResolver(templateSendSchema),
    defaultValues: {
      templateId: '',
      recipient: '',
      variables: {},
    },
  });

  // Handle direct send
  const onDirectSend = async (values: DirectSendFormValues) => {
    try {
      await sendCommunication({
        merchantId,
        ...values,
      }).unwrap();

      toast.success('Message sent', {
        description: `Your ${values.type} has been sent successfully.`,
      });

      directSendForm.reset();
    } catch (error) {
      toast.error('Error', {
        description: 'Failed to send message. Please try again.',
      });
    }
  };

  // Handle template send
  const onTemplateSend = async (values: TemplateSendFormValues) => {
    try {
      await sendWithTemplate({
        merchantId,
        templateId: values.templateId,
        recipient: values.recipient,
        variables: previewData,
      }).unwrap();

      toast.success('Message sent', {
        description: 'Your message has been sent successfully using the template.',
      });

      templateSendForm.reset();
      setPreviewData({});
    } catch (error) {
      toast.error('Error', {
        description: 'Failed to send message. Please try again.',
      });
    }
  };

  // Handle template selection
  const handleTemplateChange = (templateId: string) => {
    setSelectedTemplateId(templateId);
    templateSendForm.setValue('templateId', templateId);

    // Get the selected template
    const template = templates?.find(t => t.id === templateId);

    if (template) {
      // Initialize preview data with empty values for all variables
      const initialPreviewData: Record<string, string> = {};
      template.variables.forEach(variable => {
        initialPreviewData[variable] = '';
      });
      setPreviewData(initialPreviewData);
    }
  };

  // Handle preview data change
  const handlePreviewDataChange = (variable: string, value: string) => {
    setPreviewData(prev => ({
      ...prev,
      [variable]: value,
    }));
  };

  return (
    <div className="space-y-6">
      <Tabs value={sendMode} onValueChange={(value) => setSendMode(value as 'direct' | 'template')}>
        <TabsList>
          <TabsTrigger value="template">
            <Mail className="h-4 w-4 mr-2" />
            Use Template
          </TabsTrigger>
          <TabsTrigger value="direct">
            <MessageSquare className="h-4 w-4 mr-2" />
            Direct Message
          </TabsTrigger>
        </TabsList>

        <TabsContent value="template" className="space-y-6 pt-4">
          <Form {...templateSendForm}>
            <form onSubmit={templateSendForm.handleSubmit(onTemplateSend)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-6">
                  <FormField
                    control={templateSendForm.control}
                    name="templateId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Template</FormLabel>
                        <Select
                          onValueChange={(value) => {
                            field.onChange(value);
                            handleTemplateChange(value);
                          }}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a template" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {isLoadingTemplates ? (
                              <SelectItem value="loading" disabled>
                                Loading templates...
                              </SelectItem>
                            ) : templates && templates.length > 0 ? (
                              templates.map((template) => (
                                <SelectItem key={template.id} value={template.id!}>
                                  {template.name} ({template.type})
                                </SelectItem>
                              ))
                            ) : (
                              <SelectItem value="none" disabled>
                                No templates available
                              </SelectItem>
                            )}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={templateSendForm.control}
                    name="recipient"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Recipient</FormLabel>
                        <FormControl>
                          <Input
                            placeholder={selectedTemplate?.type === 'sms' ? "Phone number" : "Email address"}
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          {selectedTemplate?.type === 'sms'
                            ? "Enter the recipient's phone number"
                            : "Enter the recipient's email address"}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {selectedTemplate && (
                    <div className="space-y-4">
                      <h3 className="text-sm font-medium">Template Variables</h3>
                      {selectedTemplate.variables.length > 0 ? (
                        selectedTemplate.variables.map((variable) => (
                          <FormItem key={variable}>
                            <FormLabel>{variable}</FormLabel>
                            <FormControl>
                              <Input
                                placeholder={`Value for ${variable}`}
                                value={previewData[variable] || ''}
                                onChange={(e) => handlePreviewDataChange(variable, e.target.value)}
                              />
                            </FormControl>
                          </FormItem>
                        ))
                      ) : (
                        <p className="text-sm text-muted-foreground">
                          This template has no variables.
                        </p>
                      )}
                    </div>
                  )}
                </div>

                <div>
                  {selectedTemplate ? (
                    <Card>
                      <CardContent className="pt-6">
                        <h3 className="text-sm font-medium mb-4">Preview</h3>
                        <TemplatePreview
                          template={{
                            type: selectedTemplate.type,
                            subject: selectedTemplate.subject,
                            content: selectedTemplate.content,
                            variables: selectedTemplate.variables,
                          }}
                          previewData={previewData}
                          onPreviewDataChange={handlePreviewDataChange}
                        />
                      </CardContent>
                    </Card>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-full p-8 text-center border rounded-lg">
                      <Mail className="h-12 w-12 text-muted-foreground mb-4" />
                      <h3 className="text-lg font-semibold mb-2">No template selected</h3>
                      <p className="text-muted-foreground">
                        Select a template to see a preview
                      </p>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex justify-end">
                <Button type="submit" disabled={isSendingTemplate || !selectedTemplateId}>
                  {isSendingTemplate ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Send className="mr-2 h-4 w-4" />
                      Send Message
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </TabsContent>

        <TabsContent value="direct" className="space-y-6 pt-4">
          <Form {...directSendForm}>
            <form onSubmit={directSendForm.handleSubmit(onDirectSend)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-6">
                  <FormField
                    control={directSendForm.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Message Type</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="email">Email</SelectItem>
                            <SelectItem value="sms">SMS</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={directSendForm.control}
                    name="recipient"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Recipient</FormLabel>
                        <FormControl>
                          <Input
                            placeholder={directSendForm.watch('type') === 'sms' ? "Phone number" : "Email address"}
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          {directSendForm.watch('type') === 'sms'
                            ? "Enter the recipient's phone number"
                            : "Enter the recipient's email address"}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {directSendForm.watch('type') === 'email' && (
                    <FormField
                      control={directSendForm.control}
                      name="subject"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Subject</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter email subject" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>

                <div>
                  <FormField
                    control={directSendForm.control}
                    name="content"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Message Content</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter your message"
                            className="min-h-[200px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className="flex justify-end">
                <Button type="submit" disabled={isSendingDirect}>
                  {isSendingDirect ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Send className="mr-2 h-4 w-4" />
                      Send Message
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </TabsContent>
      </Tabs>
    </div>
  );
}
