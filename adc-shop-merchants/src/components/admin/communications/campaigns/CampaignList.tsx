'use client';

import { useState } from 'react';
import { useGetCampaignsQuery, useDeleteCampaignMutation, useExecuteCampaignMutation } from '@/lib/redux/api/endpoints/campaignApi';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { Mail, MessageSquare, Calendar, Users, Clock, Play, Edit, Trash2, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import { format } from 'date-fns';

interface CampaignListProps {
  merchantId: string;
  onCreateCampaign: () => void;
  onEditCampaign: (campaignId: string) => void;
}

export default function CampaignList({
  merchantId,
  onCreateCampaign,
  onEditCampaign
}: CampaignListProps) {
  const [activeTab, setActiveTab] = useState<'all' | 'email' | 'sms'>('all');

  // Fetch campaigns
  const { data: campaigns, isLoading, isError, refetch } = useGetCampaignsQuery({
    merchantId,
    type: activeTab === 'all' ? undefined : activeTab,
  });

  // Delete campaign mutation
  const [deleteCampaign, { isLoading: isDeleting }] = useDeleteCampaignMutation();

  // Execute campaign mutation
  const [executeCampaign, { isLoading: isExecuting }] = useExecuteCampaignMutation();

  // Handle delete campaign
  const handleDeleteCampaign = async (campaignId: string) => {
    try {
      await deleteCampaign({ merchantId, campaignId }).unwrap();
      toast.success('Campaign deleted', {
        description: 'The campaign has been deleted successfully.',
      });
    } catch (error) {
      toast.error('Error', {
        description: 'Failed to delete the campaign. Please try again.',
      });
    }
  };

  // Handle execute campaign
  const handleExecuteCampaign = async (campaignId: string) => {
    try {
      await executeCampaign({ merchantId, campaignId }).unwrap();
      toast.success('Campaign execution started', {
        description: 'The campaign is now being executed.',
      });
    } catch (error) {
      toast.error('Error', {
        description: 'Failed to execute the campaign. Please try again.',
      });
    }
  };

  // Get campaign icon based on type
  const getCampaignIcon = (type: string) => {
    return type === 'email' ? <Mail className="h-4 w-4" /> : <MessageSquare className="h-4 w-4" />;
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-muted text-muted-foreground border-border';
      case 'scheduled':
        return 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300';
      case 'in_progress':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300';
      case 'completed':
        return 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300';
      case 'cancelled':
        return 'bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300';
      case 'failed':
        return 'bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300';
      default:
        return 'bg-muted text-muted-foreground border-border';
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, 'PPP p');
    } catch (error) {
      return 'Invalid date';
    }
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="w-full">
            <CardHeader className="pb-2">
              <Skeleton className="h-6 w-1/3" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-2/3" />
            </CardContent>
            <CardFooter>
              <Skeleton className="h-9 w-20 mr-2" />
              <Skeleton className="h-9 w-20" />
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  }

  // Render error state
  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
        <h3 className="text-lg font-semibold mb-2">Failed to load campaigns</h3>
        <p className="text-gray-500 mb-4">There was an error loading the campaigns.</p>
        <Button onClick={() => refetch()}>Try Again</Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'all' | 'email' | 'sms')}>
          <TabsList>
            <TabsTrigger value="all">All Campaigns</TabsTrigger>
            <TabsTrigger value="email">Email</TabsTrigger>
            <TabsTrigger value="sms">SMS</TabsTrigger>
          </TabsList>
        </Tabs>

        <Button onClick={onCreateCampaign}>
          <Users className="h-4 w-4 mr-2" />
          New Campaign
        </Button>
      </div>

      {campaigns && campaigns.length > 0 ? (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {campaigns.map((campaign) => (
            <Card key={campaign.id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg">{campaign.name}</CardTitle>
                  <div className="flex items-center space-x-1">
                    {getCampaignIcon(campaign.type)}
                    <span className="text-xs font-medium capitalize">{campaign.type}</span>
                  </div>
                </div>
                <CardDescription>
                  <Badge className={`${getStatusColor(campaign.status)} mt-2`}>
                    {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                  </Badge>
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Users className="h-4 w-4 mr-2" />
                    <span>{campaign.recipients.length} recipients</span>
                  </div>

                  {campaign.scheduledFor && (
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4 mr-2" />
                      <span>Scheduled for {formatDate(campaign.scheduledFor.toString())}</span>
                    </div>
                  )}

                  {campaign.template && (
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Mail className="h-4 w-4 mr-2" />
                      <span>Template: {campaign.template.name}</span>
                    </div>
                  )}

                  {campaign.executions && campaign.executions.length > 0 && (
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Clock className="h-4 w-4 mr-2" />
                      <span>Last execution: {formatDate(campaign.executions[0].createdAt.toString())}</span>
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter className="border-t pt-4 flex justify-between">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onEditCampaign(campaign.id!)}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>

                <div className="flex space-x-2">
                  {(campaign.status === 'draft' || campaign.status === 'scheduled') && (
                    <Button
                      size="sm"
                      onClick={() => handleExecuteCampaign(campaign.id!)}
                      disabled={isExecuting}
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Execute
                    </Button>
                  )}

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="outline" size="sm" className="text-red-500 border-red-200 hover:bg-red-50">
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete Campaign</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete this campaign? This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => handleDeleteCampaign(campaign.id!)}
                          className="bg-red-500 hover:bg-red-600"
                        >
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center p-8 text-center border rounded-lg">
          <div className="flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-4">
            {activeTab === 'email' ? (
              <Mail className="h-6 w-6 text-gray-500" />
            ) : activeTab === 'sms' ? (
              <MessageSquare className="h-6 w-6 text-gray-500" />
            ) : (
              <Users className="h-6 w-6 text-gray-500" />
            )}
          </div>
          <h3 className="text-lg font-semibold mb-2">No campaigns found</h3>
          <p className="text-gray-500 mb-4">
            {activeTab === 'all'
              ? 'You haven\'t created any campaigns yet.'
              : `You haven\'t created any ${activeTab} campaigns yet.`}
          </p>
          <Button onClick={onCreateCampaign}>
            <Users className="h-4 w-4 mr-2" />
            Create Campaign
          </Button>
        </div>
      )}
    </div>
  );
}
