'use client';

import { CommunicationAnalytics } from '@/services/communicationAnalyticsService';
import { Progress } from '@/components/ui/progress';
import { Mail, MessageSquare, Check, X, AlertTriangle, Ban } from 'lucide-react';

interface AnalyticsSummaryProps {
  analytics?: CommunicationAnalytics;
  detailed?: boolean;
}

export default function AnalyticsSummary({ analytics, detailed = false }: AnalyticsSummaryProps) {
  if (!analytics) {
    return <div>No analytics data available</div>;
  }
  
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <Check className="h-4 w-4 mr-2 text-green-500" />
            <span className="text-sm font-medium">Delivery Rate</span>
          </div>
          <span className="text-sm font-bold">{(analytics.deliveryRate * 100).toFixed(1)}%</span>
        </div>
        <Progress value={analytics.deliveryRate * 100} className="h-2" />
        <p className="text-xs text-muted-foreground">
          {analytics.delivered} of {analytics.totalSent} messages delivered
        </p>
      </div>
      
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <Mail className="h-4 w-4 mr-2 text-blue-500" />
            <span className="text-sm font-medium">Open Rate</span>
          </div>
          <span className="text-sm font-bold">{(analytics.openRate * 100).toFixed(1)}%</span>
        </div>
        <Progress value={analytics.openRate * 100} className="h-2" />
        <p className="text-xs text-muted-foreground">
          {analytics.opened} of {analytics.delivered} messages opened
        </p>
      </div>
      
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <MessageSquare className="h-4 w-4 mr-2 text-indigo-500" />
            <span className="text-sm font-medium">Click Rate</span>
          </div>
          <span className="text-sm font-bold">{(analytics.clickRate * 100).toFixed(1)}%</span>
        </div>
        <Progress value={analytics.clickRate * 100} className="h-2" />
        <p className="text-xs text-muted-foreground">
          {analytics.clicked} of {analytics.delivered} messages clicked
        </p>
      </div>
      
      {detailed && (
        <>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <X className="h-4 w-4 mr-2 text-red-500" />
                <span className="text-sm font-medium">Bounce Rate</span>
              </div>
              <span className="text-sm font-bold">{(analytics.bounceRate * 100).toFixed(1)}%</span>
            </div>
            <Progress value={analytics.bounceRate * 100} className="h-2" />
            <p className="text-xs text-muted-foreground">
              {analytics.bounced} of {analytics.totalSent} messages bounced
            </p>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <AlertTriangle className="h-4 w-4 mr-2 text-yellow-500" />
                <span className="text-sm font-medium">Complaint Rate</span>
              </div>
              <span className="text-sm font-bold">{(analytics.complaintRate * 100).toFixed(1)}%</span>
            </div>
            <Progress value={analytics.complaintRate * 100} className="h-2" />
            <p className="text-xs text-muted-foreground">
              {analytics.complained} of {analytics.delivered} messages complained about
            </p>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <Ban className="h-4 w-4 mr-2 text-red-500" />
                <span className="text-sm font-medium">Unsubscribe Rate</span>
              </div>
              <span className="text-sm font-bold">{(analytics.unsubscribeRate * 100).toFixed(1)}%</span>
            </div>
            <Progress value={analytics.unsubscribeRate * 100} className="h-2" />
            <p className="text-xs text-muted-foreground">
              {analytics.unsubscribed} of {analytics.delivered} recipients unsubscribed
            </p>
          </div>
          
          <div className="pt-2 border-t">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-xs font-medium text-muted-foreground">Click-to-Open Rate</p>
                <p className="text-sm font-bold">{(analytics.clickToOpenRate * 100).toFixed(1)}%</p>
              </div>
              <div>
                <p className="text-xs font-medium text-muted-foreground">Total Sent</p>
                <p className="text-sm font-bold">{analytics.totalSent}</p>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
