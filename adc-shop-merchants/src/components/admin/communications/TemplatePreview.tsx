'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Mail, MessageSquare, Eye, Code } from 'lucide-react';

interface TemplatePreviewProps {
  template: {
    type: 'email' | 'sms';
    subject?: string;
    content: string;
    variables: string[];
  };
  previewData: Record<string, string>;
  onPreviewDataChange: (variable: string, value: string) => void;
}

export default function TemplatePreview({ 
  template, 
  previewData, 
  onPreviewDataChange 
}: TemplatePreviewProps) {
  const [viewMode, setViewMode] = useState<'preview' | 'code'>('preview');
  
  // Replace variables in content
  const getPreviewContent = () => {
    let content = template.content;
    
    // Replace all variables with their values
    template.variables.forEach(variable => {
      const value = previewData[variable] || `[${variable}]`;
      content = content.replace(new RegExp(`{{${variable}}}`, 'g'), value);
    });
    
    return content;
  };
  
  // Format content for preview
  const formatPreviewContent = (content: string) => {
    // Replace newlines with <br> tags for HTML display
    return content.replace(/\n/g, '<br>');
  };
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">
          {template.type === 'email' ? 'Email Preview' : 'SMS Preview'}
        </h3>
        
        <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'preview' | 'code')}>
          <TabsList>
            <TabsTrigger value="preview">
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </TabsTrigger>
            <TabsTrigger value="code">
              <Code className="h-4 w-4 mr-2" />
              Code
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h4 className="text-sm font-medium mb-3">Test Data</h4>
          <div className="space-y-4">
            {template.variables.map((variable) => (
              <div key={variable} className="space-y-2">
                <Label htmlFor={`var-${variable}`}>{variable}</Label>
                <Input
                  id={`var-${variable}`}
                  value={previewData[variable] || ''}
                  onChange={(e) => onPreviewDataChange(variable, e.target.value)}
                  placeholder={`Value for ${variable}`}
                />
              </div>
            ))}
            
            {template.variables.length === 0 && (
              <p className="text-sm text-muted-foreground">
                No variables found in this template.
              </p>
            )}
          </div>
        </div>
        
        <div>
          <h4 className="text-sm font-medium mb-3">Preview</h4>
          
          <Card className="border shadow-sm">
            {template.type === 'email' && (
              <CardHeader className="pb-2 border-b">
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <CardTitle className="text-sm font-medium">
                    {template.subject || 'No Subject'}
                  </CardTitle>
                </div>
              </CardHeader>
            )}
            
            <CardContent className={template.type === 'email' ? 'pt-4' : 'pt-3'}>
              {template.type === 'sms' && (
                <div className="flex items-start mb-3">
                  <div className="bg-primary/10 rounded-full p-2 mr-3">
                    <MessageSquare className="h-5 w-5 text-primary" />
                  </div>
                </div>
              )}
              
              <TabsContent value="preview" className="m-0 p-0">
                <div 
                  className={`text-sm ${template.type === 'sms' ? 'pl-10' : ''}`}
                  dangerouslySetInnerHTML={{ __html: formatPreviewContent(getPreviewContent()) }}
                />
              </TabsContent>
              
              <TabsContent value="code" className="m-0 p-0">
                <pre className="text-xs bg-muted p-2 rounded overflow-x-auto">
                  {template.content}
                </pre>
              </TabsContent>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
