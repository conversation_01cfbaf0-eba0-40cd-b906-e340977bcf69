'use client';

import React, { useState } from 'react';
import { Link, usePathname } from '@/i18n/navigation';
import { Bell } from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import NotificationItem from './NotificationItem';
import {
  useGetNotificationsQuery,
  useUpdateNotificationMutation,
  useMarkAllNotificationsAsReadMutation
} from '@/lib/redux/api/endpoints/restaurant/notificationsApi';


export default function NotificationPopover() {
  const [open, setOpen] = useState(false);
  const pathname = usePathname();

  // Extract shop and branch slugs from the current path
  const pathSegments = pathname.split('/').filter(Boolean);
  const isRestaurantRoute = pathSegments.includes('restaurant');
  const shopSlug = isRestaurantRoute ? pathSegments[3] : null;
  const branchSlug = isRestaurantRoute ? pathSegments[4] : null;



  // Get notifications data
  const {
    data: notificationsData,
    isLoading: isLoadingNotifications
  } = useGetNotificationsQuery(
    {
      shopSlug: shopSlug!,
      branchSlug: branchSlug!,
      filters: {
        page: 1,
        limit: 5, // Only show 5 in popover
        sort_by: 'timestamp',
        sort_order: 'desc'
      }
    },
    { skip: !shopSlug || !branchSlug }
  );

  // Mutation hooks
  const [updateNotification] = useUpdateNotificationMutation();
  const [markAllNotificationsAsRead] = useMarkAllNotificationsAsReadMutation();

  // Extract data
  const notifications = notificationsData?.data || [];
  const unreadCount = notificationsData?.summary?.unreadNotifications || 0;

  // Mark a notification as read
  const markAsRead = async (id: string) => {
    if (!shopSlug || !branchSlug) return;

    try {
      await updateNotification({
        shopSlug,
        branchSlug,
        notificationId: id,
        updates: { isRead: true }
      }).unwrap();
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    if (!shopSlug || !branchSlug) return;

    try {
      await markAllNotificationsAsRead({
        shopSlug,
        branchSlug
      }).unwrap();
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5 text-[#181510]" />
          {unreadCount > 0 && (
            <span className="absolute top-0 right-0 flex h-4 w-4 items-center justify-center rounded-full bg-[#e58219] text-[10px] font-medium text-white">
              {unreadCount > 9 ? '9+' : unreadCount}
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="w-80 p-0 bg-[#fbfaf9] border-[#e5e1dc]"
        align="end"
        sideOffset={8}
      >
        <div className="flex items-center justify-between p-3 border-b border-[#e5e1dc]">
          <h3 className="text-sm font-medium text-[#181510]">Notifications</h3>
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              className="text-xs h-8 px-2 text-[#8a745c] hover:text-[#181510] hover:bg-[#f1edea]"
              onClick={markAllAsRead}
            >
              Mark all as read
            </Button>
          )}
        </div>

        <ScrollArea className="h-[300px]">
          {isLoadingNotifications ? (
            <div className="flex items-center justify-center h-[300px] text-[#8a745c]">
              Loading notifications...
            </div>
          ) : notifications.length === 0 ? (
            <div className="flex items-center justify-center h-[300px] text-[#8a745c]">
              No notifications
            </div>
          ) : (
            <div className="divide-y divide-[#e5e1dc]">
              {notifications.map((notification) => (
                <NotificationItem
                  key={notification.id}
                  notification={notification}
                  onMarkAsRead={markAsRead}
                  compact
                  shopSlug={shopSlug || undefined}
                  branchSlug={branchSlug || undefined}
                />
              ))}
            </div>
          )}
        </ScrollArea>

        <div className="p-3 border-t border-[#e5e1dc] text-center">
          <Link
            href={
              shopSlug && branchSlug
                ? `/app/restaurant/${shopSlug}/${branchSlug}/notifications`
                : "/app/notifications"
            }
            className="text-sm text-[#8a745c] hover:text-[#181510] font-medium"
            onClick={() => setOpen(false)}
          >
            View all notifications
          </Link>
        </div>
      </PopoverContent>
    </Popover>
  );
}
