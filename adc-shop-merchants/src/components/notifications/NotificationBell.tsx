'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Link, useRouter } from '@/i18n/navigation';
import { Bell, BellRing, X, Check, Clock, Settings } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useNotifications } from '@/hooks/useNotifications';

interface NotificationBellProps {
  className?: string;
  shopSlug: string;
  branchSlug: string;
}

export function NotificationBell({ className, shopSlug, branchSlug }: NotificationBellProps) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  const {
    notifications,
    isLoading: loading,
    stats,
    handleMarkAsRead,
    handleMarkAllAsRead,
    formatTimestamp,
    getTypeIcon,
  } = useNotifications({
    shopSlug,
    branchSlug,
    initialFilters: {
      page: 1,
      limit: 5, // Only get recent notifications for the bell
      sort_by: 'timestamp',
      sort_order: 'desc',
    }
  });

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Get recent notifications (last 5)
  const recentNotifications = notifications.slice(0, 5);

  // Get unread count from stats
  const unreadCount = stats?.unreadNotifications || 0;

  // Helper function to get notification route based on type
  const getNotificationRoute = (notification: typeof notifications[0]): string => {
    // If notification has a specific link, use it
    if (notification.link) {
      // If the link already starts with /app/restaurant, use it as is
      if (notification.link.startsWith('/app/restaurant')) {
        return notification.link;
      }

      // If it's a relative link, construct the full slug-based path
      if (notification.link.startsWith('/')) {
        // Remove leading slash and construct full path
        const relativePath = notification.link.substring(1);
        return `/app/restaurant/${shopSlug}/${branchSlug}/${relativePath}`;
      }

      // If it's just a page name, construct full path
      return `/app/restaurant/${shopSlug}/${branchSlug}/${notification.link}`;
    }

    // Route based on notification type
    switch (notification.type) {
      case 'order':
        return `/app/restaurant/${shopSlug}/${branchSlug}/orders`;
      case 'reservation':
        return `/app/restaurant/${shopSlug}/${branchSlug}/reservations`;
      case 'review':
        return `/app/restaurant/${shopSlug}/${branchSlug}/reviews`;
      case 'inventory':
        return `/app/restaurant/${shopSlug}/${branchSlug}/inventory`;
      case 'staff':
        return `/app/restaurant/${shopSlug}/${branchSlug}/staff`;
      case 'payment':
        return `/app/restaurant/${shopSlug}/${branchSlug}/orders`;
      case 'promotion':
        return `/app/restaurant/${shopSlug}/${branchSlug}/marketing`;
      case 'system':
        return `/app/restaurant/${shopSlug}/${branchSlug}/settings`;
      default:
        return `/app/restaurant/${shopSlug}/${branchSlug}/notifications`;
    }
  };

  // Handle notification click
  const handleNotificationClick = (notification: typeof notifications[0]) => {
    if (!notification.isRead) {
      handleMarkAsRead(notification.id);
    }

    const routeUrl = getNotificationRoute(notification);
    router.push(routeUrl);
    setIsOpen(false);
  };



  return (
    <div className={cn('relative', className)} ref={dropdownRef}>
      {/* Bell Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
        aria-label="Notifications"
      >
        {unreadCount > 0 ? (
          <BellRing className="w-6 h-6" />
        ) : (
          <Bell className="w-6 h-6" />
        )}

        {/* Unread Count Badge */}
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute right-0 top-full mt-2 w-80 bg-card border border-border rounded-lg shadow-lg z-50">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-border">
            <h3 className="font-semibold text-foreground">Notifications</h3>
            <div className="flex items-center gap-2">
              {unreadCount > 0 && (
                <button
                  onClick={() => {
                    handleMarkAllAsRead();
                  }}
                  className="text-sm text-primary hover:text-primary/80 transition-colors"
                >
                  Mark all read
                </button>
              )}
              <Link
                href="/app/notifications/settings"
                className="p-1 text-muted-foreground hover:text-foreground transition-colors"
                title="Notification Settings"
              >
                <Settings className="w-4 h-4" />
              </Link>
              <button
                onClick={() => setIsOpen(false)}
                className="p-1 text-muted-foreground hover:text-foreground transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="max-h-96 overflow-y-auto">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              </div>
            ) : recentNotifications.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Bell className="w-8 h-8 mx-auto mb-2 text-muted" />
                <p className="text-sm">No notifications</p>
              </div>
            ) : (
              <div className="divide-y divide-border">
                {recentNotifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={cn(
                      'p-4 hover:bg-muted transition-colors cursor-pointer',
                      !notification.isRead && 'bg-blue-50 dark:bg-blue-950'
                    )}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className="flex items-start gap-3">
                      {/* Icon */}
                      <div className="text-lg flex-shrink-0 mt-0.5">
                        {getTypeIcon(notification.type)}
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <p className={cn(
                              'text-sm font-medium text-foreground',
                              !notification.isRead && 'font-semibold'
                            )}>
                              {notification.title}
                            </p>
                            <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                              {notification.message}
                            </p>
                            <div className="flex items-center gap-2 mt-2">
                              <Clock className="w-3 h-3 text-muted-foreground" />
                              <span className="text-xs text-muted-foreground">
                                {formatTimestamp(notification.timestamp)}
                              </span>
                              {notification.priority === 'urgent' && (
                                <span className="bg-red-50 text-red-700 text-xs px-1.5 py-0.5 rounded dark:bg-red-950 dark:text-red-300">
                                  Urgent
                                </span>
                              )}
                            </div>
                          </div>

                          {/* Unread Indicator */}
                          {!notification.isRead && (
                            <div className="w-2 h-2 bg-blue-600 rounded-full flex-shrink-0 mt-2"></div>
                          )}
                        </div>
                      </div>

                      {/* Mark as Read Button */}
                      {!notification.isRead && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleMarkAsRead(notification.id);
                          }}
                          className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                          title="Mark as read"
                        >
                          <Check className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          {recentNotifications.length > 0 && (
            <div className="border-t border-gray-200 p-3">
              <Link
                href={`/app/restaurant/${shopSlug}/${branchSlug}/notifications`}
                className="block w-full text-center text-sm text-blue-600 hover:text-blue-700 transition-colors"
                onClick={() => setIsOpen(false)}
              >
                View all notifications
              </Link>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
