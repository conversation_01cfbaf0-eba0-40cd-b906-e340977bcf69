"use client";

import React, { useState, useEffect } from 'react';
import { X, Plus, Search, Filter } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { 
  useGetTagsQuery, 
  useGetTagCategoriesQuery, 
  useSearchTagsMutation,
  Tag,
  TagCategory 
} from '@/lib/redux/api/endpoints/tagApi';

interface TagSelectorProps {
  shopId: string;
  branchId: string;
  value: string[];
  onChange: (tags: string[]) => void;
  entityType?: string;
  placeholder?: string;
  maxTags?: number;
  allowCustomTags?: boolean;
  categoryFilter?: string;
  className?: string;
  disabled?: boolean;
}

export function TagSelector({
  shopId,
  branchId,
  value = [],
  onChange,
  entityType = 'general',
  placeholder = "Add tags...",
  maxTags = 10,
  allowCustomTags = true,
  categoryFilter,
  className,
  disabled = false
}: TagSelectorProps) {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(categoryFilter || '');
  const [inputValue, setInputValue] = useState('');

  // API hooks
  const { data: tagsResponse } = useGetTagsQuery({
    shopId,
    branchId,
    filters: {
      search: searchTerm,
      category: selectedCategory || undefined,
      is_active: true,
      limit: 50
    }
  });

  const { data: categoriesResponse } = useGetTagCategoriesQuery({
    shopId,
    branchId,
    filters: { is_active: true }
  });

  const [searchTags] = useSearchTagsMutation();

  const tags = tagsResponse?.tags || [];
  const categories = categoriesResponse?.categories || [];

  // Filter out already selected tags
  const availableTags = tags.filter(tag => !value.includes(tag.name));

  // Handle tag selection
  const handleTagSelect = (tagName: string) => {
    if (value.includes(tagName) || value.length >= maxTags) return;
    
    onChange([...value, tagName]);
    setInputValue('');
    setSearchTerm('');
  };

  // Handle tag removal
  const handleTagRemove = (tagName: string) => {
    onChange(value.filter(tag => tag !== tagName));
  };

  // Handle custom tag creation
  const handleCustomTagAdd = () => {
    const trimmedValue = inputValue.trim();
    if (!trimmedValue || value.includes(trimmedValue) || value.length >= maxTags) return;
    
    if (allowCustomTags) {
      onChange([...value, trimmedValue]);
      setInputValue('');
      setSearchTerm('');
    }
  };

  // Handle enter key
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (inputValue.trim()) {
        handleCustomTagAdd();
      }
    }
  };

  // Get tag color by category
  const getTagColor = (tagName: string) => {
    const tag = tags.find(t => t.name === tagName);
    return tag?.color || '#8a745c';
  };

  // Get tag icon by category
  const getTagIcon = (tagName: string) => {
    const tag = tags.find(t => t.name === tagName);
    return tag?.icon || '';
  };

  return (
    <div className={cn("space-y-2", className)}>
      {/* Selected Tags */}
      {value.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {value.map((tagName) => (
            <Badge
              key={tagName}
              variant="outline"
              className="text-xs"
              style={{ 
                backgroundColor: getTagColor(tagName) + '20', 
                borderColor: getTagColor(tagName) 
              }}
            >
              {getTagIcon(tagName) && (
                <span className="mr-1">{getTagIcon(tagName)}</span>
              )}
              {tagName}
              {!disabled && (
                <button
                  type="button"
                  onClick={() => handleTagRemove(tagName)}
                  className="ml-1 hover:bg-red-100 rounded-full p-0.5"
                >
                  <X className="h-3 w-3" />
                </button>
              )}
            </Badge>
          ))}
        </div>
      )}

      {/* Tag Input */}
      {!disabled && value.length < maxTags && (
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <div className="relative">
              <Input
                placeholder={placeholder}
                value={inputValue}
                onChange={(e) => {
                  setInputValue(e.target.value);
                  setSearchTerm(e.target.value);
                }}
                onKeyDown={handleKeyDown}
                className="pr-8"
              />
              <Search className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            </div>
          </PopoverTrigger>
          
          <PopoverContent className="w-80 p-0" align="start">
            <Command>
              <div className="flex items-center border-b px-3">
                <CommandInput
                  placeholder="Search tags..."
                  value={searchTerm}
                  onValueChange={setSearchTerm}
                  className="flex-1"
                />
                {categories.length > 0 && (
                  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger className="w-32 border-0 shadow-none">
                      <Filter className="h-4 w-4" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All</SelectItem>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.slug}>
                          {category.icon && <span className="mr-1">{category.icon}</span>}
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>
              
              <CommandList>
                <CommandEmpty>
                  {allowCustomTags && inputValue.trim() ? (
                    <div className="p-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleCustomTagAdd}
                        className="w-full justify-start"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Create "{inputValue.trim()}"
                      </Button>
                    </div>
                  ) : (
                    "No tags found."
                  )}
                </CommandEmpty>
                
                {categories.map((category) => {
                  const categoryTags = availableTags.filter(tag => tag.category === category.slug);
                  if (categoryTags.length === 0) return null;
                  
                  return (
                    <CommandGroup key={category.id} heading={category.name}>
                      {categoryTags.map((tag) => (
                        <CommandItem
                          key={tag.id}
                          onSelect={() => handleTagSelect(tag.name)}
                          className="cursor-pointer"
                        >
                          <div className="flex items-center gap-2">
                            {tag.icon && <span>{tag.icon}</span>}
                            <span>{tag.name}</span>
                            {tag.usage_count > 0 && (
                              <Badge variant="secondary" className="text-xs ml-auto">
                                {tag.usage_count}
                              </Badge>
                            )}
                          </div>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  );
                })}
                
                {/* Uncategorized tags */}
                {availableTags.filter(tag => !categories.some(cat => cat.slug === tag.category)).length > 0 && (
                  <CommandGroup heading="Other">
                    {availableTags
                      .filter(tag => !categories.some(cat => cat.slug === tag.category))
                      .map((tag) => (
                        <CommandItem
                          key={tag.id}
                          onSelect={() => handleTagSelect(tag.name)}
                          className="cursor-pointer"
                        >
                          <div className="flex items-center gap-2">
                            {tag.icon && <span>{tag.icon}</span>}
                            <span>{tag.name}</span>
                            {tag.usage_count > 0 && (
                              <Badge variant="secondary" className="text-xs ml-auto">
                                {tag.usage_count}
                              </Badge>
                            )}
                          </div>
                        </CommandItem>
                      ))}
                  </CommandGroup>
                )}
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      )}

      {/* Tag limit indicator */}
      {value.length > 0 && (
        <div className="text-xs text-muted-foreground">
          {value.length} / {maxTags} tags
        </div>
      )}
    </div>
  );
}
