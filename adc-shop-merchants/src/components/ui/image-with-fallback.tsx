'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/utils';

interface ImageWithFallbackProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  fallback?: React.ReactNode;
  fallbackIcon?: string;
  fallbackText?: string;
  containerClassName?: string;
  onError?: () => void;
}

export function ImageWithFallback({
  src,
  alt,
  fallback,
  fallbackIcon = '🖼️',
  fallbackText = 'No Image Available',
  className,
  containerClassName,
  onError,
  ...props
}: ImageWithFallbackProps) {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [hasTriedToLoad, setHasTriedToLoad] = useState(false);

  const handleImageError = () => {
    // Prevent infinite loops by only setting error once
    if (!hasTriedToLoad) {
      setHasTriedToLoad(true);
      setImageError(true);
      setIsLoading(false);
      onError?.();
    }
  };

  const handleImageLoad = () => {
    setHasTriedToLoad(true);
    setIsLoading(false);
  };

  // Reset states when src changes
  React.useEffect(() => {
    if (src) {
      setImageError(false);
      setIsLoading(true);
      setHasTriedToLoad(false);
    } else {
      setImageError(true);
      setIsLoading(false);
      setHasTriedToLoad(true);
    }
  }, [src]);

  // Default fallback component
  const defaultFallback = (
    <div className="flex flex-col items-center justify-center h-full w-full text-[#8a745c] bg-[#f1edea]">
      <div className="text-2xl mb-2">{fallbackIcon}</div>
      <div className="text-sm text-center px-2">{fallbackText}</div>
    </div>
  );

  // Loading state
  const loadingState = (
    <div className="flex items-center justify-center h-full w-full bg-[#f1edea]">
      <div className="animate-pulse">
        <div className="text-[#8a745c] text-sm">Loading...</div>
      </div>
    </div>
  );

  return (
    <div className={cn("relative overflow-hidden", containerClassName)}>
      {!imageError && src ? (
        <>
          {isLoading && loadingState}
          <img
            src={src}
            alt={alt}
            className={cn(
              "transition-opacity duration-300",
              isLoading ? "opacity-0" : "opacity-100",
              className
            )}
            onError={handleImageError}
            onLoad={handleImageLoad}
            {...props}
          />
        </>
      ) : (
        fallback || defaultFallback
      )}
    </div>
  );
}

// Specialized variants for common use cases
export function MenuItemImage({
  src,
  alt,
  className,
  containerClassName,
  ...props
}: Omit<ImageWithFallbackProps, 'fallbackIcon' | 'fallbackText'>) {
  return (
    <ImageWithFallback
      src={src}
      alt={alt}
      fallbackIcon="🍽️"
      fallbackText="No Image Available"
      className={cn("h-full w-full object-cover", className)}
      containerClassName={cn("h-40 bg-[#f1edea] flex items-center justify-center", containerClassName)}
      {...props}
    />
  );
}

export function ProductImage({
  src,
  alt,
  className,
  containerClassName,
  ...props
}: Omit<ImageWithFallbackProps, 'fallbackIcon' | 'fallbackText'>) {
  return (
    <ImageWithFallback
      src={src}
      alt={alt}
      fallbackIcon="📦"
      fallbackText="No Product Image"
      className={cn("h-full w-full object-cover", className)}
      containerClassName={cn("aspect-square bg-[#f1edea] flex items-center justify-center", containerClassName)}
      {...props}
    />
  );
}

export function AvatarImage({
  src,
  alt,
  className,
  containerClassName,
  ...props
}: Omit<ImageWithFallbackProps, 'fallbackIcon' | 'fallbackText'>) {
  return (
    <ImageWithFallback
      src={src}
      alt={alt}
      fallbackIcon="👤"
      fallbackText=""
      className={cn("h-full w-full object-cover rounded-full", className)}
      containerClassName={cn("w-10 h-10 bg-[#f1edea] flex items-center justify-center rounded-full", containerClassName)}
      {...props}
    />
  );
}

export function BannerImage({
  src,
  alt,
  className,
  containerClassName,
  ...props
}: Omit<ImageWithFallbackProps, 'fallbackIcon' | 'fallbackText'>) {
  return (
    <ImageWithFallback
      src={src}
      alt={alt}
      fallbackIcon="🖼️"
      fallbackText="No Banner Image"
      className={cn("h-full w-full object-cover", className)}
      containerClassName={cn("aspect-[16/9] bg-muted flex items-center justify-center", containerClassName)}
      {...props}
    />
  );
}

export function TableImage({
  src,
  alt,
  className,
  containerClassName,
  onError,
  ...props
}: Omit<ImageWithFallbackProps, 'fallbackIcon' | 'fallbackText'>) {
  return (
    <ImageWithFallback
      src={src}
      alt={alt}
      fallbackIcon="🪑"
      fallbackText="No Table Image"
      className={cn("w-full h-full object-cover", className)}
      containerClassName={cn("w-full h-full bg-gradient-to-br from-[#f1edea] to-[#e5ccb2] flex items-center justify-center", containerClassName)}
      onError={onError}
      {...props}
    />
  );
}

export function RestaurantImage({
  src,
  alt,
  className,
  containerClassName,
  ...props
}: Omit<ImageWithFallbackProps, 'fallbackIcon' | 'fallbackText'>) {
  return (
    <ImageWithFallback
      src={src}
      alt={alt}
      fallbackIcon="🏪"
      fallbackText="Restaurant Image"
      className={cn("h-full w-full object-cover", className)}
      containerClassName={cn("h-48 bg-gradient-to-br from-[#f1edea] to-[#e5ccb2] flex items-center justify-center", containerClassName)}
      {...props}
    />
  );
}
