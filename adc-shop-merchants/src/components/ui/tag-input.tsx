"use client";

import React, { useState, useRef, useEffect } from 'react';
import { X, Plus, Search } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { cn } from '@/lib/utils';
import { Tag } from '@/lib/redux/api/endpoints/tagApi';

interface TagInputProps {
  value: string[];
  onChange: (tags: string[]) => void;
  suggestions?: Tag[];
  onSearch?: (query: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  maxTags?: number;
  allowCustomTags?: boolean;
  categories?: string[];
  selectedCategory?: string;
  onCategoryChange?: (category: string) => void;
}

export function TagInput({
  value = [],
  onChange,
  suggestions = [],
  onSearch,
  placeholder = "Add tags...",
  className,
  disabled = false,
  maxTags,
  allowCustomTags = true,
  categories = [],
  selectedCategory,
  onCategoryChange,
}: TagInputProps) {
  const [inputValue, setInputValue] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleInputChange = (newValue: string) => {
    setInputValue(newValue);
    if (onSearch) {
      onSearch(newValue);
    }
  };

  const addTag = (tagName: string) => {
    if (!tagName.trim()) return;
    
    const trimmedTag = tagName.trim();
    if (value.includes(trimmedTag)) return;
    if (maxTags && value.length >= maxTags) return;

    onChange([...value, trimmedTag]);
    setInputValue('');
    setIsOpen(false);
  };

  const removeTag = (tagToRemove: string) => {
    onChange(value.filter(tag => tag !== tagToRemove));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && inputValue.trim() && allowCustomTags) {
      e.preventDefault();
      addTag(inputValue);
    } else if (e.key === 'Backspace' && !inputValue && value.length > 0) {
      removeTag(value[value.length - 1]);
    }
  };

  const filteredSuggestions = suggestions.filter(
    suggestion => 
      !value.includes(suggestion.name) &&
      suggestion.name.toLowerCase().includes(inputValue.toLowerCase()) &&
      (!selectedCategory || suggestion.category === selectedCategory)
  );

  return (
    <div className={cn("space-y-2", className)}>
      {/* Category Filter */}
      {categories.length > 0 && (
        <div className="flex gap-2 flex-wrap">
          <Button
            type="button"
            variant={!selectedCategory ? "default" : "outline"}
            size="sm"
            onClick={() => onCategoryChange?.('')}
          >
            All
          </Button>
          {categories.map((category) => (
            <Button
              key={category}
              type="button"
              variant={selectedCategory === category ? "default" : "outline"}
              size="sm"
              onClick={() => onCategoryChange?.(category)}
            >
              {category}
            </Button>
          ))}
        </div>
      )}

      {/* Selected Tags */}
      {value.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {value.map((tag) => (
            <Badge key={tag} variant="default" className="flex items-center gap-1">
              {tag}
              {!disabled && (
                <button
                  type="button"
                  onClick={() => removeTag(tag)}
                  className="ml-1 hover:text-primary-foreground"
                >
                  <X className="h-3 w-3" />
                </button>
              )}
            </Badge>
          ))}
        </div>
      )}

      {/* Input with Suggestions */}
      {!disabled && (!maxTags || value.length < maxTags) && (
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <div className="relative">
              <Input
                ref={inputRef}
                value={inputValue}
                onChange={(e) => handleInputChange(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder={placeholder}
                className="pr-10"
                onFocus={() => setIsOpen(true)}
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <Search className="h-4 w-4 text-muted-foreground" />
              </div>
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0" align="start">
            <Command>
              <CommandList>
                {filteredSuggestions.length > 0 && (
                  <CommandGroup heading="Suggestions">
                    {filteredSuggestions.map((suggestion) => (
                      <CommandItem
                        key={suggestion.id}
                        onSelect={() => addTag(suggestion.name)}
                        className="flex items-center justify-between"
                      >
                        <div className="flex items-center gap-2">
                          {suggestion.icon && (
                            <span className="text-sm">{suggestion.icon}</span>
                          )}
                          <span>{suggestion.name}</span>
                          <Badge variant="outline" className="text-xs">
                            {suggestion.category}
                          </Badge>
                        </div>
                        <span className="text-xs text-muted-foreground">
                          {suggestion.usage_count} uses
                        </span>
                      </CommandItem>
                    ))}
                  </CommandGroup>
                )}
                
                {allowCustomTags && inputValue.trim() && !filteredSuggestions.some(s => s.name.toLowerCase() === inputValue.toLowerCase()) && (
                  <CommandGroup heading="Create New">
                    <CommandItem onSelect={() => addTag(inputValue)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Create "{inputValue}"
                    </CommandItem>
                  </CommandGroup>
                )}
                
                {filteredSuggestions.length === 0 && !inputValue.trim() && (
                  <CommandEmpty>No tags found.</CommandEmpty>
                )}
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      )}

      {/* Max tags reached message */}
      {maxTags && value.length >= maxTags && (
        <p className="text-sm text-muted-foreground">
          Maximum {maxTags} tags allowed
        </p>
      )}
    </div>
  );
}
