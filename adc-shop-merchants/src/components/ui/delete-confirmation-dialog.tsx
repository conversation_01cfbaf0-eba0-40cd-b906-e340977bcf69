"use client";

import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface DeleteConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void | Promise<void>;
  title?: string;
  description?: string;
  itemName?: string;
  itemType?: string;
  isLoading?: boolean;
  confirmText?: string;
  cancelText?: string;
}

export function DeleteConfirmationDialog({
  open,
  onOpenChange,
  onConfirm,
  title,
  description,
  itemName,
  itemType = 'item',
  isLoading = false,
  confirmText,
  cancelText = 'Cancel',
}: DeleteConfirmationDialogProps) {
  const defaultTitle = title || `Delete ${itemType}`;
  const defaultDescription = description || 
    `Are you sure you want to delete ${itemName ? `"${itemName}"` : `this ${itemType}`}? This action cannot be undone.`;
  const defaultConfirmText = confirmText || `Delete ${itemType}`;

  const handleConfirm = async () => {
    try {
      await onConfirm();
    } catch (error) {
      // Error handling is done in the parent component
      console.error('Delete confirmation error:', error);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="text-red-600">
            {defaultTitle}
          </AlertDialogTitle>
          <AlertDialogDescription className="text-gray-600">
            {defaultDescription}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>
            {cancelText}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={isLoading}
            className="bg-red-500 hover:bg-red-600 focus:ring-red-500 text-white"
          >
            {isLoading ? 'Deleting...' : defaultConfirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

// Specialized components for common use cases
export function RestaurantDeleteDialog({
  open,
  onOpenChange,
  onConfirm,
  restaurantName,
  isLoading = false,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void | Promise<void>;
  restaurantName?: string;
  isLoading?: boolean;
}) {
  return (
    <DeleteConfirmationDialog
      open={open}
      onOpenChange={onOpenChange}
      onConfirm={onConfirm}
      title="Delete Restaurant"
      description={`Are you sure you want to delete ${restaurantName ? `"${restaurantName}"` : 'this restaurant'}? This action cannot be undone and will permanently remove the restaurant and all its associated data.`}
      itemType="restaurant"
      itemName={restaurantName}
      isLoading={isLoading}
      confirmText="Delete Restaurant"
    />
  );
}

export function TableDeleteDialog({
  open,
  onOpenChange,
  onConfirm,
  tableName,
  isLoading = false,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void | Promise<void>;
  tableName?: string;
  isLoading?: boolean;
}) {
  return (
    <DeleteConfirmationDialog
      open={open}
      onOpenChange={onOpenChange}
      onConfirm={onConfirm}
      title="Delete Table"
      description={`Are you sure you want to delete ${tableName ? `"${tableName}"` : 'this table'}? This action cannot be undone.`}
      itemType="table"
      itemName={tableName}
      isLoading={isLoading}
      confirmText="Delete Table"
    />
  );
}

export function MenuItemDeleteDialog({
  open,
  onOpenChange,
  onConfirm,
  itemName,
  isLoading = false,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void | Promise<void>;
  itemName?: string;
  isLoading?: boolean;
}) {
  return (
    <DeleteConfirmationDialog
      open={open}
      onOpenChange={onOpenChange}
      onConfirm={onConfirm}
      title="Delete Menu Item"
      description={`Are you sure you want to delete ${itemName ? `"${itemName}"` : 'this menu item'}? This action cannot be undone.`}
      itemType="menu item"
      itemName={itemName}
      isLoading={isLoading}
      confirmText="Delete Menu Item"
    />
  );
}

export function ReservationDeleteDialog({
  open,
  onOpenChange,
  onConfirm,
  reservationId,
  isLoading = false,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void | Promise<void>;
  reservationId?: string;
  isLoading?: boolean;
}) {
  return (
    <DeleteConfirmationDialog
      open={open}
      onOpenChange={onOpenChange}
      onConfirm={onConfirm}
      title="Delete Reservation"
      description={`Are you sure you want to delete ${reservationId ? `reservation "${reservationId}"` : 'this reservation'}? This action cannot be undone.`}
      itemType="reservation"
      itemName={reservationId}
      isLoading={isLoading}
      confirmText="Delete Reservation"
    />
  );
}
