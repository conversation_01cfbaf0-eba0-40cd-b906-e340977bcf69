'use client';

import React, { useState, useCallback } from 'react';
import { cn } from '@/lib/utils';

interface SafeAvatarProps {
  src?: string | null;
  alt: string;
  name: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const sizeClasses = {
  sm: 'w-8 h-8 text-xs',
  md: 'w-10 h-10 text-sm',
  lg: 'w-12 h-12 text-base',
};

export function SafeAvatar({ 
  src, 
  alt, 
  name, 
  size = 'md', 
  className 
}: SafeAvatarProps) {
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(!!src);

  const handleError = useCallback(() => {
    setHasError(true);
    setIsLoading(false);
  }, []);

  const handleLoad = useCallback(() => {
    setIsLoading(false);
  }, []);

  // Get initials from name
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Don't try to load image if src is empty, null, or undefined
  const shouldShowImage = src && !hasError && typeof src === 'string' && src.trim() !== '';

  return (
    <div className={cn(
      'rounded-full bg-[#e5e1dc] flex items-center justify-center text-[#8a745c] font-medium overflow-hidden',
      sizeClasses[size],
      className
    )}>
      {shouldShowImage ? (
        <>
          {isLoading && (
            <div className="animate-pulse bg-[#f1edea] w-full h-full flex items-center justify-center">
              <span className="text-xs">...</span>
            </div>
          )}
          <img
            src={src}
            alt={alt}
            className={cn(
              'w-full h-full object-cover transition-opacity duration-200',
              isLoading ? 'opacity-0' : 'opacity-100'
            )}
            onError={handleError}
            onLoad={handleLoad}
            loading="lazy"
          />
        </>
      ) : (
        <span className="select-none">
          {getInitials(name)}
        </span>
      )}
    </div>
  );
}

// Specialized variant for review avatars
export function ReviewAvatar({ 
  src, 
  alt, 
  name, 
  className 
}: Omit<SafeAvatarProps, 'size'>) {
  return (
    <SafeAvatar
      src={src}
      alt={alt}
      name={name}
      size="md"
      className={className}
    />
  );
}
