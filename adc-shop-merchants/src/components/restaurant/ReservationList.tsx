/**
 * Reusable reservation list component
 * Handles display of multiple reservations with filtering and pagination
 */

import React, { useState } from 'react';
import { ReservationCard, ReservationCardSkeleton, type Reservation } from './ReservationCard';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Search, Filter, Calendar, Users } from 'lucide-react';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { MESSAGES } from '@/lib/constants/messages';
import { cn } from '@/lib/utils';

export interface ReservationFilters {
  status?: string;
  date?: string;
  search?: string;
}

interface ReservationListProps {
  reservations: Reservation[];
  isLoading?: boolean;
  totalCount?: number;
  currentPage?: number;
  totalPages?: number;
  filters?: ReservationFilters;
  statusCounts?: Record<string, number>;
  onFiltersChange?: (filters: ReservationFilters) => void;
  onPageChange?: (page: number) => void;
  onReservationView?: (reservation: Reservation) => void;
  onReservationEdit?: (reservation: Reservation) => void;
  onReservationCancel?: (reservation: Reservation) => void;
  onReservationConfirm?: (reservation: Reservation) => void;
  showFilters?: boolean;
  showPagination?: boolean;
  compact?: boolean;
  className?: string;
}

const STATUS_OPTIONS = [
  { value: 'all', label: 'All Status' },
  { value: 'pending', label: MESSAGES.STATUS.PENDING },
  { value: 'confirmed', label: MESSAGES.STATUS.CONFIRMED },
  { value: 'completed', label: MESSAGES.STATUS.COMPLETED },
  { value: 'cancelled', label: MESSAGES.STATUS.CANCELLED },
];

export function ReservationList({
  reservations,
  isLoading = false,
  totalCount = 0,
  currentPage = 1,
  totalPages = 1,
  filters = {},
  statusCounts = {},
  onFiltersChange,
  onPageChange,
  onReservationView,
  onReservationEdit,
  onReservationCancel,
  onReservationConfirm,
  showFilters = true,
  showPagination = true,
  compact = false,
  className
}: ReservationListProps) {
  const [localFilters, setLocalFilters] = useState<ReservationFilters>(filters);

  const handleFilterChange = (key: keyof ReservationFilters, value: string) => {
    const newFilters = { ...localFilters, [key]: value === 'all' || !value ? undefined : value };
    setLocalFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const clearFilters = () => {
    const clearedFilters = {};
    setLocalFilters(clearedFilters);
    onFiltersChange?.(clearedFilters);
  };

  const hasActiveFilters = Object.values(localFilters).some(value => value);

  return (
    <div className={cn('space-y-6', className)}>
      {/* Filters */}
      {showFilters && (
        <div className="space-y-4">
          {/* Search and Status Filter */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#8a745c] h-4 w-4" />
              <Input
                placeholder={MESSAGES.PLACEHOLDER.SEARCH_RESERVATIONS}
                value={localFilters.search || ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="pl-10 bg-[#f1edea] border-[#e5e1dc] text-[#181510] placeholder:text-[#8a745c]"
              />
            </div>

            <Select
              value={localFilters.status || 'all'}
              onValueChange={(value) => handleFilterChange('status', value)}
            >
              <SelectTrigger className="w-full sm:w-48 bg-[#f1edea] border-[#e5e1dc]">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                {STATUS_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Input
              type="date"
              value={localFilters.date || ''}
              onChange={(e) => handleFilterChange('date', e.target.value)}
              className="w-full sm:w-48 bg-[#f1edea] border-[#e5e1dc]"
            />
          </div>

          {/* Status Counts */}
          {Object.keys(statusCounts).length > 0 && (
            <div className="flex flex-wrap gap-2">
              <Badge variant="outline" className="bg-[#f9f7f4] border-[#e5e1dc]">
                <Users className="h-3 w-3 mr-1" />
                Total: {totalCount}
              </Badge>
              {Object.entries(statusCounts).map(([status, count]) => (
                <Badge
                  key={status}
                  variant="outline"
                  className="bg-[#f9f7f4] border-[#e5e1dc] cursor-pointer hover:bg-[#f1edea]"
                  onClick={() => handleFilterChange('status', status)}
                >
                  {MESSAGES.STATUS[status.toUpperCase() as keyof typeof MESSAGES.STATUS] || status}: {count}
                </Badge>
              ))}
            </div>
          )}

          {/* Clear Filters */}
          {hasActiveFilters && (
            <div className="flex justify-between items-center">
              <p className="text-sm text-[#8a745c]">
                {reservations.length} of {totalCount} reservations
              </p>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="text-[#8a745c] hover:text-[#181510]"
              >
                {MESSAGES.ACTION.CLEAR} filters
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="grid gap-4">
          {Array.from({ length: 6 }).map((_, index) => (
            <ReservationCardSkeleton key={index} compact={compact} />
          ))}
        </div>
      )}

      {/* Empty State */}
      {!isLoading && reservations.length === 0 && (
        <div className="text-center py-12">
          <Calendar className="h-12 w-12 text-[#8a745c] mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-[#181510] mb-2">
            {hasActiveFilters ? MESSAGES.INFO.NO_RESULTS : MESSAGES.INFO.NO_DATA}
          </h3>
          <p className="text-[#8a745c] mb-4">
            {hasActiveFilters
              ? 'Try adjusting your filters to see more reservations.'
              : 'No reservations have been made yet.'
            }
          </p>
          {hasActiveFilters && (
            <Button
              variant="outline"
              onClick={clearFilters}
              className="bg-[#f1edea] border-[#e5e1dc] text-[#181510] hover:bg-[#e2dcd4]"
            >
              {MESSAGES.ACTION.CLEAR} filters
            </Button>
          )}
        </div>
      )}

      {/* Reservations Grid */}
      {!isLoading && reservations.length > 0 && (
        <div className="grid gap-4">
          {reservations.map((reservation) => (
            <ReservationCard
              key={reservation.id}
              reservation={reservation}
              onView={onReservationView}
              onEdit={onReservationEdit}
              onCancel={onReservationCancel}
              onConfirm={onReservationConfirm}
              compact={compact}
            />
          ))}
        </div>
      )}

      {/* Pagination */}
      {showPagination && totalPages > 1 && (
        <div className="flex justify-center">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => onPageChange?.(Math.max(1, currentPage - 1))}
                  className={cn(
                    currentPage === 1 && 'pointer-events-none opacity-50'
                  )}
                />
              </PaginationItem>

              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const page = i + 1;
                return (
                  <PaginationItem key={page}>
                    <PaginationLink
                      onClick={() => onPageChange?.(page)}
                      isActive={currentPage === page}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                );
              })}

              <PaginationItem>
                <PaginationNext
                  onClick={() => onPageChange?.(Math.min(totalPages, currentPage + 1))}
                  className={cn(
                    currentPage === totalPages && 'pointer-events-none opacity-50'
                  )}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
}
