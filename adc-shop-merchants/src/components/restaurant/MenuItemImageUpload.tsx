'use client';

import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { MenuItemImage } from '@/components/ui/image-with-fallback';
import { useUploadMenuItemImageMutation } from '@/lib/redux/api/endpoints/restaurant/menuApi';
import { Upload, X, Camera, RotateCcw } from 'lucide-react';
import { toast } from 'sonner';

interface MenuItemImageUploadProps {
  shopId: string;
  branchId: string;
  itemId: string;
  itemName: string;
  currentImageUrl?: string;
  onImageUploaded?: (imageUrl: string) => void;
  className?: string;
}

export function MenuItemImageUpload({
  shopId,
  branchId,
  itemId,
  itemName,
  currentImageUrl,
  onImageUploaded,
  className = ''
}: MenuItemImageUploadProps) {
  const [uploadImage, { isLoading: isUploading }] = useUploadMenuItemImageMutation();
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const displayImageUrl = previewUrl || currentImageUrl;

  const handleFileSelect = (file: File) => {
    // Validate file type - only allow specific image types
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Please select a JPEG, PNG, or WebP image file');
      return;
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('File size must be less than 5MB');
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = () => {
      setPreviewUrl(reader.result as string);
    };
    reader.readAsDataURL(file);

    // Upload the file
    handleUpload(file);
  };

  const handleUpload = async (file: File) => {
    try {
      const result = await uploadImage({
        merchantId: shopId,
        branchId,
        itemId,
        file
      }).unwrap();

      toast.success('Menu item image uploaded successfully');
      setPreviewUrl(null); // Clear preview since we now have the real URL
      onImageUploaded?.(result.imageUrl);
    } catch (error) {
      console.error('Upload error:', error);
      toast.error('Failed to upload image. Please try again.');
      setPreviewUrl(null); // Clear preview on error
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  const handleClearPreview = () => {
    setPreviewUrl(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <Card className={`bg-[#fbfaf9] border-[#e2dcd4] ${className}`}>
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Current/Preview Image */}
          <div className="space-y-2">
            <h3 className="text-[#181510] font-medium">
              {itemName} Image
            </h3>

            <div className="relative w-full h-48 border border-[#e2dcd4] rounded-lg overflow-hidden bg-[#f1edea]">
              <MenuItemImage
                src={displayImageUrl}
                alt={`${itemName} preview`}
                containerClassName="w-full h-full"
                className="w-full h-full object-cover"
              />

              {/* Loading overlay */}
              {isUploading && (
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                  <div className="text-white text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
                    <p className="text-sm">Uploading...</p>
                  </div>
                </div>
              )}

              {/* Preview controls */}
              {previewUrl && !isUploading && (
                <div className="absolute top-2 right-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleClearPreview}
                    className="bg-white/90 hover:bg-white border-[#e2dcd4]"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* Upload Controls */}
          <div className="space-y-3">
            {/* Drag and Drop Area */}
            <div
              className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                isDragOver
                  ? 'border-[#e5ccb2] bg-[#f1edea]'
                  : 'border-[#e2dcd4] hover:border-[#e5ccb2] hover:bg-[#f9f8f7]'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <Camera className="mx-auto h-8 w-8 text-[#8a745c] mb-2" />
              <p className="text-[#181510] font-medium mb-1">
                Drag and drop an image here, or click to browse
              </p>
              <p className="text-[#8a745c] text-sm mb-4">
                Supported formats: JPG, PNG, WebP. Max size: 5MB
              </p>

              <input
                ref={fileInputRef}
                type="file"
                accept="image/jpeg,image/png,image/webp"
                onChange={handleFileInputChange}
                className="hidden"
                disabled={isUploading}
              />

              <Button
                variant="outline"
                onClick={handleBrowseClick}
                disabled={isUploading}
                className="border-[#e2dcd4] text-[#8a745c] hover:bg-[#f1edea]"
              >
                <Upload className="w-4 h-4 mr-2" />
                Choose File
              </Button>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={handleClearPreview}
                disabled={!previewUrl || isUploading}
                className="flex-1 border-[#e2dcd4] text-[#8a745c] hover:bg-[#f1edea]"
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                Reset
              </Button>
            </div>
          </div>

          {/* Upload Status */}
          {isUploading && (
            <div className="text-center">
              <p className="text-[#8a745c] text-sm">
                Uploading image for {itemName}...
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
