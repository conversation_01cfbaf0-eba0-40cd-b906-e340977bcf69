/**
 * Reusable reservation card component
 * Displays reservation information in a card format
 */

import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Calendar, Clock, Users, MapPin, Phone, Mail } from 'lucide-react';
import { dateFormatters } from '@/lib/utils/formatters';
import { MESSAGES } from '@/lib/constants/messages';
import { cn } from '@/lib/utils';

export interface Reservation {
  id: string;
  slug: string;
  customerName: string;
  customerEmail?: string;
  customerPhone?: string;
  date: string;
  time: string;
  partySize: number;
  status: string;
  tableNumber?: string;
  specialRequests?: string;
  createdAt: string;
}

interface ReservationCardProps {
  reservation: Reservation;
  onView?: (reservation: Reservation) => void;
  onEdit?: (reservation: Reservation) => void;
  onCancel?: (reservation: Reservation) => void;
  onConfirm?: (reservation: Reservation) => void;
  showActions?: boolean;
  compact?: boolean;
  className?: string;
}

export function ReservationCard({
  reservation,
  onView,
  onEdit,
  onCancel,
  onConfirm,
  showActions = true,
  compact = false,
  className
}: ReservationCardProps) {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300';
      case 'pending':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300';
      case 'cancelled':
        return 'bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300';
      case 'completed':
        return 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300';
      default:
        return 'bg-muted text-muted-foreground border-border';
    }
  };

  const getStatusText = (status: string) => {
    return MESSAGES.STATUS[status.toUpperCase() as keyof typeof MESSAGES.STATUS] || status;
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const isUpcoming = new Date(reservation.date) >= new Date();
  const isPending = reservation.status.toLowerCase() === 'pending';
  const isCancelled = reservation.status.toLowerCase() === 'cancelled';

  return (
    <Card className={cn(
      'transition-all duration-200 hover:shadow-md',
      isCancelled && 'opacity-60',
      className
    )}>
      <CardHeader className={cn('pb-3', compact && 'pb-2')}>
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <Avatar className="h-10 w-10">
              <AvatarFallback className="bg-muted text-foreground font-medium">
                {getInitials(reservation.customerName)}
              </AvatarFallback>
            </Avatar>
            <div>
              <h3 className="font-semibold text-foreground text-sm">
                {reservation.customerName}
              </h3>
              {!compact && reservation.customerEmail && (
                <p className="text-xs text-muted-foreground flex items-center mt-1">
                  <Mail className="h-3 w-3 mr-1" />
                  {reservation.customerEmail}
                </p>
              )}
            </div>
          </div>
          <Badge className={cn('text-xs', getStatusColor(reservation.status))}>
            {getStatusText(reservation.status)}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className={cn('pt-0', compact && 'pb-3')}>
        <div className="space-y-2">
          {/* Date and Time */}
          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center text-muted-foreground">
              <Calendar className="h-4 w-4 mr-2" />
              <span>{dateFormatters.display(reservation.date)}</span>
            </div>
            <div className="flex items-center text-muted-foreground">
              <Clock className="h-4 w-4 mr-2" />
              <span>{reservation.time}</span>
            </div>
          </div>

          {/* Party Size and Table */}
          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center text-muted-foreground">
              <Users className="h-4 w-4 mr-2" />
              <span>{reservation.partySize} {reservation.partySize === 1 ? 'guest' : 'guests'}</span>
            </div>
            {reservation.tableNumber && (
              <div className="flex items-center text-muted-foreground">
                <MapPin className="h-4 w-4 mr-2" />
                <span>Table {reservation.tableNumber}</span>
              </div>
            )}
          </div>

          {/* Phone */}
          {!compact && reservation.customerPhone && (
            <div className="flex items-center text-sm text-muted-foreground">
              <Phone className="h-4 w-4 mr-2" />
              <span>{reservation.customerPhone}</span>
            </div>
          )}

          {/* Special Requests */}
          {!compact && reservation.specialRequests && (
            <div className="mt-3 p-2 bg-muted rounded-md">
              <p className="text-xs text-muted-foreground font-medium mb-1">
                {MESSAGES.RESTAURANT.SPECIAL_REQUESTS}:
              </p>
              <p className="text-sm text-foreground">{reservation.specialRequests}</p>
            </div>
          )}

          {/* Actions */}
          {showActions && (
            <div className="flex items-center justify-between pt-3 border-t border-border">
              <div className="flex space-x-2">
                {onView && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onView(reservation)}
                    className="text-muted-foreground hover:text-foreground"
                  >
                    {MESSAGES.ACTION.VIEW}
                  </Button>
                )}
                {onEdit && !isCancelled && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onEdit(reservation)}
                    className="text-muted-foreground hover:text-foreground"
                  >
                    {MESSAGES.ACTION.EDIT}
                  </Button>
                )}
              </div>

              <div className="flex space-x-2">
                {onConfirm && isPending && isUpcoming && (
                  <Button
                    size="sm"
                    onClick={() => onConfirm(reservation)}
                    className="bg-green-600 hover:bg-green-700 text-white dark:bg-green-700 dark:hover:bg-green-800"
                  >
                    {MESSAGES.ACTION.CONFIRM}
                  </Button>
                )}
                {onCancel && !isCancelled && isUpcoming && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onCancel(reservation)}
                    className="text-destructive border-destructive/20 hover:bg-destructive/10"
                  >
                    {MESSAGES.ACTION.CANCEL}
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Compact version for lists
export function ReservationCardCompact(props: Omit<ReservationCardProps, 'compact'>) {
  return <ReservationCard {...props} compact={true} />;
}

// Loading skeleton
export function ReservationCardSkeleton({ compact = false }: { compact?: boolean }) {
  return (
    <Card className="animate-pulse">
      <CardHeader className={cn('pb-3', compact && 'pb-2')}>
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 bg-muted rounded-full" />
            <div>
              <div className="h-4 w-24 bg-muted rounded" />
              {!compact && <div className="h-3 w-32 bg-muted rounded mt-1" />}
            </div>
          </div>
          <div className="h-5 w-16 bg-muted rounded-full" />
        </div>
      </CardHeader>
      <CardContent className={cn('pt-0', compact && 'pb-3')}>
        <div className="space-y-2">
          <div className="flex space-x-4">
            <div className="h-4 w-20 bg-muted rounded" />
            <div className="h-4 w-16 bg-muted rounded" />
          </div>
          <div className="flex space-x-4">
            <div className="h-4 w-16 bg-muted rounded" />
            <div className="h-4 w-20 bg-muted rounded" />
          </div>
          {!compact && (
            <div className="pt-3 border-t border-border">
              <div className="flex justify-between">
                <div className="flex space-x-2">
                  <div className="h-8 w-12 bg-muted rounded" />
                  <div className="h-8 w-12 bg-muted rounded" />
                </div>
                <div className="h-8 w-16 bg-muted rounded" />
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
