/**
 * Reusable menu item card component
 * Displays menu item information in a card format
 */

import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Star, Clock, Users, Edit, Trash2, Eye } from 'lucide-react';
import { numberFormatters } from '@/lib/utils/formatters';
import { MESSAGES } from '@/lib/constants/messages';
import { cn } from '@/lib/utils';
import { MenuItemImage } from '@/components/ui/image-with-fallback';

export interface MenuItem {
  id: string;
  name: string;
  slug: string;
  description?: string;
  price: number;
  category: string;
  image?: string;
  available: boolean;
  featured: boolean;
  calories?: number;
  prepTime?: number;
  allergens?: string[];
  dietaryInfo?: string[];
  createdAt: string;
}

interface MenuItemCardProps {
  menuItem: MenuItem;
  onView?: (menuItem: MenuItem) => void;
  onEdit?: (menuItem: MenuItem) => void;
  onDelete?: (menuItem: MenuItem) => void;
  onToggleAvailability?: (menuItem: MenuItem, available: boolean) => void;
  onToggleFeatured?: (menuItem: MenuItem, featured: boolean) => void;
  showActions?: boolean;
  showAvailabilityToggle?: boolean;
  showFeaturedToggle?: boolean;
  compact?: boolean;
  className?: string;
}

export function MenuItemCard({
  menuItem,
  onView,
  onEdit,
  onDelete,
  onToggleAvailability,
  onToggleFeatured,
  showActions = true,
  showAvailabilityToggle = true,
  showFeaturedToggle = true,
  compact = false,
  className
}: MenuItemCardProps) {
  const getCategoryColor = (category: string) => {
    const colors = {
      'appetizer': 'bg-orange-50 text-orange-700 border-orange-200 dark:bg-orange-950 dark:text-orange-300',
      'main': 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300',
      'dessert': 'bg-pink-50 text-pink-700 border-pink-200 dark:bg-pink-950 dark:text-pink-300',
      'beverage': 'bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-950 dark:text-purple-300',
      'side': 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300',
    };
    return colors[category.toLowerCase() as keyof typeof colors] || 'bg-muted text-muted-foreground border-border';
  };

  const getAvailabilityColor = (available: boolean) => {
    return available
      ? 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300'
      : 'bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300';
  };

  return (
    <Card className={cn(
      'transition-all duration-200 hover:shadow-md',
      !menuItem.available && 'opacity-60',
      className
    )}>
      <CardHeader className={cn('pb-3', compact && 'pb-2')}>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="font-semibold text-foreground text-sm line-clamp-1">
                {menuItem.name}
              </h3>
              {menuItem.featured && (
                <Star className="h-4 w-4 text-yellow-500 fill-current" />
              )}
            </div>

            {!compact && menuItem.description && (
              <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
                {menuItem.description}
              </p>
            )}

            <div className="flex items-center gap-2 flex-wrap">
              <Badge className={getCategoryColor(menuItem.category)}>
                {menuItem.category}
              </Badge>
              <Badge className={getAvailabilityColor(menuItem.available)}>
                {menuItem.available ? MESSAGES.STATUS.ACTIVE : MESSAGES.STATUS.INACTIVE}
              </Badge>
            </div>
          </div>

          {menuItem.image && (
            <div className="ml-3">
              <MenuItemImage
                src={menuItem.image}
                alt={menuItem.name}
                className="w-16 h-16 object-cover rounded-lg"
                containerClassName="w-16 h-16 rounded-lg"
              />
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className={cn('pt-0', compact && 'pb-3')}>
        <div className="space-y-3">
          {/* Price */}
          <div className="flex items-center justify-between">
            <span className="text-lg font-bold text-foreground">
              {numberFormatters.currency(menuItem.price)}
            </span>

            {!compact && (
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                {menuItem.prepTime && (
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    <span>{menuItem.prepTime}m</span>
                  </div>
                )}
                {menuItem.calories && (
                  <div className="flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    <span>{menuItem.calories} cal</span>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Dietary Info */}
          {!compact && menuItem.dietaryInfo && menuItem.dietaryInfo.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {menuItem.dietaryInfo.slice(0, 3).map((info) => (
                <Badge
                  key={info}
                  variant="outline"
                  className="text-xs"
                >
                  {info}
                </Badge>
              ))}
              {menuItem.dietaryInfo.length > 3 && (
                <Badge
                  variant="outline"
                  className="text-xs"
                >
                  +{menuItem.dietaryInfo.length - 3} more
                </Badge>
              )}
            </div>
          )}

          {/* Toggles */}
          {(showAvailabilityToggle || showFeaturedToggle) && (
            <div className="flex items-center justify-between pt-2 border-t border-border">
              {showAvailabilityToggle && onToggleAvailability && (
                <div className="flex items-center gap-2">
                  <Switch
                    checked={menuItem.available}
                    onCheckedChange={(checked) => onToggleAvailability(menuItem, checked)}
                    className="data-[state=checked]:bg-green-600"
                  />
                  <span className="text-xs text-muted-foreground">Available</span>
                </div>
              )}

              {showFeaturedToggle && onToggleFeatured && (
                <div className="flex items-center gap-2">
                  <Switch
                    checked={menuItem.featured}
                    onCheckedChange={(checked) => onToggleFeatured(menuItem, checked)}
                    className="data-[state=checked]:bg-yellow-600"
                  />
                  <span className="text-xs text-muted-foreground">Featured</span>
                </div>
              )}
            </div>
          )}

          {/* Actions */}
          {showActions && (
            <div className="flex items-center justify-between pt-2 border-t border-border">
              <div className="flex space-x-1">
                {onView && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onView(menuItem)}
                    className="text-muted-foreground hover:text-foreground h-8 px-2"
                  >
                    <Eye className="h-3 w-3 mr-1" />
                    {MESSAGES.ACTION.VIEW}
                  </Button>
                )}
                {onEdit && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onEdit(menuItem)}
                    className="text-muted-foreground hover:text-foreground h-8 px-2"
                  >
                    <Edit className="h-3 w-3 mr-1" />
                    {MESSAGES.ACTION.EDIT}
                  </Button>
                )}
              </div>

              {onDelete && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onDelete(menuItem)}
                  className="text-destructive hover:text-destructive/80 hover:bg-destructive/10 h-8 px-2"
                >
                  <Trash2 className="h-3 w-3 mr-1" />
                  {MESSAGES.ACTION.DELETE}
                </Button>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Compact version for lists
export function MenuItemCardCompact(props: Omit<MenuItemCardProps, 'compact'>) {
  return <MenuItemCard {...props} compact={true} />;
}

// Grid version for menu display
export function MenuItemCardGrid(props: MenuItemCardProps) {
  return (
    <MenuItemCard
      {...props}
      className={cn('aspect-square', props.className)}
      compact={false}
    />
  );
}

// Loading skeleton
export function MenuItemCardSkeleton({ compact = false }: { compact?: boolean }) {
  return (
    <Card className="animate-pulse">
      <CardHeader className={cn('pb-3', compact && 'pb-2')}>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="h-4 w-32 bg-muted rounded mb-2" />
            {!compact && <div className="h-3 w-full bg-muted rounded mb-2" />}
            <div className="flex gap-2">
              <div className="h-5 w-16 bg-muted rounded-full" />
              <div className="h-5 w-16 bg-muted rounded-full" />
            </div>
          </div>
          <div className="w-16 h-16 bg-muted rounded-lg ml-3" />
        </div>
      </CardHeader>
      <CardContent className={cn('pt-0', compact && 'pb-3')}>
        <div className="space-y-3">
          <div className="flex justify-between">
            <div className="h-6 w-20 bg-muted rounded" />
            {!compact && <div className="h-4 w-16 bg-muted rounded" />}
          </div>
          {!compact && (
            <div className="flex gap-1">
              <div className="h-5 w-12 bg-muted rounded-full" />
              <div className="h-5 w-12 bg-muted rounded-full" />
            </div>
          )}
          <div className="pt-2 border-t border-border">
            <div className="flex justify-between">
              <div className="flex space-x-1">
                <div className="h-8 w-12 bg-muted rounded" />
                <div className="h-8 w-12 bg-muted rounded" />
              </div>
              <div className="h-8 w-16 bg-muted rounded" />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
