'use client';

import React, { useState } from 'react';
import { Link, usePathname } from '@/i18n/navigation';
import { useAuth } from '@/hooks/useAuth';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  User,
  Settings,
  LogOut,
  Bell,
  HelpCircle,
  Menu,
  Search,
  ChevronDown,
  Building2,
  Store,
  Shield,
  CreditCard
} from 'lucide-react';
import { NotificationBellWrapper } from '@/components/notifications/NotificationBellWrapper';
import { cn } from '@/lib/utils';

interface AuthHeaderProps {
  title?: string;
  showSearch?: boolean;
  showNotifications?: boolean;
  className?: string;
  onMenuClick?: () => void;
}

export default function AuthHeader({
  title = 'Restaurant Management',
  showSearch = true,
  showNotifications = true,
  className,
  onMenuClick
}: AuthHeaderProps) {
  const { user, isAuthenticated, isLoading, logout } = useAuth();
  const pathname = usePathname();
  const [isProfileOpen, setIsProfileOpen] = useState(false);

  // Extract route information for breadcrumbs
  const pathSegments = pathname.split('/').filter(Boolean);
  const isRestaurantRoute = pathSegments.includes('restaurant');
  const isRetailRoute = pathSegments.includes('retail');
  const isServiceRoute = pathSegments.includes('service');

  // Get business type and context
  const getBusinessContext = () => {
    if (isRestaurantRoute) {
      const slugShop = pathSegments[3];
      const slugBranch = pathSegments[4];
      return {
        type: 'restaurant',
        icon: <Building2 size={16} className="text-[#8a745c]" />,
        shopSlug: slugShop,
        branchSlug: slugBranch
      };
    }
    if (isRetailRoute) {
      return {
        type: 'retail',
        icon: <Store size={16} className="text-[#8a745c]" />
      };
    }
    if (isServiceRoute) {
      return {
        type: 'service',
        icon: <Shield size={16} className="text-[#8a745c]" />
      };
    }
    return null;
  };

  const businessContext = getBusinessContext();

  const handleLogout = async () => {
    try {
      await logout();
      setIsProfileOpen(false);
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className={cn(
        "sticky top-0 z-50 w-full border-b border-[#e2dcd4] bg-[#fbfaf9]",
        className
      )}>
        <div className="flex h-16 items-center justify-between px-4 md:px-6 lg:px-8">
          <div className="flex items-center gap-4">
            <div className="h-6 w-32 bg-[#f1edea] rounded animate-pulse" />
          </div>
          <div className="flex items-center gap-3">
            <div className="h-8 w-8 bg-[#f1edea] rounded-full animate-pulse" />
            <div className="h-8 w-8 bg-[#f1edea] rounded-full animate-pulse" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn(
      "sticky top-0 z-50 w-full border-b border-border bg-background backdrop-blur-sm",
      className
    )}>
      <div className="flex h-16 items-center justify-between px-4 md:px-6 lg:px-8">
        {/* Left Section */}
        <div className="flex items-center gap-4">
          {/* Mobile Menu Button */}
          {onMenuClick && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onMenuClick}
              className="md:hidden text-primary hover:text-foreground hover:bg-muted"
            >
              <Menu size={20} />
            </Button>
          )}

          {/* Title and Business Context */}
          <div className="flex items-center gap-3">
            {businessContext && (
              <div className="flex items-center gap-2">
                {businessContext.icon}
                <Badge
                  variant="outline"
                  className="border-[#e5e1dc] text-[#8a745c] bg-[#f9f7f4] capitalize"
                >
                  {businessContext.type}
                </Badge>
              </div>
            )}
            <div className="text-[#181510] font-medium">
              {title}
            </div>
          </div>
        </div>

        {/* Center Section - Search */}
        {showSearch && (
          <div className="hidden md:flex flex-1 max-w-md mx-8">
            <div className="relative w-full">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#8a745c] h-4 w-4" />
              <input
                type="text"
                placeholder="Search..."
                className="w-full pl-10 pr-4 py-2 bg-[#f1edea] border border-[#e5e1dc] rounded-lg text-sm text-[#181510] placeholder-[#8a745c] focus:outline-none focus:ring-2 focus:ring-[#8a745c] focus:border-transparent"
              />
            </div>
          </div>
        )}

        {/* Right Section */}
        <div className="flex items-center gap-3">
          {/* Notifications */}
          {showNotifications && isAuthenticated && (
            <NotificationBellWrapper />
          )}

          {/* Authentication State */}
          {isAuthenticated && user ? (
            <Popover open={isProfileOpen} onOpenChange={setIsProfileOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="ghost"
                  className="flex items-center gap-2 h-auto p-2 hover:bg-[#f1edea]"
                >
                  <div className="flex items-center gap-2">
                    <div className="h-8 w-8 bg-gradient-to-br from-[#8a745c] to-[#6d5a48] rounded-full flex items-center justify-center text-white text-sm font-medium">
                      {user.name ? user.name.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase()}
                    </div>
                    {/* <div className="hidden md:block text-left">
                      <div className="text-sm font-medium text-[#181510]">
                        {user.name || 'User'}
                      </div>
                      <div className="text-xs text-[#8a745c] capitalize">
                        {user.role}
                      </div>
                    </div> */}
                    <ChevronDown size={16} className="text-[#8a745c]" />
                  </div>
                </Button>
              </PopoverTrigger>
              <PopoverContent
                className="w-64 p-0 bg-[#fbfaf9] border-[#e5e1dc] shadow-lg"
                align="end"
                sideOffset={8}
              >
                {/* Profile Header */}
                <div className="p-4 border-b border-[#e5e1dc]">
                  <div className="flex items-center gap-3">
                    <div className="h-12 w-12 bg-gradient-to-br from-[#8a745c] to-[#6d5a48] rounded-full flex items-center justify-center text-white text-lg font-medium">
                      {user.name ? user.name.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <div className="font-medium text-[#181510]">
                        {user.name || 'User'}
                      </div>
                      <div className="text-sm text-[#8a745c]">
                        {user.email}
                      </div>
                      <Badge
                        variant="outline"
                        className="mt-1 text-xs border-[#e5e1dc] text-[#8a745c] bg-[#f9f7f4] capitalize"
                      >
                        {user.role?.name || 'User'}
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* Menu Items */}
                <div className="py-2">
                  {businessContext?.shopSlug && businessContext?.branchSlug ? (
                    <>
                      <Link
                        href={`/app/restaurant/${businessContext.shopSlug}/${businessContext.branchSlug}/profile`}
                        className="flex items-center gap-3 px-4 py-2 text-sm text-[#181510] hover:bg-[#f1edea] transition-colors"
                        onClick={() => setIsProfileOpen(false)}
                      >
                        <User size={16} className="text-[#8a745c]" />
                        Profile Settings
                      </Link>

                      <Link
                        href={`/app/restaurant/${businessContext.shopSlug}/${businessContext.branchSlug}/settings`}
                        className="flex items-center gap-3 px-4 py-2 text-sm text-[#181510] hover:bg-[#f1edea] transition-colors"
                        onClick={() => setIsProfileOpen(false)}
                      >
                        <Settings size={16} className="text-[#8a745c]" />
                        Business Settings
                      </Link>

                      <Link
                        href={`/app/restaurant/${businessContext.shopSlug}/${businessContext.branchSlug}/billing`}
                        className="flex items-center gap-3 px-4 py-2 text-sm text-[#181510] hover:bg-[#f1edea] transition-colors"
                        onClick={() => setIsProfileOpen(false)}
                      >
                        <CreditCard size={16} className="text-[#8a745c]" />
                        Billing & Plans
                      </Link>

                      <Link
                        href={`/app/restaurant/${businessContext.shopSlug}/${businessContext.branchSlug}/help`}
                        className="flex items-center gap-3 px-4 py-2 text-sm text-[#181510] hover:bg-[#f1edea] transition-colors"
                        onClick={() => setIsProfileOpen(false)}
                      >
                        <HelpCircle size={16} className="text-[#8a745c]" />
                        Help & Support
                      </Link>
                    </>
                  ) : (
                    <>
                      <Link
                        href="/app/profile"
                        className="flex items-center gap-3 px-4 py-2 text-sm text-[#181510] hover:bg-[#f1edea] transition-colors"
                        onClick={() => setIsProfileOpen(false)}
                      >
                        <User size={16} className="text-[#8a745c]" />
                        Profile Settings
                      </Link>

                      <Link
                        href="/app/billing"
                        className="flex items-center gap-3 px-4 py-2 text-sm text-[#181510] hover:bg-[#f1edea] transition-colors"
                        onClick={() => setIsProfileOpen(false)}
                      >
                        <CreditCard size={16} className="text-[#8a745c]" />
                        Billing & Plans
                      </Link>

                      <Link
                        href="/app/help"
                        className="flex items-center gap-3 px-4 py-2 text-sm text-[#181510] hover:bg-[#f1edea] transition-colors"
                        onClick={() => setIsProfileOpen(false)}
                      >
                        <HelpCircle size={16} className="text-[#8a745c]" />
                        Help & Support
                      </Link>
                    </>
                  )}
                </div>

                {/* Logout */}
                <div className="border-t border-border py-2">
                  <button
                    onClick={handleLogout}
                    className="flex w-full items-center gap-3 px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors"
                  >
                    <LogOut size={16} className="text-muted-foreground" />
                    Sign Out
                  </button>
                </div>
              </PopoverContent>
            </Popover>
          ) : (
            /* Not Authenticated */
            <div className="flex items-center gap-2">
              <Link href="/login">
                <Button
                  variant="outline"
                  size="sm"
                  className="border-border text-foreground hover:bg-muted"
                >
                  Sign In
                </Button>
              </Link>
              <Link href="/register">
                <Button
                  size="sm"
                >
                  Sign Up
                </Button>
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
