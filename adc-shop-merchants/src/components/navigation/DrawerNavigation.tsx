'use client';

import React, { useState } from 'react';
import { Link } from '@/i18n/navigation';
import { usePathname } from '@/i18n/navigation';
import { NavigationItem } from '@/lib/types/navigation';
import { Menu, X, Search, Bell, PanelLeft } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
  DrawerClose,
  DrawerFooter
} from '@/components/ui/drawer';
import ProfileMenu from '@/components/navigation/ProfileMenu';

interface DrawerNavigationProps {
  navItems?: NavigationItem[];
  showIcons?: boolean;
}

export default function DrawerNavigation({ navItems = [], showIcons = true }: DrawerNavigationProps) {
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <Drawer open={isOpen} onOpenChange={setIsOpen}>
        {/* <DrawerTrigger asChild>
          <button className="absolute top-3 left-3 text-[#181510] hover:text-[#8a745c] transition-colors z-10">
            <Menu size={24} />
          </button>
        </DrawerTrigger> */}
        <DrawerContent className="bg-[#fbfaf9] p-0 h-full">
          <DrawerHeader className="p-4 border-b border-[#f1edea]">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="size-4">
                  <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M44 11.2727C44 14.0109 39.8386 16.3957 33.69 17.6364C39.8386 18.877 44 21.2618 44 24C44 26.7382 39.8386 29.123 33.69 30.3636C39.8386 31.6043 44 33.9891 44 36.7273C44 40.7439 35.0457 44 24 44C12.9543 44 4 40.7439 4 36.7273C4 33.9891 8.16144 31.6043 14.31 30.3636C8.16144 29.123 4 26.7382 4 24C4 21.2618 8.16144 18.877 14.31 17.6364C8.16144 16.3957 4 14.0109 4 11.2727C4 7.25611 12.9543 4 24 4C35.0457 4 44 7.25611 44 11.2727Z"
                      fill="currentColor"
                    ></path>
                  </svg>
                </div>
                <DrawerTitle className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em]">
                  Table Manager
                </DrawerTitle>
              </div>
              <DrawerClose className="text-[#8a745c] hover:text-[#181510] transition-colors">
                <X size={18} />
              </DrawerClose>
            </div>
          </DrawerHeader>

          <div className="px-4 py-3">
            <div className="flex w-full items-stretch rounded-lg h-10 bg-muted">
              <div className="text-muted-foreground flex border-none items-center justify-center pl-4 rounded-l-lg border-r-0">
                <Search size={18} />
              </div>
              <input
                placeholder="Search"
                className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-lg text-foreground focus:outline-0 focus:ring-0 border-none bg-muted focus:border-none h-full placeholder:text-muted-foreground px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
                defaultValue=""
              />
            </div>
          </div>

          <nav className="flex-1 overflow-y-auto py-4 px-4">
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
              {navItems.map((item) => (
                <DrawerClose key={item.href} asChild>
                  <Link
                    href={item.href}
                    className={cn(
                      'flex flex-col items-center justify-center p-4 rounded-lg text-sm font-medium transition-colors min-h-[80px]',
                      pathname === item.href
                        ? 'bg-[#f1edea] text-[#181510] font-bold'
                        : 'text-[#8a745c] hover:bg-[#f1edea] hover:text-[#181510]'
                    )}
                  >
                    {showIcons && item.icon && (
                      <span className="text-current mb-2 text-xl">
                        {item.icon}
                      </span>
                    )}
                    <span className="text-center text-xs">{item.name}</span>
                  </Link>
                </DrawerClose>
              ))}
            </div>
          </nav>

          <DrawerFooter className="p-4 border-t border-[#f1edea] flex justify-between items-center">
            <ProfileMenu userName="John Doe" userRole="Restaurant Owner" />
            <button className="text-[#8a745c] hover:text-[#181510] transition-colors">
              <Bell size={18} />
            </button>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>

      {/* Fixed Drawer Control Button */}
      <Drawer open={isOpen} onOpenChange={setIsOpen}>
        <DrawerTrigger asChild>
          <button
            className="fixed bottom-6 right-6 z-50 flex items-center justify-center w-12 h-12 rounded-full bg-primary text-primary-foreground shadow-lg hover:bg-primary/90 transition-all duration-300 focus:outline-none"
            aria-label="Open navigation drawer"
          >
            <PanelLeft size={20} />
          </button>
        </DrawerTrigger>
        <DrawerContent className="bg-background p-0 h-full">
          <DrawerHeader className="p-4 border-b border-border">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="size-4">
                  <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M44 11.2727C44 14.0109 39.8386 16.3957 33.69 17.6364C39.8386 18.877 44 21.2618 44 24C44 26.7382 39.8386 29.123 33.69 30.3636C39.8386 31.6043 44 33.9891 44 36.7273C44 40.7439 35.0457 44 24 44C12.9543 44 4 40.7439 4 36.7273C4 33.9891 8.16144 31.6043 14.31 30.3636C8.16144 29.123 4 26.7382 4 24C4 21.2618 8.16144 18.877 14.31 17.6364C8.16144 16.3957 4 14.0109 4 11.2727C4 7.25611 12.9543 4 24 4C35.0457 4 44 7.25611 44 11.2727Z"
                      fill="currentColor"
                    ></path>
                  </svg>
                </div>
                <DrawerTitle className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em]">
                  Table Manager
                </DrawerTitle>
              </div>
              <DrawerClose className="text-[#8a745c] hover:text-[#181510] transition-colors">
                <X size={18} />
              </DrawerClose>
            </div>
          </DrawerHeader>

          <div className="px-4 py-3">
            <div className="flex w-full items-stretch rounded-lg h-10 bg-muted">
              <div className="text-muted-foreground flex border-none items-center justify-center pl-4 rounded-l-lg border-r-0">
                <Search size={18} />
              </div>
              <input
                placeholder="Search"
                className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-lg text-foreground focus:outline-0 focus:ring-0 border-none bg-muted focus:border-none h-full placeholder:text-muted-foreground px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
                defaultValue=""
              />
            </div>
          </div>

          <nav className="flex-1 overflow-y-auto py-2 px-4">
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
              {navItems.map((item) => (
                <DrawerClose key={item.href} asChild>
                  <Link
                    href={item.href}
                    className={cn(
                      'flex flex-col items-center justify-center p-4 rounded-lg text-sm font-medium transition-colors min-h-[80px]',
                      pathname === item.href
                        ? 'bg-[#f1edea] text-[#181510] font-bold'
                        : 'text-[#8a745c] hover:bg-[#f1edea] hover:text-[#181510]'
                    )}
                  >
                    {showIcons && item.icon && (
                      <span className="text-current mb-2 text-xl">
                        {item.icon}
                      </span>
                    )}
                    <span className="text-center text-xs">{item.name}</span>
                  </Link>
                </DrawerClose>
              ))}
            </div>
          </nav>

          {/* <DrawerFooter className="p-4 border-t border-[#f1edea] flex justify-between items-center">
            <ProfileMenu userName="John Doe" userRole="Restaurant Owner" />
            <button className="text-[#8a745c] hover:text-[#181510] transition-colors">
              <Bell size={18} />
            </button>
          </DrawerFooter> */}
        </DrawerContent>
      </Drawer>
    </>
  );
}
