'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from '@/i18n/navigation';
import { usePathname } from '@/i18n/navigation';
import { NavigationItem } from '@/lib/types/navigation';
import { ExpandableTabs } from '@/components/ui/expandable-tabs-animated';

interface ExpandableHeaderNavigationProps {
  navItems?: NavigationItem[];
  showIcons?: boolean;
  autoHide?: boolean;
}

export default function ExpandableHeaderNavigation({
  navItems = [],
  showIcons = true,
  autoHide = false
}: ExpandableHeaderNavigationProps) {
  const pathname = usePathname();
  const router = useRouter();

  // Convert NavigationItem[] to the format expected by ExpandableTabs
  const tabs = navItems.map(item => ({
    title: item.name,
    icon: item.icon, // Pass the React element directly
    href: item.href,
  }));

  // Find the active tab index based on the current pathname
  const activeTabIndex = tabs.findIndex(tab => pathname === tab.href);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(
    activeTabIndex >= 0 ? activeTabIndex : null
  );

  // Update selected index when pathname changes
  useEffect(() => {
    const newActiveIndex = tabs.findIndex(tab => pathname === tab.href);
    setSelectedIndex(newActiveIndex >= 0 ? newActiveIndex : null);
  }, [pathname, tabs]);

  const handleTabClick = (tab: any, index: number) => {
    if (tab.href) {
      router.push(tab.href);
    }
  };

  const handleTabChange = (index: number | null) => {
    setSelectedIndex(index);
  };

  return (
    <header className="flex flex-col border-b border-solid border-b-[#f1edea] px-4 py-3">
      <ExpandableTabs
        tabs={tabs}
        className="bg-[#f1edea] border-[#e5e1dc]"
        activeColor="text-[#181510]"
        activeIndex={selectedIndex}
        onChange={handleTabChange}
        onTabClick={handleTabClick}
      />
    </header>
  );
}
