import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'

/**
 * Exchange NextAuth JWT for backend API token
 * This endpoint should be called after successful NextAuth login
 */
export async function POST(request: NextRequest) {
  try {
    // Get NextAuth JWT token from the request
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    })

    if (!token) {
      return NextResponse.json(
        { error: 'No authentication token found' },
        { status: 401 }
      )
    }

    // Call your Golang backend to exchange the user info for a backend token
    const backendUrl = process.env.BACKEND_API_URL || 'http://localhost:8080/api/v1'
    
    try {
      const response = await fetch(`${backendUrl}/auth/exchange-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: token.sub,
          email: token.email,
          name: token.name,
          role: token.role || 'user',
        }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || `Backend responded with ${response.status}`)
      }

      const backendData = await response.json()
      
      // Return the backend token
      return NextResponse.json({
        success: true,
        token: backendData.token,
        user: {
          id: token.sub,
          email: token.email,
          name: token.name,
          role: token.role,
        },
      })

    } catch (backendError) {
      console.error('Backend token exchange failed:', backendError)
      
      // If backend is not available, we can still proceed with user headers
      // This allows the frontend to work even if backend token exchange fails
      return NextResponse.json({
        success: true,
        token: null, // No backend token available
        user: {
          id: token.sub,
          email: token.email,
          name: token.name,
          role: token.role,
        },
        warning: 'Backend token exchange failed, using user headers only'
      })
    }

  } catch (error) {
    console.error('Token exchange error:', error)
    return NextResponse.json(
      { error: 'Failed to exchange token' },
      { status: 500 }
    )
  }
}
