/**
 * Generic API proxy route for all backend services
 * Handles all HTTP methods and forwards requests to the backend API
 * Uses the [...services] catch-all pattern to proxy any API route
 *
 * Note: Auth routes (/api/auth/*) are handled separately and won't reach this proxy
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/nextauth.config';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';
import { handleApiResponse } from '@/lib/fetch/fetchClient';

// Backend API configuration
// const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:8080/api/v1';

/**
 * Generic handler for all HTTP methods
 */
async function handleRequest(
  request: NextRequest,
  { params }: { params: Promise<{ services: string[] }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const resolvedParams = await params;
    const servicePath = resolvedParams.services.join('/');
    const { searchParams } = new URL(request.url);
    const method = request.method;

    // Skip auth routes - they should be handled by their specific route handlers
    if (servicePath.startsWith('auth/')) {
      return NextResponse.json(
        { error: 'Auth routes should not be proxied through this handler' },
        { status: 400 }
      );
    }

    // Build the backend URL
    let backendUrl = `/${servicePath}`;

    // Add query parameters if they exist
    if (searchParams.toString()) {
      backendUrl += `?${searchParams.toString()}`;
    }

    console.log('Generic API proxy:', {
      method,
      servicePath,
      backendUrl,
      queryParams: Object.fromEntries(searchParams),
      hasSession: !!session
    });

    // Prepare request options
    const requestOptions: any = {
      method,
      headers: {},
    };

    // Add body for methods that support it
    if (['POST', 'PUT', 'PATCH'].includes(method)) {
      try {
        const contentType = request.headers.get('content-type');

        if (contentType?.includes('multipart/form-data')) {
          // For multipart form data (file uploads), pass the FormData directly
          const formData = await request.formData();
          requestOptions.body = formData;
          // Don't set Content-Type header - let the browser set it with boundary
        } else {
          // For JSON and other content types
          const body = await request.text();
          if (body) {
            requestOptions.body = body;
            requestOptions.headers['Content-Type'] = contentType || 'application/json';
          }
        }
      } catch (error) {
        console.warn('Failed to read request body:', error);
      }
    }

    // Forward request to backend using serverFetchClient which handles auth
    const response = await serverFetchClient(backendUrl, request, requestOptions);

    // Handle the response
    if (method === 'DELETE' && response.ok) {
      // For DELETE requests, return success message
      return NextResponse.json({ success: true });
    }

    // For other methods, return the data
    const data = await handleApiResponse(response);

    // Determine status code based on method
    let statusCode = 200;
    if (method === 'POST') {
      statusCode = 201;
    }

    return NextResponse.json(data, { status: statusCode });
  } catch (error) {
    console.error(`Error in ${request.method} /api/[...services]:`, error);

    // Determine error status - ensure it's a valid HTTP status code
    let status = 500;
    if (error instanceof Error && 'status' in error) {
      const errorStatus = (error as any).status;
      // Ensure status is a valid HTTP status code (200-599)
      if (errorStatus >= 200 && errorStatus <= 599) {
        status = errorStatus;
      } else if (errorStatus === 0) {
        // Network errors typically have status 0, map to 503 Service Unavailable
        status = 503;
      }
    }

    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : `Failed to ${request.method.toLowerCase()} data`
      },
      { status }
    );
  }
}

// Export all HTTP method handlers
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ services: string[] }> }
) {
  return handleRequest(request, context);
}

export async function POST(
  request: NextRequest,
  context: { params: Promise<{ services: string[] }> }
) {
  return handleRequest(request, context);
}

export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ services: string[] }> }
) {
  return handleRequest(request, context);
}

export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ services: string[] }> }
) {
  return handleRequest(request, context);
}

export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ services: string[] }> }
) {
  return handleRequest(request, context);
}
