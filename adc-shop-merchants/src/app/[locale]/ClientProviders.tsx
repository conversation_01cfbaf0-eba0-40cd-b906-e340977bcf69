'use client';

import { NextIntlClientProvider } from 'next-intl';
import { PropsWithChildren } from 'react';
import { Toaster } from 'sonner';
import { Provider } from 'react-redux';
import { store } from '@/lib/redux/store';
import { AppearanceProvider } from '@/lib/context/AppearanceContext';
import NextAuthProvider from '@/components/auth/NextAuthProvider';
import AuthSyncProvider from '@/components/auth/AuthSyncProvider';
import NoSSR from '@/components/common/NoSSR';

type ClientProvidersProps = PropsWithChildren<{
  locale: string;
  messages: Record<string, any>;
}>;

export default function ClientProviders({
  children,
  locale,
  messages
}: ClientProvidersProps) {
  return (
    <NextAuthProvider>
      <Provider store={store}>
        <AuthSyncProvider>
          <NoSSR fallback={<div suppressHydrationWarning />}>
            <AppearanceProvider>
              <NextIntlClientProvider locale={locale} messages={messages} timeZone="Asia/Bangkok">
                {children}
                <Toaster position="top-right" richColors />
              </NextIntlClientProvider>
            </AppearanceProvider>
          </NoSSR>
        </AuthSyncProvider>
      </Provider>
    </NextAuthProvider>
  );
}
