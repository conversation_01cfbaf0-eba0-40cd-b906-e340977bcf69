'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import {
  BarChart3,
  TrendingUp,
  DollarSign,
  Users,
  ShoppingCart,
  Calendar,
  Download,
  Filter,
  RefreshCw,
  Eye,
  FileText,
  Pie<PERSON>hart,
  LineChart,
  Activity
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Types
interface ReportCard {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  category: 'sales' | 'customers' | 'inventory' | 'staff' | 'financial' | 'operations';
  href: string;
  lastUpdated: Date;
  status: 'ready' | 'generating' | 'error';
  size?: string;
}

interface QuickStat {
  label: string;
  value: string;
  change: string;
  trend: 'up' | 'down' | 'neutral';
  icon: React.ReactNode;
}

export default function ReportsPage() {
  const t = useTranslations('reports');

  // State
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);

  // Mock data
  const quickStats: QuickStat[] = [
    {
      label: 'Total Revenue',
      value: '$24,580',
      change: '+12.5%',
      trend: 'up',
      icon: <DollarSign className="w-5 h-5" />
    },
    {
      label: 'Orders Today',
      value: '156',
      change: '+8.2%',
      trend: 'up',
      icon: <ShoppingCart className="w-5 h-5" />
    },
    {
      label: 'Active Customers',
      value: '1,247',
      change: '+5.1%',
      trend: 'up',
      icon: <Users className="w-5 h-5" />
    },
    {
      label: 'Avg Order Value',
      value: '$32.45',
      change: '-2.3%',
      trend: 'down',
      icon: <TrendingUp className="w-5 h-5" />
    }
  ];

  const reportCards: ReportCard[] = [
    {
      id: 'sales-overview',
      title: 'Sales Overview',
      description: 'Comprehensive sales analytics and revenue trends',
      icon: <BarChart3 className="w-6 h-6" />,
      category: 'sales',
      href: '/app/reports/sales',
      lastUpdated: new Date(Date.now() - 30 * 60 * 1000),
      status: 'ready',
      size: '2.4 MB'
    },
    {
      id: 'customer-analytics',
      title: 'Customer Analytics',
      description: 'Customer behavior, demographics, and retention metrics',
      icon: <Users className="w-6 h-6" />,
      category: 'customers',
      href: '/app/reports/customers',
      lastUpdated: new Date(Date.now() - 45 * 60 * 1000),
      status: 'ready',
      size: '1.8 MB'
    },
    {
      id: 'inventory-report',
      title: 'Inventory Report',
      description: 'Stock levels, usage patterns, and reorder recommendations',
      icon: <Activity className="w-6 h-6" />,
      category: 'inventory',
      href: '/app/reports/inventory',
      lastUpdated: new Date(Date.now() - 2 * 60 * 60 * 1000),
      status: 'ready',
      size: '3.1 MB'
    },
    {
      id: 'financial-summary',
      title: 'Financial Summary',
      description: 'P&L, expenses, profit margins, and financial KPIs',
      icon: <DollarSign className="w-6 h-6" />,
      category: 'financial',
      href: '/app/reports/financial',
      lastUpdated: new Date(Date.now() - 60 * 60 * 1000),
      status: 'ready',
      size: '1.2 MB'
    },
    {
      id: 'staff-performance',
      title: 'Staff Performance',
      description: 'Employee productivity, schedules, and performance metrics',
      icon: <Users className="w-6 h-6" />,
      category: 'staff',
      href: '/app/reports/staff',
      lastUpdated: new Date(Date.now() - 3 * 60 * 60 * 1000),
      status: 'ready',
      size: '890 KB'
    },
    {
      id: 'operations-dashboard',
      title: 'Operations Dashboard',
      description: 'Kitchen efficiency, order fulfillment, and operational metrics',
      icon: <Activity className="w-6 h-6" />,
      category: 'operations',
      href: '/app/reports/operations',
      lastUpdated: new Date(Date.now() - 15 * 60 * 1000),
      status: 'ready',
      size: '2.7 MB'
    },
    {
      id: 'menu-analysis',
      title: 'Menu Analysis',
      description: 'Item popularity, profitability, and menu optimization insights',
      icon: <PieChart className="w-6 h-6" />,
      category: 'sales',
      href: '/app/reports/menu',
      lastUpdated: new Date(Date.now() - 4 * 60 * 60 * 1000),
      status: 'ready',
      size: '1.5 MB'
    },
    {
      id: 'marketing-roi',
      title: 'Marketing ROI',
      description: 'Campaign performance, customer acquisition, and marketing metrics',
      icon: <TrendingUp className="w-6 h-6" />,
      category: 'customers',
      href: '/app/reports/marketing',
      lastUpdated: new Date(Date.now() - 6 * 60 * 60 * 1000),
      status: 'generating',
      size: 'Generating...'
    }
  ];

  const categories = [
    { id: 'all', label: 'All Reports', count: reportCards.length },
    { id: 'sales', label: 'Sales', count: reportCards.filter(r => r.category === 'sales').length },
    { id: 'customers', label: 'Customers', count: reportCards.filter(r => r.category === 'customers').length },
    { id: 'financial', label: 'Financial', count: reportCards.filter(r => r.category === 'financial').length },
    { id: 'inventory', label: 'Inventory', count: reportCards.filter(r => r.category === 'inventory').length },
    { id: 'staff', label: 'Staff', count: reportCards.filter(r => r.category === 'staff').length },
    { id: 'operations', label: 'Operations', count: reportCards.filter(r => r.category === 'operations').length }
  ];

  // Filter reports
  const filteredReports = reportCards.filter(report => {
    const matchesCategory = selectedCategory === 'all' || report.category === selectedCategory;
    const matchesSearch = report.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         report.description.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  const formatTimestamp = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));

    if (minutes < 60) return `${minutes} minutes ago`;
    if (hours < 24) return `${hours} hours ago`;
    return date.toLocaleDateString();
  };

  const getStatusColor = (status: ReportCard['status']) => {
    switch (status) {
      case 'ready': return 'text-green-600 bg-green-50';
      case 'generating': return 'text-yellow-600 bg-yellow-50';
      case 'error': return 'text-red-600 bg-red-50';
    }
  };

  const getStatusText = (status: ReportCard['status']) => {
    switch (status) {
      case 'ready': return 'Ready';
      case 'generating': return 'Generating';
      case 'error': return 'Error';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <BarChart3 className="w-8 h-8 text-blue-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Reports & Analytics</h1>
            <p className="text-gray-600">Comprehensive business insights and data analysis</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <button className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
            <RefreshCw className="w-4 h-4" />
            Refresh
          </button>
          <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <Download className="w-4 h-4" />
            Export All
          </button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {quickStats.map((stat, index) => (
          <div key={index} className="bg-card p-6 rounded-lg border border-border">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-50 text-blue-700 rounded-lg dark:bg-blue-950 dark:text-blue-300">
                  {stat.icon}
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{stat.label}</p>
                  <p className="text-2xl font-bold text-foreground">{stat.value}</p>
                </div>
              </div>
              <div className={cn(
                'flex items-center gap-1 text-sm font-medium',
                stat.trend === 'up' ? 'text-green-700 dark:text-green-300' :
                stat.trend === 'down' ? 'text-red-700 dark:text-red-300' : 'text-muted-foreground'
              )}>
                <TrendingUp className={cn(
                  'w-4 h-4',
                  stat.trend === 'down' && 'rotate-180'
                )} />
                {stat.change}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <input
            type="text"
            placeholder="Search reports..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div className="flex gap-2 overflow-x-auto">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={cn(
                'flex items-center gap-2 px-4 py-2 rounded-lg whitespace-nowrap transition-colors',
                selectedCategory === category.id
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-300'
              )}
            >
              {category.label}
              <span className={cn(
                'px-2 py-0.5 text-xs rounded-full',
                selectedCategory === category.id
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-600'
              )}>
                {category.count}
              </span>
            </button>
          ))}
        </div>
      </div>

      {/* Reports Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredReports.map((report) => (
          <div key={report.id} className="bg-card rounded-lg border border-border hover:shadow-md transition-shadow">
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-50 text-blue-700 rounded-lg dark:bg-blue-950 dark:text-blue-300">
                    {report.icon}
                  </div>
                  <div>
                    <h3 className="font-semibold text-foreground">{report.title}</h3>
                    <span className={cn(
                      'inline-flex items-center px-2 py-1 text-xs font-medium rounded-full',
                      getStatusColor(report.status)
                    )}>
                      {getStatusText(report.status)}
                    </span>
                  </div>
                </div>
              </div>

              <p className="text-gray-600 text-sm mb-4">{report.description}</p>

              <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                <span>Updated {formatTimestamp(report.lastUpdated)}</span>
                <span>{report.size}</span>
              </div>

              <div className="flex items-center gap-2">
                <Link
                  href={report.href}
                  className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Eye className="w-4 h-4" />
                  View Report
                </Link>
                <button
                  className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Download Report"
                >
                  <Download className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {filteredReports.length === 0 && (
        <div className="text-center py-12">
          <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No reports found</h3>
          <p className="text-gray-600">
            {searchQuery ? 'Try adjusting your search terms' : 'No reports available for the selected category'}
          </p>
        </div>
      )}
    </div>
  );
}
