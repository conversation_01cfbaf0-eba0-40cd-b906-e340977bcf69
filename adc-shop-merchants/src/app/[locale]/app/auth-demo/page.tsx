'use client';

import AuthHeader from '@/components/navigation/AuthHeader';
import AuthStatus from '@/components/auth/AuthStatus';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Code,
  Eye,
  Settings,
  Smartphone,
  Monitor,
  Tablet,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

export default function AuthDemoPage() {
  return (
    <div className="min-h-screen bg-[#fbfaf9]">
      {/* Demo Header */}
      <AuthHeader
        title="Authentication Demo"
        showSearch={true}
        showNotifications={true}
      />

      <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-[#8a745c] rounded-lg">
              <Code className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-[#181510]">
                Authentication Header Demo
              </h1>
              <p className="text-[#8a745c] mt-1">
                Interactive demonstration of the AuthHeader component with authentication state management
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Badge className="bg-green-100 text-green-800 border-green-200">
              <CheckCircle className="h-3 w-3 mr-1" />
              Live Demo
            </Badge>
            <Badge variant="outline" className="border-border text-primary bg-background">
              NextAuth Integration
            </Badge>
            <Badge variant="outline" className="border-border text-primary bg-background">
              Responsive Design
            </Badge>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Authentication Status */}
          <div className="space-y-6">
            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-[#181510]">
                  <Eye className="h-5 w-5 text-[#8a745c]" />
                  Current Authentication State
                </CardTitle>
              </CardHeader>
              <CardContent>
                <AuthStatus />
              </CardContent>
            </Card>

            {/* Features */}
            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-[#181510]">
                  <Settings className="h-5 w-5 text-[#8a745c]" />
                  Header Features
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <div className="font-medium text-[#181510]">Dynamic Authentication State</div>
                      <div className="text-sm text-[#8a745c]">
                        Shows different UI based on user authentication status
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <div className="font-medium text-[#181510]">Business Context Detection</div>
                      <div className="text-sm text-[#8a745c]">
                        Automatically detects restaurant, retail, or service context from URL
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <div className="font-medium text-[#181510]">User Profile Management</div>
                      <div className="text-sm text-[#8a745c]">
                        Complete profile dropdown with settings and logout
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <div className="font-medium text-[#181510]">Search Integration</div>
                      <div className="text-sm text-[#8a745c]">
                        Built-in search functionality with proper styling
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <div className="font-medium text-[#181510]">Notification Center</div>
                      <div className="text-sm text-[#8a745c]">
                        Integrated notification popover for authenticated users
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Responsive Design */}
          <div className="space-y-6">
            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-[#181510]">
                  <Monitor className="h-5 w-5 text-[#8a745c]" />
                  Responsive Design
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Monitor className="h-5 w-5 text-[#8a745c]" />
                    <div>
                      <div className="font-medium text-[#181510]">Desktop</div>
                      <div className="text-sm text-[#8a745c]">Full header with search bar and user details</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <Tablet className="h-5 w-5 text-[#8a745c]" />
                    <div>
                      <div className="font-medium text-[#181510]">Tablet</div>
                      <div className="text-sm text-[#8a745c]">Optimized layout with condensed elements</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <Smartphone className="h-5 w-5 text-[#8a745c]" />
                    <div>
                      <div className="font-medium text-[#181510]">Mobile</div>
                      <div className="text-sm text-[#8a745c]">Compact design with hamburger menu support</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Implementation Notes */}
            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-[#181510]">
                  <Code className="h-5 w-5 text-[#8a745c]" />
                  Implementation Details
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div>
                    <div className="font-medium text-[#181510] mb-1">Component Location</div>
                    <code className="bg-[#f1edea] px-2 py-1 rounded text-[#8a745c]">
                      src/components/navigation/AuthHeader.tsx
                    </code>
                  </div>

                  <div>
                    <div className="font-medium text-[#181510] mb-1">Authentication Hook</div>
                    <code className="bg-[#f1edea] px-2 py-1 rounded text-[#8a745c]">
                      src/hooks/useAuth.ts
                    </code>
                  </div>

                  <div>
                    <div className="font-medium text-[#181510] mb-1">State Management</div>
                    <code className="bg-[#f1edea] px-2 py-1 rounded text-[#8a745c]">
                      Redux Toolkit + NextAuth
                    </code>
                  </div>

                  <div>
                    <div className="font-medium text-[#181510] mb-1">Styling</div>
                    <code className="bg-[#f1edea] px-2 py-1 rounded text-[#8a745c]">
                      Tailwind CSS + Custom Theme
                    </code>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Test Actions */}
            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-[#181510]">
                  <AlertCircle className="h-5 w-5 text-[#8a745c]" />
                  Test Authentication
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <p className="text-sm text-[#8a745c]">
                    Test the authentication flow by visiting the login page or using the sign in/out buttons in the header.
                  </p>

                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      className="bg-[#8a745c] hover:bg-[#6d5a48] text-white"
                      onClick={() => window.location.href = '/login'}
                    >
                      Go to Login
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-[#e5e1dc] text-[#181510] hover:bg-[#f1edea]"
                      onClick={() => window.location.href = '/register'}
                    >
                      Go to Register
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
