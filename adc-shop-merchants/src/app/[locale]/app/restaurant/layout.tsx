'use client';

import { NavigationProvider } from '@/lib/context/NavigationContext';
import { usePathname } from '@/i18n/navigation';
import AuthHeader from '@/components/navigation/AuthHeader';

// Simple layout for restaurant pages
export default function RestaurantLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Check if we're in a branch page
  // Pattern: /app/restaurant/[slugShop]/[slugBranch]
  const isBranchPage = pathname.split('/').filter(Boolean).length > 3;

  return (
    <NavigationProvider>
      <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] font-be-vietnam overflow-x-hidden">
        {/* Only show the header if we're not in a branch page */}
        {!isBranchPage && (
          <AuthHeader
            title="Restaurant Management"
            showSearch={true}
            showNotifications={true}
          />
        )}
        <div className="flex-1">
          <div className="px-4 md:px-6 lg:px-8 py-6">
            <div className="layout-content-container max-w-7xl mx-auto">
              {children}
            </div>
          </div>
        </div>
      </div>
    </NavigationProvider>
  );
}
