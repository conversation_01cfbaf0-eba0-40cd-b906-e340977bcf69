'use client';

import { useState, use } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Save } from 'lucide-react';
import { useGetShopBySlugQuery, useCreateBranchMutation } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { AppLoading } from '@/components/ui/app-loading';
import { toast } from 'sonner';

interface NewBranchPageProps {
  params: Promise<{
    slugShop: string;
  }>;
}

export default function NewBranchPage({ params }: NewBranchPageProps) {
  const { slugShop } = use(params);
  const router = useRouter();

  // Fetch shop data by slug
  const { data: shop, isLoading, isError } = useGetShopBySlugQuery(slugShop);

  // Branch creation mutation
  const [createBranch, { isLoading: isCreating }] = useCreateBranchMutation();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    slug: '',
    address: '',
    phoneNumber: '',
    email: '',
    isMainBranch: false,
    settings: {
      seatingCapacity: 50,
      hasParking: false,
      hasOutdoorSeating: false,
      hasPrivateRooms: false,
      openingHours: {
        monday: { open: '10:00', close: '22:00' },
        tuesday: { open: '10:00', close: '22:00' },
        wednesday: { open: '10:00', close: '22:00' },
        thursday: { open: '10:00', close: '22:00' },
        friday: { open: '10:00', close: '23:00' },
        saturday: { open: '11:00', close: '23:00' },
        sunday: { open: '11:00', close: '22:00' },
      },
      reservationEnabled: true,
      deliveryEnabled: true,
      takeoutEnabled: true,
    }
  });

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (isError || !shop) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href="/app/restaurant">
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurants
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Restaurant Not Found</h1>
          <p className="text-[#8a745c] text-sm">The restaurant you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  // Handle form field changes
  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle settings changes
  const handleSettingsChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      settings: {
        ...prev.settings,
        [field]: value
      }
    }));
  };

  // Handle opening hours changes
  const handleHoursChange = (day: string, type: 'open' | 'close', value: string) => {
    setFormData(prev => ({
      ...prev,
      settings: {
        ...prev.settings,
        openingHours: {
          ...prev.settings.openingHours,
          [day]: {
            ...prev.settings.openingHours[day],
            [type]: value
          }
        }
      }
    }));
  };

  // Generate slug from name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^\w\s]/gi, '')
      .replace(/\s+/g, '-');
  };

  // Handle name change and auto-generate slug
  const handleNameChange = (name: string) => {
    handleChange('name', name);
    if (!formData.slug || formData.slug === generateSlug(formData.name)) {
      handleChange('slug', generateSlug(name));
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate form
      if (!formData.name) {
        toast.error('Branch name is required');
        setIsSubmitting(false);
        return;
      }

      if (!formData.slug) {
        toast.error('Slug is required');
        setIsSubmitting(false);
        return;
      }

      if (!formData.address) {
        toast.error('Address is required');
        setIsSubmitting(false);
        return;
      }

      // Prepare branch data for API (matching backend CreateBranchRequest format)
      const branchData = {
        shopId: shop.id,
        name: formData.name,
        slug: formData.slug,
        email: formData.email || '',
        phone: formData.phoneNumber || '',
        address: {
          street: formData.address,
          city: '',
          state: '',
          zip_code: '',
          country: ''
        },
        business_hours: Object.entries(formData.settings.openingHours).reduce((acc, [day, hours]) => {
          acc[day] = `${hours.open}-${hours.close}`;
          return acc;
        }, {} as Record<string, string>),
        timezone: 'UTC'
      };

      // Call the API to create the branch
      const response = await createBranch(branchData).unwrap();

      toast.success('Branch created successfully');

      // Redirect back to the restaurant page to see the new branch
      router.push(`/app/restaurant/${slugShop}`);
    } catch (error: any) {
      toast.error(error?.data?.error || 'Failed to create branch');
      console.error('Error creating branch:', error);
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to {shop.name}
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Create New Branch</h1>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">
            Add a new branch for {shop.name}
          </p>
        </div>
        <div className="flex items-end">
          <Button
            className="bg-[#8a745c] hover:bg-[#6d5a48] text-white"
            onClick={handleSubmit}
            disabled={isSubmitting || isCreating}
          >
            <Save className="mr-2 h-4 w-4" />
            {(isSubmitting || isCreating) ? 'Creating...' : 'Create Branch'}
          </Button>
        </div>
      </div>

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Branch Information</CardTitle>
            <CardDescription>Basic information about this branch</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Branch Name*</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleNameChange(e.target.value)}
                  placeholder="e.g. Downtown"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="slug">URL Slug*</Label>
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={(e) => handleChange('slug', e.target.value)}
                  placeholder="e.g. downtown"
                  required
                />
                <p className="text-xs text-[#8a745c]">
                  This will be used in your branch's URL: /restaurant/{slugShop}/{formData.slug || 'your-slug'}
                </p>
              </div>
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleChange('description', e.target.value)}
                  placeholder="Describe this branch"
                  rows={3}
                />
              </div>
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="address">Address*</Label>
                <Textarea
                  id="address"
                  value={formData.address}
                  onChange={(e) => handleChange('address', e.target.value)}
                  placeholder="Full address"
                  rows={2}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phoneNumber">Phone Number</Label>
                <Input
                  id="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={(e) => handleChange('phoneNumber', e.target.value)}
                  placeholder="e.g. +****************"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleChange('email', e.target.value)}
                  placeholder="e.g. <EMAIL>"
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="isMainBranch"
                  checked={formData.isMainBranch}
                  onCheckedChange={(checked) => handleChange('isMainBranch', checked)}
                />
                <Label htmlFor="isMainBranch">This is the main branch</Label>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Capacity</CardTitle>
            <CardDescription>Seating and space information</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="seatingCapacity">Seating Capacity</Label>
                <Input
                  id="seatingCapacity"
                  type="number"
                  value={formData.settings.seatingCapacity}
                  onChange={(e) => handleSettingsChange('seatingCapacity', parseInt(e.target.value))}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="hasParking"
                  checked={formData.settings.hasParking}
                  onCheckedChange={(checked) => handleSettingsChange('hasParking', checked)}
                />
                <Label htmlFor="hasParking">Has Parking</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="hasOutdoorSeating"
                  checked={formData.settings.hasOutdoorSeating}
                  onCheckedChange={(checked) => handleSettingsChange('hasOutdoorSeating', checked)}
                />
                <Label htmlFor="hasOutdoorSeating">Has Outdoor Seating</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="hasPrivateRooms"
                  checked={formData.settings.hasPrivateRooms}
                  onCheckedChange={(checked) => handleSettingsChange('hasPrivateRooms', checked)}
                />
                <Label htmlFor="hasPrivateRooms">Has Private Rooms</Label>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Opening Hours</CardTitle>
            <CardDescription>Set your regular business hours</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(formData.settings.openingHours).map(([day, hours]) => (
                <div key={day} className="grid grid-cols-3 gap-4 items-center">
                  <div className="font-medium capitalize">{day}</div>
                  <div className="space-y-2">
                    <Label htmlFor={`${day}-open`}>Open</Label>
                    <Input
                      id={`${day}-open`}
                      type="time"
                      value={hours.open}
                      onChange={(e) => handleHoursChange(day, 'open', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`${day}-close`}>Close</Label>
                    <Input
                      id={`${day}-close`}
                      type="time"
                      value={hours.close}
                      onChange={(e) => handleHoursChange(day, 'close', e.target.value)}
                    />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Service Options</CardTitle>
            <CardDescription>Configure available services for this branch</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="flex items-center space-x-2">
                <Switch
                  id="reservationEnabled"
                  checked={formData.settings.reservationEnabled}
                  onCheckedChange={(checked) => handleSettingsChange('reservationEnabled', checked)}
                />
                <div>
                  <Label htmlFor="reservationEnabled" className="block">Reservations</Label>
                  <p className="text-sm text-[#8a745c]">Allow customers to make reservations</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="deliveryEnabled"
                  checked={formData.settings.deliveryEnabled}
                  onCheckedChange={(checked) => handleSettingsChange('deliveryEnabled', checked)}
                />
                <div>
                  <Label htmlFor="deliveryEnabled" className="block">Delivery</Label>
                  <p className="text-sm text-[#8a745c]">Offer delivery services</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="takeoutEnabled"
                  checked={formData.settings.takeoutEnabled}
                  onCheckedChange={(checked) => handleSettingsChange('takeoutEnabled', checked)}
                />
                <div>
                  <Label htmlFor="takeoutEnabled" className="block">Takeout</Label>
                  <p className="text-sm text-[#8a745c]">Allow customers to order for pickup</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
