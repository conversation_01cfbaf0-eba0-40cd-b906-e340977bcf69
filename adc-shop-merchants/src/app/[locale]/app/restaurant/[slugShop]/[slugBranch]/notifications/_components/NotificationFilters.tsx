import React from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, Filter, X, RotateCcw } from 'lucide-react';
import { NotificationsFilters } from '@/lib/redux/api/endpoints/restaurant/notificationsApi';

interface NotificationFiltersProps {
  filters: NotificationsFilters;
  searchTerm: string;
  onSearchChange: (value: string) => void;
  onSearch: (term: string) => void;
  onTypeFilter: (type: NotificationsFilters['type']) => void;
  onPriorityFilter: (priority: NotificationsFilters['priority']) => void;
  onReadStatusFilter: (isRead: boolean | undefined) => void;
  onDateRangeFilter: (dateRange: NotificationsFilters['dateRange']) => void;
  onResetFilters: () => void;
  stats: {
    totalNotifications: number;
    unreadNotifications: number;
    byType: Record<string, number>;
    byPriority: Record<string, number>;
  };
}

export function NotificationFilters({
  filters,
  searchTerm,
  onSearchChange,
  onSearch,
  onTypeFilter,
  onPriorityFilter,
  onReadStatusFilter,
  onDateRangeFilter,
  onResetFilters,
  stats
}: NotificationFiltersProps) {
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(searchTerm);
  };

  const hasActiveFilters = !!(
    filters.type ||
    filters.priority ||
    filters.isRead !== undefined ||
    filters.search ||
    filters.dateRange
  );

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'order': return '🛒';
      case 'reservation': return '📅';
      case 'review': return '⭐';
      case 'system': return '⚙️';
      case 'staff': return '👥';
      case 'inventory': return '📦';
      case 'payment': return '💳';
      case 'promotion': return '🎉';
      default: return '📢';
    }
  };

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <form onSubmit={handleSearchSubmit} className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search notifications..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button type="submit" variant="outline">
          <Filter className="h-4 w-4" />
        </Button>
        {hasActiveFilters && (
          <Button type="button" variant="outline" onClick={onResetFilters}>
            <RotateCcw className="h-4 w-4" />
          </Button>
        )}
      </form>

      {/* Filter Tabs */}
      <Tabs defaultValue="all" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all" onClick={() => onReadStatusFilter(undefined)}>
            All ({stats.totalNotifications})
          </TabsTrigger>
          <TabsTrigger value="unread" onClick={() => onReadStatusFilter(false)}>
            Unread ({stats.unreadNotifications})
          </TabsTrigger>
          <TabsTrigger value="read" onClick={() => onReadStatusFilter(true)}>
            Read ({stats.totalNotifications - stats.unreadNotifications})
          </TabsTrigger>
          <TabsTrigger value="filters">
            Filters
          </TabsTrigger>
        </TabsList>

        <TabsContent value="filters" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Type Filter */}
            <div>
              <label className="text-sm font-medium mb-2 block">Type</label>
              <Select value={filters.type || 'all'} onValueChange={(value) => onTypeFilter(value === 'all' ? undefined : value as NotificationsFilters['type'])}>
                <SelectTrigger>
                  <SelectValue placeholder="All types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All types</SelectItem>
                  {Object.entries(stats.byType).map(([type, count]) => (
                    <SelectItem key={type} value={type}>
                      <span className="flex items-center gap-2">
                        <span>{getTypeIcon(type)}</span>
                        {type} ({count})
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Priority Filter */}
            <div>
              <label className="text-sm font-medium mb-2 block">Priority</label>
              <Select value={filters.priority || 'all'} onValueChange={(value) => onPriorityFilter(value === 'all' ? undefined : value as NotificationsFilters['priority'])}>
                <SelectTrigger>
                  <SelectValue placeholder="All priorities" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All priorities</SelectItem>
                  {Object.entries(stats.byPriority).map(([priority, count]) => (
                    <SelectItem key={priority} value={priority}>
                      {priority} ({count})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Date Range Filter */}
            <div>
              <label className="text-sm font-medium mb-2 block">Date Range</label>
              <Select value={filters.dateRange || 'all'} onValueChange={(value) => onDateRangeFilter(value === 'all' ? undefined : value as NotificationsFilters['dateRange'])}>
                <SelectTrigger>
                  <SelectValue placeholder="All time" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All time</SelectItem>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="yesterday">Yesterday</SelectItem>
                  <SelectItem value="week">This week</SelectItem>
                  <SelectItem value="month">This month</SelectItem>
                  <SelectItem value="quarter">This quarter</SelectItem>
                  <SelectItem value="year">This year</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* Active Filters */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          {filters.search && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Search: "{filters.search}"
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => onSearch('')}
              />
            </Badge>
          )}
          {filters.type && (
            <Badge variant="secondary" className="flex items-center gap-1">
              <span>{getTypeIcon(filters.type)}</span>
              {filters.type}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => onTypeFilter(undefined)}
              />
            </Badge>
          )}
          {filters.priority && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Priority: {filters.priority}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => onPriorityFilter(undefined)}
              />
            </Badge>
          )}
          {filters.isRead !== undefined && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {filters.isRead ? 'Read' : 'Unread'}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => onReadStatusFilter(undefined)}
              />
            </Badge>
          )}
          {filters.dateRange && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {filters.dateRange}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => onDateRangeFilter(undefined)}
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}
