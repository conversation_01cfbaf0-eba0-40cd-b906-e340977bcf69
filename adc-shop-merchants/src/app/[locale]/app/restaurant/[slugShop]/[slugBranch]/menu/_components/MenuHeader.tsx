'use client';

import React from 'react';
import { Link } from '@/i18n/navigation';
import { Sparkles, Plus } from 'lucide-react';

interface MenuHeaderProps {
  slugShop: string;
  slugBranch: string;
}

export function MenuHeader({ slugShop, slugBranch }: MenuHeaderProps) {
  return (
    <div className="flex flex-wrap justify-between gap-6 p-6 bg-card rounded-lg border border-border shadow-sm">
      <div className="flex min-w-72 flex-col gap-3">
        <div className="flex items-center gap-3">
          <div className="w-2 h-8 bg-primary rounded-full"></div>
          <h1 className="text-foreground tracking-tight text-[32px] font-bold leading-tight">Menu Management</h1>
        </div>
        <p className="text-muted-foreground text-sm font-normal leading-relaxed ml-5">
          Manage your restaurant's menu items, including descriptions, ingredients, pricing, and availability.
        </p>
      </div>
      <div className="flex items-start gap-3">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/menu/add`}>
          <button className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-11 px-6 bg-primary text-primary-foreground text-sm font-semibold leading-normal tracking-[0.015em] gap-2 hover:bg-primary/90 transition-all duration-200 shadow-sm hover:shadow-md">
            <Plus size={16} />
            <span className="truncate">Add Menu Item</span>
          </button>
        </Link>
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/menu/ai-generate`}>
          <button className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-11 px-6 bg-secondary text-secondary-foreground text-sm font-semibold leading-normal tracking-[0.015em] gap-2 hover:bg-secondary/90 transition-all duration-200 shadow-sm hover:shadow-md border border-border">
            <Sparkles size={16} />
            <span className="truncate">AI Generate</span>
          </button>
        </Link>
      </div>
    </div>
  );
}
