import React from 'react'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'

// Mock @/i18n/navigation
jest.mock('@/i18n/navigation', () => ({
  Link: ({ children, href, ...props }: any) => {
    return React.createElement('a', { href, ...props }, children)
  },
}))

// Mock the APIs and hooks
const mockMerchantApi = {
  useGetMerchantsQuery: jest.fn(),
}

const mockUseReservations = {
  reservations: [
    {
      id: 'res-1',
      customerName: '<PERSON>',
      customerPhone: '+**********',
      customerEmail: '<EMAIL>',
      partySize: 4,
      reservationDate: '2024-01-15',
      reservationTime: '19:00',
      status: 'confirmed',
      tableId: 'table-1',
      tableName: 'Table 5',
      specialRequests: 'Window seat preferred',
      createdAt: new Date().toISOString(),
    },
    {
      id: 'res-2',
      customerName: '<PERSON>',
      customerPhone: '+**********',
      customerEmail: '<EMAIL>',
      partySize: 2,
      reservationDate: '2024-01-15',
      reservationTime: '20:00',
      status: 'pending',
      tableId: 'table-2',
      tableName: 'Table 3',
      specialRequests: '',
      createdAt: new Date().toISOString(),
    },
  ],
  upcomingReservations: [
    {
      id: 'res-3',
      customerName: 'Bob Wilson',
      customerPhone: '+1234567892',
      customerEmail: '<EMAIL>',
      partySize: 6,
      reservationDate: '2024-01-16',
      reservationTime: '18:30',
      status: 'confirmed',
      tableId: 'table-3',
      tableName: 'Table 8',
      specialRequests: 'Birthday celebration',
      createdAt: new Date().toISOString(),
    },
  ],
  isLoading: false,
  isError: false,
  error: null,
  refetch: jest.fn(),
}

jest.mock('@/lib/redux/api/endpoints/restaurant/shopApi', () => ({
  useGetMerchantsQuery: () => mockMerchantApi.useGetMerchantsQuery(),
}))

jest.mock('@/hooks/useReservations', () => ({
  useReservations: jest.fn(() => mockUseReservations),
}))

// Mock components
jest.mock('@/components/ui/app-loading', () => ({
  AppLoading: () => <div data-testid="app-loading">Loading...</div>,
}))

// Mock data
const mockMerchant = {
  id: 'merchant-1',
  slug: 'test-restaurant',
  name: 'Test Restaurant',
  branches: [
    {
      id: 'branch-1',
      slug: 'main-branch',
      name: 'Main Branch',
      address: '123 Test St',
    },
  ],
}

// Create a simple mock component for testing
const MockReservationsPage = ({ params }: { params: Promise<{ slugShop: string; slugBranch: string }> | { slugShop: string; slugBranch: string } }) => {
  // Always call hooks in the same order
  const [resolvedParams, setResolvedParams] = React.useState<any>(null)
  const [searchTerm, setSearchTerm] = React.useState('')
  const [activeTab, setActiveTab] = React.useState<'today' | 'upcoming'>('today')

  React.useEffect(() => {
    if (params && typeof params.then === 'function') {
      params.then(setResolvedParams)
    } else {
      setResolvedParams(params)
    }
  }, [params])

  // Always call these hooks
  const { useGetMerchantsQuery } = require('@/lib/redux/api/endpoints/restaurant/shopApi')
  const { useReservations } = require('@/hooks/useReservations')

  const merchantQuery = useGetMerchantsQuery()
  const reservationsData = useReservations()

  if (!resolvedParams) {
    return <div data-testid="app-loading">Loading...</div>
  }

  if (merchantQuery.isLoading || reservationsData.isLoading) {
    return <div data-testid="app-loading">Loading...</div>
  }

  if (reservationsData.isError) {
    return (
      <div>
        <h1>Error Loading Reservations</h1>
        <p>There was an error loading the reservations. Please try again later.</p>
      </div>
    )
  }

  const merchants = merchantQuery.data?.data || []
  const merchant = merchants.find((m: any) => m.slug === resolvedParams.slugShop)
  const branch = merchant?.branches?.find((b: any) => b.slug === resolvedParams.slugBranch)

  if (!merchant || !branch) {
    return (
      <div>
        <h1>Restaurant Not Found</h1>
        <p>The restaurant or branch you are looking for does not exist.</p>
      </div>
    )
  }

  const filteredReservations = (activeTab === 'today' ? reservationsData.reservations : reservationsData.upcomingReservations)
    .filter((reservation: any) =>
      reservation.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      reservation.customerPhone.includes(searchTerm) ||
      reservation.tableName.toLowerCase().includes(searchTerm.toLowerCase())
    )

  return (
    <div className="font-be-vietnam">
      <div className="flex items-center mb-6">
        <a href={`/app/restaurant/${resolvedParams.slugShop}/${resolvedParams.slugBranch}/dashboard`}>
          <button>Back to Dashboard</button>
        </a>
      </div>
      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1>Reservations Management</h1>
          <p>Manage reservations for {merchant.name} - {branch.name}</p>
        </div>
        <div className="flex items-center gap-3">
          <button onClick={reservationsData.refetch}>Refresh</button>
          <a href={`/app/restaurant/${resolvedParams.slugShop}/${resolvedParams.slugBranch}/reservations/new`}>
            <button>Add Reservation</button>
          </a>
        </div>
      </div>

      <div className="mb-6">
        <input
          type="text"
          placeholder="Search reservations..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full max-w-md px-3 py-2 border rounded-md"
        />
      </div>

      <div className="mb-6">
        <div className="flex border-b">
          <button
            onClick={() => setActiveTab('today')}
            className={`px-4 py-2 ${activeTab === 'today' ? 'border-b-2 border-blue-500' : ''}`}
          >
            Today's Reservations ({reservationsData.reservations.length})
          </button>
          <button
            onClick={() => setActiveTab('upcoming')}
            className={`px-4 py-2 ${activeTab === 'upcoming' ? 'border-b-2 border-blue-500' : ''}`}
          >
            Upcoming Reservations ({reservationsData.upcomingReservations.length})
          </button>
        </div>
      </div>

      <div className="space-y-4">
        {filteredReservations.length === 0 ? (
          <div className="text-center py-8">
            <p>No reservations found</p>
          </div>
        ) : (
          filteredReservations.map((reservation: any) => (
            <div key={reservation.id} className="border rounded-lg p-4">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-semibold">{reservation.customerName}</h3>
                  <p className="text-sm text-gray-600">{reservation.customerPhone}</p>
                  <p className="text-sm text-gray-600">{reservation.customerEmail}</p>
                  <div className="mt-2">
                    <span className="text-sm">Party of {reservation.partySize}</span>
                    <span className="mx-2">•</span>
                    <span className="text-sm">{reservation.reservationDate} at {reservation.reservationTime}</span>
                    <span className="mx-2">•</span>
                    <span className="text-sm">{reservation.tableName}</span>
                  </div>
                  {reservation.specialRequests && (
                    <p className="text-sm text-gray-600 mt-1">
                      Special requests: {reservation.specialRequests}
                    </p>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <span className={`px-2 py-1 rounded text-xs ${
                    reservation.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                    reservation.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {reservation.status}
                  </span>
                  <a href={`/app/restaurant/${resolvedParams.slugShop}/${resolvedParams.slugBranch}/reservations/${reservation.id}`}>
                    <button className="text-sm">View Details</button>
                  </a>
                  <a href={`/app/restaurant/${resolvedParams.slugShop}/${resolvedParams.slugBranch}/reservations/${reservation.id}/edit`}>
                    <button className="text-sm">Edit</button>
                  </a>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  )
}

const createMockStore = () => {
  return configureStore({
    reducer: {
      api: () => ({}),
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
      }),
  })
}

const renderWithProvider = (component: React.ReactElement) => {
  const store = createMockStore()
  return render(<Provider store={store}>{component}</Provider>)
}

describe('ReservationsPage', () => {
  const mockParams = {
    slugShop: 'test-restaurant',
    slugBranch: 'main-branch',
  }

  beforeEach(() => {
    jest.clearAllMocks()
    mockMerchantApi.useGetMerchantsQuery.mockReturnValue({
      data: { data: [mockMerchant] },
      isLoading: false,
      error: null,
    })
  })

  describe('Loading States', () => {
    it('shows loading spinner when merchant data is loading', async () => {
      mockMerchantApi.useGetMerchantsQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      })

      renderWithProvider(<MockReservationsPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByTestId('app-loading')).toBeInTheDocument()
      })
    })

    it('shows loading spinner when reservations data is loading', async () => {
      const { useReservations } = require('@/hooks/useReservations')
      useReservations.mockReturnValue({
        ...mockUseReservations,
        isLoading: true,
      })

      renderWithProvider(<MockReservationsPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByTestId('app-loading')).toBeInTheDocument()
      })
    })
  })

  describe('Error States', () => {
    it('shows restaurant not found when merchant does not exist', async () => {
      mockMerchantApi.useGetMerchantsQuery.mockReturnValue({
        data: { data: [] },
        isLoading: false,
        error: null,
      })

      renderWithProvider(<MockReservationsPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Restaurant Not Found')).toBeInTheDocument()
        expect(
          screen.getByText('The restaurant or branch you are looking for does not exist.')
        ).toBeInTheDocument()
      })
    })

    it('shows error message when reservations fail to load', async () => {
      const { useReservations } = require('@/hooks/useReservations')
      useReservations.mockReturnValue({
        ...mockUseReservations,
        isError: true,
        error: { data: { message: 'Failed to load reservations' } },
      })

      renderWithProvider(<MockReservationsPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Error Loading Reservations')).toBeInTheDocument()
        expect(
          screen.getByText('There was an error loading the reservations. Please try again later.')
        ).toBeInTheDocument()
      })
    })
  })

  describe('Reservations Content', () => {
    it('renders reservations management title and description', async () => {
      renderWithProvider(<MockReservationsPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Reservations Management')).toBeInTheDocument()
        expect(screen.getByText('Manage reservations for Test Restaurant - Main Branch')).toBeInTheDocument()
      })
    })

    it('renders back to dashboard button', async () => {
      renderWithProvider(<MockReservationsPage params={mockParams} />)

      await waitFor(() => {
        const backButton = screen.getByText('Back to Dashboard')
        expect(backButton).toBeInTheDocument()
        expect(backButton.closest('a')).toHaveAttribute(
          'href',
          '/app/restaurant/test-restaurant/main-branch/dashboard'
        )
      })
    })

    it('renders add reservation button', async () => {
      renderWithProvider(<MockReservationsPage params={mockParams} />)

      await waitFor(() => {
        const addButton = screen.getByText('Add Reservation')
        expect(addButton).toBeInTheDocument()
        expect(addButton.closest('a')).toHaveAttribute(
          'href',
          '/app/restaurant/test-restaurant/main-branch/reservations/new'
        )
      })
    })

    it('renders search input', async () => {
      renderWithProvider(<MockReservationsPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByPlaceholderText('Search reservations...')).toBeInTheDocument()
      })
    })
  })

  describe('Reservations Display', () => {
    it('displays today\'s reservations by default', async () => {
      renderWithProvider(<MockReservationsPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
        expect(screen.getByText('Jane Smith')).toBeInTheDocument()
        expect(screen.getByText('Party of 4')).toBeInTheDocument()
        expect(screen.getByText('Party of 2')).toBeInTheDocument()
        expect(screen.getByText('Table 5')).toBeInTheDocument()
        expect(screen.getByText('Table 3')).toBeInTheDocument()
      })
    })

    it('switches to upcoming reservations when tab is clicked', async () => {
      const user = userEvent.setup()
      renderWithProvider(<MockReservationsPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Upcoming Reservations (1)')).toBeInTheDocument()
      })

      await user.click(screen.getByText('Upcoming Reservations (1)'))

      await waitFor(() => {
        expect(screen.getByText('Bob Wilson')).toBeInTheDocument()
        expect(screen.getByText('Party of 6')).toBeInTheDocument()
        expect(screen.getByText('Table 8')).toBeInTheDocument()
        expect(screen.getByText('Birthday celebration')).toBeInTheDocument()
        expect(screen.queryByText('John Doe')).not.toBeInTheDocument()
      })
    })

    it('displays reservation status correctly', async () => {
      renderWithProvider(<MockReservationsPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('confirmed')).toBeInTheDocument()
        expect(screen.getByText('pending')).toBeInTheDocument()
      })
    })

    it('displays view and edit links for each reservation', async () => {
      renderWithProvider(<MockReservationsPage params={mockParams} />)

      await waitFor(() => {
        const viewButtons = screen.getAllByText('View Details')
        const editButtons = screen.getAllByText('Edit')

        expect(viewButtons).toHaveLength(2)
        expect(editButtons).toHaveLength(2)
      })
    })
  })

  describe('Search Functionality', () => {
    it('filters reservations by customer name', async () => {
      const user = userEvent.setup()
      renderWithProvider(<MockReservationsPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
        expect(screen.getByText('Jane Smith')).toBeInTheDocument()
      })

      const searchInput = screen.getByPlaceholderText('Search reservations...')
      await user.type(searchInput, 'john')

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
        expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument()
      })
    })

    it('filters reservations by phone number', async () => {
      const user = userEvent.setup()
      renderWithProvider(<MockReservationsPage params={mockParams} />)

      const searchInput = screen.getByPlaceholderText('Search reservations...')
      await user.type(searchInput, '+**********')

      await waitFor(() => {
        expect(screen.getByText('Jane Smith')).toBeInTheDocument()
        expect(screen.queryByText('John Doe')).not.toBeInTheDocument()
      })
    })

    it('shows no results message when search has no matches', async () => {
      const user = userEvent.setup()
      renderWithProvider(<MockReservationsPage params={mockParams} />)

      const searchInput = screen.getByPlaceholderText('Search reservations...')
      await user.type(searchInput, 'nonexistent')

      await waitFor(() => {
        expect(screen.getByText('No reservations found')).toBeInTheDocument()
      })
    })
  })

  describe('User Interactions', () => {
    it('calls refetch when refresh button is clicked', async () => {
      const user = userEvent.setup()
      renderWithProvider(<MockReservationsPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Refresh')).toBeInTheDocument()
      })

      await user.click(screen.getByText('Refresh'))

      expect(mockUseReservations.refetch).toHaveBeenCalled()
    })

    it('highlights active tab correctly', async () => {
      const user = userEvent.setup()
      renderWithProvider(<MockReservationsPage params={mockParams} />)

      // Today's tab should be active by default
      await waitFor(() => {
        const todayTab = screen.getByText('Today\'s Reservations (2)').closest('button')
        const upcomingTab = screen.getByText('Upcoming Reservations (1)').closest('button')

        expect(todayTab).toHaveClass('border-b-2', 'border-blue-500')
        expect(upcomingTab).not.toHaveClass('border-b-2', 'border-blue-500')
      })

      // Switch to upcoming tab
      await user.click(screen.getByText('Upcoming Reservations (1)'))

      await waitFor(() => {
        const todayTab = screen.getByText('Today\'s Reservations (2)').closest('button')
        const upcomingTab = screen.getByText('Upcoming Reservations (1)').closest('button')

        expect(upcomingTab).toHaveClass('border-b-2', 'border-blue-500')
        expect(todayTab).not.toHaveClass('border-b-2', 'border-blue-500')
      })
    })
  })

  describe('Empty States', () => {
    it('shows empty state when no reservations exist', async () => {
      const { useReservations } = require('@/hooks/useReservations')
      useReservations.mockReturnValue({
        ...mockUseReservations,
        reservations: [],
        upcomingReservations: [],
      })

      renderWithProvider(<MockReservationsPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('No reservations found')).toBeInTheDocument()
      })
    })
  })
})
