import React from 'react'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import MenuPage from '../page'

// Mock @/i18n/navigation
jest.mock('@/i18n/navigation', () => ({
  Link: ({ children, href, ...props }: any) => {
    return React.createElement('a', { href, ...props }, children)
  },
}))

// Mock the APIs
const mockMerchantApi = {
  useGetMerchantsQuery: jest.fn(),
}

const mockMenuApi = {
  useGetMenuItemsQuery: jest.fn(),
}

jest.mock('@/lib/redux/api/endpoints/restaurant/shopApi', () => ({
  useGetMerchantsQuery: () => mockMerchantApi.useGetMerchantsQuery(),
}))

jest.mock('@/lib/redux/api/endpoints/menuApi', () => ({
  useGetMenuItemsQuery: () => mockMenuApi.useGetMenuItemsQuery(),
}))

// Mock components
jest.mock('@/components/ui/app-loading', () => ({
  AppLoading: () => <div data-testid="app-loading">Loading...</div>,
}))

jest.mock('@/components/ui/fallback-image', () => ({
  FallbackBackgroundImage: ({ src, fallbackSrc, className, ...props }: any) => (
    <div className={className} data-testid="menu-item-image" {...props}>
      Image: {src || fallbackSrc}
    </div>
  ),
}))

// Mock data
const mockMerchant = {
  id: 'merchant-1',
  slug: 'test-restaurant',
  name: 'Test Restaurant',
  branches: [
    {
      id: 'branch-1',
      slug: 'main-branch',
      name: 'Main Branch',
      address: '123 Test St',
    },
  ],
}

const mockMenuItems = [
  {
    id: 'item-1',
    slug: 'burger-deluxe',
    name: 'Burger Deluxe',
    description: 'A delicious burger with all the fixings',
    category: 'Main Course',
    price: 15.99,
    available: true,
    image: 'https://example.com/burger.jpg',
  },
  {
    id: 'item-2',
    slug: 'caesar-salad',
    name: 'Caesar Salad',
    description: 'Fresh romaine lettuce with caesar dressing',
    category: 'Salads',
    price: 12.50,
    available: false,
    image: 'https://example.com/salad.jpg',
  },
  {
    id: 'item-3',
    slug: 'chocolate-cake',
    name: 'Chocolate Cake',
    description: 'Rich chocolate cake with frosting',
    category: 'Desserts',
    price: 8.99,
    available: true,
    image: null,
  },
]

const createMockStore = () => {
  return configureStore({
    reducer: {
      api: () => ({}),
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
      }),
  })
}

const renderWithProvider = (component: React.ReactElement) => {
  const store = createMockStore()
  return render(<Provider store={store}>{component}</Provider>)
}

describe('MenuPage', () => {
  const mockParams = Promise.resolve({
    slugShop: 'test-restaurant',
    slugBranch: 'main-branch',
  })

  beforeEach(() => {
    jest.clearAllMocks()
    mockMerchantApi.useGetMerchantsQuery.mockReturnValue({
      data: { data: [mockMerchant] },
      isLoading: false,
      error: null,
    })
    mockMenuApi.useGetMenuItemsQuery.mockReturnValue({
      data: mockMenuItems,
      isLoading: false,
      isError: false,
    })
  })

  describe('Loading States', () => {
    it('shows loading spinner when merchant data is loading', async () => {
      mockMerchantApi.useGetMerchantsQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      })

      renderWithProvider(<MenuPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByTestId('app-loading')).toBeInTheDocument()
      })
    })

    it('shows loading spinner when menu items are loading', async () => {
      mockMenuApi.useGetMenuItemsQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        isError: false,
      })

      renderWithProvider(<MenuPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByTestId('app-loading')).toBeInTheDocument()
      })
    })
  })

  describe('Error States', () => {
    it('shows restaurant not found when merchant does not exist', async () => {
      mockMerchantApi.useGetMerchantsQuery.mockReturnValue({
        data: { data: [] },
        isLoading: false,
        error: null,
      })

      renderWithProvider(<MenuPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Restaurant Not Found')).toBeInTheDocument()
        expect(
          screen.getByText('The restaurant or branch you are looking for does not exist.')
        ).toBeInTheDocument()
      })
    })

    it('shows error message when menu items fail to load', async () => {
      mockMenuApi.useGetMenuItemsQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        isError: true,
      })

      renderWithProvider(<MenuPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Error Loading Menu')).toBeInTheDocument()
        expect(
          screen.getByText('There was an error loading the menu items. Please try again later.')
        ).toBeInTheDocument()
      })
    })
  })

  describe('Menu Content', () => {
    it('renders menu management title and description', async () => {
      renderWithProvider(<MenuPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Menu Management')).toBeInTheDocument()
        expect(
          screen.getByText("Manage your restaurant's menu items, including descriptions, ingredients, pricing, and availability.")
        ).toBeInTheDocument()
      })
    })

    it('renders AI Generate button with correct link', async () => {
      renderWithProvider(<MenuPage params={mockParams} />)

      await waitFor(() => {
        const aiButton = screen.getByText('AI Generate')
        expect(aiButton).toBeInTheDocument()
        expect(aiButton.closest('a')).toHaveAttribute(
          'href',
          '/app/restaurant/test-restaurant/main-branch/menu/ai-generate'
        )
      })
    })

    it('renders search input', async () => {
      renderWithProvider(<MenuPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByPlaceholderText('Search menu items')).toBeInTheDocument()
      })
    })

    it('renders view mode toggle buttons', async () => {
      renderWithProvider(<MenuPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Table View')).toBeInTheDocument()
        expect(screen.getByText('Grid View')).toBeInTheDocument()
      })
    })
  })

  describe('Menu Items Display', () => {
    it('displays menu items in grid view by default', async () => {
      renderWithProvider(<MenuPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Burger Deluxe')).toBeInTheDocument()
        expect(screen.getByText('Caesar Salad')).toBeInTheDocument()
        expect(screen.getByText('Chocolate Cake')).toBeInTheDocument()
        expect(screen.getByText('Main Course - $15.99')).toBeInTheDocument()
        expect(screen.getByText('Salads - $12.50')).toBeInTheDocument()
        expect(screen.getByText('Desserts - $8.99')).toBeInTheDocument()
      })
    })

    it('switches to table view when table view button is clicked', async () => {
      const user = userEvent.setup()
      renderWithProvider(<MenuPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Table View')).toBeInTheDocument()
      })

      await user.click(screen.getByText('Table View'))

      await waitFor(() => {
        expect(screen.getByText('Item Name')).toBeInTheDocument()
        expect(screen.getByText('Category')).toBeInTheDocument()
        expect(screen.getByText('Price')).toBeInTheDocument()
        expect(screen.getByText('Availability')).toBeInTheDocument()
        expect(screen.getByText('Actions')).toBeInTheDocument()
      })
    })

    it('displays correct availability status in table view', async () => {
      const user = userEvent.setup()
      renderWithProvider(<MenuPage params={mockParams} />)

      await user.click(screen.getByText('Table View'))

      await waitFor(() => {
        const availableElements = screen.getAllByText('Available')
        const unavailableElements = screen.getAllByText('Unavailable')
        expect(availableElements.length).toBeGreaterThan(0)
        expect(unavailableElements.length).toBeGreaterThan(0)
      })
    })

    it('displays view and edit links in table view', async () => {
      const user = userEvent.setup()
      renderWithProvider(<MenuPage params={mockParams} />)

      await user.click(screen.getByText('Table View'))

      await waitFor(() => {
        const viewLinks = screen.getAllByText('View')
        const editLinks = screen.getAllByText('Edit')

        expect(viewLinks).toHaveLength(3)
        expect(editLinks).toHaveLength(3)

        // Check first item links
        expect(viewLinks[0].closest('a')).toHaveAttribute(
          'href',
          '/app/restaurant/test-restaurant/main-branch/menu/burger-deluxe'
        )
        expect(editLinks[0].closest('a')).toHaveAttribute(
          'href',
          '/app/restaurant/test-restaurant/main-branch/menu/burger-deluxe/edit'
        )
      })
    })
  })

  describe('Search Functionality', () => {
    it('filters menu items by name', async () => {
      const user = userEvent.setup()
      renderWithProvider(<MenuPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Burger Deluxe')).toBeInTheDocument()
        expect(screen.getByText('Caesar Salad')).toBeInTheDocument()
      })

      const searchInput = screen.getByPlaceholderText('Search menu items')
      await user.type(searchInput, 'burger')

      await waitFor(() => {
        expect(screen.getByText('Burger Deluxe')).toBeInTheDocument()
        expect(screen.queryByText('Caesar Salad')).not.toBeInTheDocument()
        expect(screen.queryByText('Chocolate Cake')).not.toBeInTheDocument()
      })
    })

    it('filters menu items by category', async () => {
      const user = userEvent.setup()
      renderWithProvider(<MenuPage params={mockParams} />)

      const searchInput = screen.getByPlaceholderText('Search menu items')
      await user.type(searchInput, 'desserts')

      await waitFor(() => {
        expect(screen.getByText('Chocolate Cake')).toBeInTheDocument()
        expect(screen.queryByText('Burger Deluxe')).not.toBeInTheDocument()
        expect(screen.queryByText('Caesar Salad')).not.toBeInTheDocument()
      })
    })

    it('shows no results message when search has no matches', async () => {
      const user = userEvent.setup()
      renderWithProvider(<MenuPage params={mockParams} />)

      const searchInput = screen.getByPlaceholderText('Search menu items')
      await user.type(searchInput, 'nonexistent')

      await waitFor(() => {
        expect(screen.getByText('No menu items match your search')).toBeInTheDocument()
      })
    })

    it('clears search results when search is cleared', async () => {
      const user = userEvent.setup()
      renderWithProvider(<MenuPage params={mockParams} />)

      const searchInput = screen.getByPlaceholderText('Search menu items')
      await user.type(searchInput, 'burger')

      await waitFor(() => {
        expect(screen.queryByText('Caesar Salad')).not.toBeInTheDocument()
      })

      await user.clear(searchInput)

      await waitFor(() => {
        expect(screen.getByText('Burger Deluxe')).toBeInTheDocument()
        expect(screen.getByText('Caesar Salad')).toBeInTheDocument()
        expect(screen.getByText('Chocolate Cake')).toBeInTheDocument()
      })
    })
  })

  describe('Empty States', () => {
    it('shows empty state when no menu items exist', async () => {
      mockMenuApi.useGetMenuItemsQuery.mockReturnValue({
        data: [],
        isLoading: false,
        isError: false,
      })

      renderWithProvider(<MenuPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('No menu items found')).toBeInTheDocument()
      })
    })

    it('shows empty state in table view when no menu items exist', async () => {
      const user = userEvent.setup()
      mockMenuApi.useGetMenuItemsQuery.mockReturnValue({
        data: [],
        isLoading: false,
        isError: false,
      })

      renderWithProvider(<MenuPage params={mockParams} />)

      await user.click(screen.getByText('Table View'))

      await waitFor(() => {
        expect(screen.getByText('No menu items found')).toBeInTheDocument()
      })
    })
  })

  describe('View Mode Toggle', () => {
    it('highlights active view mode', async () => {
      const user = userEvent.setup()
      renderWithProvider(<MenuPage params={mockParams} />)

      // Grid view should be active by default
      await waitFor(() => {
        const gridButton = screen.getByText('Grid View').closest('button')
        const tableButton = screen.getByText('Table View').closest('button')

        expect(gridButton).toHaveClass('border-b-[#161412]')
        expect(tableButton).toHaveClass('border-b-transparent')
      })

      // Switch to table view
      await user.click(screen.getByText('Table View'))

      await waitFor(() => {
        const gridButton = screen.getByText('Grid View').closest('button')
        const tableButton = screen.getByText('Table View').closest('button')

        expect(tableButton).toHaveClass('border-b-[#161412]')
        expect(gridButton).toHaveClass('border-b-transparent')
      })
    })
  })
})
