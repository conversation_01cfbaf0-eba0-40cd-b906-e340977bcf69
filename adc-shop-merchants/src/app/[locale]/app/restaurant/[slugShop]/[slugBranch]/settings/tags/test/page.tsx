"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { TagSelector } from '@/components/ui/tag-selector';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import {
  useGetTagsQuery,
  useGetTagCategoriesQuery,
  useGetTagAnalyticsQuery,
  useSearchTagsMutation
} from '@/lib/redux/api/endpoints/tagApi';

interface TagTestPageProps {
  params: {
    slugShop: string;
    slugBranch: string;
    locale: string;
  };
}

export default function TagTestPage({ params }: TagTestPageProps) {
  const { slugShop, slugBranch } = params;

  // Test states for different entity types
  const [menuItemTags, setMenuItemTags] = useState<string[]>([]);
  const [reviewTags, setReviewTags] = useState<string[]>([]);
  const [staffTags, setStaffTags] = useState<string[]>([]);
  const [reservationTags, setReservationTags] = useState<string[]>([]);
  const [orderTags, setOrderTags] = useState<string[]>([]);

  // API hooks for testing
  const { data: tagsResponse, isLoading: isLoadingTags } = useGetTagsQuery({
    shopId: slugShop,
    branchId: slugBranch,
    filters: { is_active: true, limit: 50 }
  });

  const { data: categoriesResponse } = useGetTagCategoriesQuery({
    shopId: slugShop,
    branchId: slugBranch,
    filters: { is_active: true }
  });

  const { data: analytics } = useGetTagAnalyticsQuery({
    shopId: slugShop,
    branchId: slugBranch
  });

  const [searchTags] = useSearchTagsMutation();

  const tags = tagsResponse?.tags || [];
  const categories = categoriesResponse?.categories || [];

  const handleTestSearch = async () => {
    try {
      const result = await searchTags({
        shopId: slugShop,
        branchId: slugBranch,
        request: {
          query: 'spicy',
          entity_type: 'menu_item',
          limit: 10
        }
      }).unwrap();

      toast.success(`Search Test Successful - Found ${result.suggestions?.length || 0} suggestions`);
    } catch (error) {
      toast.error("Search Test Failed - Could not perform tag search");
    }
  };

  const clearAllTags = () => {
    setMenuItemTags([]);
    setReviewTags([]);
    setStaffTags([]);
    setReservationTags([]);
    setOrderTags([]);

    toast.success("Tags Cleared - All test tags have been cleared");
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Tag System Test Page</h1>
          <p className="text-muted-foreground">
            Test the centralized tag system across different entity types
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleTestSearch} variant="outline">
            Test Search
          </Button>
          <Button onClick={clearAllTags} variant="outline">
            Clear All Tags
          </Button>
        </div>
      </div>

      {/* System Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Total Tags</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{tags.length}</div>
            <p className="text-xs text-muted-foreground">
              {isLoadingTags ? 'Loading...' : 'Available tags'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Categories</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{categories.length}</div>
            <p className="text-xs text-muted-foreground">Tag categories</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Analytics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics?.total_tags || 0}</div>
            <p className="text-xs text-muted-foreground">Total system tags</p>
          </CardContent>
        </Card>
      </div>

      {/* Tag Testing Interface */}
      <Tabs defaultValue="menu-items" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="menu-items">Menu Items</TabsTrigger>
          <TabsTrigger value="reviews">Reviews</TabsTrigger>
          <TabsTrigger value="staff">Staff</TabsTrigger>
          <TabsTrigger value="reservations">Reservations</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
        </TabsList>

        <TabsContent value="menu-items" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Menu Item Tags</CardTitle>
              <CardDescription>
                Test tag selection for menu items with dietary, style, and popularity categories
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <TagSelector
                shopId={slugShop}
                branchId={slugBranch}
                value={menuItemTags}
                onChange={setMenuItemTags}
                entityType="menu_item"
                placeholder="Add menu item tags (e.g., Spicy, Vegetarian, Popular)..."
                allowCustomTags={true}
                maxTags={10}
              />

              <div className="space-y-2">
                <h4 className="font-medium">Selected Tags ({menuItemTags.length}):</h4>
                <div className="flex flex-wrap gap-2">
                  {menuItemTags.map((tag, index) => (
                    <Badge key={index} variant="outline">{tag}</Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reviews" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Review Tags</CardTitle>
              <CardDescription>
                Test tag selection for customer reviews and feedback categorization
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <TagSelector
                shopId={slugShop}
                branchId={slugBranch}
                value={reviewTags}
                onChange={setReviewTags}
                entityType="review"
                placeholder="Add review tags (e.g., Excellent Service, Great Food, Slow Service)..."
                categoryFilter="feedback"
                allowCustomTags={true}
                maxTags={8}
              />

              <div className="space-y-2">
                <h4 className="font-medium">Selected Tags ({reviewTags.length}):</h4>
                <div className="flex flex-wrap gap-2">
                  {reviewTags.map((tag, index) => (
                    <Badge key={index} variant="outline">{tag}</Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="staff" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Staff Tags</CardTitle>
              <CardDescription>
                Test tag selection for staff skills, certifications, and specialties
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <TagSelector
                shopId={slugShop}
                branchId={slugBranch}
                value={staffTags}
                onChange={setStaffTags}
                entityType="staff"
                placeholder="Add staff tags (e.g., Barista, Sommelier, Food Safety Certified)..."
                categoryFilter="skills"
                allowCustomTags={true}
                maxTags={15}
              />

              <div className="space-y-2">
                <h4 className="font-medium">Selected Tags ({staffTags.length}):</h4>
                <div className="flex flex-wrap gap-2">
                  {staffTags.map((tag, index) => (
                    <Badge key={index} variant="outline">{tag}</Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reservations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Reservation Tags</CardTitle>
              <CardDescription>
                Test tag selection for reservations, occasions, and special requests
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <TagSelector
                shopId={slugShop}
                branchId={slugBranch}
                value={reservationTags}
                onChange={setReservationTags}
                entityType="reservation"
                placeholder="Add reservation tags (e.g., Date Night, Birthday, Window Seat)..."
                categoryFilter="occasion"
                allowCustomTags={true}
                maxTags={6}
              />

              <div className="space-y-2">
                <h4 className="font-medium">Selected Tags ({reservationTags.length}):</h4>
                <div className="flex flex-wrap gap-2">
                  {reservationTags.map((tag, index) => (
                    <Badge key={index} variant="outline">{tag}</Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="orders" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Order Tags</CardTitle>
              <CardDescription>
                Test tag selection for orders, special requests, and dietary needs
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <TagSelector
                shopId={slugShop}
                branchId={slugBranch}
                value={orderTags}
                onChange={setOrderTags}
                entityType="order"
                placeholder="Add order tags (e.g., No Onions, Extra Spicy, Takeout)..."
                categoryFilter="requests"
                allowCustomTags={true}
                maxTags={8}
              />

              <div className="space-y-2">
                <h4 className="font-medium">Selected Tags ({orderTags.length}):</h4>
                <div className="flex flex-wrap gap-2">
                  {orderTags.map((tag, index) => (
                    <Badge key={index} variant="outline">{tag}</Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Available Categories */}
      <Card>
        <CardHeader>
          <CardTitle>Available Categories</CardTitle>
          <CardDescription>
            All tag categories available in the system
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {categories.map((category) => (
              <div key={category.id} className="flex items-center gap-2 p-2 border rounded">
                {category.icon && <span>{category.icon}</span>}
                <div>
                  <div className="font-medium">{category.name}</div>
                  <div className="text-xs text-muted-foreground">{category.slug}</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Test Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Test Summary</CardTitle>
          <CardDescription>
            Overview of all selected tags across entity types
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Total Tags Selected: {
                menuItemTags.length + reviewTags.length + staffTags.length +
                reservationTags.length + orderTags.length
              }</h4>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <h5 className="font-medium text-sm">Menu Items ({menuItemTags.length})</h5>
                <p className="text-xs text-muted-foreground">{menuItemTags.join(', ') || 'None'}</p>
              </div>
              <div>
                <h5 className="font-medium text-sm">Reviews ({reviewTags.length})</h5>
                <p className="text-xs text-muted-foreground">{reviewTags.join(', ') || 'None'}</p>
              </div>
              <div>
                <h5 className="font-medium text-sm">Staff ({staffTags.length})</h5>
                <p className="text-xs text-muted-foreground">{staffTags.join(', ') || 'None'}</p>
              </div>
              <div>
                <h5 className="font-medium text-sm">Reservations ({reservationTags.length})</h5>
                <p className="text-xs text-muted-foreground">{reservationTags.join(', ') || 'None'}</p>
              </div>
              <div>
                <h5 className="font-medium text-sm">Orders ({orderTags.length})</h5>
                <p className="text-xs text-muted-foreground">{orderTags.join(', ') || 'None'}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
