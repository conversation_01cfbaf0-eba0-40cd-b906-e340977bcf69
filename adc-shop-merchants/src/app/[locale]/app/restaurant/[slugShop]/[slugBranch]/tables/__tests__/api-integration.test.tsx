import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import { rest } from 'msw'
import { setupServer } from 'msw/node'
import TablesPage from '../page'
import { shopApi } from '@/lib/redux/api/endpoints/restaurant/shopApi'
import { tablesApi } from '@/lib/redux/api/endpoints/restaurant/tablesApi'
import { reservationsApi } from '@/lib/redux/api/endpoints/restaurant/reservationsApi'

// Mock server setup
const server = setupServer(
  // Merchants API
  rest.get('/api/merchants', (req, res, ctx) => {
    return res(
      ctx.json({
        data: [
          {
            id: 'merchant-1',
            slug: 'test-restaurant',
            name: 'Test Restaurant',
            branches: [
              {
                id: 'branch-1',
                slug: 'main-branch',
                name: 'Main Branch',
                address: '123 Test St',
              },
            ],
          },
        ],
      })
    )
  }),

  // Tables API
  rest.get('/api/merchants/:merchantId/branches/:branchId/tables', (req, res, ctx) => {
    const { merchantId, branchId } = req.params

    if (merchantId === 'merchant-1' && branchId === 'branch-1') {
      return res(
        ctx.json([
          {
            id: 'table-1',
            number: 1,
            name: 'Table 1',
            capacity: 4,
            status: 'available',
            area: 'dining',
          },
          {
            id: 'table-2',
            number: 2,
            name: 'Table 2',
            capacity: 2,
            status: 'occupied',
            area: 'dining',
          },
        ])
      )
    }

    return res(ctx.status(404), ctx.json({ error: 'Not found' }))
  }),

  // Table Areas API
  rest.get('/api/merchants/:merchantId/branches/:branchId/tables/areas', (req, res, ctx) => {
    const { merchantId, branchId } = req.params

    if (merchantId === 'merchant-1' && branchId === 'branch-1') {
      return res(
        ctx.json([
          {
            id: 'area-1',
            name: 'Dining Area',
            description: 'Main dining area',
          },
          {
            id: 'area-2',
            name: 'Outdoor Patio',
            description: 'Outdoor seating area',
          },
        ])
      )
    }

    return res(ctx.status(404), ctx.json({ error: 'Not found' }))
  }),

  // Reservations API
  rest.get('/api/merchants/:merchantId/branches/:branchId/reservations', (req, res, ctx) => {
    const { merchantId, branchId } = req.params

    if (merchantId === 'merchant-1' && branchId === 'branch-1') {
      return res(
        ctx.json({
          data: [
            {
              id: 'reservation-1',
              time: '7:00 PM',
              customerName: 'John Doe',
              partySize: 4,
              tableId: 'table-1',
              tableName: 'Table 1',
              status: 'confirmed',
              date: '2024-01-15',
            },
          ],
        })
      )
    }

    return res(ctx.status(404), ctx.json({ error: 'Not found' }))
  })
)

// Create a real store with the actual API slices
const createTestStore = () => {
  return configureStore({
    reducer: {
      api: shopApi.reducer,
      tablesApi: tablesApi.reducer,
      reservationsApi: reservationsApi.reducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
      }).concat(
        shopApi.middleware,
        tablesApi.middleware,
        reservationsApi.middleware
      ),
  })
}

const renderWithRealStore = (component: React.ReactElement) => {
  const store = createTestStore()
  return render(<Provider store={store}>{component}</Provider>)
}

describe('TablesPage API Integration', () => {
  const mockParams = Promise.resolve({
    slugShop: 'test-restaurant',
    slugBranch: 'main-branch',
  })

  beforeAll(() => {
    server.listen()
  })

  afterEach(() => {
    server.resetHandlers()
  })

  afterAll(() => {
    server.close()
  })

  it('successfully loads data from all APIs', async () => {
    renderWithRealStore(<TablesPage params={mockParams} />)

    // Wait for loading to complete
    await waitFor(
      () => {
        expect(screen.queryByTestId('app-loading')).not.toBeInTheDocument()
      },
      { timeout: 5000 }
    )

    // Check that data from all APIs is displayed
    await waitFor(() => {
      expect(screen.getByText('Tables & Reservations')).toBeInTheDocument()
      expect(screen.getByText('Table 1')).toBeInTheDocument()
      expect(screen.getByText('Table 2')).toBeInTheDocument()
      expect(screen.getByText('Dining Area')).toBeInTheDocument()
    })
  })

  it('handles API errors gracefully', async () => {
    // Override the tables API to return an error
    server.use(
      rest.get('/api/merchants/:merchantId/branches/:branchId/tables', (req, res, ctx) => {
        return res(ctx.status(500), ctx.json({ error: 'Internal server error' }))
      })
    )

    renderWithRealStore(<TablesPage params={mockParams} />)

    await waitFor(
      () => {
        expect(screen.getByText('Error Loading Data')).toBeInTheDocument()
      },
      { timeout: 5000 }
    )
  })

  it('handles network timeouts', async () => {
    // Override APIs to simulate timeout
    server.use(
      rest.get('/api/merchants', (req, res, ctx) => {
        return res(ctx.delay('infinite'))
      })
    )

    renderWithRealStore(<TablesPage params={mockParams} />)

    // Should show loading state
    expect(screen.getByTestId('app-loading')).toBeInTheDocument()

    // After timeout, should still be loading (in real app, would show timeout error)
    await new Promise(resolve => setTimeout(resolve, 1000))
    expect(screen.getByTestId('app-loading')).toBeInTheDocument()
  })

  it('handles empty responses correctly', async () => {
    // Override APIs to return empty data
    server.use(
      rest.get('/api/merchants/:merchantId/branches/:branchId/tables', (req, res, ctx) => {
        return res(ctx.json([]))
      }),
      rest.get('/api/merchants/:merchantId/branches/:branchId/reservations', (req, res, ctx) => {
        return res(ctx.json({ data: [] }))
      })
    )

    renderWithRealStore(<TablesPage params={mockParams} />)

    await waitFor(
      () => {
        expect(screen.queryByTestId('app-loading')).not.toBeInTheDocument()
      },
      { timeout: 5000 }
    )

    // Should show empty states
    await waitFor(() => {
      expect(screen.getByText('No tables found. Add tables to get started.')).toBeInTheDocument()
    })
  })

  it('correctly passes API parameters', async () => {
    let capturedParams: any = {}

    server.use(
      rest.get('/api/merchants/:merchantId/branches/:branchId/tables', (req, res, ctx) => {
        capturedParams = req.params
        return res(ctx.json([]))
      })
    )

    renderWithRealStore(<TablesPage params={mockParams} />)

    await waitFor(() => {
      expect(capturedParams.merchantId).toBe('merchant-1')
      expect(capturedParams.branchId).toBe('branch-1')
    })
  })

  it('handles concurrent API calls correctly', async () => {
    let apiCallCount = 0

    server.use(
      rest.get('/api/merchants', (req, res, ctx) => {
        apiCallCount++
        return res(
          ctx.json({
            data: [
              {
                id: 'merchant-1',
                slug: 'test-restaurant',
                name: 'Test Restaurant',
                branches: [
                  {
                    id: 'branch-1',
                    slug: 'main-branch',
                    name: 'Main Branch',
                  },
                ],
              },
            ],
          })
        )
      }),
      rest.get('/api/merchants/:merchantId/branches/:branchId/tables', (req, res, ctx) => {
        apiCallCount++
        return res(ctx.json([]))
      }),
      rest.get('/api/merchants/:merchantId/branches/:branchId/tables/areas', (req, res, ctx) => {
        apiCallCount++
        return res(ctx.json([]))
      }),
      rest.get('/api/merchants/:merchantId/branches/:branchId/reservations', (req, res, ctx) => {
        apiCallCount++
        return res(ctx.json({ data: [] }))
      })
    )

    renderWithRealStore(<TablesPage params={mockParams} />)

    await waitFor(
      () => {
        expect(screen.queryByTestId('app-loading')).not.toBeInTheDocument()
      },
      { timeout: 5000 }
    )

    // All APIs should have been called
    expect(apiCallCount).toBeGreaterThanOrEqual(4)
  })
})
