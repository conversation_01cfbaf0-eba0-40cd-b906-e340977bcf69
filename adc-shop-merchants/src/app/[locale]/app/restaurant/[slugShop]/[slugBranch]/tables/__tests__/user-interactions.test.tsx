import React from 'react'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import TablesPage from '../page'

// Mock the APIs with successful data
const mockMerchantApi = {
  useGetMerchantsQuery: jest.fn(() => ({
    data: {
      data: [
        {
          id: 'merchant-1',
          slug: 'test-restaurant',
          name: 'Test Restaurant',
          branches: [
            {
              id: 'branch-1',
              slug: 'main-branch',
              name: 'Main Branch',
              address: '123 Test St',
            },
          ],
        },
      ],
    },
    isLoading: false,
    error: null,
  })),
}

const mockTablesApi = {
  useGetTablesQuery: jest.fn(() => ({
    data: [
      {
        id: 'table-1',
        number: 1,
        name: 'Table 1',
        capacity: 4,
        status: 'available',
        area: 'dining',
      },
      {
        id: 'table-2',
        number: 2,
        name: 'Table 2',
        capacity: 2,
        status: 'occupied',
        area: 'outdoor',
      },
    ],
    isLoading: false,
    error: null,
  })),
  useGetTableAreasQuery: jest.fn(() => ({
    data: [
      {
        id: 'area-1',
        name: 'Dining Area',
        description: 'Main dining area',
      },
      {
        id: 'area-2',
        name: 'Outdoor Patio',
        description: 'Outdoor seating area',
      },
    ],
    isLoading: false,
    error: null,
  })),
}

const mockReservationsApi = {
  useGetReservationsQuery: jest.fn(() => ({
    data: {
      data: [
        {
          id: 'reservation-1',
          time: '7:00 PM',
          customerName: 'John Doe',
          partySize: 4,
          tableId: 'table-1',
          tableName: 'Table 1',
          status: 'confirmed',
          date: '2024-01-15',
        },
        {
          id: 'reservation-2',
          time: '8:00 PM',
          customerName: 'Jane Smith',
          partySize: 2,
          tableId: 'table-2',
          tableName: 'Table 2',
          status: 'pending',
          date: '2024-01-15',
        },
      ],
    },
    isLoading: false,
    error: null,
  })),
}

jest.mock('@/lib/redux/api/endpoints/restaurant/shopApi', () => ({
  useGetMerchantsQuery: () => mockMerchantApi.useGetMerchantsQuery(),
}))

jest.mock('@/lib/redux/api/endpoints/restaurant/tablesApi', () => ({
  useGetTablesQuery: () => mockTablesApi.useGetTablesQuery(),
  useGetTableAreasQuery: () => mockTablesApi.useGetTableAreasQuery(),
}))

jest.mock('@/lib/redux/api/endpoints/restaurant/reservationsApi', () => ({
  useGetReservationsQuery: () => mockReservationsApi.useGetReservationsQuery(),
}))

const createMockStore = () => {
  return configureStore({
    reducer: {
      api: () => ({}),
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
      }),
  })
}

const renderWithProvider = (component: React.ReactElement) => {
  const store = createMockStore()
  return render(<Provider store={store}>{component}</Provider>)
}

describe('TablesPage User Interactions', () => {
  const mockParams = Promise.resolve({
    slugShop: 'test-restaurant',
    slugBranch: 'main-branch',
  })

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Tab Navigation', () => {
    it('switches between floor plan and reservations tabs', async () => {
      const user = userEvent.setup()
      renderWithProvider(<TablesPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Floor Plan')).toBeInTheDocument()
        expect(screen.getByText('Reservations')).toBeInTheDocument()
      })

      // Initially should show floor plan
      expect(screen.getByText('Table 1')).toBeInTheDocument()
      expect(screen.getByText('Dining Area')).toBeInTheDocument()

      // Click on reservations tab
      const reservationsTab = screen.getByText('Reservations')
      await user.click(reservationsTab)

      // Should show reservations content
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
        expect(screen.getByText('Jane Smith')).toBeInTheDocument()
      })

      // Click back to floor plan
      const floorPlanTab = screen.getByText('Floor Plan')
      await user.click(floorPlanTab)

      // Should show floor plan content again
      await waitFor(() => {
        expect(screen.getByText('Table 1')).toBeInTheDocument()
        expect(screen.getByText('Dining Area')).toBeInTheDocument()
      })
    })

    it('maintains tab state when switching', async () => {
      const user = userEvent.setup()
      renderWithProvider(<TablesPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Floor Plan')).toBeInTheDocument()
      })

      // Switch to reservations
      await user.click(screen.getByText('Reservations'))

      // Verify reservations content is visible
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
      })

      // Switch back to floor plan
      await user.click(screen.getByText('Floor Plan'))

      // Verify floor plan content is visible again
      await waitFor(() => {
        expect(screen.getByText('Table 1')).toBeInTheDocument()
      })
    })
  })

  describe('Table Interactions', () => {
    it('displays table information correctly', async () => {
      renderWithProvider(<TablesPage params={mockParams} />)

      await waitFor(() => {
        // Check table names
        expect(screen.getByText('Table 1')).toBeInTheDocument()
        expect(screen.getByText('Table 2')).toBeInTheDocument()

        // Check table numbers
        expect(screen.getByText('Table 1')).toBeInTheDocument()
        expect(screen.getByText('Table 2')).toBeInTheDocument()

        // Check capacities
        expect(screen.getByText('Capacity: 4')).toBeInTheDocument()
        expect(screen.getByText('Capacity: 2')).toBeInTheDocument()

        // Check statuses
        expect(screen.getByText('Status: available')).toBeInTheDocument()
        expect(screen.getByText('Status: occupied')).toBeInTheDocument()
      })
    })

    it('groups tables by areas correctly', async () => {
      renderWithProvider(<TablesPage params={mockParams} />)

      await waitFor(() => {
        // Check area headers
        expect(screen.getByText('Dining Area')).toBeInTheDocument()
        expect(screen.getByText('Outdoor Patio')).toBeInTheDocument()

        // Verify tables are under correct areas
        const diningSection = screen.getByText('Dining Area').closest('div')
        const outdoorSection = screen.getByText('Outdoor Patio').closest('div')

        expect(diningSection).toBeInTheDocument()
        expect(outdoorSection).toBeInTheDocument()
      })
    })

    it('shows correct status indicators', async () => {
      renderWithProvider(<TablesPage params={mockParams} />)

      await waitFor(() => {
        // Check for status indicator dots (by class or data-testid if added)
        const statusElements = screen.getAllByText(/Status: (available|occupied|reserved)/)
        expect(statusElements).toHaveLength(2)
      })
    })
  })

  describe('Reservations Interactions', () => {
    it('displays reservation information in table format', async () => {
      const user = userEvent.setup()
      renderWithProvider(<TablesPage params={mockParams} />)

      // Switch to reservations tab
      await user.click(screen.getByText('Reservations'))

      await waitFor(() => {
        // Check table headers
        expect(screen.getByText('Time')).toBeInTheDocument()
        expect(screen.getByText('Customer')).toBeInTheDocument()
        expect(screen.getByText('Party Size')).toBeInTheDocument()
        expect(screen.getByText('Table')).toBeInTheDocument()
        expect(screen.getByText('Status')).toBeInTheDocument()
        expect(screen.getByText('Actions')).toBeInTheDocument()

        // Check reservation data
        expect(screen.getByText('7:00 PM')).toBeInTheDocument()
        expect(screen.getByText('8:00 PM')).toBeInTheDocument()
        expect(screen.getByText('John Doe')).toBeInTheDocument()
        expect(screen.getByText('Jane Smith')).toBeInTheDocument()
        expect(screen.getByText('4')).toBeInTheDocument()
        expect(screen.getByText('2')).toBeInTheDocument()
      })
    })

    it('shows different status styles for reservations', async () => {
      const user = userEvent.setup()
      renderWithProvider(<TablesPage params={mockParams} />)

      await user.click(screen.getByText('Reservations'))

      await waitFor(() => {
        // Check for status buttons with different styles
        const confirmedStatus = screen.getByText('confirmed')
        const pendingStatus = screen.getByText('pending')

        expect(confirmedStatus).toBeInTheDocument()
        expect(pendingStatus).toBeInTheDocument()

        // Check that they have different styling (classes)
        expect(confirmedStatus.closest('button')).toHaveClass('bg-[#f4f2f0]')
        expect(pendingStatus.closest('button')).toHaveClass('bg-yellow-100')
      })
    })

    it('provides view links for reservations', async () => {
      const user = userEvent.setup()
      renderWithProvider(<TablesPage params={mockParams} />)

      await user.click(screen.getByText('Reservations'))

      await waitFor(() => {
        const viewLinks = screen.getAllByText('View')
        expect(viewLinks).toHaveLength(2)

        // Check that links have correct hrefs
        viewLinks.forEach((link) => {
          expect(link.closest('a')).toHaveAttribute('href')
          expect(link.closest('a')?.getAttribute('href')).toMatch(
            /\/app\/restaurant\/test-restaurant\/main-branch\/reservations\//
          )
        })
      })
    })
  })

  describe('Responsive Behavior', () => {
    it('handles window resize gracefully', async () => {
      renderWithProvider(<TablesPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Tables & Reservations')).toBeInTheDocument()
      })

      // Simulate window resize
      global.innerWidth = 768
      global.dispatchEvent(new Event('resize'))

      // Component should still be functional
      expect(screen.getByText('Tables & Reservations')).toBeInTheDocument()
      expect(screen.getByText('Table 1')).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', async () => {
      renderWithProvider(<TablesPage params={mockParams} />)

      await waitFor(() => {
        // Check for proper heading structure
        const mainHeading = screen.getByRole('heading', { level: 1 })
        expect(mainHeading).toHaveTextContent('Tables & Reservations')

        // Check for tab navigation
        const tabList = screen.getByRole('tablist')
        expect(tabList).toBeInTheDocument()

        const tabs = screen.getAllByRole('tab')
        expect(tabs).toHaveLength(2)
      })
    })

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup()
      renderWithProvider(<TablesPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Floor Plan')).toBeInTheDocument()
      })

      // Tab to the reservations tab and activate with Enter
      const reservationsTab = screen.getByText('Reservations')
      await user.tab()
      await user.keyboard('{Enter}')

      // Should switch to reservations
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
      })
    })
  })
})
