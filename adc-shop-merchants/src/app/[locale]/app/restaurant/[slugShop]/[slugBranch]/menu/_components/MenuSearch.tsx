'use client';

import React from 'react';
import { Search } from 'lucide-react';

interface MenuSearchProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
}

export function MenuSearch({ searchTerm, onSearchChange }: MenuSearchProps) {
  return (
    <div className="px-4 py-4">
      <div className="relative max-w-md">
        <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
          <Search size={20} className="text-muted-foreground" />
        </div>
        <input
          type="text"
          placeholder="Search menu items..."
          className="w-full h-12 pl-12 pr-4 text-foreground bg-muted border border-border rounded-xl focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all duration-200 placeholder:text-muted-foreground text-sm font-medium"
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
        />
      </div>
    </div>
  );
}
