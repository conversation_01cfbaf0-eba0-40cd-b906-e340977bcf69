'use client';

import React, { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { useRouter } from '@/i18n/navigation';
import { Calendar, Clock, ArrowLeft, Trash2 } from 'lucide-react';
import { AppLoading } from '@/components/ui/app-loading';
import { useGetShopBySlugQuery, type Shop, type ShopBranch } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { useReservation } from '@/hooks/useReservations';
import { useGetTablesQuery } from '@/lib/redux/api/endpoints/restaurant/tablesApi';
import { toast } from 'sonner';
import { ReservationDeleteDialog } from '@/components/ui/delete-confirmation-dialog';

interface EditReservationPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
    slug: string;
  }>;
}

export default function EditReservationPage({ params }: EditReservationPageProps) {
  const { slugShop, slugBranch, slug } = React.use(params);
  const router = useRouter();

  // Get shop data from backend by slug (includes branches)
  const { data: shop, isLoading: isLoadingShop } = useGetShopBySlugQuery(slugShop);

  // Find the branch from the shop data
  const branch = shop?.branches?.find((b: ShopBranch) => b.slug === slugBranch);

  const shopId = shop?.id;
  const branchId = branch?.id;

  // Get tables for the branch
  const { data: tablesData, isLoading: isLoadingTables } = useGetTablesQuery(
    { shopId: shopId || '', branchId: branchId || '' },
    { skip: !shopId || !branchId }
  );

  // Use reservation hook for data fetching and actions
  const {
    reservation,
    isLoading: isLoadingReservation,
    isUpdating,
    isCancelling,
    isError,
    error,
    updateReservation,
    cancelReservation,
  } = useReservation(slugShop, slugBranch, slug);

  // Form state
  const [formData, setFormData] = useState({
    customerName: '',
    email: '',
    contactNumber: '',
    reservationDate: '',
    reservationTime: '',
    partySize: '',
    tableId: '',
    notes: ''
  });

  // Delete dialog state
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const isLoading = isLoadingShop || isLoadingReservation || isLoadingTables;
  const tables = tablesData || [];

  // Load reservation data into form
  useEffect(() => {
    if (reservation) {
      setFormData({
        customerName: reservation.customerName,
        email: reservation.customerEmail || '',
        contactNumber: reservation.customerPhone,
        reservationDate: reservation.date,
        reservationTime: reservation.time,
        partySize: reservation.partySize.toString(),
        tableId: reservation.tableId || '',
        notes: reservation.notes || ''
      });
    }
  }, [reservation]);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!shop || !branch) {
    return (
      <div className="text-center py-12">
        <h1 className="text-[#181511] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
        <p className="text-[#887663] text-sm">The branch you are looking for does not exist.</p>
      </div>
    );
  }

  if (isError || !reservation) {
    return (
      <div className="text-center py-12">
        <h1 className="text-[#181511] text-[32px] font-bold leading-tight mb-2">Reservation Not Found</h1>
        <p className="text-[#887663] text-sm">
          {error && 'data' in error && typeof error.data === 'object' && error.data && 'message' in error.data
            ? (error.data as { message: string }).message
            : 'The reservation you are looking for does not exist.'}
        </p>
      </div>
    );
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // Validate form
      if (!formData.customerName) {
        toast.error('Customer name is required');
        return;
      }

      if (!formData.contactNumber) {
        toast.error('Contact number is required');
        return;
      }

      if (!formData.reservationDate) {
        toast.error('Reservation date is required');
        return;
      }

      if (!formData.reservationTime) {
        toast.error('Reservation time is required');
        return;
      }

      if (!formData.partySize) {
        toast.error('Party size is required');
        return;
      }

      // Prepare update data
      const updateData = {
        customerName: formData.customerName,
        customerEmail: formData.email,
        customerPhone: formData.contactNumber,
        date: formData.reservationDate,
        time: formData.reservationTime,
        partySize: parseInt(formData.partySize),
        tableId: formData.tableId || undefined,
        notes: formData.notes,
      };

      // Update reservation using real API
      await updateReservation(updateData);

      toast.success('Reservation updated successfully!');
      router.push(`/app/restaurant/${slugShop}/${slugBranch}/reservations/${slug}`);
    } catch (error) {
      toast.error('Failed to update reservation');
      console.error('Error updating reservation:', error);
    }
  };

  const handleDelete = () => {
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    try {
      await cancelReservation();
      toast.success('Reservation deleted successfully!');
      setIsDeleteDialogOpen(false);
      router.push(`/app/restaurant/${slugShop}/${slugBranch}/reservations`);
    } catch (error) {
      toast.error('Failed to delete reservation');
      console.error('Error deleting reservation:', error);
    }
  };

  return (
    <>
      {/* Back Button */}
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/reservations/${slug}`}>
          <button className="flex items-center gap-2 text-[#887663] hover:text-[#181511] transition-colors">
            <ArrowLeft size={20} />
            <span className="text-sm font-medium">Back to Details</span>
          </button>
        </Link>
      </div>

      <div className="flex flex-col w-[512px] max-w-[512px] py-5 mx-auto">
        <div className="flex flex-wrap justify-between gap-3 p-4">
          <p className="text-[#181511] tracking-light text-[32px] font-bold leading-tight min-w-72">Edit Reservation</p>
        </div>

        <form onSubmit={handleSubmit}>
          {/* Customer Name */}
          <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
            <label className="flex flex-col min-w-40 flex-1">
              <p className="text-[#181511] text-base font-medium leading-normal pb-2">Customer Name</p>
              <input
                name="customerName"
                value={formData.customerName}
                onChange={handleInputChange}
                placeholder="Enter customer name"
                className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f0] focus:border-none h-14 placeholder:text-[#887663] p-4 text-base font-normal leading-normal"
                required
              />
            </label>
          </div>

          {/* Email */}
          <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
            <label className="flex flex-col min-w-40 flex-1">
              <p className="text-[#181511] text-base font-medium leading-normal pb-2">Email (Optional)</p>
              <input
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Enter email address"
                className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f0] focus:border-none h-14 placeholder:text-[#887663] p-4 text-base font-normal leading-normal"
              />
            </label>
          </div>

          {/* Contact Number */}
          <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
            <label className="flex flex-col min-w-40 flex-1">
              <p className="text-[#181511] text-base font-medium leading-normal pb-2">Contact Number</p>
              <input
                name="contactNumber"
                type="tel"
                value={formData.contactNumber}
                onChange={handleInputChange}
                placeholder="Enter contact number"
                className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f0] focus:border-none h-14 placeholder:text-[#887663] p-4 text-base font-normal leading-normal"
                required
              />
            </label>
          </div>

          {/* Reservation Date */}
          <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
            <label className="flex flex-col min-w-40 flex-1">
              <p className="text-[#181511] text-base font-medium leading-normal pb-2">Reservation Date</p>
              <div className="flex w-full flex-1 items-stretch rounded-xl">
                <input
                  name="reservationDate"
                  type="date"
                  value={formData.reservationDate}
                  onChange={handleInputChange}
                  placeholder="Select date"
                  className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f0] focus:border-none h-14 placeholder:text-[#887663] p-4 rounded-r-none border-r-0 pr-2 text-base font-normal leading-normal"
                  required
                />
                <div className="text-[#887663] flex border-none bg-[#f4f2f0] items-center justify-center pr-4 rounded-r-xl border-l-0">
                  <Calendar size={24} />
                </div>
              </div>
            </label>
          </div>

          {/* Reservation Time */}
          <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
            <label className="flex flex-col min-w-40 flex-1">
              <p className="text-[#181511] text-base font-medium leading-normal pb-2">Reservation Time</p>
              <div className="flex w-full flex-1 items-stretch rounded-xl">
                <input
                  name="reservationTime"
                  type="time"
                  value={formData.reservationTime}
                  onChange={handleInputChange}
                  placeholder="Select time"
                  className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f0] focus:border-none h-14 placeholder:text-[#887663] p-4 rounded-r-none border-r-0 pr-2 text-base font-normal leading-normal"
                  required
                />
                <div className="text-[#887663] flex border-none bg-[#f4f2f0] items-center justify-center pr-4 rounded-r-xl border-l-0">
                  <Clock size={24} />
                </div>
              </div>
            </label>
          </div>

          {/* Party Size */}
          <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
            <label className="flex flex-col min-w-40 flex-1">
              <p className="text-[#181511] text-base font-medium leading-normal pb-2">Party Size</p>
              <input
                name="partySize"
                type="number"
                min="1"
                max="20"
                value={formData.partySize}
                onChange={handleInputChange}
                placeholder="Enter party size"
                className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f0] focus:border-none h-14 placeholder:text-[#887663] p-4 text-base font-normal leading-normal"
                required
              />
            </label>
          </div>

          {/* Table Preference */}
          <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
            <label className="flex flex-col min-w-40 flex-1">
              <p className="text-[#181511] text-base font-medium leading-normal pb-2">Table Preference (Optional)</p>
              <select
                name="tableId"
                value={formData.tableId}
                onChange={handleInputChange}
                className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f0] focus:border-none h-14 placeholder:text-[#887663] p-4 text-base font-normal leading-normal appearance-none"
                style={{
                  backgroundImage: `url('data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 width=%2724px%27 height=%2724px%27 fill=%27rgb(136,118,99)%27 viewBox=%270 0 256 256%27%3e%3cpath d=%27M181.66,170.34a8,8,0,0,1,0,11.32l-48,48a8,8,0,0,1-11.32,0l-48-48a8,8,0,0,1,11.32-11.32L128,212.69l42.34-42.35A8,8,0,0,1,181.66,170.34Zm-96-84.68L128,43.31l42.34,42.35a8,8,0,0,0,11.32-11.32l-48-48a8,8,0,0,0-11.32,0l-48,48A8,8,0,0,0,85.66,85.66Z%27%3e%3c/path%3e%3c/svg%3e')`,
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: 'right 1rem center',
                  backgroundSize: '1.5rem'
                }}
              >
                <option value="">Select table preference</option>
                {tables.map((table) => (
                  <option key={table.id} value={table.id}>
                    {table.name} ({table.capacity} seats)
                  </option>
                ))}
              </select>
            </label>
          </div>

          {/* Notes */}
          <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
            <label className="flex flex-col min-w-40 flex-1">
              <p className="text-[#181511] text-base font-medium leading-normal pb-2">Special Requests (Optional)</p>
              <textarea
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                placeholder="Any special requests or notes..."
                rows={3}
                className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f0] focus:border-none placeholder:text-[#887663] p-4 text-base font-normal leading-normal"
              />
            </label>
          </div>

          {/* Action Buttons */}
          <div className="flex px-4 py-3 justify-between">
            <button
              type="button"
              onClick={handleDelete}
              disabled={isCancelling}
              className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-red-500 text-white text-sm font-bold leading-normal tracking-[0.015em] hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed gap-2"
            >
              <Trash2 size={16} />
              <span className="truncate">{isCancelling ? 'Deleting...' : 'Delete'}</span>
            </button>

            <button
              type="submit"
              disabled={isUpdating}
              className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#e58219] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em] hover:bg-[#d4741a] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span className="truncate">{isUpdating ? 'Updating...' : 'Update Reservation'}</span>
            </button>
          </div>
        </form>
      </div>

      {/* Delete Confirmation Dialog */}
      <ReservationDeleteDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={confirmDelete}
        reservationId={reservation?.id}
        isLoading={isCancelling}
      />
    </>
  );
}
