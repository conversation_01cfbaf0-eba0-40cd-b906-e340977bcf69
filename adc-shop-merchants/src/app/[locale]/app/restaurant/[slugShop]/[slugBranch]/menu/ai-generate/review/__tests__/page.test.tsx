import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import ReviewMenuPage from '../page';

// Mock the next/navigation module
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

// Mock the next-intl module
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => key,
}));

describe('ReviewMenuPage', () => {
  const mockPush = jest.fn();
  
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush,
    });
  });

  it('renders the review page with menu items', () => {
    render(<ReviewMenuPage />);
    
    // Check for page title
    expect(screen.getByText('Review and Edit Menu')).toBeInTheDocument();
    
    // Check for menu name
    expect(screen.getByText('Italian Delights')).toBeInTheDocument();
    
    // Check for menu items
    expect(screen.getByText('Spaghetti Carbonara')).toBeInTheDocument();
    expect(screen.getByText('Margherita Pizza')).toBeInTheDocument();
    expect(screen.getByText('Tiramisu')).toBeInTheDocument();
  });

  it('opens edit modal when clicking edit button', () => {
    render(<ReviewMenuPage />);
    
    // Find all edit buttons
    const editButtons = screen.getAllByText('Edit');
    
    // Click the first edit button
    fireEvent.click(editButtons[0]);
    
    // Check if modal is open
    expect(screen.getByText('Edit Menu Item')).toBeInTheDocument();
  });

  it('publishes menu and redirects when clicking publish button', async () => {
    // Mock window.alert
    const alertMock = jest.spyOn(window, 'alert').mockImplementation(() => {});
    
    render(<ReviewMenuPage />);
    
    // Find and click publish button
    const publishButton = screen.getByText('Publish Menu');
    fireEvent.click(publishButton);
    
    // Wait for the timeout to complete
    await waitFor(() => {
      expect(alertMock).toHaveBeenCalledWith('Menu published successfully!');
      expect(mockPush).toHaveBeenCalledWith('/app/restaurant/menu');
    }, { timeout: 2000 });
    
    // Restore the original implementation
    alertMock.mockRestore();
  });

  it('edits a menu item', () => {
    render(<ReviewMenuPage />);
    
    // Find all edit buttons
    const editButtons = screen.getAllByText('Edit');
    
    // Click the first edit button
    fireEvent.click(editButtons[0]);
    
    // Find the name input and change it
    const nameInput = screen.getByLabelText('Name');
    fireEvent.change(nameInput, { target: { value: 'Updated Spaghetti Carbonara' } });
    
    // Find and click save button
    const saveButton = screen.getByText('Save Changes');
    fireEvent.click(saveButton);
    
    // Check if the item name was updated
    expect(screen.getByText('Updated Spaghetti Carbonara')).toBeInTheDocument();
  });
});
