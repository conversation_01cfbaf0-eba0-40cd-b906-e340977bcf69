'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

// Define the form schema
const reservationFormSchema = z.object({
  customerName: z.string().min(1, 'Customer name is required'),
  contactInformation: z.string().min(1, 'Contact information is required'),
  date: z.string().min(1, 'Date is required'),
  time: z.string().min(1, 'Time is required'),
  tableId: z.string().optional().transform(val => val === 'none' ? undefined : val),
  partySize: z.number().int().min(1, 'Party size must be at least 1').max(20, 'Party size cannot exceed 20'),
});

type ReservationFormValues = z.infer<typeof reservationFormSchema>;

interface Table {
  id: string;
  number: number;
  name?: string;
  capacity?: number;
}

interface ReservationFormProps {
  tables: Table[];
  onSuccess?: (reservationData: ReservationFormValues) => void;
  onCancel?: () => void;
  initialData?: Partial<ReservationFormValues>;
  isLoading?: boolean;
}

export function ReservationForm({
  tables,
  onSuccess,
  onCancel,
  initialData,
  isLoading = false
}: ReservationFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isEditing = !!initialData;

  const form = useForm<ReservationFormValues>({
    resolver: zodResolver(reservationFormSchema),
    defaultValues: {
      customerName: initialData?.customerName || '',
      contactInformation: (initialData as any)?.contactInformation || '',
      date: initialData?.date || new Date().toISOString().split('T')[0],
      time: initialData?.time || '19:00',
      tableId: initialData?.tableId || 'none',
      partySize: initialData?.partySize || 2,
    },
  });

  const onSubmit = async (data: ReservationFormValues) => {
    setIsSubmitting(true);
    try {
      await onSuccess?.(data);
    } catch (error) {
      console.error('Error submitting reservation:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Generate time slots (every 30 minutes from 10:00 to 22:00)
  const timeSlots = [];
  for (let hour = 10; hour <= 22; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      if (hour === 22 && minute > 0) break; // Stop at 22:00
      const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
      timeSlots.push(timeString);
    }
  }

  return (
    <div className="w-full max-w-md mx-auto space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Customer Name */}
          <FormField
            control={form.control}
            name="customerName"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-[#181510] font-medium">Customer Name</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter customer name"
                    className="bg-[#f5f5f5] border-[#e0e0e0] h-12 rounded-lg"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Contact Information */}
          <FormField
            control={form.control}
            name="contactInformation"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-[#181510] font-medium">Contact Information</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter contact information"
                    className="bg-[#f5f5f5] border-[#e0e0e0] h-12 rounded-lg"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Reservation Date */}
          <FormField
            control={form.control}
            name="date"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-[#181510] font-medium">Reservation Date</FormLabel>
                <FormControl>
                  <Input
                    type="date"
                    placeholder="Select date"
                    className="bg-[#f5f5f5] border-[#e0e0e0] h-12 rounded-lg"
                    min={new Date().toISOString().split('T')[0]}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Reservation Time */}
          <FormField
            control={form.control}
            name="time"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-[#181510] font-medium">Reservation Time</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="bg-[#f5f5f5] border-[#e0e0e0] h-12 rounded-lg">
                      <SelectValue placeholder="Select time" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {timeSlots.map((time) => (
                      <SelectItem key={time} value={time}>
                        {time}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Table Preference */}
          <FormField
            control={form.control}
            name="tableId"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-[#181510] font-medium">Table Preference</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="bg-[#f5f5f5] border-[#e0e0e0] h-12 rounded-lg">
                      <SelectValue placeholder="Select table" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="none">No preference</SelectItem>
                    {tables.map((table) => (
                      <SelectItem key={table.id} value={table.id}>
                        Table {table.number} {table.capacity && `(${table.capacity} seats)`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Party Size */}
          <FormField
            control={form.control}
            name="partySize"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-[#181510] font-medium">Party Size</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    min="1"
                    max="20"
                    placeholder="Enter party size"
                    className="bg-[#f5f5f5] border-[#e0e0e0] h-12 rounded-lg"
                    {...field}
                    onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={isSubmitting || isLoading}
            className="w-full bg-[#d4b896] hover:bg-[#c4a886] text-[#181510] font-medium h-12 rounded-lg"
          >
            {isSubmitting ? 'Adding...' : 'Add Reservation'}
          </Button>
        </form>
      </Form>
    </div>
  );
}
