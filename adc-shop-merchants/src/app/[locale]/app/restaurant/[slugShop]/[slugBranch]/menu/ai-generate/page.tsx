'use client';

import { useState, useRef, ChangeEvent, useCallback } from 'react';
import { useRouter } from '@/i18n/navigation';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, Image, Palette, Sparkles, Settings, ChevronDown, ChevronRight, Upload, X } from 'lucide-react';
import {
  useCreateAIGenerationJobMutation,
  useUploadAIFileMutation,
  AIGenerationJob
} from '@/lib/redux/api/endpoints/restaurant/aiGenerationApi';
import { useWebSocket } from '@/hooks/useWebSocket';
import { toast } from 'sonner';
import { ImageWithFallback } from '@/components/ui/image-with-fallback';
import React from 'react';

interface AIGenerateMenuPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

interface WebSocketMessage {
  type: string;
  data: {
    job_id?: string;
    progress?: number;
    message?: string;
    status?: string;
    error?: string;
  };
}

export default function AIGenerateMenuPage({ params }: AIGenerateMenuPageProps) {
  const { slugShop, slugBranch } = React.use(params);
  const router = useRouter();

  // Use the slug values for API calls
  const shopSlug = slugShop;
  const branchSlug = slugBranch;

  // API hooks
  const [createAIGenerationJob] = useCreateAIGenerationJobMutation();
  const [uploadAIFile] = useUploadAIFileMutation();

  // State for active tab
  const [activeTab, setActiveTab] = useState<'menu_image' | 'text' | 'food_images'>('menu_image');

  // State for file uploads
  const [menuImage, setMenuImage] = useState<File | null>(null);
  const [menuImagePreview, setMenuImagePreview] = useState<string | null>(null);
  const [foodImages, setFoodImages] = useState<File[]>([]);
  const [foodImagesPreview, setFoodImagesPreview] = useState<string[]>([]);

  // State for text input
  const [menuText, setMenuText] = useState('');

  // State for additional context
  const [cuisineType] = useState('');
  const [priceRange] = useState('$$');
  const [restaurantName] = useState('');

  // State for generation process
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentJob, setCurrentJob] = useState<AIGenerationJob | null>(null);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generationStatus, setGenerationStatus] = useState('');

  // AI generation options
  const [generateImages, setGenerateImages] = useState(true);
  const [aiProvider, setAiProvider] = useState<'openai' | 'gemini'>('openai');
  const [imageStyle, setImageStyle] = useState('natural');
  const [imageTheme, setImageTheme] = useState('restaurant');
  const [imageQuality, setImageQuality] = useState('standard');
  const [imageSize, setImageSize] = useState('1024x1024');

  // Advanced options visibility
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);

  // Refs for file inputs
  const menuImageInputRef = useRef<HTMLInputElement>(null);
  const foodImagesInputRef = useRef<HTMLInputElement>(null);

  // WebSocket message handler
  const handleWebSocketMessage = useCallback((message: WebSocketMessage) => {
    if (message.type === 'ai_generation_status' && currentJob && message.data?.job_id === currentJob.id) {
      setGenerationProgress(message.data?.progress || 0);
      setGenerationStatus(message.data?.message || '');

      if (message.data?.status === 'completed') {
        setIsGenerating(false);
        toast.success('AI generation completed!');
        router.push(`/app/restaurant/${slugShop}/${slugBranch}/menu/ai-generate/review?jobId=${currentJob.id}`);
      } else if (message.data?.status === 'failed') {
        setIsGenerating(false);
        toast.error(message.data?.error || 'AI generation failed');
      }
    }
  }, [currentJob, router, slugShop, slugBranch]);

  // WebSocket for real-time updates
  const { isConnected } = useWebSocket({
    onMessage: handleWebSocketMessage
  });

  // Handle menu image upload
  const handleMenuImageUpload = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        toast.error('Please select a JPEG, PNG, or WebP image file');
        return;
      }

      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('File size must be less than 5MB');
        return;
      }

      setMenuImage(file);

      // Create preview
      const reader = new FileReader();
      reader.onload = () => {
        setMenuImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle food images upload
  const handleFoodImagesUpload = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const files = Array.from(e.target.files);
      const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
      const maxSize = 5 * 1024 * 1024; // 5MB

      // Validate each file
      const validFiles: File[] = [];
      for (const file of files) {
        // Check file type
        if (!allowedTypes.includes(file.type)) {
          toast.error(`${file.name}: Please select a JPEG, PNG, or WebP image file`);
          continue;
        }

        // Check file size
        if (file.size > maxSize) {
          toast.error(`${file.name}: File size must be less than 5MB`);
          continue;
        }

        validFiles.push(file);
      }

      if (validFiles.length === 0) return;

      setFoodImages(prevImages => [...prevImages, ...validFiles]);

      // Create previews for valid files
      validFiles.forEach(file => {
        const reader = new FileReader();
        reader.onload = () => {
          setFoodImagesPreview(prev => [...prev, reader.result as string]);
        };
        reader.readAsDataURL(file);
      });
    }
  };

  // Handle text input
  const handleTextChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    setMenuText(e.target.value);
  };

  // Handle form submission for menu image
  const handleGenerateFromMenuImage = async () => {
    if (!menuImage) return;

    setIsGenerating(true);

    try {
      // First upload the image
      const uploadResult = await uploadAIFile({
        shopSlug,
        branchSlug,
        file: menuImage,
        type: 'menu_image'
      }).unwrap();

      // Then create the AI generation job
      const job = await createAIGenerationJob({
        shopSlug,
        branchSlug,
        data: {
          type: 'menu_image',
          input_data: {
            menu_image_url: uploadResult.file_url,
            cuisine_type: cuisineType,
            price_range: priceRange,
            restaurant_name: restaurantName,
            generate_images: generateImages,
            ai_provider: aiProvider,
            image_style: imageStyle,
            image_theme: imageTheme,
            image_quality: imageQuality,
            image_size: imageSize
          }
        }
      }).unwrap();

      setCurrentJob(job);

      // Navigate to review page with job ID
      router.push(`/app/restaurant/${slugShop}/${slugBranch}/menu/ai-generate/review?jobId=${job.id}`);
    } catch (error) {
      console.error('Failed to generate menu from image:', error);
      setIsGenerating(false);
    }
  };

  // Handle form submission for text
  const handleGenerateFromText = async () => {
    if (!menuText.trim()) return;

    setIsGenerating(true);

    try {
      // Create the AI generation job
      const job = await createAIGenerationJob({
        shopSlug,
        branchSlug,
        data: {
          type: 'text',
          input_data: {
            menu_text: menuText,
            cuisine_type: cuisineType,
            price_range: priceRange,
            restaurant_name: restaurantName,
            generate_images: generateImages,
            ai_provider: aiProvider,
            image_style: imageStyle,
            image_theme: imageTheme,
            image_quality: imageQuality,
            image_size: imageSize
          }
        }
      }).unwrap();

      setCurrentJob(job);

      // Navigate to review page with job ID
      router.push(`/app/restaurant/${slugShop}/${slugBranch}/menu/ai-generate/review?jobId=${job.id}`);
    } catch (error) {
      console.error('Failed to generate menu from text:', error);
      setIsGenerating(false);
    }
  };

  // Handle form submission for food images
  const handleGenerateFromFoodImages = async () => {
    if (foodImages.length === 0) return;

    setIsGenerating(true);

    try {
      // Upload all food images
      const uploadPromises = foodImages.map(file =>
        uploadAIFile({
          shopSlug,
          branchSlug,
          file,
          type: 'food_image'
        }).unwrap()
      );

      const uploadResults = await Promise.all(uploadPromises);
      const imageUrls = uploadResults.map(result => result.file_url);

      // Create the AI generation job
      const job = await createAIGenerationJob({
        shopSlug,
        branchSlug,
        data: {
          type: 'food_images',
          input_data: {
            food_image_urls: imageUrls,
            cuisine_type: cuisineType,
            price_range: priceRange,
            restaurant_name: restaurantName,
            generate_images: generateImages,
            ai_provider: aiProvider,
            image_style: imageStyle,
            image_theme: imageTheme,
            image_quality: imageQuality,
            image_size: imageSize
          }
        }
      }).unwrap();

      setCurrentJob(job);

      // Navigate to review page with job ID
      router.push(`/app/restaurant/${slugShop}/${slugBranch}/menu/ai-generate/review?jobId=${job.id}`);
    } catch (error) {
      console.error('Failed to generate menu from food images:', error);
      setIsGenerating(false);
    }
  };



  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-wrap justify-between gap-3 p-4">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-foreground tracking-tight text-3xl font-bold leading-tight">AI Menu Generator</h1>
          <p className="text-muted-foreground text-sm font-normal leading-normal">Generate menu items using AI based on your restaurant&apos;s menu images, text, or food photos.</p>
        </div>
      </div>

      {/* Main Content with Tabs */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'menu_image' | 'text' | 'food_images')} className="w-full">
        <div className="px-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="menu_image" className="flex items-center gap-2">
              <Image className="h-4 w-4" />
              Menu Image
            </TabsTrigger>
            <TabsTrigger value="text" className="flex items-center gap-2">
              <Sparkles className="h-4 w-4" />
              Text
            </TabsTrigger>
            <TabsTrigger value="food_images" className="flex items-center gap-2">
              <Upload className="h-4 w-4" />
              Food Images
            </TabsTrigger>
          </TabsList>
        </div>

        {/* Advanced Options */}
        <div className="px-4 py-4 border-b border-border">
          <Collapsible open={showAdvancedOptions} onOpenChange={setShowAdvancedOptions}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="flex items-center justify-between w-full p-3 hover:bg-muted/50">
                <div className="flex items-center gap-2">
                  <Settings className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Advanced Options</span>
                </div>
                {showAdvancedOptions ? (
                  <ChevronDown className="h-4 w-4 text-muted-foreground" />
                ) : (
                  <ChevronRight className="h-4 w-4 text-muted-foreground" />
                )}
              </Button>
            </CollapsibleTrigger>

            <CollapsibleContent className="space-y-4 pt-4">
              {/* Basic AI Options */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Generate Images Toggle */}
                <Card>
                  <CardHeader className="pb-3">
                    <div className="flex items-center gap-2">
                      <Image className="h-4 w-4 text-primary" />
                      <CardTitle className="text-sm">Generate Images</CardTitle>
                    </div>
                    <CardDescription className="text-xs">
                      Create custom food images using AI
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="generate-images"
                        checked={generateImages}
                        onCheckedChange={setGenerateImages}
                      />
                      <Label htmlFor="generate-images" className="text-sm">
                        {generateImages ? 'Enabled' : 'Disabled'}
                      </Label>
                    </div>
                  </CardContent>
                </Card>

              {/* AI Provider Selection */}
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-2">
                    <Sparkles className="h-4 w-4 text-primary" />
                    <CardTitle className="text-sm">AI Provider</CardTitle>
                  </div>
                  <CardDescription className="text-xs">
                    Choose your preferred AI service
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Select value={aiProvider} onValueChange={(value: 'openai' | 'gemini') => setAiProvider(value)}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select provider" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="openai">OpenAI (GPT-4 + DALL-E)</SelectItem>
                      <SelectItem value="gemini">Gemini (Gemini 1.5 Pro + Imagen 4)</SelectItem>
                    </SelectContent>
                  </Select>
                </CardContent>
              </Card>
            </div>

            {/* Image Generation Options */}
            {generateImages && (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Image Style Selection */}
                  <Card>
                    <CardHeader className="pb-3">
                      <div className="flex items-center gap-2">
                        <Palette className="h-4 w-4 text-primary" />
                        <CardTitle className="text-sm">Image Style</CardTitle>
                      </div>
                      <CardDescription className="text-xs">
                        Choose the visual style for generated images
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Select value={imageStyle} onValueChange={setImageStyle}>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select style" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="natural">Natural</SelectItem>
                          <SelectItem value="vivid">Vivid</SelectItem>
                        </SelectContent>
                      </Select>
                    </CardContent>
                  </Card>

                  {/* Image Theme Selection */}
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm">Theme</CardTitle>
                      <CardDescription className="text-xs">
                        Choose the presentation style
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Select value={imageTheme} onValueChange={setImageTheme}>
                        <SelectTrigger className="w-full">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="restaurant">Restaurant Style</SelectItem>
                          <SelectItem value="rustic">Rustic & Homemade</SelectItem>
                          <SelectItem value="elegant">Fine Dining</SelectItem>
                          <SelectItem value="casual">Casual Dining</SelectItem>
                          <SelectItem value="street">Street Food</SelectItem>
                          <SelectItem value="modern">Modern Minimalist</SelectItem>
                          <SelectItem value="traditional">Traditional</SelectItem>
                        </SelectContent>
                      </Select>
                    </CardContent>
                  </Card>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Image Quality */}
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm">Quality</CardTitle>
                      <CardDescription className="text-xs">
                        Image resolution quality
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Select value={imageQuality} onValueChange={setImageQuality}>
                        <SelectTrigger className="w-full">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="standard">Standard</SelectItem>
                          <SelectItem value="hd">HD Quality</SelectItem>
                        </SelectContent>
                      </Select>
                    </CardContent>
                  </Card>

                  {/* Image Size */}
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm">Size</CardTitle>
                      <CardDescription className="text-xs">
                        Image dimensions
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Select value={imageSize} onValueChange={setImageSize}>
                        <SelectTrigger className="w-full">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1024x1024">Square (1024×1024)</SelectItem>
                          <SelectItem value="1792x1024">Landscape (1792×1024)</SelectItem>
                          <SelectItem value="1024x1792">Portrait (1024×1792)</SelectItem>
                        </SelectContent>
                      </Select>
                    </CardContent>
                  </Card>
                </div>
              </>
            )}

            {/* Connection Status */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Connection Status</CardTitle>
                <CardDescription className="text-xs">
                  Real-time WebSocket connection
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <div className={`h-2 w-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
                  <span className="text-sm text-muted-foreground">
                    {isConnected ? 'Connected' : 'Disconnected'}
                  </span>
                </div>
              </CardContent>
            </Card>
            </CollapsibleContent>
          </Collapsible>

          {/* Real-time Status Display - Always visible when generating */}
          {isGenerating && (
            <Card className="mt-4">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin text-primary" />
                  <CardTitle className="text-sm">Generation Progress</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{generationProgress}%</span>
                  </div>
                  <Progress value={generationProgress} className="w-full" />
                  {generationStatus && (
                    <p className="text-xs text-muted-foreground">{generationStatus}</p>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Menu Image Tab */}
        <TabsContent value="menu_image" className="space-y-6">
          <div className="flex flex-col p-4">
            <p className="text-muted-foreground text-sm font-normal leading-normal mb-4">
              Upload a clear image of your menu, and our AI will automatically create an interactive online menu for your restaurant.
            </p>

            <div className="flex flex-col items-center gap-6 rounded-xl border-2 border-dashed border-border px-6 py-14">
              {menuImagePreview ? (
                <div className="flex flex-col items-center gap-4 w-full">
                  <ImageWithFallback
                    src={menuImagePreview}
                    alt="Menu preview"
                    className="max-w-full max-h-[300px] object-contain rounded-lg"
                    containerClassName="max-w-full max-h-[300px]"
                    fallbackIcon="📋"
                    fallbackText="Menu Preview"
                  />
                  <Button
                    variant="secondary"
                    onClick={() => {
                      setMenuImage(null);
                      setMenuImagePreview(null);
                    }}
                  >
                    <X className="h-4 w-4 mr-2" />
                    Remove
                  </Button>
                </div>
              ) : (
                <>
                  <div className="flex max-w-[480px] flex-col items-center gap-2">
                    <p className="text-foreground text-lg font-bold leading-tight tracking-tight max-w-[480px] text-center">Drag and drop or browse to upload</p>
                    <p className="text-muted-foreground text-sm font-normal leading-normal max-w-[480px] text-center">Supported formats: JPG, PNG, WebP. Max size: 5MB</p>
                  </div>
                  <input
                    type="file"
                    ref={menuImageInputRef}
                    onChange={handleMenuImageUpload}
                    accept="image/jpeg,image/png,image/webp"
                    className="hidden"
                  />
                  <Button
                    variant="outline"
                    onClick={() => menuImageInputRef.current?.click()}
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Browse Files
                  </Button>
                </>
              )}
            </div>
          </div>

          <div className="flex px-4 py-3 justify-end">
            <Button
              onClick={handleGenerateFromMenuImage}
              disabled={!menuImage || isGenerating}
              className="min-w-[84px] max-w-[480px]"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                'Generate Menu'
              )}
            </Button>
          </div>
        </TabsContent>

        {/* Text Tab */}
        <TabsContent value="text" className="space-y-6">
          <div className="flex flex-col p-4">
            <p className="text-muted-foreground text-sm font-normal leading-normal mb-4">
              Enter your menu items as text, and our AI will format them into a structured menu with descriptions and prices.
            </p>

            <div className="flex flex-col gap-4">
              <Textarea
                value={menuText}
                onChange={handleTextChange}
                placeholder="Enter your menu items here... For example:

APPETIZERS
Garlic Bread - $5.99
Mozzarella Sticks - $7.99

MAIN COURSES
Spaghetti Bolognese - $14.99
Grilled Salmon - $18.99"
                className="min-h-[300px] resize-none"
              />
            </div>
          </div>

          <div className="flex px-4 py-3 justify-end">
            <Button
              onClick={handleGenerateFromText}
              disabled={!menuText.trim() || isGenerating}
              className="min-w-[84px] max-w-[480px]"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                'Generate Menu'
              )}
            </Button>
          </div>
        </TabsContent>

        {/* Food Images Tab */}
        <TabsContent value="food_images" className="space-y-6">
          <div className="flex flex-col p-4">
            <p className="text-muted-foreground text-sm font-normal leading-normal mb-4">
              Upload photos of your dishes, and our AI will create menu items with descriptions and suggested prices.
            </p>

            <div className="flex flex-col items-center gap-6 rounded-xl border-2 border-dashed border-border px-6 py-14">
              <div className="flex max-w-[480px] flex-col items-center gap-2">
                <p className="text-foreground text-lg font-bold leading-tight tracking-tight max-w-[480px] text-center">Drag and drop or browse to upload</p>
                <p className="text-muted-foreground text-sm font-normal leading-normal max-w-[480px] text-center">Supported formats: JPG, PNG, WebP. Max size: 5MB per image</p>
              </div>
              <input
                type="file"
                ref={foodImagesInputRef}
                onChange={handleFoodImagesUpload}
                accept="image/jpeg,image/png,image/webp"
                multiple
                className="hidden"
              />
              <Button
                variant="outline"
                onClick={() => foodImagesInputRef.current?.click()}
              >
                <Upload className="h-4 w-4 mr-2" />
                Browse Files
              </Button>
            </div>

            {foodImagesPreview.length > 0 && (
              <div className="mt-6">
                <p className="text-foreground text-lg font-bold leading-tight tracking-tight mb-4">Uploaded Images</p>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {foodImagesPreview.map((preview, index) => (
                    <div key={index} className="relative">
                      <ImageWithFallback
                        src={preview}
                        alt={`Food image ${index + 1}`}
                        className="w-full h-40 object-cover rounded-lg"
                        containerClassName="w-full h-40"
                        fallbackIcon="🍽️"
                        fallbackText="Food Image"
                      />
                      <Button
                        variant="secondary"
                        size="icon"
                        onClick={() => {
                          setFoodImages(prev => prev.filter((_, i) => i !== index));
                          setFoodImagesPreview(prev => prev.filter((_, i) => i !== index));
                        }}
                        className="absolute top-2 right-2 h-8 w-8 rounded-full shadow-md"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          <div className="flex px-4 py-3 justify-end">
            <Button
              onClick={handleGenerateFromFoodImages}
              disabled={foodImages.length === 0 || isGenerating}
              className="min-w-[84px] max-w-[480px]"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                'Generate Menu'
              )}
            </Button>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
