'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel 
} from '@/components/ui/form';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  ArrowLeft, 
  Mail, 
  Calendar, 
  ShoppingCart, 
  Star, 
  Users, 
  AlertCircle,
  Clock
} from 'lucide-react';
import { toast } from 'sonner';

// Define the email notification settings schema
const emailNotificationSettingsSchema = z.object({
  // Email notifications
  emailEnabled: z.boolean().default(true),
  emailAddress: z.string().email().optional(),
  emailOrders: z.boolean().default(true),
  emailReservations: z.boolean().default(true),
  emailReviews: z.boolean().default(false),
  emailStaff: z.boolean().default(true),
  emailSystem: z.boolean().default(true),
  emailDigest: z.boolean().default(true),
  digestFrequency: z.enum(['daily', 'weekly', 'monthly']).default('daily'),
  digestTime: z.string().default('08:00'),
  digestDay: z.enum(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']).optional(),
});

type EmailNotificationSettings = z.infer<typeof emailNotificationSettingsSchema>;

// Mock data for initial settings
const mockEmailSettings: EmailNotificationSettings = {
  emailEnabled: true,
  emailAddress: '<EMAIL>',
  emailOrders: true,
  emailReservations: true,
  emailReviews: false,
  emailStaff: true,
  emailSystem: true,
  emailDigest: true,
  digestFrequency: 'daily',
  digestTime: '08:00',
};

export default function EmailNotificationsSettingsPage() {
  const [isSaving, setIsSaving] = useState(false);
  
  // Initialize form with mock data
  const form = useForm<EmailNotificationSettings>({
    resolver: zodResolver(emailNotificationSettingsSchema),
    defaultValues: mockEmailSettings,
  });
  
  // Watch for changes to enabled toggles and digest frequency
  const emailEnabled = form.watch('emailEnabled');
  const emailDigest = form.watch('emailDigest');
  const digestFrequency = form.watch('digestFrequency');
  
  // Handle form submission
  const onSubmit = async (data: EmailNotificationSettings) => {
    setIsSaving(true);
    
    try {
      // In a real implementation, this would save to an API
      console.log('Saving email notification settings:', data);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800));
      
      toast.success('Email notification settings saved', {
        description: 'Your email notification preferences have been updated.',
      });
    } catch (error) {
      toast.error('Failed to save settings', {
        description: 'There was an error saving your email notification preferences.',
      });
      console.error('Error saving email notification settings:', error);
    } finally {
      setIsSaving(false);
    }
  };
  
  return (
    <div className="p-6 font-be-vietnam">
      <div className="mb-6">
        <Link 
          href="/app/restaurant/settings/notifications" 
          className="flex items-center text-[#8a745c] hover:text-[#181510] transition-colors"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Notification Settings
        </Link>
      </div>
      
      <div className="flex flex-col gap-2 mb-6">
        <h1 className="text-[#181510] text-2xl font-bold">Email Notification Settings</h1>
        <p className="text-[#8a745c]">Configure how you receive email notifications from the system</p>
      </div>
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Mail className="h-5 w-5 text-[#8a745c]" />
                  <CardTitle>Email Notifications</CardTitle>
                </div>
                <FormField
                  control={form.control}
                  name="emailEnabled"
                  render={({ field }) => (
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              <CardDescription>
                Configure notifications sent to your email address
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <FormField
                control={form.control}
                name="emailAddress"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Address</FormLabel>
                    <FormDescription>
                      Email notifications will be sent to this address
                    </FormDescription>
                    <FormControl>
                      <Input
                        placeholder="<EMAIL>"
                        {...field}
                        disabled={!emailEnabled}
                        className="bg-white border-[#e5e1dc]"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <Separator className="my-6" />
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-[#181510]">Notification Types</h3>
                
                <FormField
                  control={form.control}
                  name="emailOrders"
                  render={({ field }) => (
                    <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                      <div className="space-y-0.5">
                        <div className="flex items-center gap-2">
                          <ShoppingCart className="h-4 w-4 text-blue-500" />
                          <FormLabel className="font-medium text-[#181510]">Order Emails</FormLabel>
                        </div>
                        <FormDescription className="text-[#8a745c]">
                          Receive email notifications about new orders and order status changes
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={!emailEnabled}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="emailReservations"
                  render={({ field }) => (
                    <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                      <div className="space-y-0.5">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-green-500" />
                          <FormLabel className="font-medium text-[#181510]">Reservation Emails</FormLabel>
                        </div>
                        <FormDescription className="text-[#8a745c]">
                          Receive email notifications about new, updated, or canceled reservations
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={!emailEnabled}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="emailReviews"
                  render={({ field }) => (
                    <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                      <div className="space-y-0.5">
                        <div className="flex items-center gap-2">
                          <Star className="h-4 w-4 text-yellow-500" />
                          <FormLabel className="font-medium text-[#181510]">Review Emails</FormLabel>
                        </div>
                        <FormDescription className="text-[#8a745c]">
                          Receive email notifications when customers leave reviews
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={!emailEnabled}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="emailStaff"
                  render={({ field }) => (
                    <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                      <div className="space-y-0.5">
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-purple-500" />
                          <FormLabel className="font-medium text-[#181510]">Staff Emails</FormLabel>
                        </div>
                        <FormDescription className="text-[#8a745c]">
                          Receive email notifications about staff schedule changes and requests
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={!emailEnabled}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="emailSystem"
                  render={({ field }) => (
                    <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                      <div className="space-y-0.5">
                        <div className="flex items-center gap-2">
                          <AlertCircle className="h-4 w-4 text-red-500" />
                          <FormLabel className="font-medium text-[#181510]">System Emails</FormLabel>
                        </div>
                        <FormDescription className="text-[#8a745c]">
                          Receive important system alerts and updates via email
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={!emailEnabled}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              
              <Separator className="my-6" />
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium text-[#181510]">Daily Digest</h3>
                    <p className="text-sm text-[#8a745c]">Receive a summary of all activity in your restaurant</p>
                  </div>
                  <FormField
                    control={form.control}
                    name="emailDigest"
                    render={({ field }) => (
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={!emailEnabled}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <FormField
                    control={form.control}
                    name="digestFrequency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Frequency</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          disabled={!emailEnabled || !emailDigest}
                        >
                          <FormControl>
                            <SelectTrigger className="bg-white border-[#e5e1dc]">
                              <SelectValue placeholder="Select frequency" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="daily">Daily</SelectItem>
                            <SelectItem value="weekly">Weekly</SelectItem>
                            <SelectItem value="monthly">Monthly</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormItem>
                    )}
                  />
                  
                  {digestFrequency === 'weekly' && (
                    <FormField
                      control={form.control}
                      name="digestDay"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Day of Week</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            disabled={!emailEnabled || !emailDigest}
                          >
                            <FormControl>
                              <SelectTrigger className="bg-white border-[#e5e1dc]">
                                <SelectValue placeholder="Select day" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="monday">Monday</SelectItem>
                              <SelectItem value="tuesday">Tuesday</SelectItem>
                              <SelectItem value="wednesday">Wednesday</SelectItem>
                              <SelectItem value="thursday">Thursday</SelectItem>
                              <SelectItem value="friday">Friday</SelectItem>
                              <SelectItem value="saturday">Saturday</SelectItem>
                              <SelectItem value="sunday">Sunday</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormItem>
                      )}
                    />
                  )}
                  
                  <FormField
                    control={form.control}
                    name="digestTime"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Time</FormLabel>
                        <div className="flex items-center">
                          <FormControl>
                            <Input
                              type="time"
                              {...field}
                              disabled={!emailEnabled || !emailDigest}
                              className="bg-white border-[#e5e1dc]"
                            />
                          </FormControl>
                          <Clock className="h-4 w-4 ml-2 text-[#8a745c]" />
                        </div>
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end gap-4 pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => form.reset(mockEmailSettings)}
                className="border-[#e5e1dc] text-[#8a745c] hover:bg-[#f1edea] hover:text-[#181510]"
              >
                Reset to Default
              </Button>
              <Button 
                type="submit" 
                disabled={isSaving}
                className="bg-[#8a745c] hover:bg-[#6d5a49] text-white"
              >
                {isSaving ? 'Saving...' : 'Save Changes'}
              </Button>
            </CardFooter>
          </Card>
        </form>
      </Form>
    </div>
  );
}
