"use client";

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { TagSelector } from '@/components/ui/tag-selector';
import { toast } from 'sonner';

const enhancedReservationSchema = z.object({
  customer_name: z.string().min(1, 'Customer name is required'),
  customer_email: z.string().email('Valid email is required').optional().or(z.literal('')),
  customer_phone: z.string().min(1, 'Phone number is required'),
  party_size: z.number().min(1, 'Party size must be at least 1').max(20, 'Party size cannot exceed 20'),
  date: z.string().min(1, 'Date is required'),
  time: z.string().min(1, 'Time is required'),
  duration: z.number().default(120), // Default 2 hours
  table_preference: z.string().optional(),
  special_requests: z.string().optional(),
  occasion_tags: z.array(z.string()).default([]),
  dietary_tags: z.array(z.string()).default([]),
  seating_tags: z.array(z.string()).default([]),
  notes: z.string().optional(),
  status: z.enum(['pending', 'confirmed', 'seated', 'completed', 'cancelled', 'no_show']).default('pending'),
});

type EnhancedReservationFormData = z.infer<typeof enhancedReservationSchema>;

interface EnhancedReservationFormProps {
  shopId: string;
  branchId: string;
  slugShop: string;
  slugBranch: string;
  mode: 'create' | 'edit';
  initialData?: Partial<EnhancedReservationFormData & { id: string }>;
  onSuccess?: (data: EnhancedReservationFormData) => void;
  onCancel?: () => void;
  isLoading?: boolean;
}

export function EnhancedReservationForm({
  shopId,
  branchId,
  slugShop,
  slugBranch,
  mode,
  initialData,
  onSuccess,
  onCancel,
  isLoading = false
}: EnhancedReservationFormProps) {

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<EnhancedReservationFormData>({
    resolver: zodResolver(enhancedReservationSchema),
    defaultValues: {
      customer_name: initialData?.customer_name || '',
      customer_email: initialData?.customer_email || '',
      customer_phone: initialData?.customer_phone || '',
      party_size: initialData?.party_size || 2,
      date: initialData?.date || '',
      time: initialData?.time || '',
      duration: initialData?.duration || 120,
      table_preference: initialData?.table_preference || '',
      special_requests: initialData?.special_requests || '',
      occasion_tags: initialData?.occasion_tags || [],
      dietary_tags: initialData?.dietary_tags || [],
      seating_tags: initialData?.seating_tags || [],
      notes: initialData?.notes || '',
      status: initialData?.status || 'pending',
    }
  });

  const occasionTags = watch('occasion_tags');
  const dietaryTags = watch('dietary_tags');
  const seatingTags = watch('seating_tags');

  const onSubmit = async (data: EnhancedReservationFormData) => {
    try {
      await onSuccess?.(data);
      toast.success(`Reservation ${mode === 'create' ? 'created' : 'updated'} successfully`);
    } catch (error) {
      toast.error(`Failed to ${mode} reservation`);
    }
  };

  // Generate time slots (every 30 minutes from 10:00 to 22:00)
  const timeSlots = [];
  for (let hour = 10; hour <= 22; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      if (hour === 22 && minute > 0) break; // Stop at 22:00
      const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
      timeSlots.push(timeString);
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>{mode === 'create' ? 'New Reservation' : 'Edit Reservation'}</CardTitle>
        <CardDescription>
          {mode === 'create'
            ? 'Create a new reservation with detailed preferences and requirements'
            : 'Update reservation details and customer preferences'
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Customer Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="customer_name">Customer Name *</Label>
              <Input
                id="customer_name"
                {...register('customer_name')}
                placeholder="Enter customer name"
                className="bg-[#f5f5f5] border-[#e0e0e0]"
              />
              {errors.customer_name && (
                <p className="text-sm text-red-500">{errors.customer_name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="customer_phone">Phone Number *</Label>
              <Input
                id="customer_phone"
                {...register('customer_phone')}
                placeholder="+****************"
                className="bg-[#f5f5f5] border-[#e0e0e0]"
              />
              {errors.customer_phone && (
                <p className="text-sm text-red-500">{errors.customer_phone.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="customer_email">Email</Label>
              <Input
                id="customer_email"
                type="email"
                {...register('customer_email')}
                placeholder="<EMAIL>"
                className="bg-[#f5f5f5] border-[#e0e0e0]"
              />
              {errors.customer_email && (
                <p className="text-sm text-red-500">{errors.customer_email.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="party_size">Party Size *</Label>
              <Input
                id="party_size"
                type="number"
                min="1"
                max="20"
                {...register('party_size', { valueAsNumber: true })}
                placeholder="2"
                className="bg-[#f5f5f5] border-[#e0e0e0]"
              />
              {errors.party_size && (
                <p className="text-sm text-red-500">{errors.party_size.message}</p>
              )}
            </div>
          </div>

          {/* Reservation Details */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="date">Date *</Label>
              <Input
                id="date"
                type="date"
                {...register('date')}
                min={new Date().toISOString().split('T')[0]}
                className="bg-[#f5f5f5] border-[#e0e0e0]"
              />
              {errors.date && (
                <p className="text-sm text-red-500">{errors.date.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="time">Time *</Label>
              <Select onValueChange={(value) => setValue('time', value)} defaultValue={watch('time')}>
                <SelectTrigger className="bg-[#f5f5f5] border-[#e0e0e0]">
                  <SelectValue placeholder="Select time" />
                </SelectTrigger>
                <SelectContent>
                  {timeSlots.map((time) => (
                    <SelectItem key={time} value={time}>
                      {time}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.time && (
                <p className="text-sm text-red-500">{errors.time.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="duration">Duration</Label>
              <Select onValueChange={(value) => setValue('duration', parseInt(value))} defaultValue={watch('duration')?.toString()}>
                <SelectTrigger className="bg-[#f5f5f5] border-[#e0e0e0]">
                  <SelectValue placeholder="Select duration" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="60">1 hour</SelectItem>
                  <SelectItem value="90">1.5 hours</SelectItem>
                  <SelectItem value="120">2 hours</SelectItem>
                  <SelectItem value="150">2.5 hours</SelectItem>
                  <SelectItem value="180">3 hours</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Tags & Preferences */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Occasion & Event Type</Label>
              <TagSelector
                shopId={shopId}
                branchId={branchId}
                value={occasionTags}
                onChange={(newTags) => setValue('occasion_tags', newTags)}
                entityType="reservation"
                placeholder="Add occasion tags (e.g., Date Night, Birthday, Anniversary, Business Meeting)..."
                categoryFilter="occasion"
                allowCustomTags={true}
                maxTags={5}
              />
              <p className="text-xs text-muted-foreground">
                Help us prepare for the occasion and provide the best experience.
              </p>
            </div>

            <div className="space-y-2">
              <Label>Dietary Requirements & Preferences</Label>
              <TagSelector
                shopId={shopId}
                branchId={branchId}
                value={dietaryTags}
                onChange={(newTags) => setValue('dietary_tags', newTags)}
                entityType="reservation"
                placeholder="Add dietary tags (e.g., Vegetarian, Vegan, Gluten-Free, Nut Allergy)..."
                categoryFilter="dietary"
                allowCustomTags={true}
                maxTags={6}
              />
              <p className="text-xs text-muted-foreground">
                Let us know about any dietary restrictions or preferences.
              </p>
            </div>

            <div className="space-y-2">
              <Label>Seating Preferences</Label>
              <TagSelector
                shopId={shopId}
                branchId={branchId}
                value={seatingTags}
                onChange={(newTags) => setValue('seating_tags', newTags)}
                entityType="reservation"
                placeholder="Add seating preferences (e.g., Window Seat, Quiet Table, Outdoor, Private Booth)..."
                categoryFilter="seating"
                allowCustomTags={true}
                maxTags={4}
              />
              <p className="text-xs text-muted-foreground">
                Specify preferred seating arrangements and table features.
              </p>
            </div>
          </div>

          {/* Special Requests */}
          <div className="space-y-2">
            <Label htmlFor="special_requests">Special Requests</Label>
            <Textarea
              id="special_requests"
              {...register('special_requests')}
              placeholder="Any special requests or additional information..."
              rows={3}
              className="bg-[#f5f5f5] border-[#e0e0e0]"
            />
          </div>

          {/* Form Actions */}
          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              disabled={isLoading}
              className="flex-1 bg-[#d4b896] hover:bg-[#c4a886] text-[#181510]"
            >
              {isLoading
                ? (mode === 'create' ? 'Creating...' : 'Updating...')
                : (mode === 'create' ? 'Create Reservation' : 'Update Reservation')
              }
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isLoading}
                className="border-[#e0e0e0]"
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
