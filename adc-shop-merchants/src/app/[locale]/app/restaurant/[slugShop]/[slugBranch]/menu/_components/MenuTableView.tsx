'use client';

import React from 'react';
import { Link } from '@/i18n/navigation';
import { MenuEmptyState } from './MenuEmptyState';
import { MenuItemImage } from '@/components/ui/image-with-fallback';

interface MenuItem {
  id: string;
  name: string;
  slug: string;
  description?: string;
  category: string;
  price: number;
  image?: string;
  available: boolean;
}

interface MenuTableViewProps {
  items: MenuItem[];
  slugShop: string;
  slugBranch: string;
  searchTerm: string;
}

export function MenuTableView({ items, slugShop, slugBranch, searchTerm }: MenuTableViewProps) {
  if (items.length === 0) {
    return (
      <MenuEmptyState
        slugShop={slugShop}
        slugBranch={slugBranch}
        searchTerm={searchTerm}
      />
    );
  }

  return (
    <div className="px-4 py-3">
      <div className="flex overflow-hidden rounded-xl border border-border bg-card">
        <table className="flex-1">
          <thead>
            <tr className="bg-card">
              <th className="px-4 py-3 text-left text-foreground w-20 text-sm font-medium leading-normal">
                Image
              </th>
              <th className="px-4 py-3 text-left text-foreground w-[300px] text-sm font-medium leading-normal">
                Item Name
              </th>
              <th className="px-4 py-3 text-left text-foreground w-[200px] text-sm font-medium leading-normal">
                Category
              </th>
              <th className="px-4 py-3 text-left text-foreground w-[150px] text-sm font-medium leading-normal">Price</th>
              <th className="px-4 py-3 text-left text-foreground w-32 text-sm font-medium leading-normal">Availability</th>
              <th className="px-4 py-3 text-left text-muted-foreground w-32 text-sm font-medium leading-normal">
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {items.map((item) => (
              <tr key={item.id} className="border-t border-t-border">
                <td className="h-[72px] px-4 py-2 w-20">
                  <MenuItemImage
                    src={item.image || ''}
                    alt={item.name}
                    containerClassName="w-12 h-12 rounded-lg overflow-hidden"
                    className="w-full h-full object-cover"
                  />
                </td>
                <td className="h-[72px] px-4 py-2 w-[300px] text-foreground text-sm font-normal leading-normal">
                  {item.name}
                </td>
                <td className="h-[72px] px-4 py-2 w-[200px] text-muted-foreground text-sm font-normal leading-normal">
                  {item.category}
                </td>
                <td className="h-[72px] px-4 py-2 w-[150px] text-muted-foreground text-sm font-normal leading-normal">
                  ${item.price.toFixed(2)}
                </td>
                <td className="h-[72px] px-4 py-2 w-32 text-sm font-normal leading-normal">
                  <button
                    className={`flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 text-sm font-medium leading-normal w-full ${
                      item.available
                        ? 'bg-muted text-foreground'
                        : 'bg-red-50 text-red-700 dark:bg-red-950 dark:text-red-300'
                    }`}
                  >
                    <span className="truncate">{item.available ? 'Available' : 'Unavailable'}</span>
                  </button>
                </td>
                <td className="h-[72px] px-4 py-2 w-32 text-muted-foreground text-sm font-bold leading-normal tracking-[0.015em]">
                  <div className="flex gap-2">
                    <Link href={`/app/restaurant/${slugShop}/${slugBranch}/menu/${item.slug}`} className="hover:text-foreground transition-colors">
                      View
                    </Link>
                    <span className="text-muted-foreground">|</span>
                    <Link href={`/app/restaurant/${slugShop}/${slugBranch}/menu/${item.slug}/edit`} className="hover:text-foreground transition-colors">
                      Edit
                    </Link>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
