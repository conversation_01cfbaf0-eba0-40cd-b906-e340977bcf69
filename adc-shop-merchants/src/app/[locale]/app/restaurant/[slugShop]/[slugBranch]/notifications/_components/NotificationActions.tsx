import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  CheckCircle,
  Circle,
  Trash2,
  MoreHorizontal,
  RefreshCw,
  Download,
  CheckSquare,
  Square
} from 'lucide-react';

interface NotificationActionsProps {
  selectedCount: number;
  totalCount: number;
  unreadCount: number;
  onMarkAllAsRead: () => void;
  onClearAll: () => void;
  onBulkMarkAsRead: (ids: string[]) => void;
  onRefresh: () => void;
  isLoading?: boolean;
  selectedNotificationIds: string[];
  showSelection?: boolean;
  onToggleSelection?: () => void;
  onSelectAll?: (selected: boolean) => void;
  allSelected?: boolean;
}

export function NotificationActions({
  selectedCount,
  totalCount,
  unreadCount,
  onMarkAllAsRead,
  onClearAll,
  onBulkMarkAsRead,
  onRefresh,
  isLoading = false,
  selectedNotificationIds,
  showSelection = false,
  onToggleSelection,
  onSelectAll,
  allSelected = false
}: NotificationActionsProps) {
  const [showClearAllDialog, setShowClearAllDialog] = useState(false);
  const [showMarkAllDialog, setShowMarkAllDialog] = useState(false);

  const handleMarkAllAsRead = () => {
    if (unreadCount > 10) {
      setShowMarkAllDialog(true);
    } else {
      onMarkAllAsRead();
    }
  };

  const handleClearAll = () => {
    if (totalCount > 5) {
      setShowClearAllDialog(true);
    } else {
      onClearAll();
    }
  };

  const handleExport = () => {
    // TODO: Implement export functionality
    console.log('Export notifications');
  };

  return (
    <>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {/* Selection Toggle */}
          <Button
            variant={showSelection ? "default" : "outline"}
            size="sm"
            onClick={onToggleSelection}
            className="flex items-center gap-2"
          >
            {showSelection ? <CheckSquare className="h-4 w-4" /> : <Square className="h-4 w-4" />}
            {showSelection ? 'Exit Selection' : 'Select'}
          </Button>

          {/* Select All (only when in selection mode) */}
          {showSelection && totalCount > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onSelectAll?.(!allSelected)}
              className="flex items-center gap-2"
            >
              {allSelected ? <CheckSquare className="h-4 w-4" /> : <Square className="h-4 w-4" />}
              {allSelected ? 'Deselect All' : 'Select All'}
            </Button>
          )}

          {/* Refresh Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>

          {/* Selection Info */}
          {selectedCount > 0 && (
            <span className="text-sm text-muted-foreground">
              {selectedCount} selected
            </span>
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* Quick Actions for Selection */}
          {selectedCount > 0 && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onBulkMarkAsRead(selectedNotificationIds)}
                className="flex items-center gap-2"
              >
                <CheckCircle className="h-4 w-4" />
                Mark Selected as Read
              </Button>
            </>
          )}

          {/* Quick Mark All as Read */}
          {unreadCount > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleMarkAllAsRead}
              className="flex items-center gap-2"
            >
              <CheckCircle className="h-4 w-4" />
              Mark All as Read ({unreadCount})
            </Button>
          )}

          {/* More Actions Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {unreadCount > 0 && (
                <DropdownMenuItem onClick={handleMarkAllAsRead}>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Mark all as read ({unreadCount})
                </DropdownMenuItem>
              )}

              <DropdownMenuItem onClick={handleExport}>
                <Download className="h-4 w-4 mr-2" />
                Export notifications
              </DropdownMenuItem>

              <DropdownMenuSeparator />

              <DropdownMenuItem
                onClick={handleClearAll}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Clear all notifications
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Mark All as Read Confirmation Dialog */}
      <AlertDialog open={showMarkAllDialog} onOpenChange={setShowMarkAllDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Mark All as Read</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to mark all {unreadCount} unread notifications as read?
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                onMarkAllAsRead();
                setShowMarkAllDialog(false);
              }}
            >
              Mark All as Read
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Clear All Confirmation Dialog */}
      <AlertDialog open={showClearAllDialog} onOpenChange={setShowClearAllDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Clear All Notifications</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete all {totalCount} notifications?
              This action cannot be undone and will permanently remove all notification history.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                onClearAll();
                setShowClearAllDialog(false);
              }}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete All
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
