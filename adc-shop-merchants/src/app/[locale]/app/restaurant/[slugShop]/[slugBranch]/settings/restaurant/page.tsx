'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { 
  ArrowLeft,
  Store,
  Clock,
  MapPin,
  Phone,
  Mail,
  Globe,
  Camera,
  Save,
  Plus,
  Trash2,
  Edit
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';

// Types
interface BusinessHours {
  day: string;
  isOpen: boolean;
  openTime: string;
  closeTime: string;
  isBreak: boolean;
  breakStart?: string;
  breakEnd?: string;
}

interface RestaurantInfo {
  name: string;
  description: string;
  cuisine: string;
  priceRange: string;
  phone: string;
  email: string;
  website: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  businessHours: BusinessHours[];
  features: string[];
  images: {
    logo?: string;
    cover?: string;
    gallery: string[];
  };
}

export default function RestaurantSettingsPage() {
  const t = useTranslations('settings');
  const params = useParams();
  const { slugShop, slugBranch } = params;

  // State
  const [loading, setLoading] = useState(false);
  const [restaurantInfo, setRestaurantInfo] = useState<RestaurantInfo>({
    name: 'DineEase Restaurant',
    description: 'A modern dining experience with fresh, locally sourced ingredients and innovative cuisine.',
    cuisine: 'Modern American',
    priceRange: '$$',
    phone: '+****************',
    email: '<EMAIL>',
    website: 'https://dineease.com',
    address: {
      street: '123 Main Street',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      country: 'United States',
    },
    businessHours: [
      { day: 'Monday', isOpen: true, openTime: '11:00', closeTime: '22:00', isBreak: false },
      { day: 'Tuesday', isOpen: true, openTime: '11:00', closeTime: '22:00', isBreak: false },
      { day: 'Wednesday', isOpen: true, openTime: '11:00', closeTime: '22:00', isBreak: false },
      { day: 'Thursday', isOpen: true, openTime: '11:00', closeTime: '22:00', isBreak: false },
      { day: 'Friday', isOpen: true, openTime: '11:00', closeTime: '23:00', isBreak: false },
      { day: 'Saturday', isOpen: true, openTime: '10:00', closeTime: '23:00', isBreak: false },
      { day: 'Sunday', isOpen: true, openTime: '10:00', closeTime: '21:00', isBreak: false },
    ],
    features: ['WiFi', 'Parking', 'Outdoor Seating', 'Takeout', 'Delivery'],
    images: {
      logo: '/images/restaurant-logo.jpg',
      cover: '/images/restaurant-cover.jpg',
      gallery: ['/images/gallery1.jpg', '/images/gallery2.jpg'],
    },
  });

  const cuisineTypes = [
    'American', 'Italian', 'Chinese', 'Japanese', 'Mexican', 'Indian', 'French', 'Thai',
    'Mediterranean', 'Greek', 'Korean', 'Vietnamese', 'Spanish', 'German', 'British',
    'Modern American', 'Fusion', 'Vegetarian', 'Vegan', 'Seafood', 'Steakhouse', 'BBQ'
  ];

  const priceRanges = [
    { value: '$', label: '$ - Budget (Under $15)' },
    { value: '$$', label: '$$ - Moderate ($15-30)' },
    { value: '$$$', label: '$$$ - Expensive ($30-60)' },
    { value: '$$$$', label: '$$$$ - Very Expensive ($60+)' },
  ];

  const availableFeatures = [
    'WiFi', 'Parking', 'Valet Parking', 'Outdoor Seating', 'Private Dining',
    'Takeout', 'Delivery', 'Catering', 'Bar', 'Live Music', 'Pet Friendly',
    'Wheelchair Accessible', 'Kids Menu', 'Brunch', 'Late Night', 'Reservations'
  ];

  // Handle form submission
  const handleSave = async () => {
    setLoading(true);
    try {
      // TODO: Implement API call to save restaurant info
      console.log('Saving restaurant info:', restaurantInfo);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Show success message
      alert('Restaurant information saved successfully!');
    } catch (error) {
      console.error('Error saving restaurant info:', error);
      alert('Failed to save restaurant information. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Update business hours
  const updateBusinessHours = (dayIndex: number, field: keyof BusinessHours, value: any) => {
    const updatedHours = [...restaurantInfo.businessHours];
    updatedHours[dayIndex] = { ...updatedHours[dayIndex], [field]: value };
    setRestaurantInfo({ ...restaurantInfo, businessHours: updatedHours });
  };

  // Toggle feature
  const toggleFeature = (feature: string) => {
    const features = restaurantInfo.features.includes(feature)
      ? restaurantInfo.features.filter(f => f !== feature)
      : [...restaurantInfo.features, feature];
    setRestaurantInfo({ ...restaurantInfo, features });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/settings`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Settings
            </Button>
          </Link>
          <div className="flex items-center gap-3">
            <Store className="w-8 h-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Restaurant Information</h1>
              <p className="text-gray-600">Manage your restaurant details and settings</p>
            </div>
          </div>
        </div>

        <Button onClick={handleSave} disabled={loading}>
          <Save className="w-4 h-4 mr-2" />
          {loading ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
          <p className="text-sm text-gray-600">Essential details about your restaurant</p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Restaurant Name</Label>
              <Input
                id="name"
                value={restaurantInfo.name}
                onChange={(e) => setRestaurantInfo({ ...restaurantInfo, name: e.target.value })}
                placeholder="Enter restaurant name"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="cuisine">Cuisine Type</Label>
              <Select
                value={restaurantInfo.cuisine}
                onValueChange={(value) => setRestaurantInfo({ ...restaurantInfo, cuisine: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select cuisine type" />
                </SelectTrigger>
                <SelectContent>
                  {cuisineTypes.map((cuisine) => (
                    <SelectItem key={cuisine} value={cuisine}>
                      {cuisine}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="priceRange">Price Range</Label>
              <Select
                value={restaurantInfo.priceRange}
                onValueChange={(value) => setRestaurantInfo({ ...restaurantInfo, priceRange: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select price range" />
                </SelectTrigger>
                <SelectContent>
                  {priceRanges.map((range) => (
                    <SelectItem key={range.value} value={range.value}>
                      {range.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={restaurantInfo.description}
              onChange={(e) => setRestaurantInfo({ ...restaurantInfo, description: e.target.value })}
              placeholder="Describe your restaurant..."
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Phone className="w-5 h-5" />
            Contact Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                value={restaurantInfo.phone}
                onChange={(e) => setRestaurantInfo({ ...restaurantInfo, phone: e.target.value })}
                placeholder="+****************"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                value={restaurantInfo.email}
                onChange={(e) => setRestaurantInfo({ ...restaurantInfo, email: e.target.value })}
                placeholder="<EMAIL>"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="website">Website</Label>
              <Input
                id="website"
                value={restaurantInfo.website}
                onChange={(e) => setRestaurantInfo({ ...restaurantInfo, website: e.target.value })}
                placeholder="https://restaurant.com"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Address */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="w-5 h-5" />
            Address
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="street">Street Address</Label>
            <Input
              id="street"
              value={restaurantInfo.address.street}
              onChange={(e) => setRestaurantInfo({
                ...restaurantInfo,
                address: { ...restaurantInfo.address, street: e.target.value }
              })}
              placeholder="123 Main Street"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="city">City</Label>
              <Input
                id="city"
                value={restaurantInfo.address.city}
                onChange={(e) => setRestaurantInfo({
                  ...restaurantInfo,
                  address: { ...restaurantInfo.address, city: e.target.value }
                })}
                placeholder="New York"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="state">State/Province</Label>
              <Input
                id="state"
                value={restaurantInfo.address.state}
                onChange={(e) => setRestaurantInfo({
                  ...restaurantInfo,
                  address: { ...restaurantInfo.address, state: e.target.value }
                })}
                placeholder="NY"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="zipCode">ZIP/Postal Code</Label>
              <Input
                id="zipCode"
                value={restaurantInfo.address.zipCode}
                onChange={(e) => setRestaurantInfo({
                  ...restaurantInfo,
                  address: { ...restaurantInfo.address, zipCode: e.target.value }
                })}
                placeholder="10001"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="country">Country</Label>
              <Input
                id="country"
                value={restaurantInfo.address.country}
                onChange={(e) => setRestaurantInfo({
                  ...restaurantInfo,
                  address: { ...restaurantInfo.address, country: e.target.value }
                })}
                placeholder="United States"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Business Hours */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5" />
            Business Hours
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {restaurantInfo.businessHours.map((hours, index) => (
              <div key={hours.day} className="flex items-center gap-4 p-4 border border-gray-200 rounded-lg">
                <div className="w-20">
                  <span className="font-medium">{hours.day}</span>
                </div>

                <div className="flex items-center gap-2">
                  <Switch
                    checked={hours.isOpen}
                    onCheckedChange={(checked) => updateBusinessHours(index, 'isOpen', checked)}
                  />
                  <span className="text-sm text-gray-600">Open</span>
                </div>

                {hours.isOpen && (
                  <>
                    <div className="flex items-center gap-2">
                      <Input
                        type="time"
                        value={hours.openTime}
                        onChange={(e) => updateBusinessHours(index, 'openTime', e.target.value)}
                        className="w-32"
                      />
                      <span className="text-sm text-gray-600">to</span>
                      <Input
                        type="time"
                        value={hours.closeTime}
                        onChange={(e) => updateBusinessHours(index, 'closeTime', e.target.value)}
                        className="w-32"
                      />
                    </div>

                    <div className="flex items-center gap-2">
                      <Switch
                        checked={hours.isBreak}
                        onCheckedChange={(checked) => updateBusinessHours(index, 'isBreak', checked)}
                      />
                      <span className="text-sm text-gray-600">Break</span>
                    </div>

                    {hours.isBreak && (
                      <div className="flex items-center gap-2">
                        <Input
                          type="time"
                          value={hours.breakStart || ''}
                          onChange={(e) => updateBusinessHours(index, 'breakStart', e.target.value)}
                          className="w-32"
                          placeholder="Break start"
                        />
                        <span className="text-sm text-gray-600">to</span>
                        <Input
                          type="time"
                          value={hours.breakEnd || ''}
                          onChange={(e) => updateBusinessHours(index, 'breakEnd', e.target.value)}
                          className="w-32"
                          placeholder="Break end"
                        />
                      </div>
                    )}
                  </>
                )}

                {!hours.isOpen && (
                  <Badge variant="secondary">Closed</Badge>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Features & Amenities */}
      <Card>
        <CardHeader>
          <CardTitle>Features & Amenities</CardTitle>
          <p className="text-sm text-gray-600">Select the features available at your restaurant</p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {availableFeatures.map((feature) => (
              <div
                key={feature}
                onClick={() => toggleFeature(feature)}
                className={cn(
                  'p-3 border rounded-lg cursor-pointer transition-colors',
                  restaurantInfo.features.includes(feature)
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 hover:border-gray-300'
                )}
              >
                <span className="text-sm font-medium">{feature}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Images */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Camera className="w-5 h-5" />
            Restaurant Images
          </CardTitle>
          <p className="text-sm text-gray-600">Upload your restaurant logo, cover image, and gallery photos</p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <Label>Logo</Label>
                <div className="mt-2 border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <Camera className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600">Upload restaurant logo</p>
                  <Button variant="outline" size="sm" className="mt-2">
                    Choose File
                  </Button>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <Label>Cover Image</Label>
                <div className="mt-2 border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <Camera className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600">Upload cover image</p>
                  <Button variant="outline" size="sm" className="mt-2">
                    Choose File
                  </Button>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6">
            <Label>Gallery Images</Label>
            <div className="mt-2 border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <Camera className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600">Upload gallery images (up to 10 photos)</p>
              <Button variant="outline" size="sm" className="mt-2">
                <Plus className="w-4 h-4 mr-2" />
                Add Images
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
