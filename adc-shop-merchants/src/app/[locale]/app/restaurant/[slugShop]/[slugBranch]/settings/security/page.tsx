'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { toast } from 'sonner';
import {
  ArrowLeft,
  Shield,
  Key,
  Smartphone,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  AlertTriangle,
  CheckCircle,
  Clock,
  MapPin,
  Monitor,
  Save,
  RefreshCw,
  Download,
  Trash2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/components/ui/alert';
import { AppLoading } from '@/components/ui/app-loading';
import {
  useGetShopBySlugQuery,
  useGetBranchBySlugQuery,
  useGetBranchSettingsQuery,
  useUpdateBranchSettingsMutation
} from '@/lib/redux/api/endpoints/restaurant/shopApi';
import type { Shop, Branch } from '@/lib/redux/api/endpoints/restaurant/shopApi';

// Types
interface SecuritySettings {
  twoFactorEnabled: boolean;
  sessionTimeout: number;
  passwordRequirements: {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSymbols: boolean;
  };
  loginAttempts: {
    maxAttempts: number;
    lockoutDuration: number;
  };
  ipWhitelist: string[];
  auditLogging: boolean;
  dataEncryption: boolean;
  backupEncryption: boolean;
}

interface LoginSession {
  id: string;
  device: string;
  browser: string;
  location: string;
  ipAddress: string;
  loginTime: Date;
  lastActivity: Date;
  isCurrentSession: boolean;
}

interface SecurityEvent {
  id: string;
  type: 'login' | 'logout' | 'failed_login' | 'password_change' | 'settings_change';
  description: string;
  timestamp: Date;
  ipAddress: string;
  userAgent: string;
  severity: 'low' | 'medium' | 'high';
}

interface SecuritySettingsPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function SecuritySettingsPage({ params }: SecuritySettingsPageProps) {
  const { slugShop, slugBranch } = React.use(params);

  // Get shop and branch data from backend using slug-based queries
  const { data: shopData, isLoading: isShopLoading, error: shopError } = useGetShopBySlugQuery(slugShop) as {
    data: Shop | undefined;
    isLoading: boolean;
    error: unknown;
  };
  const { data: branchData, isLoading: isBranchLoading, error: branchError } = useGetBranchBySlugQuery({
    shopSlug: slugShop,
    branchSlug: slugBranch
  }) as {
    data: Branch | undefined;
    isLoading: boolean;
    error: unknown;
  };

  // Get branch settings
  const {
    data: branchSettings,
    isLoading: isLoadingSettings,
  } = useGetBranchSettingsQuery(
    { shopId: shopData?.id || '', branchId: branchData?.id || '' },
    { skip: !shopData?.id || !branchData?.id }
  );

  // Update branch settings mutation
  const [updateBranchSettings, { isLoading: isUpdating }] = useUpdateBranchSettingsMutation();

  // State
  const [loading, setLoading] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const [securitySettings, setSecuritySettings] = useState<SecuritySettings>({
    twoFactorEnabled: false,
    sessionTimeout: 30,
    passwordRequirements: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSymbols: false,
    },
    loginAttempts: {
      maxAttempts: 5,
      lockoutDuration: 15,
    },
    ipWhitelist: [],
    auditLogging: true,
    dataEncryption: true,
    backupEncryption: true,
  });

  const isLoading = isShopLoading || isBranchLoading || isLoadingSettings;
  const hasError = shopError || branchError;

  // Load security settings from branch settings
  useEffect(() => {
    if (branchSettings) {
      setSecuritySettings(prev => ({
        ...prev,
        twoFactorEnabled: branchSettings.twoFactorEnabled || false,
        sessionTimeout: branchSettings.sessionTimeout || 30,
        auditLogging: branchSettings.auditLogging || true,
        dataEncryption: branchSettings.dataEncryption || true,
        backupEncryption: branchSettings.backupEncryption || true,
        passwordRequirements: {
          minLength: branchSettings.passwordMinLength || 8,
          requireUppercase: branchSettings.passwordRequireUppercase || true,
          requireLowercase: branchSettings.passwordRequireLowercase || true,
          requireNumbers: branchSettings.passwordRequireNumbers || true,
          requireSymbols: branchSettings.passwordRequireSymbols || false,
        },
        loginAttempts: {
          maxAttempts: branchSettings.maxLoginAttempts || 5,
          lockoutDuration: branchSettings.lockoutDuration || 15,
        },
        ipWhitelist: branchSettings.ipWhitelist || [],
      }));
    }
  }, [branchSettings]);

  // Mock data
  const mockSessions: LoginSession[] = [
    {
      id: '1',
      device: 'MacBook Pro',
      browser: 'Chrome 120.0',
      location: 'New York, NY',
      ipAddress: '***********00',
      loginTime: new Date(Date.now() - 2 * 60 * 60 * 1000),
      lastActivity: new Date(Date.now() - 5 * 60 * 1000),
      isCurrentSession: true,
    },
    {
      id: '2',
      device: 'iPhone 15',
      browser: 'Safari 17.0',
      location: 'New York, NY',
      ipAddress: '***********01',
      loginTime: new Date(Date.now() - 6 * 60 * 60 * 1000),
      lastActivity: new Date(Date.now() - 30 * 60 * 1000),
      isCurrentSession: false,
    },
    {
      id: '3',
      device: 'iPad Pro',
      browser: 'Safari 17.0',
      location: 'Brooklyn, NY',
      ipAddress: '*********',
      loginTime: new Date(Date.now() - 24 * 60 * 60 * 1000),
      lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000),
      isCurrentSession: false,
    },
  ];

  const mockSecurityEvents: SecurityEvent[] = [
    {
      id: '1',
      type: 'login',
      description: 'Successful login from new device',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      ipAddress: '***********00',
      userAgent: 'Chrome/120.0.0.0',
      severity: 'low',
    },
    {
      id: '2',
      type: 'settings_change',
      description: 'Security settings updated',
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
      ipAddress: '***********00',
      userAgent: 'Chrome/120.0.0.0',
      severity: 'medium',
    },
    {
      id: '3',
      type: 'failed_login',
      description: 'Failed login attempt',
      timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000),
      ipAddress: '***********',
      userAgent: 'Unknown',
      severity: 'high',
    },
  ];

  // Handle password change
  const handlePasswordChange = async () => {
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      toast.error('New passwords do not match');
      return;
    }

    if (passwordForm.newPassword.length < securitySettings.passwordRequirements.minLength) {
      toast.error(`Password must be at least ${securitySettings.passwordRequirements.minLength} characters long`);
      return;
    }

    setLoading(true);
    try {
      // TODO: Implement API call to change password
      console.log('Changing password...');

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Reset form
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });

      toast.success('Password changed successfully!');
    } catch (error) {
      console.error('Error changing password:', error);
      toast.error('Failed to change password. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle settings save
  const handleSaveSettings = async () => {
    if (!shopData?.id || !branchData?.id) {
      toast.error('Missing shop or branch information');
      return;
    }

    try {
      // Update branch settings using real API
      await updateBranchSettings({
        shopId: shopData.id,
        branchId: branchData.id,
        settings: {
          // Security settings
          twoFactorEnabled: securitySettings.twoFactorEnabled,
          sessionTimeout: securitySettings.sessionTimeout,
          auditLogging: securitySettings.auditLogging,
          dataEncryption: securitySettings.dataEncryption,
          backupEncryption: securitySettings.backupEncryption,
          passwordMinLength: securitySettings.passwordRequirements.minLength,
          passwordRequireUppercase: securitySettings.passwordRequirements.requireUppercase,
          passwordRequireLowercase: securitySettings.passwordRequirements.requireLowercase,
          passwordRequireNumbers: securitySettings.passwordRequirements.requireNumbers,
          passwordRequireSymbols: securitySettings.passwordRequirements.requireSymbols,
          maxLoginAttempts: securitySettings.loginAttempts.maxAttempts,
          lockoutDuration: securitySettings.loginAttempts.lockoutDuration,
          ipWhitelist: securitySettings.ipWhitelist,
        },
      }).unwrap();

      toast.success('Security settings saved successfully!');
    } catch (error) {
      console.error('Error saving security settings:', error);
      toast.error('Failed to save security settings. Please try again.');
    }
  };

  // Terminate session
  const terminateSession = async (sessionId: string) => {
    try {
      // TODO: Implement API call to terminate session
      console.log('Terminating session:', sessionId);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      toast.success('Session terminated successfully!');
    } catch (error) {
      console.error('Error terminating session:', error);
      toast.error('Failed to terminate session. Please try again.');
    }
  };

  // Get severity badge
  const getSeverityBadge = (severity: SecurityEvent['severity']) => {
    const severityConfig = {
      low: { color: 'bg-green-100 text-green-800', label: 'Low' },
      medium: { color: 'bg-yellow-100 text-yellow-800', label: 'Medium' },
      high: { color: 'bg-red-100 text-red-800', label: 'High' },
    };

    const config = severityConfig[severity];

    return (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    );
  };

  // Format date
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Format time ago
  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (hasError || !shopData || !branchData) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Restaurant Not Found</h1>
          <p className="text-[#8a745c] text-sm">The restaurant or branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/settings`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Settings
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Security & Privacy</h1>
          <p className="text-[#8a745c] text-sm">Manage security and privacy settings for {shopData.name} - {branchData.name}</p>
        </div>
        <Button
          onClick={handleSaveSettings}
          disabled={isUpdating}
          className="bg-[#e58219] hover:bg-[#d67917] text-white font-bold px-6 h-12"
        >
          <Save className="w-4 h-4 mr-2" />
          {isUpdating ? 'Saving...' : 'Save Settings'}
        </Button>
      </div>

    <div className="max-w-4xl space-y-6">

      {/* Security Status */}
      <Alert>
        <CheckCircle className="h-4 w-4" />
        <AlertTitle>Security Status: Good</AlertTitle>
        <AlertDescription>
          Your account security is properly configured. Two-factor authentication is recommended for enhanced security.
        </AlertDescription>
      </Alert>

      {/* Password Change */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="w-5 h-5" />
            Change Password
          </CardTitle>
          <p className="text-sm text-gray-600">Update your account password</p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="currentPassword">Current Password</Label>
            <div className="relative">
              <Input
                id="currentPassword"
                type={showCurrentPassword ? 'text' : 'password'}
                value={passwordForm.currentPassword}
                onChange={(e) => setPasswordForm({ ...passwordForm, currentPassword: e.target.value })}
                placeholder="Enter current password"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowCurrentPassword(!showCurrentPassword)}
              >
                {showCurrentPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="newPassword">New Password</Label>
              <div className="relative">
                <Input
                  id="newPassword"
                  type={showNewPassword ? 'text' : 'password'}
                  value={passwordForm.newPassword}
                  onChange={(e) => setPasswordForm({ ...passwordForm, newPassword: e.target.value })}
                  placeholder="Enter new password"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                >
                  {showNewPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm New Password</Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={passwordForm.confirmPassword}
                  onChange={(e) => setPasswordForm({ ...passwordForm, confirmPassword: e.target.value })}
                  placeholder="Confirm new password"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>

          <Button onClick={handlePasswordChange} disabled={loading}>
            <Key className="w-4 h-4 mr-2" />
            {loading ? 'Changing...' : 'Change Password'}
          </Button>
        </CardContent>
      </Card>

      {/* Two-Factor Authentication */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Smartphone className="w-5 h-5" />
            Two-Factor Authentication
          </CardTitle>
          <p className="text-sm text-gray-600">Add an extra layer of security to your account</p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">Enable Two-Factor Authentication</h4>
              <p className="text-sm text-gray-600">
                Require a verification code from your phone when signing in
              </p>
            </div>
            <Switch
              checked={securitySettings.twoFactorEnabled}
              onCheckedChange={(checked) =>
                setSecuritySettings({ ...securitySettings, twoFactorEnabled: checked })
              }
            />
          </div>

          {!securitySettings.twoFactorEnabled && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Recommended</AlertTitle>
              <AlertDescription>
                Enable two-factor authentication to significantly improve your account security.
              </AlertDescription>
            </Alert>
          )}

          {securitySettings.twoFactorEnabled && (
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="font-medium text-green-800">Two-Factor Authentication Enabled</span>
              </div>
              <p className="text-sm text-green-700">
                Your account is protected with two-factor authentication.
              </p>
              <div className="mt-3 flex gap-2">
                <Button variant="outline" size="sm">
                  View Recovery Codes
                </Button>
                <Button variant="outline" size="sm">
                  Reconfigure
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Security Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Security Preferences</CardTitle>
          <p className="text-sm text-gray-600">Configure additional security options</p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">Audit Logging</h4>
              <p className="text-sm text-gray-600">
                Keep detailed logs of all account activities
              </p>
            </div>
            <Switch
              checked={securitySettings.auditLogging}
              onCheckedChange={(checked) =>
                setSecuritySettings({ ...securitySettings, auditLogging: checked })
              }
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">Data Encryption</h4>
              <p className="text-sm text-gray-600">
                Encrypt sensitive data at rest and in transit
              </p>
            </div>
            <Switch
              checked={securitySettings.dataEncryption}
              onCheckedChange={(checked) =>
                setSecuritySettings({ ...securitySettings, dataEncryption: checked })
              }
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">Backup Encryption</h4>
              <p className="text-sm text-gray-600">
                Encrypt all backup files and exports
              </p>
            </div>
            <Switch
              checked={securitySettings.backupEncryption}
              onCheckedChange={(checked) =>
                setSecuritySettings({ ...securitySettings, backupEncryption: checked })
              }
            />
          </div>

          <Separator />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="sessionTimeout">Session Timeout (minutes)</Label>
              <Input
                id="sessionTimeout"
                type="number"
                value={securitySettings.sessionTimeout}
                onChange={(e) => setSecuritySettings({
                  ...securitySettings,
                  sessionTimeout: parseInt(e.target.value) || 30
                })}
                min="5"
                max="480"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxAttempts">Max Login Attempts</Label>
              <Input
                id="maxAttempts"
                type="number"
                value={securitySettings.loginAttempts.maxAttempts}
                onChange={(e) => setSecuritySettings({
                  ...securitySettings,
                  loginAttempts: {
                    ...securitySettings.loginAttempts,
                    maxAttempts: parseInt(e.target.value) || 5
                  }
                })}
                min="3"
                max="10"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Password Requirements */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lock className="w-5 h-5" />
            Password Requirements
          </CardTitle>
          <p className="text-sm text-gray-600">Configure password complexity requirements</p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="minLength">Minimum Length</Label>
              <Input
                id="minLength"
                type="number"
                value={securitySettings.passwordRequirements.minLength}
                onChange={(e) => setSecuritySettings({
                  ...securitySettings,
                  passwordRequirements: {
                    ...securitySettings.passwordRequirements,
                    minLength: parseInt(e.target.value) || 8
                  }
                })}
                min="6"
                max="32"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="lockoutDuration">Lockout Duration (minutes)</Label>
              <Input
                id="lockoutDuration"
                type="number"
                value={securitySettings.loginAttempts.lockoutDuration}
                onChange={(e) => setSecuritySettings({
                  ...securitySettings,
                  loginAttempts: {
                    ...securitySettings.loginAttempts,
                    lockoutDuration: parseInt(e.target.value) || 15
                  }
                })}
                min="5"
                max="120"
              />
            </div>
          </div>

          <Separator />

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Require Uppercase Letters</h4>
                <p className="text-sm text-gray-600">
                  Password must contain at least one uppercase letter (A-Z)
                </p>
              </div>
              <Switch
                checked={securitySettings.passwordRequirements.requireUppercase}
                onCheckedChange={(checked) =>
                  setSecuritySettings({
                    ...securitySettings,
                    passwordRequirements: {
                      ...securitySettings.passwordRequirements,
                      requireUppercase: checked
                    }
                  })
                }
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Require Lowercase Letters</h4>
                <p className="text-sm text-gray-600">
                  Password must contain at least one lowercase letter (a-z)
                </p>
              </div>
              <Switch
                checked={securitySettings.passwordRequirements.requireLowercase}
                onCheckedChange={(checked) =>
                  setSecuritySettings({
                    ...securitySettings,
                    passwordRequirements: {
                      ...securitySettings.passwordRequirements,
                      requireLowercase: checked
                    }
                  })
                }
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Require Numbers</h4>
                <p className="text-sm text-gray-600">
                  Password must contain at least one number (0-9)
                </p>
              </div>
              <Switch
                checked={securitySettings.passwordRequirements.requireNumbers}
                onCheckedChange={(checked) =>
                  setSecuritySettings({
                    ...securitySettings,
                    passwordRequirements: {
                      ...securitySettings.passwordRequirements,
                      requireNumbers: checked
                    }
                  })
                }
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Require Special Characters</h4>
                <p className="text-sm text-gray-600">
                  Password must contain at least one special character (!@#$%^&*)
                </p>
              </div>
              <Switch
                checked={securitySettings.passwordRequirements.requireSymbols}
                onCheckedChange={(checked) =>
                  setSecuritySettings({
                    ...securitySettings,
                    passwordRequirements: {
                      ...securitySettings.passwordRequirements,
                      requireSymbols: checked
                    }
                  })
                }
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* IP Whitelist */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            IP Address Whitelist
          </CardTitle>
          <p className="text-sm text-gray-600">Restrict access to specific IP addresses (optional)</p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="ipWhitelist">Allowed IP Addresses</Label>
            <Input
              id="ipWhitelist"
              placeholder="Enter IP addresses separated by commas (e.g., ***********, ********)"
              value={securitySettings.ipWhitelist.join(', ')}
              onChange={(e) => {
                const ips = e.target.value
                  .split(',')
                  .map(ip => ip.trim())
                  .filter(ip => ip.length > 0);
                setSecuritySettings({
                  ...securitySettings,
                  ipWhitelist: ips
                });
              }}
            />
            <p className="text-xs text-gray-500">
              Leave empty to allow access from any IP address. Use comma-separated values for multiple IPs.
            </p>
          </div>

          {securitySettings.ipWhitelist.length > 0 && (
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Shield className="w-4 h-4 text-blue-600" />
                <span className="font-medium text-blue-800">IP Whitelist Active</span>
              </div>
              <p className="text-sm text-blue-700">
                Access is restricted to {securitySettings.ipWhitelist.length} IP address(es).
              </p>
              <div className="mt-2 flex flex-wrap gap-1">
                {securitySettings.ipWhitelist.map((ip, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {ip}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {securitySettings.ipWhitelist.length === 0 && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>No IP Restrictions</AlertTitle>
              <AlertDescription>
                Access is allowed from any IP address. Consider adding trusted IPs for enhanced security.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Active Sessions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Monitor className="w-5 h-5" />
            Active Sessions
          </CardTitle>
          <p className="text-sm text-gray-600">Manage your active login sessions</p>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Device & Browser</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Login Time</TableHead>
                <TableHead>Last Activity</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockSessions.map((session) => (
                <TableRow key={session.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium flex items-center gap-2">
                        {session.device}
                        {session.isCurrentSession && (
                          <Badge variant="secondary">Current</Badge>
                        )}
                      </div>
                      <div className="text-sm text-gray-500">{session.browser}</div>
                      <div className="text-xs text-gray-400">{session.ipAddress}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <MapPin className="w-3 h-3 text-gray-400" />
                      {session.location}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {formatDate(session.loginTime)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {formatTimeAgo(session.lastActivity)}
                    </div>
                  </TableCell>
                  <TableCell>
                    {!session.isCurrentSession && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => terminateSession(session.id)}
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Terminate
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Security Events */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" />
                Recent Security Events
              </CardTitle>
              <p className="text-sm text-gray-600">Monitor recent security-related activities</p>
            </div>
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export Log
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Event</TableHead>
                <TableHead>Timestamp</TableHead>
                <TableHead>IP Address</TableHead>
                <TableHead>Severity</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockSecurityEvents.map((event) => (
                <TableRow key={event.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{event.description}</div>
                      <div className="text-sm text-gray-500 capitalize">{event.type.replace('_', ' ')}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {formatDate(event.timestamp)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm font-mono">
                      {event.ipAddress}
                    </div>
                  </TableCell>
                  <TableCell>
                    {getSeverityBadge(event.severity)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
      </div>
    </div>
  );
}
