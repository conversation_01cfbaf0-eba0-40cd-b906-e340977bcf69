"use client";

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { TagSelector } from '@/components/ui/tag-selector';
import { toast } from 'sonner';
import { useCreateStaffMemberMutation, useUpdateStaffMemberMutation } from '@/lib/redux/api/endpoints/restaurant/staffApi';

const staffSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Valid email is required'),
  phone: z.string().optional(),
  position: z.string().min(1, 'Position is required'),
  department: z.string().optional(),
  roleId: z.string().min(1, 'Role is required'),
  employeeId: z.string().optional(),
  hireDate: z.string().optional(),
  salary: z.number().optional(),
  skills: z.array(z.string()).default([]),
  certifications: z.array(z.string()).default([]),
  languages: z.array(z.string()).default([]),
  notes: z.string().optional(),
  status: z.enum(['active', 'inactive', 'suspended']).default('active'),
});

type StaffFormData = z.infer<typeof staffSchema>;

interface StaffFormProps {
  shopId: string;
  branchId: string;
  slugShop: string;
  slugBranch: string;
  mode: 'create' | 'edit';
  initialData?: Partial<StaffFormData & { id: string }>;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function StaffForm({
  shopId,
  branchId,
  slugShop,
  slugBranch,
  mode,
  initialData,
  onSuccess,
  onCancel
}: StaffFormProps) {
  const [createStaffMember, { isLoading: isCreating }] = useCreateStaffMemberMutation();
  const [updateStaffMember, { isLoading: isUpdating }] = useUpdateStaffMemberMutation();

  const isLoading = isCreating || isUpdating;

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<StaffFormData>({
    resolver: zodResolver(staffSchema),
    defaultValues: {
      firstName: initialData?.firstName || '',
      lastName: initialData?.lastName || '',
      email: initialData?.email || '',
      phone: initialData?.phone || '',
      position: initialData?.position || '',
      department: initialData?.department || '',
      roleId: initialData?.roleId || '',
      employeeId: initialData?.employeeId || '',
      hireDate: initialData?.hireDate || '',
      salary: initialData?.salary || undefined,
      skills: initialData?.skills || [],
      certifications: initialData?.certifications || [],
      languages: initialData?.languages || [],
      notes: initialData?.notes || '',
      status: initialData?.status || 'active',
    }
  });

  const skills = watch('skills');
  const certifications = watch('certifications');
  const languages = watch('languages');

  const onSubmit = async (data: StaffFormData) => {
    try {
      if (mode === 'create') {
        await createStaffMember({
          shopSlug: slugShop,
          branchSlug: slugBranch,
          staffData: data
        }).unwrap();
      } else if (initialData?.id) {
        await updateStaffMember({
          shopSlug: slugShop,
          branchSlug: slugBranch,
          staffData: { ...data, id: initialData.id }
        }).unwrap();
      }

      toast.success(`Staff member ${mode === 'create' ? 'created' : 'updated'} successfully`);
      onSuccess?.();
    } catch (error) {
      toast.error(`Failed to ${mode} staff member`);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>{mode === 'create' ? 'Add New Staff Member' : 'Edit Staff Member'}</CardTitle>
        <CardDescription>
          {mode === 'create'
            ? 'Add a new team member to your restaurant staff'
            : 'Update staff member information and skills'
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName">First Name *</Label>
              <Input
                id="firstName"
                {...register('firstName')}
                placeholder="Enter first name"
              />
              {errors.firstName && (
                <p className="text-sm text-red-500">{errors.firstName.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="lastName">Last Name *</Label>
              <Input
                id="lastName"
                {...register('lastName')}
                placeholder="Enter last name"
              />
              {errors.lastName && (
                <p className="text-sm text-red-500">{errors.lastName.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                {...register('email')}
                placeholder="<EMAIL>"
              />
              {errors.email && (
                <p className="text-sm text-red-500">{errors.email.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                {...register('phone')}
                placeholder="+****************"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="position">Position *</Label>
              <Select onValueChange={(value) => setValue('position', value)} defaultValue={watch('position')}>
                <SelectTrigger>
                  <SelectValue placeholder="Select position" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="manager">Manager</SelectItem>
                  <SelectItem value="chef">Chef</SelectItem>
                  <SelectItem value="sous_chef">Sous Chef</SelectItem>
                  <SelectItem value="line_cook">Line Cook</SelectItem>
                  <SelectItem value="server">Server</SelectItem>
                  <SelectItem value="bartender">Bartender</SelectItem>
                  <SelectItem value="host">Host/Hostess</SelectItem>
                  <SelectItem value="cashier">Cashier</SelectItem>
                  <SelectItem value="cleaner">Cleaner</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
              {errors.position && (
                <p className="text-sm text-red-500">{errors.position.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="roleId">Role *</Label>
              <Select onValueChange={(value) => setValue('roleId', value)} defaultValue={watch('roleId')}>
                <SelectTrigger>
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="staff">Staff</SelectItem>
                  <SelectItem value="supervisor">Supervisor</SelectItem>
                  <SelectItem value="manager">Manager</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                </SelectContent>
              </Select>
              {errors.roleId && (
                <p className="text-sm text-red-500">{errors.roleId.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="department">Department</Label>
              <Select onValueChange={(value) => setValue('department', value)} defaultValue={watch('department')}>
                <SelectTrigger>
                  <SelectValue placeholder="Select department" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="kitchen">Kitchen</SelectItem>
                  <SelectItem value="front_of_house">Front of House</SelectItem>
                  <SelectItem value="bar">Bar</SelectItem>
                  <SelectItem value="management">Management</SelectItem>
                  <SelectItem value="cleaning">Cleaning</SelectItem>
                  <SelectItem value="delivery">Delivery</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select onValueChange={(value) => setValue('status', value as any)} defaultValue={watch('status')}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="on_leave">On Leave</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Skills & Certifications */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Skills & Expertise</Label>
              <TagSelector
                shopId={shopId}
                branchId={branchId}
                value={skills}
                onChange={(newSkills) => setValue('skills', newSkills)}
                entityType="staff"
                placeholder="Add skills (e.g., Barista, Sommelier, Pastry Chef, Customer Service)..."
                categoryFilter="skills"
                allowCustomTags={true}
                maxTags={15}
              />
              <p className="text-xs text-muted-foreground">
                Add skills and areas of expertise for this staff member.
              </p>
            </div>

            <div className="space-y-2">
              <Label>Certifications</Label>
              <TagSelector
                shopId={shopId}
                branchId={branchId}
                value={certifications}
                onChange={(newCertifications) => setValue('certifications', newCertifications)}
                entityType="staff"
                placeholder="Add certifications (e.g., Food Safety, Wine Expert, First Aid)..."
                categoryFilter="certifications"
                allowCustomTags={true}
                maxTags={10}
              />
              <p className="text-xs text-muted-foreground">
                Add professional certifications and qualifications.
              </p>
            </div>

            <div className="space-y-2">
              <Label>Languages</Label>
              <TagSelector
                shopId={shopId}
                branchId={branchId}
                value={languages}
                onChange={(newLanguages) => setValue('languages', newLanguages)}
                entityType="staff"
                placeholder="Add languages (e.g., English, Spanish, French, Italian)..."
                categoryFilter="languages"
                allowCustomTags={true}
                maxTags={8}
              />
              <p className="text-xs text-muted-foreground">
                Add languages this staff member can speak.
              </p>
            </div>
          </div>

          {/* Additional Information */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              {...register('notes')}
              placeholder="Additional notes about this staff member..."
              rows={3}
            />
          </div>

          {/* Form Actions */}
          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              disabled={isLoading}
              className="flex-1"
            >
              {isLoading
                ? (mode === 'create' ? 'Creating...' : 'Updating...')
                : (mode === 'create' ? 'Add Staff Member' : 'Update Staff Member')
              }
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isLoading}
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
