'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { TableCanvas } from './_components/TableCanvas';
import { TableControls } from './_components/TableControls';
import { Link } from '@/i18n/navigation';
import {
  useGetTablesQuery,
  useGetTableAreasQuery,
  useCreateTableMutation,
  useUpdateTableMutation,
  useDeleteTableMutation,
  useCreateTableAreaMutation,
  useUpdateTableAreaMutation,
  useDeleteTableAreaMutation,
  Table as ApiTable,
  TableArea as ApiTableArea,
  CreateTableRequest
} from '@/lib/redux/api/endpoints/restaurant/tablesApi';
import { Floor as ApiFloor, useGetFloorsQuery, useCreateFloorMutation } from '@/lib/redux/api/endpoints/restaurant/floorsApi';
import { useGetBranchWithShopQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { AppLoading } from '@/components/ui/app-loading';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
  DialogDescription
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Plus,
  Trash2,
  Save,
  ArrowLeft,
  Move,
  Square,
  Circle,
  Settings,
  Edit,
  LayoutGrid
} from 'lucide-react';

interface LayoutEditorPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

// Define default floors (simplified for now - can be enhanced later)
const defaultFloors = [
  { id: 'floor-1', name: 'Ground Floor', order: 1 },
];

// Define table shapes
const tableShapes = [
  { id: 'square', name: 'Square', icon: <Square className="h-4 w-4" /> },
  { id: 'round', name: 'Round', icon: <Circle className="h-4 w-4" /> },
  { id: 'rectangle', name: 'Rectangle', icon: <Square className="h-4 w-4 rotate-90" /> },
];

interface Floor {
  id: string;
  name: string;
  order: number;
}

interface Area {
  id: string;
  name: string;
  floorId: string;
}

// Note: Using ApiTable from tablesApi instead of local Table interface

export default function LayoutEditorPage({ params }: LayoutEditorPageProps) {
  const { slugShop, slugBranch } = React.use(params);

  // Get branch data directly by slug (includes shop data)
  const { data: branchData, isLoading: isLoadingBranch, error: branchError } = useGetBranchWithShopQuery({
    shopSlug: slugShop,
    branchSlug: slugBranch
  });

  // Extract shop and branch from the response
  const organize = branchData?.shop;
  const branch = branchData;

  // Get tables data from backend
  const {
    data: tablesData,
    isLoading: isLoadingTables,
    error: tablesError,
    refetch: refetchTables
  } = useGetTablesQuery(
    {
      shopId: organize?.id || '',
      branchId: branch?.id || ''
    },
    {
      skip: !organize?.id || !branch?.id
    }
  );

  // Get table areas from backend
  const {
    data: areasData,
    isLoading: isLoadingAreas,
    error: areasError,
    refetch: refetchAreas
  } = useGetTableAreasQuery(
    {
      shopId: organize?.id || '',
      branchId: branch?.id || ''
    },
    {
      skip: !organize?.id || !branch?.id
    }
  );

  // Get floors from backend
  const {
    data: floorsData,
    isLoading: isLoadingFloors,
    error: floorsError,
    refetch: refetchFloors
  } = useGetFloorsQuery(
    {
      shopId: organize?.id || '',
      branchId: branch?.id || '',
      filters: { include_areas: true }
    },
    {
      skip: !organize?.id || !branch?.id
    }
  );

  // API mutations
  const [createTable, { isLoading: isCreatingTable }] = useCreateTableMutation();
  const [updateTable, { isLoading: isUpdatingTable }] = useUpdateTableMutation();
  const [deleteTable, { isLoading: isDeletingTable }] = useDeleteTableMutation();
  const [createTableArea, { isLoading: isCreatingArea }] = useCreateTableAreaMutation();
  const [updateTableArea, { isLoading: isUpdatingArea }] = useUpdateTableAreaMutation();
  const [deleteTableArea, { isLoading: isDeletingArea }] = useDeleteTableAreaMutation();
  const [createFloor, { isLoading: isCreatingFloor }] = useCreateFloorMutation();

  // State for UI
  const [selectedFloor, setSelectedFloor] = useState('');
  const [selectedArea, setSelectedArea] = useState('all-areas');
  const [selectedTable, setSelectedTable] = useState<ApiTable | null>(null);
  const [isAddTableOpen, setIsAddTableOpen] = useState(false);
  const [isEditTableOpen, setIsEditTableOpen] = useState(false);
  const [isAddAreaOpen, setIsAddAreaOpen] = useState(false);
  const [isAddFloorOpen, setIsAddFloorOpen] = useState(false);
  const [localTablePositions, setLocalTablePositions] = useState<Record<string, { x: number; y: number }>>({});
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showGrid, setShowGrid] = useState(true);
  const [canvasZoom, setCanvasZoom] = useState(1);
  const canvasRef = useRef<HTMLDivElement>(null);

  // Form state for adding/editing tables
  const [tableForm, setTableForm] = useState({
    name: '',
    capacity: 2,
    shape: 'square' as 'square' | 'round' | 'rectangle',
    width: 80,
    height: 80,
    floorId: '',
    areaId: '',
  });

  // Form state for adding areas
  const [areaForm, setAreaForm] = useState({
    name: '',
    description: '',
    color: '#8a745c',
  });

  // Form state for adding floors
  const [floorForm, setFloorForm] = useState({
    name: '',
    order: 1,
  });

  // Use real data from backend or fallback to empty arrays
  const tables = tablesData || [];
  const areas = areasData || [];
  const floors = floorsData?.data || [];

  // Set default selected area when areas are loaded
  React.useEffect(() => {
    if (areas.length > 0 && !selectedArea) {
      setSelectedArea(areas[0].name);
    }
  }, [areas, selectedArea]);

  // Set default selected floor when floors are loaded
  React.useEffect(() => {
    if (floors.length > 0 && !selectedFloor) {
      setSelectedFloor(floors[0].id);
    }
  }, [floors, selectedFloor]);

  // Set default form values when add table modal opens
  React.useEffect(() => {
    if (isAddTableOpen) {
      setTableForm(prev => ({
        ...prev,
        floorId: selectedFloor,
        areaId: areas.find(area => area.name === selectedArea)?.id || '',
      }));
    }
  }, [isAddTableOpen, selectedFloor, selectedArea, areas]);

  // Filter areas by selected floor using the floor_id field from areas API
  const filteredAreas = selectedFloor
    ? areas.filter(area => area.floor_id === selectedFloor)
    : areas;

  // Filter tables by selected floor and area
  const filteredTables = (() => {
    let tablesToShow = tables;

    // First filter by floor (through areas)
    if (selectedFloor) {
      const floorAreaIds = filteredAreas.map(area => area.id);
      tablesToShow = tablesToShow.filter(table =>
        table.area_id && floorAreaIds.includes(table.area_id)
      );
    }

    // Then filter by specific area if selected
    if (selectedArea && selectedArea !== 'all-areas') {
      tablesToShow = tablesToShow.filter(table => table.area?.name === selectedArea);
    }

    return tablesToShow;
  })();

  // Debug logging
  console.log('=== SLUG-BASED API DEBUG INFO ===');
  console.log('URL slugs:', { slugShop, slugBranch });
  console.log('Branch data from slug API:', branchData);
  console.log('Extracted organize (shop):', organize);
  console.log('Extracted branch:', branch);
  console.log('Shop ID:', organize?.id);
  console.log('Branch ID:', branch?.id);
  console.log('=== TABLE DEBUG INFO ===');
  console.log('Tables data from API:', tablesData);
  console.log('Tables array:', tables);
  console.log('Tables length:', tables.length);
  console.log('Areas data from API:', areasData);
  console.log('Floors data from API:', floorsData);
  console.log('Selected floor:', selectedFloor);
  console.log('Selected area:', selectedArea);
  console.log('Filtered areas:', filteredAreas);
  console.log('Filtered tables:', filteredTables);
  console.log('Filtering logic:');
  console.log('- selectedFloor:', selectedFloor);
  console.log('- selectedArea:', selectedArea);
  console.log('- areas before filter:', areas.length);
  console.log('- areas after filter:', filteredAreas.length);
  console.log('- tables before filter:', tables.length);
  console.log('- tables after filter:', filteredTables.length);
  console.log('- areas with floor_id:', areas.map(a => ({ id: a.id, name: a.name, floor_id: a.floor_id })));
  console.log('- table areas:', tables.map(t => ({ id: t.id, area: t.area?.name || 'No area', area_id: t.area_id })));
  console.log('- floor areas:', floors.map(f => ({ id: f.id, name: f.name, areas: f.areas?.map(a => ({ id: a.id, name: a.name })) })));
  console.log('========================');

  // Loading state
  const isLoading = isLoadingBranch || isLoadingTables || isLoadingAreas;
  const hasError = branchError || tablesError || areasError;

  // Handle table selection
  const handleTableClick = (table: ApiTable, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedTable(table);
  };

  // Handle canvas click (deselect table)
  const handleCanvasClick = () => {
    setSelectedTable(null);
  };

  // Handle table drag with Framer Motion
  const handleDragEnd = (tableId: string, info: { offset: { x: number; y: number }; point: { x: number; y: number } }) => {
    const { point } = info;

    console.log('Drag ended for table:', tableId, 'New position:', point);

    // Update local position state
    setLocalTablePositions(prev => {
      const newPositions = {
        ...prev,
        [tableId]: { x: point.x, y: point.y }
      };
      console.log('Updated local positions:', newPositions);
      return newPositions;
    });

    // Mark as having unsaved changes
    setHasUnsavedChanges(true);
  };

  // Canvas control handlers
  const handleToggleGrid = () => {
    setShowGrid(!showGrid);
    toast.info(`Grid ${!showGrid ? 'enabled' : 'disabled'}`);
  };

  const handleZoomIn = () => {
    setCanvasZoom(prev => {
      const newZoom = Math.min(prev + 0.1, 2);
      toast.info(`Zoom: ${Math.round(newZoom * 100)}%`);
      return newZoom;
    });
  };

  const handleZoomOut = () => {
    setCanvasZoom(prev => {
      const newZoom = Math.max(prev - 0.1, 0.5);
      toast.info(`Zoom: ${Math.round(newZoom * 100)}%`);
      return newZoom;
    });
  };

  const handleResetLayout = () => {
    setLocalTablePositions({});
    setHasUnsavedChanges(false);
    setCanvasZoom(1);
    toast.success('Layout reset to original positions');
  };

  // Handle adding a new table
  const handleAddTable = async () => {
    if (!organize?.id || !branch?.id) {
      toast.error('Unable to create table: missing organize or branch information');
      return;
    }

    try {
      // Validate required fields
      if (!tableForm.capacity || tableForm.capacity < 1) {
        toast.error('Table capacity must be at least 1');
        return;
      }

      // Generate next table number based on existing tables
      const existingNumbers = tables.map(t => parseInt(t.number || '0')).filter(n => !isNaN(n));
      const nextNumber = existingNumbers.length > 0 ? Math.max(...existingNumbers) + 1 : 1;

      // Use custom name if provided, otherwise generate default name
      const tableName = tableForm.name.trim() || `Table ${nextNumber}`;

      // Use the area ID from the form
      const tableData: CreateTableRequest = {
        name: tableName,
        number: String(nextNumber),
        capacity: tableForm.capacity,
        min_capacity: 1,
        max_capacity: tableForm.capacity,
        area_id: tableForm.areaId || undefined,
        position: {
          x: 100,
          y: 100,
          width: tableForm.width,
          height: tableForm.height,
          angle: 0
        },
        shape: tableForm.shape,
        is_active: true
      };

      console.log('Creating table with data:', tableData);

      await createTable({
        shopId: organize.id,
        branchId: branch.id,
        tableData
      }).unwrap();

      setIsAddTableOpen(false);
      setTableForm({
        name: '',
        capacity: 2,
        shape: 'square',
        width: 80,
        height: 80,
        floorId: '',
        areaId: '',
      });

      toast.success('Table added successfully');
      refetchTables();
    } catch (error) {
      toast.error('Failed to create table');
      console.error('Error creating table:', error);
    }
  };

  // Handle editing a table
  const handleEditTable = async () => {
    if (!selectedTable || !organize?.id || !branch?.id) return;

    try {
      // Use custom name if provided, otherwise keep existing name
      const tableName = tableForm.name.trim() || selectedTable.name || `Table ${selectedTable.number}`;

      await updateTable({
        shopId: organize.id,
        branchId: branch.id,
        tableData: {
          id: selectedTable.id,
          name: tableName,
          capacity: tableForm.capacity,
          shape: tableForm.shape,
          area_id: tableForm.areaId || undefined,
          position: {
            x: selectedTable.position?.x || 100,
            y: selectedTable.position?.y || 100,
            width: tableForm.width,
            height: tableForm.height,
            angle: 0
          }
        }
      }).unwrap();

      setIsEditTableOpen(false);
      toast.success('Table updated successfully');
      refetchTables();
    } catch (error) {
      toast.error('Failed to update table');
      console.error('Error updating table:', error);
    }
  };

  // Handle deleting a table
  const handleDeleteTable = async () => {
    if (!selectedTable || !organize?.id || !branch?.id) return;

    try {
      await deleteTable({
        shopId: organize.id,
        branchId: branch.id,
        tableId: selectedTable.id
      }).unwrap();

      setSelectedTable(null);
      toast.success('Table deleted successfully');
      refetchTables();
    } catch (error) {
      toast.error('Failed to delete table');
      console.error('Error deleting table:', error);
    }
  };

  // Open edit dialog
  const openEditDialog = () => {
    if (!selectedTable) return;

    setTableForm({
      name: selectedTable.name || '',
      capacity: selectedTable.capacity,
      shape: selectedTable.shape,
      width: selectedTable.position?.width || 80,
      height: selectedTable.position?.height || 80,
      floorId: selectedFloor,
      areaId: selectedTable.area?.id || '',
    });

    setIsEditTableOpen(true);
  };

  // Handle adding a new area
  const handleAddArea = async () => {
    if (!organize?.id || !branch?.id) {
      toast.error('Unable to create area: missing organize or branch information');
      return;
    }

    if (!areaForm.name.trim()) {
      toast.error('Area name is required');
      return;
    }

    try {
      await createTableArea({
        shopId: organize.id,
        branchId: branch.id,
        areaData: {
          name: areaForm.name.trim(),
          description: areaForm.description.trim(),
          color: areaForm.color,
        }
      }).unwrap();

      setIsAddAreaOpen(false);
      setAreaForm({
        name: '',
        description: '',
        color: '#8a745c',
      });

      toast.success('Area created successfully');
      refetchAreas();
    } catch (error) {
      toast.error('Failed to create area');
      console.error('Error creating area:', error);
    }
  };

  // Handle adding a new floor (local state for now)
  const handleAddFloor = async () => {
    if (!floorForm.name.trim()) {
      toast.error('Floor name is required');
      return;
    }

    if (!organize?.id || !branch?.id) {
      toast.error('Shop or branch information is missing');
      return;
    }

    try {
      const result = await createFloor({
        shopId: organize.id,
        branchId: branch.id,
        floorData: {
          name: floorForm.name.trim(),
          order: floorForm.order,
          layout: {
            width: 800,
            height: 600,
            grid_size: 20,
            show_grid: true,
          },
        },
      }).unwrap();

      setSelectedFloor(result.id);
      setIsAddFloorOpen(false);
      setFloorForm({
        name: '',
        order: floors.length + 1,
      });

      toast.success('Floor created successfully');
      refetchFloors(); // Refresh floors data
    } catch (error) {
      console.error('Error creating floor:', error);
      const errorMessage = error && typeof error === 'object' && 'data' in error &&
        error.data && typeof error.data === 'object' && 'error' in error.data
        ? String(error.data.error)
        : 'Failed to create floor';
      toast.error(errorMessage);
    }
  };

  // Save layout - update all table positions to server
  const saveLayout = async () => {
    if (!organize?.id || !branch?.id) {
      toast.error('Unable to save layout: missing organize or branch information');
      return;
    }

    if (Object.keys(localTablePositions).length === 0) {
      toast.info('No changes to save');
      return;
    }

    try {
      // Update all tables with new positions
      const updatePromises = Object.entries(localTablePositions).map(([tableId, position]) => {
        const table = tables.find(t => t.id === tableId);
        if (!table) return Promise.resolve();

        return updateTable({
          shopId: organize.id,
          branchId: branch.id,
          tableData: {
            id: tableId,
            position: {
              x: position.x,
              y: position.y,
              width: table.position?.width || 80,
              height: table.position?.height || 80,
              angle: table.position?.angle || 0
            }
          }
        }).unwrap();
      });

      await Promise.all(updatePromises);

      // Clear local positions and unsaved changes
      setLocalTablePositions({});
      setHasUnsavedChanges(false);

      toast.success('Layout saved successfully');
      refetchTables();
    } catch (error) {
      toast.error('Failed to save layout');
      console.error('Error saving layout:', error);
    }
  };

  // Loading state
  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  // Error state
  if (hasError) {
    return (
      <div className="text-center py-12">
        <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Error Loading Data</h1>
        <p className="text-[#8a745c] text-sm">There was an error loading the layout editor. Please try again.</p>
      </div>
    );
  }

  // Not found state
  if (!organize || !branch) {
    return (
      <div className="p-6 font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Restaurant Not Found</h1>
          <p className="text-[#8a745c] text-sm">The restaurant or branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-3 sm:p-6 font-be-vietnam">
      {/* Header - Responsive for mobile */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-[#181510]">Table Layout Editor</h1>
          <p className="text-[#8a745c] text-sm">Design your restaurant floor plan for {organize.name} - {branch.name}</p>
        </div>
        <div className="flex flex-wrap gap-2 sm:gap-3 w-full sm:w-auto">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/tables`} className="w-1/2 sm:w-auto">
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510] w-full sm:w-auto">
              <ArrowLeft className="h-4 w-4 mr-2 hidden sm:inline" />
              Back
            </Button>
          </Link>
          <Button
            className={`w-1/2 sm:w-auto ${
              hasUnsavedChanges
                ? 'bg-orange-500 text-white hover:bg-orange-600'
                : 'bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e]'
            }`}
            onClick={saveLayout}
          >
            <Save className="h-4 w-4 mr-2 hidden sm:inline" />
            {hasUnsavedChanges ? 'Save Changes' : 'Save Layout'}
          </Button>
        </div>
      </div>

      {/* Floor and Area Selection */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex-1">
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium text-[#181510]">Floor</label>
            <Dialog open={isAddFloorOpen} onOpenChange={setIsAddFloorOpen}>
              <DialogTrigger asChild>
                <Button size="sm" variant="outline" className="border-[#e2dcd4] text-[#181510] text-xs">
                  <Plus className="h-3 w-3 mr-1" />
                  Add Floor
                </Button>
              </DialogTrigger>
              <DialogContent className="bg-[#fbfaf9]">
                <DialogHeader>
                  <DialogTitle>Add New Floor</DialogTitle>
                  <DialogDescription>
                    Create a new floor for your restaurant layout.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-[#181510] mb-2">Floor Name</label>
                    <Input
                      placeholder="e.g., Ground Floor, Second Floor, Basement"
                      value={floorForm.name}
                      onChange={(e) => setFloorForm({ ...floorForm, name: e.target.value })}
                      className="bg-[#fbfaf9] border-[#e2dcd4]"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-[#181510] mb-2">Floor Order</label>
                    <Input
                      type="number"
                      min="1"
                      value={floorForm.order}
                      onChange={(e) => setFloorForm({ ...floorForm, order: parseInt(e.target.value) || 1 })}
                      className="bg-[#fbfaf9] border-[#e2dcd4]"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <DialogClose asChild>
                    <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
                      Cancel
                    </Button>
                  </DialogClose>
                  <Button onClick={handleAddFloor} className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e]">
                    Create Floor
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
          <Select value={selectedFloor} onValueChange={setSelectedFloor}>
            <SelectTrigger className="bg-[#fbfaf9] border-[#e2dcd4]">
              <SelectValue placeholder="Select floor" />
            </SelectTrigger>
            <SelectContent>
              {floors.map((floor) => (
                <SelectItem key={floor.id} value={floor.id}>
                  {floor.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex-1">
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium text-[#181510]">Area</label>
            <Dialog open={isAddAreaOpen} onOpenChange={setIsAddAreaOpen}>
              <DialogTrigger asChild>
                <Button size="sm" variant="outline" className="border-[#e2dcd4] text-[#181510] text-xs">
                  <Plus className="h-3 w-3 mr-1" />
                  Add Area
                </Button>
              </DialogTrigger>
              <DialogContent className="bg-[#fbfaf9]">
                <DialogHeader>
                  <DialogTitle>Add New Area</DialogTitle>
                  <DialogDescription>
                    Create a new dining area for your restaurant.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-[#181510] mb-2">Area Name</label>
                    <Input
                      placeholder="e.g., Dining Area, Outdoor Patio"
                      value={areaForm.name}
                      onChange={(e) => setAreaForm({ ...areaForm, name: e.target.value })}
                      className="bg-background border-border"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">Description (Optional)</label>
                    <Textarea
                      placeholder="Brief description of the area"
                      value={areaForm.description}
                      onChange={(e) => setAreaForm({ ...areaForm, description: e.target.value })}
                      className="bg-background border-border"
                      rows={3}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">Color</label>
                    <Input
                      type="color"
                      value={areaForm.color}
                      onChange={(e) => setAreaForm({ ...areaForm, color: e.target.value })}
                      className="bg-background border-border h-10 w-20"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <DialogClose asChild>
                    <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
                      Cancel
                    </Button>
                  </DialogClose>
                  <Button onClick={handleAddArea} className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e]">
                    Create Area
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
          <Select value={selectedArea} onValueChange={setSelectedArea}>
            <SelectTrigger className="bg-[#fbfaf9] border-[#e2dcd4]">
              <SelectValue placeholder="Select area" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all-areas">All Areas (Show All Tables)</SelectItem>
              {filteredAreas.map((area) => (
                <SelectItem key={area.id} value={area.name}>
                  {area.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Main content - Responsive grid layout */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6">
        {/* Sidebar - Controls and Selected Table */}
        <div className="lg:col-span-1 order-2 lg:order-1">
          <TableControls
            selectedTable={selectedTable}
            hasUnsavedChanges={hasUnsavedChanges}
            showGrid={showGrid}
            zoom={canvasZoom}
            onAddTable={() => setIsAddTableOpen(true)}
            onEditTable={openEditDialog}
            onDeleteTable={handleDeleteTable}
            onToggleGrid={handleToggleGrid}
            onResetLayout={handleResetLayout}
            onZoomIn={handleZoomIn}
            onZoomOut={handleZoomOut}
          />

          {/* Add Table Dialog */}
          <Dialog open={isAddTableOpen} onOpenChange={setIsAddTableOpen}>
              <DialogContent className="bg-[#fbfaf9]">
                <DialogHeader>
                  <DialogTitle>Add New Table</DialogTitle>
                  <DialogDescription>
                    Create a new table for your restaurant.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-[#181510] mb-2">Table Name (Optional)</label>
                    <Input
                      type="text"
                      placeholder="e.g., VIP Table, Window Table (leave empty for auto-naming)"
                      value={tableForm.name}
                      onChange={(e) => setTableForm({ ...tableForm, name: e.target.value })}
                      className="bg-[#fbfaf9] border-[#e2dcd4]"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-[#181510] mb-2">Floor</label>
                      <Select value={tableForm.floorId} onValueChange={(value) => setTableForm({ ...tableForm, floorId: value })}>
                        <SelectTrigger className="bg-[#fbfaf9] border-[#e2dcd4]">
                          <SelectValue placeholder="Select floor" />
                        </SelectTrigger>
                        <SelectContent>
                          {floors.map((floor) => (
                            <SelectItem key={floor.id} value={floor.id}>
                              {floor.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-[#181510] mb-2">Area</label>
                      <Select value={tableForm.areaId} onValueChange={(value) => setTableForm({ ...tableForm, areaId: value })}>
                        <SelectTrigger className="bg-[#fbfaf9] border-[#e2dcd4]">
                          <SelectValue placeholder="Select area" />
                        </SelectTrigger>
                        <SelectContent>
                          {areas.map((area) => (
                            <SelectItem key={area.id} value={area.id}>
                              {area.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-[#181510] mb-2">Capacity</label>
                    <Input
                      type="number"
                      min="1"
                      max="20"
                      value={tableForm.capacity}
                      onChange={(e) => setTableForm({ ...tableForm, capacity: parseInt(e.target.value) || 2 })}
                      className="bg-[#fbfaf9] border-[#e2dcd4]"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-[#181510] mb-2">Shape</label>
                    <Select value={tableForm.shape} onValueChange={(value: 'square' | 'round' | 'rectangle') => setTableForm({ ...tableForm, shape: value })}>
                      <SelectTrigger className="bg-[#fbfaf9] border-[#e2dcd4]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {tableShapes.map((shape) => (
                          <SelectItem key={shape.id} value={shape.id}>
                            <div className="flex items-center gap-2">
                              {shape.icon}
                              {shape.name}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-[#181510] mb-2">Width</label>
                      <Input
                        type="number"
                        min="40"
                        max="200"
                        value={tableForm.width}
                        onChange={(e) => setTableForm({ ...tableForm, width: parseInt(e.target.value) || 80 })}
                        className="bg-[#fbfaf9] border-[#e2dcd4]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-[#181510] mb-2">Height</label>
                      <Input
                        type="number"
                        min="40"
                        max="200"
                        value={tableForm.height}
                        onChange={(e) => setTableForm({ ...tableForm, height: parseInt(e.target.value) || 80 })}
                        className="bg-[#fbfaf9] border-[#e2dcd4]"
                      />
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <DialogClose asChild>
                    <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
                      Cancel
                    </Button>
                  </DialogClose>
                  <Button onClick={handleAddTable} className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e]">
                    Add Table
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
        </div>

        {/* Canvas - Table Layout */}
        <div className="lg:col-span-3 order-1 lg:order-2">
          <TableCanvas
            tables={filteredTables}
            selectedTable={selectedTable}
            localTablePositions={localTablePositions}
            canvasRef={canvasRef}
            onCanvasClick={handleCanvasClick}
            onTableClick={handleTableClick}
            onTableDragEnd={handleDragEnd}
            showGrid={showGrid}
            gridSize={20}
            zoom={canvasZoom}
          />
        </div>
      </div>

      {/* Edit Table Dialog */}
      <Dialog open={isEditTableOpen} onOpenChange={setIsEditTableOpen}>
        <DialogContent className="bg-[#fbfaf9]">
          <DialogHeader>
            <DialogTitle>Edit Table {selectedTable?.number}</DialogTitle>
            <DialogDescription>
              Modify the table properties.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-[#181510] mb-2">Table Name</label>
              <Input
                type="text"
                placeholder="e.g., VIP Table, Window Table"
                value={tableForm.name}
                onChange={(e) => setTableForm({ ...tableForm, name: e.target.value })}
                className="bg-[#fbfaf9] border-[#e2dcd4]"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-[#181510] mb-2">Floor</label>
                <Select value={tableForm.floorId} onValueChange={(value) => setTableForm({ ...tableForm, floorId: value })}>
                  <SelectTrigger className="bg-[#fbfaf9] border-[#e2dcd4]">
                    <SelectValue placeholder="Select floor" />
                  </SelectTrigger>
                  <SelectContent>
                    {floors.map((floor) => (
                      <SelectItem key={floor.id} value={floor.id}>
                        {floor.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block text-sm font-medium text-[#181510] mb-2">Area</label>
                <Select value={tableForm.areaId} onValueChange={(value) => setTableForm({ ...tableForm, areaId: value })}>
                  <SelectTrigger className="bg-[#fbfaf9] border-[#e2dcd4]">
                    <SelectValue placeholder="Select area" />
                  </SelectTrigger>
                  <SelectContent>
                    {areas.map((area) => (
                      <SelectItem key={area.id} value={area.id}>
                        {area.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-[#181510] mb-2">Capacity</label>
              <Input
                type="number"
                min="1"
                max="20"
                value={tableForm.capacity}
                onChange={(e) => setTableForm({ ...tableForm, capacity: parseInt(e.target.value) || 2 })}
                className="bg-[#fbfaf9] border-[#e2dcd4]"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-[#181510] mb-2">Shape</label>
              <Select value={tableForm.shape} onValueChange={(value: 'square' | 'round' | 'rectangle') => setTableForm({ ...tableForm, shape: value })}>
                <SelectTrigger className="bg-[#fbfaf9] border-[#e2dcd4]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {tableShapes.map((shape) => (
                    <SelectItem key={shape.id} value={shape.id}>
                      <div className="flex items-center gap-2">
                        {shape.icon}
                        {shape.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-[#181510] mb-2">Width</label>
                <Input
                  type="number"
                  min="40"
                  max="200"
                  value={tableForm.width}
                  onChange={(e) => setTableForm({ ...tableForm, width: parseInt(e.target.value) || 80 })}
                  className="bg-[#fbfaf9] border-[#e2dcd4]"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[#181510] mb-2">Height</label>
                <Input
                  type="number"
                  min="40"
                  max="200"
                  value={tableForm.height}
                  onChange={(e) => setTableForm({ ...tableForm, height: parseInt(e.target.value) || 80 })}
                  className="bg-[#fbfaf9] border-[#e2dcd4]"
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
                Cancel
              </Button>
            </DialogClose>
            <Button onClick={handleEditTable} className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e]">
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
