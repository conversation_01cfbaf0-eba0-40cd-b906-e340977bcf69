'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Link } from '@/i18n/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft } from 'lucide-react';
import { AppLoading } from '@/components/ui/app-loading';
import { toast } from 'sonner';
import { useStaff } from '@/hooks/useStaff';
import { CreateStaffRequest } from '@/lib/redux/api/endpoints/restaurant/staffApi';
import React from 'react';

interface StaffAddPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function StaffAddPage({ params }: StaffAddPageProps) {
  const { slugShop, slugBranch } = React.use(params);
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Use staff hook for data fetching and actions
  const {
    roles,
    permissions,
    isLoading,
    isError,
    error,
    createStaffMember,
  } = useStaff({
    shopSlug: slugShop,
    branchSlug: slugBranch,
  });

  // Form state
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    position: '',
    department: '',
    roleId: '',
    email: '',
    phone: '',
    employeeId: '',
    permissions: [] as string[],
  });

  // Debug logging
  console.log('Roles:', roles);
  console.log('Form data:', formData);

  const handleChange = (field: string, value: string) => {
    setFormData(prev => {
      const newData = {
        ...prev,
        [field]: value
      };

      // If role is changed, automatically update permissions based on the selected role
      if (field === 'roleId' && value) {
        const selectedRole = roles?.find(role => role.id === value);
        if (selectedRole) {
          newData.permissions = selectedRole.permissions || [];
        }
      }

      return newData;
    });
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate form
      if (!formData.firstName) {
        toast.error('First name is required');
        setIsSubmitting(false);
        return;
      }

      if (!formData.lastName) {
        toast.error('Last name is required');
        setIsSubmitting(false);
        return;
      }

      if (!formData.email) {
        toast.error('Email is required');
        setIsSubmitting(false);
        return;
      }

      if (!formData.position) {
        toast.error('Position is required');
        setIsSubmitting(false);
        return;
      }

      if (!formData.roleId) {
        toast.error('Role is required');
        setIsSubmitting(false);
        return;
      }

      // Validate that roleId is a valid UUID format
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(formData.roleId)) {
        toast.error('Invalid role selected');
        setIsSubmitting(false);
        return;
      }

      if (!formData.employeeId) {
        toast.error('Employee ID is required');
        setIsSubmitting(false);
        return;
      }

      // Create staff member using real API
      const staffData: CreateStaffRequest = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone,
        position: formData.position,
        department: formData.department,
        roleId: formData.roleId,
        employeeId: formData.employeeId,
        hireDate: new Date().toISOString().split('T')[0],
      };

      await createStaffMember(staffData);

      toast.success('Staff member added successfully');

      // Redirect to staff list
      router.push(`/app/restaurant/${slugShop}/${slugBranch}/staff`);
    } catch (error) {
      // The useStaff hook already shows the error toast with the specific backend message
      // We just need to handle the form state here
      console.error('Error adding staff member:', error);
      setIsSubmitting(false);

      // Don't show additional toast here since useStaff hook already handles it
      // The error handling in useStaff will show the specific backend error message
    }
  };

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (isError) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Staff
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Error Loading Data</h1>
          <p className="text-[#8a745c] text-sm">
            {error && 'data' in error && typeof error.data === 'object' && error.data && 'message' in error.data
              ? (error.data as { message: string }).message
              : 'Failed to load staff data'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Staff
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Add New Staff Member</h1>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">
            Add a new team member to your restaurant
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="max-w-[512px] space-y-6">
        {/* First Name */}
        <div className="space-y-2">
          <Label htmlFor="firstName" className="text-[#181511] text-base font-medium leading-normal text-left">First Name</Label>
          <Input
            id="firstName"
            placeholder="Enter first name"
            className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
            value={formData.firstName}
            onChange={(e) => handleChange('firstName', e.target.value)}
            required
          />
        </div>

        {/* Last Name */}
        <div className="space-y-2">
          <Label htmlFor="lastName" className="text-[#181511] text-base font-medium leading-normal text-left">Last Name</Label>
          <Input
            id="lastName"
            placeholder="Enter last name"
            className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
            value={formData.lastName}
            onChange={(e) => handleChange('lastName', e.target.value)}
            required
          />
        </div>

        {/* Position */}
        <div className="space-y-2">
          <Label htmlFor="position" className="text-[#181511] text-base font-medium leading-normal text-left">Position</Label>
          <Input
            id="position"
            placeholder="Enter position (e.g., Chef, Server, Manager)"
            className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
            value={formData.position}
            onChange={(e) => handleChange('position', e.target.value)}
            required
          />
        </div>

        {/* Department */}
        <div className="space-y-2">
          <Label htmlFor="department" className="text-[#181511] text-base font-medium leading-normal text-left">Department</Label>
          <Select value={formData.department} onValueChange={(value) => handleChange('department', value)}>
            <SelectTrigger className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal">
              <SelectValue placeholder="Select department" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Kitchen">Kitchen</SelectItem>
              <SelectItem value="Front of House">Front of House</SelectItem>
              <SelectItem value="Management">Management</SelectItem>
              <SelectItem value="Bar">Bar</SelectItem>
              <SelectItem value="Cleaning">Cleaning</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Role */}
        <div className="space-y-2">
          <Label htmlFor="roleId" className="text-[#181511] text-base font-medium leading-normal text-left">Role</Label>
          <Select value={formData.roleId} onValueChange={(value) => handleChange('roleId', value)}>
            <SelectTrigger className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal">
              <SelectValue placeholder="Select role" />
            </SelectTrigger>
            <SelectContent>
              {(roles || []).map((role) => (
                <SelectItem key={role.id} value={role.id}>
                  {role.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Employee ID */}
        <div className="space-y-2">
          <Label htmlFor="employeeId" className="text-[#181511] text-base font-medium leading-normal text-left">Employee ID</Label>
          <Input
            id="employeeId"
            placeholder="Enter employee ID"
            className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
            value={formData.employeeId}
            onChange={(e) => handleChange('employeeId', e.target.value)}
            required
          />
        </div>

        {/* Contact Number */}
        <div className="space-y-2">
          <Label htmlFor="phone" className="text-[#181511] text-base font-medium leading-normal text-left">Contact Number</Label>
          <Input
            id="phone"
            placeholder="Enter contact number"
            className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
            value={formData.phone}
            onChange={(e) => handleChange('phone', e.target.value)}
          />
        </div>

        {/* Email Address */}
        <div className="space-y-2">
          <Label htmlFor="email" className="text-[#181511] text-base font-medium leading-normal text-left">Email Address</Label>
          <Input
            id="email"
            type="email"
            placeholder="Enter email address"
            className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
            value={formData.email}
            onChange={(e) => handleChange('email', e.target.value)}
            required
          />
        </div>

        {/* Role-based Permissions */}
        <div className="space-y-3">
          <h3 className="text-[#181511] text-lg font-bold leading-tight tracking-[-0.015em]">Permissions</h3>
          <p className="text-[#8a745c] text-sm">
            {formData.roleId
              ? `Permissions are automatically assigned based on the selected role: ${roles?.find(r => r.id === formData.roleId)?.name || 'Selected Role'}`
              : 'Select a role above to see the permissions that will be assigned'
            }
          </p>

          {formData.roleId && formData.permissions.length > 0 && (
            <div className="bg-[#f8f6f3] rounded-lg p-4 border border-[#e2dcd4]">
              <h4 className="text-[#181511] text-sm font-semibold mb-3">
                Assigned Permissions ({formData.permissions.length})
              </h4>
              <div className="flex gap-2 flex-wrap">
                {formData.permissions.map((permissionName, index) => {
                  const permission = permissions?.find(p => p.name === permissionName);
                  return (
                    <div
                      key={permissionName || index}
                      className="flex h-8 shrink-0 items-center justify-center gap-x-2 rounded-full pl-4 pr-4 bg-[#e58219] text-white"
                    >
                      <p className="text-sm font-medium leading-normal">
                        {permission?.name || permissionName}
                      </p>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {!formData.roleId && (
            <div className="bg-[#f8f6f3] rounded-lg p-4 border border-[#e2dcd4] text-center">
              <p className="text-[#8a745c] text-sm">
                Please select a role to see the permissions that will be assigned to this staff member.
              </p>
            </div>
          )}
        </div>

        {/* Submit Button */}
        <div className="flex justify-end gap-3">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff`}>
            <Button type="button" variant="outline" className="border-[#e2dcd4] text-[#181510]">
              Cancel
            </Button>
          </Link>
          <Button
            type="submit"
            disabled={isSubmitting}
            className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#e58219] hover:bg-[#d4741a] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em]"
          >
            <span className="truncate">
              {isSubmitting ? 'Adding...' : 'Add Staff Member'}
            </span>
          </Button>
        </div>
      </form>
    </div>
  );
}
