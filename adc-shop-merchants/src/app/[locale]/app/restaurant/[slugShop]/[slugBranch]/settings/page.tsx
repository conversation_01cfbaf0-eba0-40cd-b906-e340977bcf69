'use client';

import React from 'react';
import { Link } from '@/i18n/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowLeft, User, Bell, Globe, Shield, Palette, Settings, Smartphone } from 'lucide-react';
import { AppLoading } from '@/components/ui/app-loading';
import { useGetShopBySlugQuery, useGetBranchBySlugQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import type { Shop, Branch } from '@/lib/types/shop';

interface SettingsPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

interface SettingCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  href: string;
}

const SettingCard = ({ icon, title, description, href }: SettingCardProps) => (
  <Link href={href} className="block transition-transform hover:scale-[1.02]">
    <Card className="h-full">
      <CardContent className="p-6 flex items-start gap-4">
        <div className="bg-muted p-3 rounded-lg">
          {icon}
        </div>
        <div>
          <h3 className="text-foreground font-bold mb-1">{title}</h3>
          <p className="text-muted-foreground text-sm">{description}</p>
        </div>
      </CardContent>
    </Card>
  </Link>
);

export default function SettingsPage({ params }: SettingsPageProps) {
  const { slugShop, slugBranch } = React.use(params);

  // Get shop and branch data from backend using slug-based queries
  const { data: shopData, isLoading: isShopLoading, error: shopError } = useGetShopBySlugQuery(slugShop) as {
    data: Shop | undefined;
    isLoading: boolean;
    error: unknown;
  };
  const { data: branchData, isLoading: isBranchLoading, error: branchError } = useGetBranchBySlugQuery({
    shopSlug: slugShop,
    branchSlug: slugBranch
  }) as {
    data: Branch | undefined;
    isLoading: boolean;
    error: unknown;
  };

  const isLoading = isShopLoading || isBranchLoading;
  const hasError = shopError || branchError;

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (hasError || !shopData || !branchData) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-foreground text-[32px] font-bold leading-tight mb-2">Restaurant Not Found</h1>
          <p className="text-muted-foreground text-sm">The restaurant or branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/dashboard`}>
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-foreground text-[32px] font-bold leading-tight">Settings</h1>
          <p className="text-muted-foreground text-sm">Configure your account and restaurant settings for {shopData.name} - {branchData.name}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <SettingCard
          icon={<Settings className="h-6 w-6 text-muted-foreground" />}
          title="Restaurant Preferences"
          description="Configure restaurant settings, hours, and payment methods"
          href={`/app/restaurant/${slugShop}/${slugBranch}/settings/preferences`}
        />

        <SettingCard
          icon={<User className="h-6 w-6 text-muted-foreground" />}
          title="Profile"
          description="Manage your personal information and account settings"
          href={`/app/restaurant/${slugShop}/${slugBranch}/settings/profile`}
        />

        <SettingCard
          icon={<Bell className="h-6 w-6 text-muted-foreground" />}
          title="Notifications"
          description="Configure your notification preferences"
          href={`/app/restaurant/${slugShop}/${slugBranch}/settings/notifications`}
        />

        <SettingCard
          icon={<Smartphone className="h-6 w-6 text-muted-foreground" />}
          title="Payment Integration"
          description="Configure PromPay and Stripe Connect for payment processing"
          href={`/app/restaurant/${slugShop}/${slugBranch}/settings/payments`}
        />

        <SettingCard
          icon={<Globe className="h-6 w-6 text-muted-foreground" />}
          title="Language & Region"
          description="Set your preferred language and regional settings"
          href={`/app/restaurant/${slugShop}/${slugBranch}/settings/localization`}
        />

        <SettingCard
          icon={<Shield className="h-6 w-6 text-muted-foreground" />}
          title="Security"
          description="Manage your account security and privacy settings"
          href={`/app/restaurant/${slugShop}/${slugBranch}/settings/security`}
        />

        <SettingCard
          icon={<Palette className="h-6 w-6 text-muted-foreground" />}
          title="Appearance"
          description="Customize the look and feel of your restaurant dashboard"
          href={`/app/restaurant/${slugShop}/${slugBranch}/settings/appearance`}
        />
      </div>
    </div>
  );
}
