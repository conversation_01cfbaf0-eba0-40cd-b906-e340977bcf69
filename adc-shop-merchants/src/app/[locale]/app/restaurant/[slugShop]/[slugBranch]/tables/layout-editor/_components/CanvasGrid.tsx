'use client';

import React from 'react';

interface CanvasGridProps {
  gridSize?: number;
  opacity?: number;
  color?: string;
}

export const CanvasGrid: React.FC<CanvasGridProps> = ({
  gridSize = 20,
  opacity = 0.2,
  color = '#8a745c'
}) => {
  return (
    <div className="absolute inset-0" style={{ opacity }}>
      <svg width="100%" height="100%">
        <defs>
          <pattern 
            id="grid" 
            width={gridSize} 
            height={gridSize} 
            patternUnits="userSpaceOnUse"
          >
            <path 
              d={`M ${gridSize} 0 L 0 0 0 ${gridSize}`} 
              fill="none" 
              stroke={color} 
              strokeWidth="0.5"
            />
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#grid)" />
      </svg>
    </div>
  );
};
