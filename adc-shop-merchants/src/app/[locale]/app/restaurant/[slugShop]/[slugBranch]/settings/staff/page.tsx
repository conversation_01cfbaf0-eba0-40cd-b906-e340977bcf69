'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import {
  ArrowLeft,
  Users,
  Plus,
  Edit,
  Trash2,
  MoreHorizontal,
  Shield,
  Mail,
  Phone,
  Calendar,
  UserCheck,
  UserX,
  Crown,
  Key,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Switch } from '@/components/ui/switch';

// Types
interface StaffMember {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  role: 'owner' | 'manager' | 'server' | 'chef' | 'cashier' | 'host';
  status: 'active' | 'inactive' | 'pending';
  permissions: string[];
  joinDate: Date;
  lastLogin?: Date;
  avatar?: string;
}

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  color: string;
}

export default function StaffSettingsPage() {
  const t = useTranslations('settings');
  const params = useParams();
  const { slugShop, slugBranch } = params;

  // State
  const [staffMembers, setStaffMembers] = useState<StaffMember[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(true);
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [newInvite, setNewInvite] = useState({
    email: '',
    firstName: '',
    lastName: '',
    role: '',
    phone: '',
  });

  // Available permissions
  const availablePermissions = [
    { id: 'dashboard.view', name: 'View Dashboard', category: 'Dashboard' },
    { id: 'orders.view', name: 'View Orders', category: 'Orders' },
    { id: 'orders.create', name: 'Create Orders', category: 'Orders' },
    { id: 'orders.edit', name: 'Edit Orders', category: 'Orders' },
    { id: 'orders.delete', name: 'Delete Orders', category: 'Orders' },
    { id: 'menu.view', name: 'View Menu', category: 'Menu' },
    { id: 'menu.edit', name: 'Edit Menu', category: 'Menu' },
    { id: 'reservations.view', name: 'View Reservations', category: 'Reservations' },
    { id: 'reservations.manage', name: 'Manage Reservations', category: 'Reservations' },
    { id: 'tables.view', name: 'View Tables', category: 'Tables' },
    { id: 'tables.manage', name: 'Manage Tables', category: 'Tables' },
    { id: 'staff.view', name: 'View Staff', category: 'Staff' },
    { id: 'staff.manage', name: 'Manage Staff', category: 'Staff' },
    { id: 'inventory.view', name: 'View Inventory', category: 'Inventory' },
    { id: 'inventory.manage', name: 'Manage Inventory', category: 'Inventory' },
    { id: 'reports.view', name: 'View Reports', category: 'Reports' },
    { id: 'settings.view', name: 'View Settings', category: 'Settings' },
    { id: 'settings.manage', name: 'Manage Settings', category: 'Settings' },
  ];

  // Mock data
  const mockRoles: Role[] = [
    {
      id: 'owner',
      name: 'Owner',
      description: 'Full access to all features and settings',
      permissions: availablePermissions.map(p => p.id),
      color: 'bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-950 dark:text-purple-300',
    },
    {
      id: 'manager',
      name: 'Manager',
      description: 'Manage daily operations and staff',
      permissions: [
        'dashboard.view', 'orders.view', 'orders.create', 'orders.edit',
        'menu.view', 'menu.edit', 'reservations.view', 'reservations.manage',
        'tables.view', 'tables.manage', 'staff.view', 'inventory.view',
        'inventory.manage', 'reports.view'
      ],
      color: 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300',
    },
    {
      id: 'server',
      name: 'Server',
      description: 'Take orders and manage tables',
      permissions: [
        'dashboard.view', 'orders.view', 'orders.create', 'orders.edit',
        'menu.view', 'reservations.view', 'tables.view'
      ],
      color: 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300',
    },
    {
      id: 'chef',
      name: 'Chef',
      description: 'Manage kitchen operations and menu',
      permissions: [
        'dashboard.view', 'orders.view', 'menu.view', 'menu.edit',
        'inventory.view', 'inventory.manage'
      ],
      color: 'bg-orange-50 text-orange-700 border-orange-200 dark:bg-orange-950 dark:text-orange-300',
    },
    {
      id: 'cashier',
      name: 'Cashier',
      description: 'Process payments and handle orders',
      permissions: [
        'dashboard.view', 'orders.view', 'orders.create', 'menu.view'
      ],
      color: 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300',
    },
    {
      id: 'host',
      name: 'Host',
      description: 'Manage reservations and seating',
      permissions: [
        'dashboard.view', 'reservations.view', 'reservations.manage',
        'tables.view', 'tables.manage'
      ],
      color: 'bg-pink-100 text-pink-800',
    },
  ];

  const mockStaffMembers: StaffMember[] = [
    {
      id: '1',
      firstName: 'John',
      lastName: 'Smith',
      email: '<EMAIL>',
      phone: '+****************',
      role: 'owner',
      status: 'active',
      permissions: availablePermissions.map(p => p.id),
      joinDate: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000),
      lastLogin: new Date(Date.now() - 2 * 60 * 60 * 1000),
    },
    {
      id: '2',
      firstName: 'Sarah',
      lastName: 'Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      role: 'manager',
      status: 'active',
      permissions: mockRoles.find(r => r.id === 'manager')?.permissions || [],
      joinDate: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000),
      lastLogin: new Date(Date.now() - 4 * 60 * 60 * 1000),
    },
    {
      id: '3',
      firstName: 'Mike',
      lastName: 'Chen',
      email: '<EMAIL>',
      phone: '+****************',
      role: 'chef',
      status: 'active',
      permissions: mockRoles.find(r => r.id === 'chef')?.permissions || [],
      joinDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
      lastLogin: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    },
    {
      id: '4',
      firstName: 'Emily',
      lastName: 'Davis',
      email: '<EMAIL>',
      phone: '+****************',
      role: 'server',
      status: 'active',
      permissions: mockRoles.find(r => r.id === 'server')?.permissions || [],
      joinDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
      lastLogin: new Date(Date.now() - 6 * 60 * 60 * 1000),
    },
    {
      id: '5',
      firstName: 'David',
      lastName: 'Wilson',
      email: '<EMAIL>',
      phone: '+****************',
      role: 'host',
      status: 'pending',
      permissions: mockRoles.find(r => r.id === 'host')?.permissions || [],
      joinDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    },
  ];

  // Load data
  useEffect(() => {
    const timer = setTimeout(() => {
      setStaffMembers(mockStaffMembers);
      setRoles(mockRoles);
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Get role badge
  const getRoleBadge = (roleId: string) => {
    const role = roles.find(r => r.id === roleId);
    if (!role) return null;

    return (
      <Badge className={role.color}>
        {role.name}
      </Badge>
    );
  };

  // Get status badge
  const getStatusBadge = (status: StaffMember['status']) => {
    const statusConfig = {
      active: { color: 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300', icon: UserCheck },
      inactive: { color: 'bg-muted text-muted-foreground border-border', icon: UserX },
      pending: { color: 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300', icon: Calendar },
    };

    const config = statusConfig[status];
    const Icon = config.icon;

    return (
      <Badge className={cn('flex items-center gap-1', config.color)}>
        <Icon className="w-3 h-3" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  // Format date
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Format time ago
  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(hours / 24);

    if (hours < 1) return 'Just now';
    if (hours < 24) return `${hours}h ago`;
    if (days < 30) return `${days}d ago`;
    return formatDate(date);
  };

  // Handle invite staff
  const handleInviteStaff = async () => {
    try {
      // TODO: Implement API call to invite staff
      console.log('Inviting staff:', newInvite);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Reset form and close dialog
      setNewInvite({
        email: '',
        firstName: '',
        lastName: '',
        role: '',
        phone: '',
      });
      setIsInviteDialogOpen(false);

      // Show success message
      alert('Staff invitation sent successfully!');
    } catch (error) {
      console.error('Error inviting staff:', error);
      alert('Failed to send invitation. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/settings`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Settings
            </Button>
          </Link>
          <div className="flex items-center gap-3">
            <Users className="w-8 h-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Staff & Permissions</h1>
              <p className="text-gray-600">Manage team members and their access levels</p>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/settings/staff/roles`}>
            <Button variant="outline">
              <Shield className="w-4 h-4 mr-2" />
              Manage Roles
            </Button>
          </Link>

          <Dialog open={isInviteDialogOpen} onOpenChange={setIsInviteDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Invite Staff
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Invite Staff Member</DialogTitle>
                <DialogDescription>
                  Send an invitation to a new team member
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">First Name</Label>
                    <Input
                      id="firstName"
                      value={newInvite.firstName}
                      onChange={(e) => setNewInvite({ ...newInvite, firstName: e.target.value })}
                      placeholder="John"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last Name</Label>
                    <Input
                      id="lastName"
                      value={newInvite.lastName}
                      onChange={(e) => setNewInvite({ ...newInvite, lastName: e.target.value })}
                      placeholder="Smith"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    value={newInvite.email}
                    onChange={(e) => setNewInvite({ ...newInvite, email: e.target.value })}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    value={newInvite.phone}
                    onChange={(e) => setNewInvite({ ...newInvite, phone: e.target.value })}
                    placeholder="+****************"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="role">Role</Label>
                  <Select value={newInvite.role} onValueChange={(value) => setNewInvite({ ...newInvite, role: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a role" />
                    </SelectTrigger>
                    <SelectContent>
                      {roles.filter(role => role.id !== 'owner').map((role) => (
                        <SelectItem key={role.id} value={role.id}>
                          {role.name} - {role.description}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsInviteDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleInviteStaff}>
                  Send Invitation
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Staff</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{staffMembers.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <UserCheck className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {staffMembers.filter(s => s.status === 'active').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Calendar className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {staffMembers.filter(s => s.status === 'pending').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Roles</CardTitle>
            <Shield className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{roles.length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Staff Table */}
      <Card>
        <CardHeader>
          <CardTitle>Team Members</CardTitle>
          <p className="text-sm text-gray-600">Manage your restaurant staff and their permissions</p>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Staff Member</TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Login</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {staffMembers.map((member) => (
                <TableRow key={member.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-medium">
                        {member.firstName[0]}{member.lastName[0]}
                      </div>
                      <div>
                        <div className="font-medium">{member.firstName} {member.lastName}</div>
                        <div className="text-sm text-gray-500">
                          Joined {formatDate(member.joinDate)}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 text-sm">
                        <Mail className="w-3 h-3 text-gray-400" />
                        {member.email}
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Phone className="w-3 h-3 text-gray-400" />
                        {member.phone}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getRoleBadge(member.role)}
                      {member.role === 'owner' && (
                        <Crown className="w-4 h-4 text-yellow-600" />
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(member.status)}</TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {member.lastLogin ? formatTimeAgo(member.lastLogin) : 'Never'}
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Profile
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Key className="mr-2 h-4 w-4" />
                          Manage Permissions
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Settings className="mr-2 h-4 w-4" />
                          Account Settings
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        {member.status === 'active' ? (
                          <DropdownMenuItem className="text-orange-600">
                            <UserX className="mr-2 h-4 w-4" />
                            Deactivate
                          </DropdownMenuItem>
                        ) : (
                          <DropdownMenuItem className="text-green-600">
                            <UserCheck className="mr-2 h-4 w-4" />
                            Activate
                          </DropdownMenuItem>
                        )}
                        {member.role !== 'owner' && (
                          <DropdownMenuItem className="text-red-600">
                            <Trash2 className="mr-2 h-4 w-4" />
                            Remove Staff
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Roles Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Roles & Permissions
          </CardTitle>
          <p className="text-sm text-gray-600">Overview of available roles and their permissions</p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {roles.map((role) => (
              <div key={role.id} className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <Badge className={role.color}>
                    {role.name}
                  </Badge>
                  <span className="text-sm text-gray-500">
                    {staffMembers.filter(s => s.role === role.id).length} members
                  </span>
                </div>

                <p className="text-sm text-gray-600 mb-3">{role.description}</p>

                <div className="text-sm">
                  <span className="font-medium">{role.permissions.length}</span>
                  <span className="text-gray-500"> permissions</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
