'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Bell, 
  TrendingUp, 
  AlertTriangle, 
  Clock, 
  ShoppingCart, 
  Calendar, 
  Star, 
  Users, 
  Package, 
  CreditCard, 
  Gift,
  Settings,
  Wifi,
  WifiOff
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useNotificationContext } from '@/contexts/NotificationContext';

interface BranchNotificationDashboardProps {
  shopName: string;
  branchName: string;
  stats: {
    totalNotifications: number;
    unreadNotifications: number;
    readNotifications: number;
    urgentNotifications: number;
    highPriorityNotifications: number;
    byType: Record<string, number>;
    byPriority: Record<string, number>;
  };
  onRefresh: () => void;
  isLoading: boolean;
}

export function BranchNotificationDashboard({
  shopName,
  branchName,
  stats,
  onRefresh,
  isLoading
}: BranchNotificationDashboardProps) {
  const { isConnected, isConnecting } = useNotificationContext();

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'order':
        return <ShoppingCart className="h-4 w-4" />;
      case 'reservation':
        return <Calendar className="h-4 w-4" />;
      case 'review':
        return <Star className="h-4 w-4" />;
      case 'staff':
        return <Users className="h-4 w-4" />;
      case 'inventory':
        return <Package className="h-4 w-4" />;
      case 'payment':
        return <CreditCard className="h-4 w-4" />;
      case 'promotion':
        return <Gift className="h-4 w-4" />;
      case 'system':
        return <Settings className="h-4 w-4" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'order':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'reservation':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'review':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'staff':
        return 'text-purple-600 bg-purple-50 border-purple-200';
      case 'inventory':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'payment':
        return 'text-indigo-600 bg-indigo-50 border-indigo-200';
      case 'promotion':
        return 'text-pink-600 bg-pink-50 border-pink-200';
      case 'system':
        return 'text-gray-600 bg-gray-50 border-gray-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="space-y-6">
      {/* Branch Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-foreground">Notifications Dashboard</h1>
          <p className="text-muted-foreground">
            {shopName} - {branchName}
          </p>
        </div>
        <div className="flex items-center gap-4">
          {/* WebSocket Status */}
          <div className="flex items-center gap-2">
            {isConnecting ? (
              <div className="flex items-center gap-2 text-yellow-600">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600"></div>
                <span className="text-sm">Connecting...</span>
              </div>
            ) : isConnected ? (
              <div className="flex items-center gap-2 text-green-600">
                <Wifi className="h-4 w-4" />
                <span className="text-sm">Live Updates</span>
              </div>
            ) : (
              <div className="flex items-center gap-2 text-red-600">
                <WifiOff className="h-4 w-4" />
                <span className="text-sm">Offline</span>
              </div>
            )}
          </div>
          <Button 
            onClick={onRefresh} 
            disabled={isLoading}
            variant="outline"
            size="sm"
          >
            {isLoading ? 'Refreshing...' : 'Refresh'}
          </Button>
        </div>
      </div>

      {/* Statistics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Bell className="h-5 w-5 text-muted-foreground mr-2" />
              <div>
                <p className="text-sm text-muted-foreground">Total</p>
                <p className="text-2xl font-bold text-foreground">{stats.totalNotifications}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <TrendingUp className="h-5 w-5 text-orange-600 mr-2" />
              <div>
                <p className="text-sm text-muted-foreground">Unread</p>
                <p className="text-2xl font-bold text-orange-600">{stats.unreadNotifications}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Clock className="h-5 w-5 text-green-600 mr-2" />
              <div>
                <p className="text-sm text-muted-foreground">Read</p>
                <p className="text-2xl font-bold text-green-600">{stats.readNotifications}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
              <div>
                <p className="text-sm text-muted-foreground">Urgent</p>
                <p className="text-2xl font-bold text-red-600">{stats.urgentNotifications}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Notifications by Type */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Notifications by Type</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(stats.byType).map(([type, count]) => (
                <div key={type} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getTypeIcon(type)}
                    <span className="capitalize text-sm">{type}</span>
                  </div>
                  <Badge variant="outline" className={cn("text-xs", getTypeColor(type))}>
                    {count}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Notifications by Priority</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(stats.byPriority).map(([priority, count]) => (
                <div key={priority} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className={cn(
                      "w-3 h-3 rounded-full",
                      priority === 'urgent' && "bg-red-500",
                      priority === 'high' && "bg-orange-500",
                      priority === 'medium' && "bg-blue-500",
                      priority === 'low' && "bg-gray-500"
                    )} />
                    <span className="capitalize text-sm">{priority}</span>
                  </div>
                  <Badge variant="outline" className={cn(
                    "text-xs",
                    priority === 'urgent' && "text-red-600 bg-red-50 border-red-200",
                    priority === 'high' && "text-orange-600 bg-orange-50 border-orange-200",
                    priority === 'medium' && "text-blue-600 bg-blue-50 border-blue-200",
                    priority === 'low' && "text-gray-600 bg-gray-50 border-gray-200"
                  )}>
                    {count}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
