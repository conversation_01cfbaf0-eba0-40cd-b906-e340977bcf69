import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Bell,
  BellOff,
  ExternalLink,
  MoreHorizontal,
  Trash2,
  Eye,
  EyeOff,
  Clock,
  AlertCircle,
  CheckCircle,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Notification } from '@/lib/redux/api/endpoints/restaurant/notificationsApi';
import { useRouter } from 'next/navigation';

interface NotificationItemProps {
  notification: Notification;
  shopSlug: string;
  branchSlug: string;
  onMarkAsRead: (id: string) => void;
  onMarkAsUnread: (id: string) => void;
  onDelete: (id: string) => void;
  formatTimestamp: (timestamp: string) => string;
  getPriorityColor: (priority: string) => string;
  getTypeIcon: (type: string) => string;
  isSelected?: boolean;
  onSelect?: (id: string, selected: boolean) => void;
  showSelection?: boolean;
}

export function NotificationItem({
  notification,
  shopSlug,
  branchSlug,
  onMarkAsRead,
  onMarkAsUnread,
  onDelete,
  formatTimestamp,
  getPriorityColor,
  getTypeIcon,
  isSelected = false,
  onSelect,
  showSelection = false
}: NotificationItemProps) {
  const router = useRouter();

  const handleActionClick = () => {
    if (notification.link) {
      // Check if it's an external link or internal route
      if (notification.link.startsWith('http')) {
        window.open(notification.link, '_blank');
      } else {
        // For internal routes, construct the full path with shop and branch slugs
        const fullPath = `/en/app/restaurant/${shopSlug}/${branchSlug}${notification.link}`;
        router.push(fullPath);
      }

      // Mark as read when action is clicked
      if (!notification.isRead) {
        onMarkAsRead(notification.id);
      }
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'urgent': return <Zap className="h-4 w-4" />;
      case 'high': return <AlertCircle className="h-4 w-4" />;
      case 'medium': return <Clock className="h-4 w-4" />;
      case 'low': return <CheckCircle className="h-4 w-4" />;
      default: return <Bell className="h-4 w-4" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'order': return 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300';
      case 'reservation': return 'bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-950 dark:text-purple-300';
      case 'review': return 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300';
      case 'system': return 'bg-muted text-muted-foreground border-border';
      case 'staff': return 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300';
      case 'inventory': return 'bg-orange-50 text-orange-700 border-orange-200 dark:bg-orange-950 dark:text-orange-300';
      case 'payment': return 'bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-950 dark:text-emerald-300';
      case 'promotion': return 'bg-pink-50 text-pink-700 border-pink-200 dark:bg-pink-950 dark:text-pink-300';
      default: return 'bg-muted text-muted-foreground border-border';
    }
  };

  return (
    <Card className={cn(
      "transition-all duration-200 hover:shadow-md",
      !notification.isRead && "border-l-4 border-l-blue-500 bg-blue-50/30"
    )}>
      <CardContent className="p-4">
        <div className="flex items-start gap-4">
          {/* Selection Checkbox */}
          {showSelection && (
            <div className="flex items-center pt-1">
              <Checkbox
                checked={isSelected}
                onCheckedChange={(checked) => onSelect?.(notification.id, !!checked)}
                aria-label={`Select notification: ${notification.title}`}
              />
            </div>
          )}

          {/* Content */}
          <div className="flex-1 min-w-0">
            {/* Header */}
            <div className="flex items-center gap-2 mb-2">
              <span className="text-lg">{getTypeIcon(notification.type)}</span>
              <Badge variant="outline" className={cn("text-xs", getTypeColor(notification.type))}>
                {notification.type}
              </Badge>
              <Badge variant="outline" className={cn("text-xs", getPriorityColor(notification.priority))}>
                <span className="flex items-center gap-1">
                  {getPriorityIcon(notification.priority)}
                  {notification.priority}
                </span>
              </Badge>
              {!notification.isRead && (
                <Badge variant="default" className="text-xs bg-blue-600">
                  New
                </Badge>
              )}
            </div>

            {/* Title */}
            <h3 className={cn(
              "font-semibold text-sm mb-1 line-clamp-1",
              !notification.isRead ? "text-gray-900" : "text-gray-600"
            )}>
              {notification.title}
            </h3>

            {/* Message */}
            <p className="text-sm text-gray-600 line-clamp-2 mb-2">
              {notification.message}
            </p>

            {/* Footer */}
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-500">
                {formatTimestamp(notification.timestamp)}
              </span>

              {/* Action Button */}
              {notification.link && notification.actionLabel && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleActionClick}
                  className="text-xs h-7"
                >
                  {notification.actionLabel}
                  <ExternalLink className="h-3 w-3 ml-1" />
                </Button>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            {/* Read/Unread Toggle */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => notification.isRead ? onMarkAsUnread(notification.id) : onMarkAsRead(notification.id)}
              className="h-8 w-8 p-0"
              title={notification.isRead ? "Mark as unread" : "Mark as read"}
            >
              {notification.isRead ? (
                <EyeOff className="h-4 w-4 text-gray-500" />
              ) : (
                <Eye className="h-4 w-4 text-blue-600" />
              )}
            </Button>

            {/* More Actions */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={() => notification.isRead ? onMarkAsUnread(notification.id) : onMarkAsRead(notification.id)}
                >
                  {notification.isRead ? (
                    <>
                      <BellOff className="h-4 w-4 mr-2" />
                      Mark as unread
                    </>
                  ) : (
                    <>
                      <Bell className="h-4 w-4 mr-2" />
                      Mark as read
                    </>
                  )}
                </DropdownMenuItem>
                {notification.link && (
                  <DropdownMenuItem onClick={handleActionClick}>
                    <ExternalLink className="h-4 w-4 mr-2" />
                    {notification.actionLabel || 'View details'}
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem
                  onClick={() => onDelete(notification.id)}
                  className="text-red-600 focus:text-red-600"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
