'use client';

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  Settings, 
  Bell, 
  BellOff, 
  Volume2, 
  VolumeX, 
  Smartphone, 
  Mail, 
  MessageSquare,
  Save,
  RotateCcw
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface NotificationPreferences {
  enabled: boolean;
  sound: boolean;
  desktop: boolean;
  email: boolean;
  sms: boolean;
  types: {
    order: boolean;
    reservation: boolean;
    review: boolean;
    inventory: boolean;
    staff: boolean;
    payment: boolean;
    promotion: boolean;
    system: boolean;
  };
  priorities: {
    urgent: boolean;
    high: boolean;
    medium: boolean;
    low: boolean;
  };
  quietHours: {
    enabled: boolean;
    start: string;
    end: string;
  };
}

interface BranchNotificationSettingsProps {
  branchName: string;
  initialPreferences?: Partial<NotificationPreferences>;
  onSave: (preferences: NotificationPreferences) => Promise<void>;
  isLoading?: boolean;
}

const defaultPreferences: NotificationPreferences = {
  enabled: true,
  sound: true,
  desktop: true,
  email: true,
  sms: false,
  types: {
    order: true,
    reservation: true,
    review: true,
    inventory: true,
    staff: true,
    payment: true,
    promotion: false,
    system: true,
  },
  priorities: {
    urgent: true,
    high: true,
    medium: true,
    low: false,
  },
  quietHours: {
    enabled: false,
    start: '22:00',
    end: '08:00',
  },
};

export function BranchNotificationSettings({
  branchName,
  initialPreferences = {},
  onSave,
  isLoading = false
}: BranchNotificationSettingsProps) {
  const [preferences, setPreferences] = useState<NotificationPreferences>({
    ...defaultPreferences,
    ...initialPreferences,
  });
  const [hasChanges, setHasChanges] = useState(false);

  const updatePreference = (path: string, value: any) => {
    setPreferences(prev => {
      const newPrefs = { ...prev };
      const keys = path.split('.');
      let current: any = newPrefs;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }
      current[keys[keys.length - 1]] = value;
      
      setHasChanges(true);
      return newPrefs;
    });
  };

  const handleSave = async () => {
    try {
      await onSave(preferences);
      setHasChanges(false);
      toast.success('Notification preferences saved successfully');
    } catch (error) {
      toast.error('Failed to save notification preferences');
    }
  };

  const handleReset = () => {
    setPreferences({ ...defaultPreferences, ...initialPreferences });
    setHasChanges(false);
    toast.info('Preferences reset to default');
  };

  const getTypeIcon = (type: string) => {
    const icons: Record<string, React.ReactNode> = {
      order: '🛒',
      reservation: '📅',
      review: '⭐',
      inventory: '📦',
      staff: '👥',
      payment: '💳',
      promotion: '🎁',
      system: '⚙️',
    };
    return icons[type] || '🔔';
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'high':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'low':
        return 'text-gray-600 bg-gray-50 border-gray-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-foreground">Notification Settings</h2>
          <p className="text-sm text-muted-foreground">
            Configure notification preferences for {branchName}
          </p>
        </div>
        <div className="flex items-center gap-2">
          {hasChanges && (
            <Badge variant="outline" className="text-orange-600 bg-orange-50 border-orange-200">
              Unsaved changes
            </Badge>
          )}
        </div>
      </div>

      {/* General Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            General Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {preferences.enabled ? <Bell className="h-4 w-4" /> : <BellOff className="h-4 w-4" />}
              <Label htmlFor="enabled">Enable notifications</Label>
            </div>
            <Switch
              id="enabled"
              checked={preferences.enabled}
              onCheckedChange={(value) => updatePreference('enabled', value)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {preferences.sound ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
              <Label htmlFor="sound">Sound notifications</Label>
            </div>
            <Switch
              id="sound"
              checked={preferences.sound}
              onCheckedChange={(value) => updatePreference('sound', value)}
              disabled={!preferences.enabled}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Smartphone className="h-4 w-4" />
              <Label htmlFor="desktop">Desktop notifications</Label>
            </div>
            <Switch
              id="desktop"
              checked={preferences.desktop}
              onCheckedChange={(value) => updatePreference('desktop', value)}
              disabled={!preferences.enabled}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              <Label htmlFor="email">Email notifications</Label>
            </div>
            <Switch
              id="email"
              checked={preferences.email}
              onCheckedChange={(value) => updatePreference('email', value)}
              disabled={!preferences.enabled}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              <Label htmlFor="sms">SMS notifications</Label>
            </div>
            <Switch
              id="sms"
              checked={preferences.sms}
              onCheckedChange={(value) => updatePreference('sms', value)}
              disabled={!preferences.enabled}
            />
          </div>
        </CardContent>
      </Card>

      {/* Notification Types */}
      <Card>
        <CardHeader>
          <CardTitle>Notification Types</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(preferences.types).map(([type, enabled]) => (
              <div key={type} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-lg">{getTypeIcon(type)}</span>
                  <Label htmlFor={`type-${type}`} className="capitalize">
                    {type} notifications
                  </Label>
                </div>
                <Switch
                  id={`type-${type}`}
                  checked={enabled}
                  onCheckedChange={(value) => updatePreference(`types.${type}`, value)}
                  disabled={!preferences.enabled}
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Priority Levels */}
      <Card>
        <CardHeader>
          <CardTitle>Priority Levels</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(preferences.priorities).map(([priority, enabled]) => (
              <div key={priority} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className={cn("text-xs", getPriorityColor(priority))}>
                    {priority}
                  </Badge>
                  <Label htmlFor={`priority-${priority}`} className="capitalize">
                    {priority} priority
                  </Label>
                </div>
                <Switch
                  id={`priority-${priority}`}
                  checked={enabled}
                  onCheckedChange={(value) => updatePreference(`priorities.${priority}`, value)}
                  disabled={!preferences.enabled}
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex items-center justify-end gap-4">
        <Button
          variant="outline"
          onClick={handleReset}
          disabled={!hasChanges || isLoading}
        >
          <RotateCcw className="h-4 w-4 mr-2" />
          Reset
        </Button>
        <Button
          onClick={handleSave}
          disabled={!hasChanges || isLoading}
        >
          <Save className="h-4 w-4 mr-2" />
          {isLoading ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </div>
  );
}
