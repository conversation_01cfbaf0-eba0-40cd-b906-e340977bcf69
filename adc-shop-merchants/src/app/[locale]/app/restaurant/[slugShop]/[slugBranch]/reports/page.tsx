'use client';

import { useState } from 'react';
import { use } from 'react';
import { Link } from '@/i18n/navigation';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft, RefreshCw, TrendingUp, TrendingDown } from 'lucide-react';
import { useReports } from '@/hooks/useReports';
import { AppLoading } from '@/components/ui/app-loading';
import { useGetShopBySlugQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import React from 'react';

interface ReportsPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function ReportsPage({ params }: ReportsPageProps) {
  const { slugShop, slugBranch } = use(params);
  const [activeTab, setActiveTab] = useState('sales-trends');

  // Get shop and branch data from backend by slug
  const { data: shop, isLoading: isLoadingShop } = useGetShopBySlugQuery(slugShop);

  // Find the branch by slug
  const branch = shop?.branches?.find((b: any) => b.slug === slugBranch);

  const shopId = shop?.id;
  const branchId = branch?.id;

  const {
    salesTrends,
    popularItems,
    customerAnalytics,
    revenueAnalytics,
    isLoading,
    filters,
    updateFilters,
    periodOptions,
    refetch,
    formatCurrency,
    formatPercentage
  } = useReports({
    shopId: shopId || '',
    branchId: branchId || '',
    initialFilters: { period: 'month' }
  });

  if (isLoadingShop || isLoading) {
    return <AppLoading />;
  }

  if (!shop || !branch) {
    return (
      <div className="p-6 font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-foreground text-[32px] font-bold leading-tight mb-2">Restaurant Not Found</h1>
          <p className="text-muted-foreground text-sm">The restaurant or branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  // Custom tooltip for the chart
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-card p-3 border border-border rounded-md shadow-sm">
          <p className="text-foreground font-medium">{`${label}`}</p>
          <p className="text-muted-foreground">{`Sales: $${payload[0].value.toLocaleString()}`}</p>
        </div>
      );
    }
    return null;
  };

  // Render the Sales Trends tab content
  const renderSalesTrends = () => {
    const totalSales = salesTrends?.reduce((sum: number, item: any) => sum + item.sales, 0) || 0;
    const growth = revenueAnalytics?.revenueGrowth || 0;

    return (
      <>
        <div className="flex justify-between items-center px-4 pb-3 pt-5">
          <h2 className="text-foreground text-[22px] font-bold leading-tight tracking-[-0.015em]">Sales Trends</h2>
          <div className="flex items-center gap-2">
            <Select value={filters.period} onValueChange={(value) => updateFilters({ period: value as any })}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {periodOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetch()}
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="flex flex-wrap gap-4 px-4 py-6">
          <div className="flex min-w-72 flex-1 flex-col gap-2 rounded-lg border border-border p-6 bg-card">
            <p className="text-foreground text-base font-medium leading-normal">Total Sales</p>
            <p className="text-foreground tracking-light text-[32px] font-bold leading-tight truncate">
              {formatCurrency(totalSales)}
            </p>
            <div className="flex gap-1">
              <p className="text-muted-foreground text-base font-normal leading-normal">
                {filters.period === 'day' ? 'Today' :
                 filters.period === 'week' ? 'This Week' :
                 filters.period === 'month' ? 'This Month' :
                 'This Period'}
              </p>
              <p className={`text-base font-medium leading-normal ${growth >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                {growth >= 0 ? <TrendingUp className="h-4 w-4 inline mr-1" /> : <TrendingDown className="h-4 w-4 inline mr-1" />}
                {formatPercentage(Math.abs(growth))}
              </p>
            </div>
            <div className="flex min-h-[180px] flex-1 flex-col gap-8 py-4">
              <ResponsiveContainer width="100%" height={148}>
                <LineChart data={salesTrends || []} margin={{ top: 5, right: 20, left: 10, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" className="stroke-border" />
                  <XAxis dataKey="name" className="fill-muted-foreground text-xs" />
                  <YAxis className="fill-muted-foreground text-xs" hide />
                  <Tooltip content={<CustomTooltip />} />
                  <Line
                    type="monotone"
                    dataKey="sales"
                    className="stroke-primary"
                    strokeWidth={3}
                    dot={{ r: 4, className: "fill-primary" }}
                    activeDot={{ r: 6, className: "fill-primary" }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      </>
    );
  };

  // Render the Popular Items tab content
  const renderPopularItems = () => {
    return (
      <>
        <h2 className="text-foreground text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Popular Menu Items</h2>
        <div className="px-4 py-3 @container">
          <div className="flex overflow-hidden rounded-lg border border-border bg-card">
            <table className="flex-1">
              <thead>
                <tr className="bg-card">
                  <th className="table-column-120 px-4 py-3 text-left text-foreground w-[400px] text-sm font-medium leading-normal">Item</th>
                  <th className="table-column-240 px-4 py-3 text-left text-foreground w-[400px] text-sm font-medium leading-normal">Category</th>
                  <th className="table-column-360 px-4 py-3 text-left text-foreground w-[400px] text-sm font-medium leading-normal">Orders</th>
                  <th className="table-column-480 px-4 py-3 text-left text-foreground w-[400px] text-sm font-medium leading-normal">Revenue</th>
                </tr>
              </thead>
              <tbody>
                {popularItems?.map((item: any) => (
                  <tr key={item.id} className="border-t border-t-border">
                    <td className="table-column-120 h-[72px] px-4 py-2 w-[400px] text-foreground text-sm font-normal leading-normal">
                      {item.name}
                    </td>
                    <td className="table-column-240 h-[72px] px-4 py-2 w-[400px] text-muted-foreground text-sm font-normal leading-normal">
                      {item.category}
                    </td>
                    <td className="table-column-360 h-[72px] px-4 py-2 w-[400px] text-muted-foreground text-sm font-normal leading-normal">{item.orders}</td>
                    <td className="table-column-480 h-[72px] px-4 py-2 w-[400px] text-muted-foreground text-sm font-normal leading-normal">{formatCurrency(item.revenue)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <style dangerouslySetInnerHTML={{
            __html: `
              @container(max-width:120px){.table-column-120{display: none;}}
              @container(max-width:240px){.table-column-240{display: none;}}
              @container(max-width:360px){.table-column-360{display: none;}}
              @container(max-width:480px){.table-column-480{display: none;}}
            `
          }} />
        </div>
      </>
    );
  };

  // Render the Customer Data tab content
  const renderCustomerData = () => {
    return (
      <>
        <h2 className="text-foreground text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Customer Data</h2>
        <div className="flex flex-wrap gap-4 p-4">
          <div className="flex min-w-[158px] flex-1 flex-col gap-2 rounded-lg p-6 border border-border bg-card">
            <p className="text-foreground text-base font-medium leading-normal">Total Customers</p>
            <p className="text-foreground tracking-light text-2xl font-bold leading-tight">{customerAnalytics?.totalCustomers?.toLocaleString() || '0'}</p>
          </div>
          <div className="flex min-w-[158px] flex-1 flex-col gap-2 rounded-lg p-6 border border-border bg-card">
            <p className="text-foreground text-base font-medium leading-normal">Average Spend</p>
            <p className="text-foreground tracking-light text-2xl font-bold leading-tight">{formatCurrency(customerAnalytics?.averageSpend || 0)}</p>
          </div>
          <div className="flex min-w-[158px] flex-1 flex-col gap-2 rounded-lg p-6 border border-border bg-card">
            <p className="text-foreground text-base font-medium leading-normal">Repeat Customers</p>
            <p className="text-foreground tracking-light text-2xl font-bold leading-tight">{formatPercentage(customerAnalytics?.repeatCustomerRate || 0)}</p>
          </div>
        </div>
      </>
    );
  };

  // Render the appropriate content based on the active tab
  const renderTabContent = () => {
    switch (activeTab) {
      case 'sales-trends':
        return renderSalesTrends();
      case 'popular-items':
        return renderPopularItems();
      case 'customer-data':
        return renderCustomerData();
      default:
        return renderSalesTrends();
    }
  };

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/dashboard`}>
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-foreground text-[32px] font-bold leading-tight">Reports & Analytics</h1>
          <p className="text-muted-foreground text-sm">Gain insights into your restaurant performance with detailed reports and analytics.</p>
        </div>
      </div>

      <div className="pb-3">
        <div className="flex border-b border-border gap-8">
          <a
            className={`flex flex-col items-center justify-center border-b-[3px] ${activeTab === 'sales-trends' ? 'border-b-primary text-foreground' : 'border-b-transparent text-muted-foreground'} pb-[13px] pt-4`}
            href="#"
            onClick={(e) => { e.preventDefault(); setActiveTab('sales-trends'); }}
          >
            <p className={`${activeTab === 'sales-trends' ? 'text-foreground' : 'text-muted-foreground'} text-sm font-bold leading-normal tracking-[0.015em]`}>Sales Trends</p>
          </a>
          <a
            className={`flex flex-col items-center justify-center border-b-[3px] ${activeTab === 'popular-items' ? 'border-b-primary text-foreground' : 'border-b-transparent text-muted-foreground'} pb-[13px] pt-4`}
            href="#"
            onClick={(e) => { e.preventDefault(); setActiveTab('popular-items'); }}
          >
            <p className={`${activeTab === 'popular-items' ? 'text-foreground' : 'text-muted-foreground'} text-sm font-bold leading-normal tracking-[0.015em]`}>Popular Items</p>
          </a>
          <a
            className={`flex flex-col items-center justify-center border-b-[3px] ${activeTab === 'customer-data' ? 'border-b-primary text-foreground' : 'border-b-transparent text-muted-foreground'} pb-[13px] pt-4`}
            href="#"
            onClick={(e) => { e.preventDefault(); setActiveTab('customer-data'); }}
          >
            <p className={`${activeTab === 'customer-data' ? 'text-foreground' : 'text-muted-foreground'} text-sm font-bold leading-normal tracking-[0.015em]`}>Customer Data</p>
          </a>
        </div>
      </div>

      {/* Render the content based on the active tab */}
      {renderTabContent()}
    </div>
  );
}
