'use client';

import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Wifi, WifiOff, Zap, Bell, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useNotificationContext } from '@/contexts/NotificationContext';
import { motion, AnimatePresence } from 'framer-motion';

interface RealTimeIndicatorProps {
  className?: string;
}

interface RecentNotification {
  id: string;
  title: string;
  message: string;
  type: string;
  priority: string;
  timestamp: number;
}

export function RealTimeIndicator({ className }: RealTimeIndicatorProps) {
  const { isConnected, isConnecting } = useNotificationContext();
  const [recentNotifications, setRecentNotifications] = useState<RecentNotification[]>([]);
  const [showRecentNotifications, setShowRecentNotifications] = useState(false);

  // Listen for new notifications (this would be connected to the WebSocket context)
  useEffect(() => {
    // This is a placeholder for real-time notification handling
    // In a real implementation, this would listen to the WebSocket context
    const handleNewNotification = (notification: RecentNotification) => {
      setRecentNotifications(prev => [notification, ...prev.slice(0, 4)]); // Keep last 5
      setShowRecentNotifications(true);
      
      // Auto-hide after 5 seconds
      setTimeout(() => {
        setShowRecentNotifications(false);
      }, 5000);
    };

    // Example: Listen to custom events or WebSocket messages
    // This would be replaced with actual WebSocket integration
    
    return () => {
      // Cleanup listeners
    };
  }, []);

  const getStatusColor = () => {
    if (isConnecting) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    if (isConnected) return 'text-green-600 bg-green-50 border-green-200';
    return 'text-red-600 bg-red-50 border-red-200';
  };

  const getStatusText = () => {
    if (isConnecting) return 'Connecting...';
    if (isConnected) return 'Live Updates Active';
    return 'Offline - No Live Updates';
  };

  const getStatusIcon = () => {
    if (isConnecting) {
      return <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600" />;
    }
    if (isConnected) {
      return <Wifi className="h-4 w-4" />;
    }
    return <WifiOff className="h-4 w-4" />;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'high':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'low':
        return 'text-gray-600 bg-gray-50 border-gray-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const formatTimeAgo = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (seconds < 60) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return new Date(timestamp).toLocaleDateString();
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Connection Status */}
      <div className="flex items-center justify-between">
        <Badge variant="outline" className={cn("text-xs px-3 py-1", getStatusColor())}>
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            <span>{getStatusText()}</span>
          </div>
        </Badge>

        {isConnected && (
          <div className="flex items-center gap-2 text-green-600">
            <Zap className="h-4 w-4" />
            <span className="text-xs">Real-time</span>
          </div>
        )}
      </div>

      {/* Recent Notifications Popup */}
      <AnimatePresence>
        {showRecentNotifications && recentNotifications.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -20, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="fixed top-4 right-4 z-50 w-80"
          >
            <Card className="shadow-lg border-2 border-blue-200 bg-blue-50">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <Bell className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-900">
                      New Notifications
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowRecentNotifications(false)}
                    className="h-6 w-6 p-0 text-blue-600 hover:text-blue-800"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {recentNotifications.map((notification) => (
                    <motion.div
                      key={notification.id}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className="p-2 bg-white rounded border border-blue-200"
                    >
                      <div className="flex items-start justify-between gap-2">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {notification.title}
                            </p>
                            <Badge 
                              variant="outline" 
                              className={cn("text-xs", getPriorityColor(notification.priority))}
                            >
                              {notification.priority}
                            </Badge>
                          </div>
                          <p className="text-xs text-gray-600 line-clamp-2">
                            {notification.message}
                          </p>
                          <p className="text-xs text-gray-500 mt-1">
                            {formatTimeAgo(notification.timestamp)}
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Connection Help */}
      {!isConnected && !isConnecting && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-3">
            <div className="flex items-start gap-2">
              <WifiOff className="h-4 w-4 text-yellow-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-yellow-900">
                  Real-time updates unavailable
                </p>
                <p className="text-xs text-yellow-700 mt-1">
                  Notifications will still update when you refresh the page or navigate between sections.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
