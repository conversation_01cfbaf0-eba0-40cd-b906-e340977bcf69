'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { 
  TrendingUp, 
  ArrowLeft,
  Users,
  DollarSign,
  Calendar,
  Star,
  Heart,
  Clock,
  MapPin,
  Smartphone,
  RefreshCw,
  Download,
  Filter
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

// Mock Chart Component
function Chart({ data, type, className }: { data: any[], type: string, className?: string }) {
  return (
    <div className={cn('bg-gray-100 rounded-lg flex items-center justify-center h-64', className)}>
      <div className="text-center text-gray-500">
        <div className="text-2xl mb-2">📊</div>
        <p>{type} Chart</p>
        <p className="text-sm">Chart visualization would go here</p>
      </div>
    </div>
  );
}

// Types
interface CustomerAnalytics {
  totalCustomers: number;
  newCustomers: number;
  returningCustomers: number;
  churnRate: number;
  averageLifetimeValue: number;
  customerAcquisitionCost: number;
  retentionRate: number;
  averageOrderFrequency: number;
}

interface DemographicData {
  ageGroups: { range: string; percentage: number; count: number }[];
  genderDistribution: { gender: string; percentage: number; count: number }[];
  locationData: { city: string; count: number; percentage: number }[];
  deviceUsage: { device: string; percentage: number; count: number }[];
}

interface BehaviorData {
  visitFrequency: { frequency: string; percentage: number; count: number }[];
  orderTiming: { time: string; orders: number }[];
  preferredChannels: { channel: string; percentage: number; count: number }[];
  seasonalTrends: { month: string; customers: number; revenue: number }[];
}

export default function CustomerAnalyticsPage() {
  const t = useTranslations('customers');
  const params = useParams();
  const { slugShop, slugBranch } = params;

  // State
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState('30d');

  // Mock data
  const mockAnalytics: CustomerAnalytics = {
    totalCustomers: 1247,
    newCustomers: 89,
    returningCustomers: 456,
    churnRate: 12.5,
    averageLifetimeValue: 342.50,
    customerAcquisitionCost: 25.80,
    retentionRate: 68.5,
    averageOrderFrequency: 2.3,
  };

  const mockDemographics: DemographicData = {
    ageGroups: [
      { range: '18-25', percentage: 18, count: 224 },
      { range: '26-35', percentage: 35, count: 436 },
      { range: '36-50', percentage: 32, count: 399 },
      { range: '50+', percentage: 15, count: 187 },
    ],
    genderDistribution: [
      { gender: 'Female', percentage: 52, count: 649 },
      { gender: 'Male', percentage: 45, count: 561 },
      { gender: 'Other', percentage: 3, count: 37 },
    ],
    locationData: [
      { city: 'New York', count: 456, percentage: 36.6 },
      { city: 'Brooklyn', count: 234, percentage: 18.8 },
      { city: 'Queens', count: 189, percentage: 15.2 },
      { city: 'Manhattan', count: 167, percentage: 13.4 },
      { city: 'Bronx', count: 123, percentage: 9.9 },
      { city: 'Other', count: 78, percentage: 6.3 },
    ],
    deviceUsage: [
      { device: 'Mobile', percentage: 68, count: 848 },
      { device: 'Desktop', percentage: 25, count: 312 },
      { device: 'Tablet', percentage: 7, count: 87 },
    ],
  };

  const mockBehavior: BehaviorData = {
    visitFrequency: [
      { frequency: 'Weekly', percentage: 25, count: 312 },
      { frequency: 'Bi-weekly', percentage: 35, count: 436 },
      { frequency: 'Monthly', percentage: 28, count: 349 },
      { frequency: 'Occasionally', percentage: 12, count: 150 },
    ],
    orderTiming: [
      { time: '11:00', orders: 45 },
      { time: '12:00', orders: 89 },
      { time: '13:00', orders: 156 },
      { time: '18:00', orders: 134 },
      { time: '19:00', orders: 178 },
      { time: '20:00', orders: 145 },
      { time: '21:00', orders: 98 },
    ],
    preferredChannels: [
      { channel: 'Dine-in', percentage: 45, count: 561 },
      { channel: 'Takeaway', percentage: 35, count: 436 },
      { channel: 'Delivery', percentage: 20, count: 249 },
    ],
    seasonalTrends: [
      { month: 'Jan', customers: 1180, revenue: 45600 },
      { month: 'Feb', customers: 1205, revenue: 48200 },
      { month: 'Mar', customers: 1247, revenue: 52100 },
      { month: 'Apr', customers: 1289, revenue: 54800 },
      { month: 'May', customers: 1334, revenue: 58900 },
      { month: 'Jun', customers: 1378, revenue: 61200 },
    ],
  };

  // Load data
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const dateRangeOptions = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 3 months' },
    { value: '1y', label: 'Last year' }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/customers`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Customers
            </Button>
          </Link>
          <div className="flex items-center gap-3">
            <TrendingUp className="w-8 h-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Customer Analytics</h1>
              <p className="text-gray-600">Insights into customer behavior and trends</p>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select date range" />
            </SelectTrigger>
            <SelectContent>
              {dateRangeOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button variant="outline" className="flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockAnalytics.totalCustomers.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              +{mockAnalytics.newCustomers} new this month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Retention Rate</CardTitle>
            <Heart className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockAnalytics.retentionRate}%</div>
            <p className="text-xs text-muted-foreground">
              {mockAnalytics.returningCustomers} returning customers
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Lifetime Value</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${mockAnalytics.averageLifetimeValue}</div>
            <p className="text-xs text-muted-foreground">
              CAC: ${mockAnalytics.customerAcquisitionCost}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Churn Rate</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockAnalytics.churnRate}%</div>
            <p className="text-xs text-muted-foreground">
              {mockAnalytics.averageOrderFrequency}x avg frequency
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Customer Growth */}
        <Card>
          <CardHeader>
            <CardTitle>Customer Growth Trend</CardTitle>
            <p className="text-sm text-gray-600">New vs returning customers over time</p>
          </CardHeader>
          <CardContent>
            <Chart data={mockBehavior.seasonalTrends} type="Line" />
          </CardContent>
        </Card>

        {/* Order Timing */}
        <Card>
          <CardHeader>
            <CardTitle>Peak Order Times</CardTitle>
            <p className="text-sm text-gray-600">Customer activity throughout the day</p>
          </CardHeader>
          <CardContent>
            <Chart data={mockBehavior.orderTiming} type="Bar" />
          </CardContent>
        </Card>
      </div>

      {/* Demographics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Age Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Age Distribution</CardTitle>
            <p className="text-sm text-gray-600">Customer age demographics</p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockDemographics.ageGroups.map((group, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="font-medium">{group.range}</span>
                    <span className="text-gray-500">
                      {group.count} ({group.percentage}%)
                    </span>
                  </div>
                  <Progress value={group.percentage} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Visit Frequency */}
        <Card>
          <CardHeader>
            <CardTitle>Visit Frequency</CardTitle>
            <p className="text-sm text-gray-600">How often customers visit</p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockBehavior.visitFrequency.map((freq, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="font-medium">{freq.frequency}</span>
                    <span className="text-gray-500">
                      {freq.count} ({freq.percentage}%)
                    </span>
                  </div>
                  <Progress value={freq.percentage} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Location & Device Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Locations */}
        <Card>
          <CardHeader>
            <CardTitle>Customer Locations</CardTitle>
            <p className="text-sm text-gray-600">Geographic distribution</p>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {mockDemographics.locationData.map((location, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <MapPin className="w-4 h-4 text-gray-400" />
                    <span className="font-medium">{location.city}</span>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{location.count}</div>
                    <div className="text-sm text-gray-500">{location.percentage}%</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Device Usage */}
        <Card>
          <CardHeader>
            <CardTitle>Device Usage</CardTitle>
            <p className="text-sm text-gray-600">How customers access your service</p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockDemographics.deviceUsage.map((device, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Smartphone className="w-4 h-4 text-gray-400" />
                    <span className="font-medium">{device.device}</span>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{device.count}</div>
                    <div className="text-sm text-gray-500">{device.percentage}%</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Order Channels */}
      <Card>
        <CardHeader>
          <CardTitle>Preferred Order Channels</CardTitle>
          <p className="text-sm text-gray-600">How customers prefer to order</p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {mockBehavior.preferredChannels.map((channel, index) => (
              <div key={index} className="text-center p-6 bg-gray-50 rounded-lg">
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {channel.percentage}%
                </div>
                <div className="font-medium mb-1">{channel.channel}</div>
                <div className="text-sm text-gray-500">
                  {channel.count} customers
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Customer Lifecycle */}
      <Card>
        <CardHeader>
          <CardTitle>Customer Lifecycle Metrics</CardTitle>
          <p className="text-sm text-gray-600">Key performance indicators</p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <div className="text-2xl font-bold text-green-600 mb-2">
                ${mockAnalytics.averageLifetimeValue}
              </div>
              <div className="text-sm font-medium mb-1">Lifetime Value</div>
              <div className="text-xs text-gray-500">Per customer</div>
            </div>

            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 mb-2">
                ${mockAnalytics.customerAcquisitionCost}
              </div>
              <div className="text-sm font-medium mb-1">Acquisition Cost</div>
              <div className="text-xs text-gray-500">Per new customer</div>
            </div>

            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <div className="text-2xl font-bold text-purple-600 mb-2">
                {mockAnalytics.averageOrderFrequency}x
              </div>
              <div className="text-sm font-medium mb-1">Order Frequency</div>
              <div className="text-xs text-gray-500">Per month</div>
            </div>

            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <div className="text-2xl font-bold text-red-600 mb-2">
                {mockAnalytics.churnRate}%
              </div>
              <div className="text-sm font-medium mb-1">Churn Rate</div>
              <div className="text-xs text-gray-500">Monthly</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Insights & Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle>Key Insights & Recommendations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="font-semibold text-green-600">Positive Trends</h4>
              <div className="space-y-3">
                <div className="flex items-start gap-2">
                  <TrendingUp className="w-4 h-4 text-green-600 mt-0.5" />
                  <div className="text-sm">
                    <p className="font-medium">Strong retention rate</p>
                    <p className="text-gray-600">68.5% of customers return within 30 days</p>
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <Users className="w-4 h-4 text-green-600 mt-0.5" />
                  <div className="text-sm">
                    <p className="font-medium">Growing customer base</p>
                    <p className="text-gray-600">89 new customers acquired this month</p>
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <DollarSign className="w-4 h-4 text-green-600 mt-0.5" />
                  <div className="text-sm">
                    <p className="font-medium">High lifetime value</p>
                    <p className="text-gray-600">$342.50 average per customer</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-semibold text-orange-600">Areas for Improvement</h4>
              <div className="space-y-3">
                <div className="flex items-start gap-2">
                  <Clock className="w-4 h-4 text-orange-600 mt-0.5" />
                  <div className="text-sm">
                    <p className="font-medium">Reduce churn rate</p>
                    <p className="text-gray-600">12.5% monthly churn needs attention</p>
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <Smartphone className="w-4 h-4 text-orange-600 mt-0.5" />
                  <div className="text-sm">
                    <p className="font-medium">Optimize mobile experience</p>
                    <p className="text-gray-600">68% use mobile but conversion could improve</p>
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <Calendar className="w-4 h-4 text-orange-600 mt-0.5" />
                  <div className="text-sm">
                    <p className="font-medium">Increase visit frequency</p>
                    <p className="text-gray-600">Target bi-weekly visitors for weekly visits</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
