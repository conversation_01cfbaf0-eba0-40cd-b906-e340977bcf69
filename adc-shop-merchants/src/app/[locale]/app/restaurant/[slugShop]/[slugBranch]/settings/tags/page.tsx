"use client";

import React, { useState } from 'react';
import { Plus, Search, Filter, BarChart3, <PERSON>ting<PERSON>, Trash2, Edit } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import {
  useGetTagsQuery,
  useGetTagCategoriesQuery,
  useGetTagAnalyticsQuery,
  useCreateTagMutation,
  useUpdateTagMutation,
  useDeleteTagMutation,
  useCreateTagCategoryMutation,
  Tag,
  TagCategory,
  CreateTagRequest,
  UpdateTagRequest,
  CreateTagCategoryRequest
} from '@/lib/redux/api/endpoints/tagApi';

interface TagManagementPageProps {
  params: {
    slugShop: string;
    slugBranch: string;
    locale: string;
  };
}

export default function TagManagementPage({ params }: TagManagementPageProps) {
  const { slugShop, slugBranch } = params;
  const { toast } = useToast();

  // State
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [isCreateTagOpen, setIsCreateTagOpen] = useState(false);
  const [isCreateCategoryOpen, setIsCreateCategoryOpen] = useState(false);
  const [editingTag, setEditingTag] = useState<Tag | null>(null);

  // API hooks
  const { data: tagsResponse, isLoading: isLoadingTags } = useGetTagsQuery({
    shopId: slugShop,
    branchId: slugBranch,
    filters: {
      search: searchTerm,
      category: selectedCategory || undefined,
      is_active: true
    }
  });

  const { data: categoriesResponse } = useGetTagCategoriesQuery({
    shopId: slugShop,
    branchId: slugBranch,
    filters: { is_active: true }
  });

  const { data: analytics } = useGetTagAnalyticsQuery({
    shopId: slugShop,
    branchId: slugBranch
  });

  const [createTag] = useCreateTagMutation();
  const [updateTag] = useUpdateTagMutation();
  const [deleteTag] = useDeleteTagMutation();
  const [createCategory] = useCreateTagCategoryMutation();

  const tags = tagsResponse?.tags || [];
  const categories = categoriesResponse?.categories || [];

  // Handlers
  const handleCreateTag = async (data: CreateTagRequest) => {
    try {
      await createTag({
        shopId: slugShop,
        branchId: slugBranch,
        tag: data
      }).unwrap();

      toast({
        title: "Success",
        description: "Tag created successfully",
      });
      setIsCreateTagOpen(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create tag",
        variant: "destructive",
      });
    }
  };

  const handleUpdateTag = async (tagId: string, data: UpdateTagRequest) => {
    try {
      await updateTag({
        shopId: slugShop,
        branchId: slugBranch,
        tagId,
        tag: data
      }).unwrap();

      toast({
        title: "Success",
        description: "Tag updated successfully",
      });
      setEditingTag(null);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update tag",
        variant: "destructive",
      });
    }
  };

  const handleDeleteTag = async (tagId: string) => {
    if (!confirm('Are you sure you want to delete this tag?')) return;

    try {
      await deleteTag({
        shopId: slugShop,
        branchId: slugBranch,
        tagId
      }).unwrap();

      toast({
        title: "Success",
        description: "Tag deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete tag",
        variant: "destructive",
      });
    }
  };

  const handleCreateCategory = async (data: CreateTagCategoryRequest) => {
    try {
      await createCategory({
        shopId: slugShop,
        branchId: slugBranch,
        category: data
      }).unwrap();

      toast({
        title: "Success",
        description: "Category created successfully",
      });
      setIsCreateCategoryOpen(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create category",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Tag Management</h1>
          <p className="text-muted-foreground">
            Manage tags and categories for your restaurant
          </p>
        </div>
        <div className="flex gap-2">
          <Dialog open={isCreateCategoryOpen} onOpenChange={setIsCreateCategoryOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                New Category
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create Tag Category</DialogTitle>
                <DialogDescription>
                  Create a new category to organize your tags
                </DialogDescription>
              </DialogHeader>
              <CreateCategoryForm onSubmit={handleCreateCategory} />
            </DialogContent>
          </Dialog>

          <Dialog open={isCreateTagOpen} onOpenChange={setIsCreateTagOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                New Tag
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create Tag</DialogTitle>
                <DialogDescription>
                  Create a new tag for your restaurant
                </DialogDescription>
              </DialogHeader>
              <CreateTagForm
                categories={categories}
                onSubmit={handleCreateTag}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs defaultValue="tags" className="space-y-4">
        <TabsList>
          <TabsTrigger value="tags">Tags</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="tags" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      placeholder="Search tags..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="All categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All categories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.slug}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Tags Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {tags.map((tag) => (
              <TagCard
                key={tag.id}
                tag={tag}
                onEdit={setEditingTag}
                onDelete={handleDeleteTag}
              />
            ))}
          </div>

          {tags.length === 0 && !isLoadingTags && (
            <Card>
              <CardContent className="pt-6 text-center">
                <p className="text-muted-foreground">No tags found</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="categories" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {categories.map((category) => (
              <CategoryCard key={category.id} category={category} />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          {analytics && <AnalyticsView analytics={analytics} />}
        </TabsContent>
      </Tabs>

      {/* Edit Tag Dialog */}
      {editingTag && (
        <Dialog open={!!editingTag} onOpenChange={() => setEditingTag(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Tag</DialogTitle>
              <DialogDescription>
                Update tag information
              </DialogDescription>
            </DialogHeader>
            <EditTagForm
              tag={editingTag}
              categories={categories}
              onSubmit={(data) => handleUpdateTag(editingTag.id, data)}
              onCancel={() => setEditingTag(null)}
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}

// Supporting Components
function TagCard({ tag, onEdit, onDelete }: {
  tag: Tag;
  onEdit: (tag: Tag) => void;
  onDelete: (id: string) => void;
}) {
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="pt-4">
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-center gap-2">
            {tag.icon && <span className="text-lg">{tag.icon}</span>}
            <Badge
              variant="outline"
              style={{ backgroundColor: tag.color + '20', borderColor: tag.color }}
            >
              {tag.name}
            </Badge>
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(tag)}
            >
              <Edit className="h-3 w-3" />
            </Button>
            {!tag.is_system && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDelete(tag.id)}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>

        {tag.description && (
          <p className="text-sm text-muted-foreground mb-2">{tag.description}</p>
        )}

        <div className="flex justify-between items-center text-xs text-muted-foreground">
          <span>{tag.category}</span>
          <span>{tag.usage_count} uses</span>
        </div>
      </CardContent>
    </Card>
  );
}

function CategoryCard({ category }: { category: TagCategory }) {
  return (
    <Card>
      <CardContent className="pt-4">
        <div className="flex items-center gap-2 mb-2">
          {category.icon && <span className="text-lg">{category.icon}</span>}
          <h3 className="font-semibold">{category.name}</h3>
        </div>

        {category.description && (
          <p className="text-sm text-muted-foreground mb-3">{category.description}</p>
        )}

        <div className="flex flex-wrap gap-1">
          {category.tags?.slice(0, 5).map((tag) => (
            <Badge key={tag.id} variant="outline" className="text-xs">
              {tag.name}
            </Badge>
          ))}
          {category.tags && category.tags.length > 5 && (
            <Badge variant="outline" className="text-xs">
              +{category.tags.length - 5} more
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

function CreateTagForm({
  categories,
  onSubmit
}: {
  categories: TagCategory[];
  onSubmit: (data: CreateTagRequest) => void;
}) {
  const [formData, setFormData] = useState<CreateTagRequest>({
    name: '',
    description: '',
    category: '',
    color: '#8a745c',
    icon: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="name">Name</Label>
        <Input
          id="name"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          required
        />
      </div>

      <div>
        <Label htmlFor="category">Category</Label>
        <Select
          value={formData.category}
          onValueChange={(value) => setFormData({ ...formData, category: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select category" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((category) => (
              <SelectItem key={category.id} value={category.slug}>
                {category.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="color">Color</Label>
          <Input
            id="color"
            type="color"
            value={formData.color}
            onChange={(e) => setFormData({ ...formData, color: e.target.value })}
          />
        </div>

        <div>
          <Label htmlFor="icon">Icon (emoji)</Label>
          <Input
            id="icon"
            value={formData.icon}
            onChange={(e) => setFormData({ ...formData, icon: e.target.value })}
            placeholder="🏷️"
          />
        </div>
      </div>

      <Button type="submit" className="w-full">Create Tag</Button>
    </form>
  );
}

function EditTagForm({
  tag,
  categories,
  onSubmit,
  onCancel
}: {
  tag: Tag;
  categories: TagCategory[];
  onSubmit: (data: UpdateTagRequest) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState<UpdateTagRequest>({
    name: tag.name,
    description: tag.description,
    category: tag.category,
    color: tag.color,
    icon: tag.icon
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="name">Name</Label>
        <Input
          id="name"
          value={formData.name || ''}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
        />
      </div>

      <div>
        <Label htmlFor="category">Category</Label>
        <Select
          value={formData.category || ''}
          onValueChange={(value) => setFormData({ ...formData, category: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select category" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((category) => (
              <SelectItem key={category.id} value={category.slug}>
                {category.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description || ''}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="color">Color</Label>
          <Input
            id="color"
            type="color"
            value={formData.color || '#8a745c'}
            onChange={(e) => setFormData({ ...formData, color: e.target.value })}
          />
        </div>

        <div>
          <Label htmlFor="icon">Icon (emoji)</Label>
          <Input
            id="icon"
            value={formData.icon || ''}
            onChange={(e) => setFormData({ ...formData, icon: e.target.value })}
            placeholder="🏷️"
          />
        </div>
      </div>

      <div className="flex gap-2">
        <Button type="submit" className="flex-1">Update Tag</Button>
        <Button type="button" variant="outline" onClick={onCancel}>Cancel</Button>
      </div>
    </form>
  );
}

function CreateCategoryForm({ onSubmit }: { onSubmit: (data: CreateTagCategoryRequest) => void }) {
  const [formData, setFormData] = useState<CreateTagCategoryRequest>({
    name: '',
    description: '',
    color: '#8a745c',
    icon: '',
    sort_order: 0
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="name">Name</Label>
        <Input
          id="name"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          required
        />
      </div>

      <div>
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
        />
      </div>

      <div className="grid grid-cols-3 gap-4">
        <div>
          <Label htmlFor="color">Color</Label>
          <Input
            id="color"
            type="color"
            value={formData.color}
            onChange={(e) => setFormData({ ...formData, color: e.target.value })}
          />
        </div>

        <div>
          <Label htmlFor="icon">Icon (emoji)</Label>
          <Input
            id="icon"
            value={formData.icon}
            onChange={(e) => setFormData({ ...formData, icon: e.target.value })}
            placeholder="📁"
          />
        </div>

        <div>
          <Label htmlFor="sort_order">Sort Order</Label>
          <Input
            id="sort_order"
            type="number"
            value={formData.sort_order}
            onChange={(e) => setFormData({ ...formData, sort_order: parseInt(e.target.value) || 0 })}
          />
        </div>
      </div>

      <Button type="submit" className="w-full">Create Category</Button>
    </form>
  );
}

function AnalyticsView({ analytics }: { analytics: any }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Total Tags:</span>
              <span className="font-semibold">{analytics.total_tags}</span>
            </div>
            <div className="flex justify-between">
              <span>Categories:</span>
              <span className="font-semibold">{analytics.total_categories}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Most Used Tags</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {analytics.most_used_tags?.slice(0, 5).map((tag: any) => (
              <div key={tag.id} className="flex justify-between items-center">
                <Badge variant="outline">{tag.name}</Badge>
                <span className="text-sm text-muted-foreground">{tag.usage_count}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Category Distribution</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {Object.entries(analytics.tags_by_category || {}).map(([category, count]) => (
              <div key={category} className="flex justify-between">
                <span className="capitalize">{category}:</span>
                <span className="font-semibold">{count as number}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
