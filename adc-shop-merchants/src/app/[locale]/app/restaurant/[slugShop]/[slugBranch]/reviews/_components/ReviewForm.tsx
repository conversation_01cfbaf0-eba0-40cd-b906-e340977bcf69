"use client";

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { StarRating } from '@/components/ui/star-rating';
import { TagSelector } from '@/components/ui/tag-selector';
import { toast } from 'sonner';
// Note: This mutation needs to be added to reviewsApi.ts
// For now, we'll create a placeholder that shows the form structure

const reviewSchema = z.object({
  customer_name: z.string().min(1, 'Customer name is required'),
  customer_email: z.string().email('Valid email is required').optional().or(z.literal('')),
  rating: z.number().min(1, 'Rating is required').max(5, 'Rating must be between 1 and 5'),
  title: z.string().optional(),
  comment: z.string().min(10, 'Comment must be at least 10 characters'),
  tags: z.array(z.string()).default([]),
  source: z.string().default('manual'),
});

type ReviewFormData = z.infer<typeof reviewSchema>;

interface ReviewFormProps {
  shopId: string;
  branchId: string;
  slugShop: string;
  slugBranch: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function ReviewForm({
  shopId,
  branchId,
  slugShop,
  slugBranch,
  onSuccess,
  onCancel
}: ReviewFormProps) {
  // Placeholder for API call - replace with actual mutation when available
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<ReviewFormData>({
    resolver: zodResolver(reviewSchema),
    defaultValues: {
      customer_name: '',
      customer_email: '',
      rating: 5,
      title: '',
      comment: '',
      tags: [],
      source: 'manual'
    }
  });

  const rating = watch('rating');
  const tags = watch('tags');

  const onSubmit = async (data: ReviewFormData) => {
    setIsLoading(true);
    try {
      // Placeholder API call - replace with actual implementation
      console.log('Review data to submit:', { shopSlug: slugShop, branchSlug: slugBranch, ...data });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast.success("Review form completed (demo mode)");
      onSuccess?.();
    } catch (error) {
      toast.error("Failed to create review");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Create Review</CardTitle>
        <CardDescription>
          Add a new customer review for this restaurant
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Customer Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="customer_name">Customer Name *</Label>
              <Input
                id="customer_name"
                {...register('customer_name')}
                placeholder="Enter customer name"
              />
              {errors.customer_name && (
                <p className="text-sm text-red-500">{errors.customer_name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="customer_email">Customer Email</Label>
              <Input
                id="customer_email"
                type="email"
                {...register('customer_email')}
                placeholder="<EMAIL>"
              />
              {errors.customer_email && (
                <p className="text-sm text-red-500">{errors.customer_email.message}</p>
              )}
            </div>
          </div>

          {/* Rating */}
          <div className="space-y-2">
            <Label>Rating *</Label>
            <div className="flex items-center gap-4">
              <StarRating
                rating={rating}
                onChange={(newRating) => setValue('rating', newRating)}
                size="lg"
                readOnly={false}
              />
              <span className="text-sm text-muted-foreground">
                {rating} out of 5 stars
              </span>
            </div>
            {errors.rating && (
              <p className="text-sm text-red-500">{errors.rating.message}</p>
            )}
          </div>

          {/* Review Title */}
          <div className="space-y-2">
            <Label htmlFor="title">Review Title</Label>
            <Input
              id="title"
              {...register('title')}
              placeholder="Brief summary of the experience"
            />
          </div>

          {/* Review Comment */}
          <div className="space-y-2">
            <Label htmlFor="comment">Review Comment *</Label>
            <Textarea
              id="comment"
              {...register('comment')}
              placeholder="Share details about the experience..."
              rows={4}
            />
            {errors.comment && (
              <p className="text-sm text-red-500">{errors.comment.message}</p>
            )}
          </div>

          {/* Tags */}
          <div className="space-y-2">
            <Label>Review Tags</Label>
            <TagSelector
              shopId={shopId}
              branchId={branchId}
              value={tags}
              onChange={(newTags) => setValue('tags', newTags)}
              entityType="review"
              placeholder="Add tags to categorize this review (e.g., excellent service, great food, slow service)..."
              categoryFilter="feedback"
              allowCustomTags={true}
              maxTags={8}
            />
            <p className="text-xs text-muted-foreground">
              Tags help categorize reviews and identify common themes in customer feedback.
            </p>
          </div>

          {/* Form Actions */}
          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              disabled={isLoading}
              className="flex-1"
            >
              {isLoading ? 'Creating...' : 'Create Review'}
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isLoading}
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
