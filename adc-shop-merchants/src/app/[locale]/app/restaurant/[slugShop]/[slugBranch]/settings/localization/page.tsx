'use client';

import React, { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import {
  ArrowLeft,
  Globe,
  Clock,
  DollarSign,
  Calendar,
  MapPin,
  Languages,
  Save,
  CheckCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { AppLoading } from '@/components/ui/app-loading';
import { toast } from 'sonner';
import { useGetShopBySlugQuery, useGetBranchBySlugQuery, useGetBranchSettingsQuery, useUpdateBranchSettingsMutation } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import type { Shop, Branch } from '@/lib/types/shop';

// Types
interface LocalizationSettings {
  language: string;
  region: string;
  timezone: string;
  currency: string;
  dateFormat: string;
  timeFormat: '12h' | '24h';
  numberFormat: string;
  firstDayOfWeek: 'sunday' | 'monday';
  rtlSupport: boolean;
  autoDetectLanguage: boolean;
  fallbackLanguage: string;
}

interface LocalizationSettingsPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function LocalizationSettingsPage({ params }: LocalizationSettingsPageProps) {
  const { slugShop, slugBranch } = React.use(params);

  // Get shop and branch data from backend using slug-based queries
  const { data: shopData, isLoading: isShopLoading, error: shopError } = useGetShopBySlugQuery(slugShop) as {
    data: Shop | undefined;
    isLoading: boolean;
    error: unknown;
  };
  const { data: branchData, isLoading: isBranchLoading, error: branchError } = useGetBranchBySlugQuery({
    shopSlug: slugShop,
    branchSlug: slugBranch
  }) as {
    data: Branch | undefined;
    isLoading: boolean;
    error: unknown;
  };

  // Get branch settings
  const {
    data: branchSettings,
    isLoading: isLoadingSettings,
  } = useGetBranchSettingsQuery(
    { shopId: shopData?.id || '', branchId: branchData?.id || '' },
    { skip: !shopData?.id || !branchData?.id }
  );

  // Update branch settings mutation
  const [updateBranchSettings, { isLoading: isUpdating }] = useUpdateBranchSettingsMutation();

  // State
  const [settings, setSettings] = useState<LocalizationSettings>({
    language: 'th',
    region: 'TH',
    timezone: 'Asia/Bangkok',
    currency: 'THB',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: '24h',
    numberFormat: 'th-TH',
    firstDayOfWeek: 'monday',
    rtlSupport: false,
    autoDetectLanguage: false,
    fallbackLanguage: 'en',
  });

  const isLoading = isShopLoading || isBranchLoading || isLoadingSettings;
  const hasError = shopError || branchError;

  // Load localization settings from branch settings
  useEffect(() => {
    if (branchSettings) {
      setSettings(prev => ({
        ...prev,
        language: branchSettings.language || 'th',
        region: branchSettings.region || 'TH',
        timezone: branchSettings.timezone || 'Asia/Bangkok',
        currency: branchSettings.currency || 'THB',
        dateFormat: branchSettings.dateFormat || 'dd/MM/yyyy',
        timeFormat: branchSettings.timeFormat || '24h',
        numberFormat: branchSettings.numberFormat || 'th-TH',
        firstDayOfWeek: branchSettings.firstDayOfWeek || 'monday',
        rtlSupport: branchSettings.rtlSupport || false,
        autoDetectLanguage: branchSettings.autoDetectLanguage || false,
        fallbackLanguage: branchSettings.fallbackLanguage || 'en',
      }));
    }
  }, [branchSettings]);

  // Available options
  const languages = [
    { code: 'en', name: 'English', nativeName: 'English' },
    { code: 'es', name: 'Spanish', nativeName: 'Español' },
    { code: 'fr', name: 'French', nativeName: 'Français' },
    { code: 'de', name: 'German', nativeName: 'Deutsch' },
    { code: 'it', name: 'Italian', nativeName: 'Italiano' },
    { code: 'pt', name: 'Portuguese', nativeName: 'Português' },
    { code: 'ru', name: 'Russian', nativeName: 'Русский' },
    { code: 'ja', name: 'Japanese', nativeName: '日本語' },
    { code: 'ko', name: 'Korean', nativeName: '한국어' },
    { code: 'zh', name: 'Chinese', nativeName: '中文' },
    { code: 'ar', name: 'Arabic', nativeName: 'العربية' },
    { code: 'hi', name: 'Hindi', nativeName: 'हिन्दी' },
    { code: 'th', name: 'Thai', nativeName: 'ไทย' },
    { code: 'vi', name: 'Vietnamese', nativeName: 'Tiếng Việt' },
  ];

  const regions = [
    { code: 'US', name: 'United States' },
    { code: 'CA', name: 'Canada' },
    { code: 'GB', name: 'United Kingdom' },
    { code: 'AU', name: 'Australia' },
    { code: 'DE', name: 'Germany' },
    { code: 'FR', name: 'France' },
    { code: 'ES', name: 'Spain' },
    { code: 'IT', name: 'Italy' },
    { code: 'JP', name: 'Japan' },
    { code: 'KR', name: 'South Korea' },
    { code: 'CN', name: 'China' },
    { code: 'IN', name: 'India' },
    { code: 'BR', name: 'Brazil' },
    { code: 'MX', name: 'Mexico' },
    { code: 'TH', name: 'Thailand' },
    { code: 'VN', name: 'Vietnam' },
  ];

  const timezones = [
    { value: 'America/New_York', label: 'Eastern Time (ET)' },
    { value: 'America/Chicago', label: 'Central Time (CT)' },
    { value: 'America/Denver', label: 'Mountain Time (MT)' },
    { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
    { value: 'America/Anchorage', label: 'Alaska Time (AKT)' },
    { value: 'Pacific/Honolulu', label: 'Hawaii Time (HST)' },
    { value: 'Europe/London', label: 'Greenwich Mean Time (GMT)' },
    { value: 'Europe/Paris', label: 'Central European Time (CET)' },
    { value: 'Europe/Berlin', label: 'Central European Time (CET)' },
    { value: 'Europe/Rome', label: 'Central European Time (CET)' },
    { value: 'Europe/Madrid', label: 'Central European Time (CET)' },
    { value: 'Asia/Tokyo', label: 'Japan Standard Time (JST)' },
    { value: 'Asia/Seoul', label: 'Korea Standard Time (KST)' },
    { value: 'Asia/Shanghai', label: 'China Standard Time (CST)' },
    { value: 'Asia/Kolkata', label: 'India Standard Time (IST)' },
    { value: 'Asia/Bangkok', label: 'Indochina Time (ICT)' },
    { value: 'Asia/Ho_Chi_Minh', label: 'Indochina Time (ICT)' },
    { value: 'Australia/Sydney', label: 'Australian Eastern Time (AET)' },
  ];

  const currencies = [
    { code: 'USD', name: 'US Dollar', symbol: '$' },
    { code: 'EUR', name: 'Euro', symbol: '€' },
    { code: 'GBP', name: 'British Pound', symbol: '£' },
    { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
    { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
    { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
    { code: 'CHF', name: 'Swiss Franc', symbol: 'CHF' },
    { code: 'CNY', name: 'Chinese Yuan', symbol: '¥' },
    { code: 'KRW', name: 'South Korean Won', symbol: '₩' },
    { code: 'INR', name: 'Indian Rupee', symbol: '₹' },
    { code: 'BRL', name: 'Brazilian Real', symbol: 'R$' },
    { code: 'MXN', name: 'Mexican Peso', symbol: '$' },
    { code: 'THB', name: 'Thai Baht', symbol: '฿' },
    { code: 'VND', name: 'Vietnamese Dong', symbol: '₫' },
  ];

  const dateFormats = [
    { value: 'MM/dd/yyyy', label: 'MM/dd/yyyy (12/31/2024)' },
    { value: 'dd/MM/yyyy', label: 'dd/MM/yyyy (31/12/2024)' },
    { value: 'yyyy-MM-dd', label: 'yyyy-MM-dd (2024-12-31)' },
    { value: 'dd.MM.yyyy', label: 'dd.MM.yyyy (31.12.2024)' },
    { value: 'MMM dd, yyyy', label: 'MMM dd, yyyy (Dec 31, 2024)' },
    { value: 'dd MMM yyyy', label: 'dd MMM yyyy (31 Dec 2024)' },
  ];

  // Handle save settings
  const handleSaveSettings = async () => {
    if (!shopData?.id || !branchData?.id) {
      toast.error('Missing shop or branch information');
      return;
    }

    try {
      // Update branch settings using real API
      await updateBranchSettings({
        shopId: shopData.id,
        branchId: branchData.id,
        settings: {
          // Localization settings
          language: settings.language,
          region: settings.region,
          timezone: settings.timezone,
          currency: settings.currency,
          dateFormat: settings.dateFormat,
          timeFormat: settings.timeFormat,
          numberFormat: settings.numberFormat,
          firstDayOfWeek: settings.firstDayOfWeek,
          rtlSupport: settings.rtlSupport,
          autoDetectLanguage: settings.autoDetectLanguage,
          fallbackLanguage: settings.fallbackLanguage,
        },
      }).unwrap();

      toast.success('Localization settings saved successfully!');
    } catch (error) {
      console.error('Error saving localization settings:', error);
      toast.error('Failed to save localization settings. Please try again.');
    }
  };

  // Get current date/time preview
  const getCurrentPreview = () => {
    const now = new Date();
    const timeZone = settings.timezone;

    // Format date
    const dateOptions: Intl.DateTimeFormatOptions = {
      timeZone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    };

    // Format time
    const timeOptions: Intl.DateTimeFormatOptions = {
      timeZone,
      hour: '2-digit',
      minute: '2-digit',
      hour12: settings.timeFormat === '12h',
    };

    // Format currency
    const currencyFormatter = new Intl.NumberFormat(settings.numberFormat, {
      style: 'currency',
      currency: settings.currency,
    });

    return {
      date: now.toLocaleDateString(settings.numberFormat, dateOptions),
      time: now.toLocaleTimeString(settings.numberFormat, timeOptions),
      currency: currencyFormatter.format(123.45),
      number: new Intl.NumberFormat(settings.numberFormat).format(1234567.89),
    };
  };

  const preview = getCurrentPreview();

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (hasError || !shopData || !branchData) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Restaurant Not Found</h1>
          <p className="text-[#8a745c] text-sm">The restaurant or branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/settings`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Settings
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Language & Region</h1>
          <p className="text-[#8a745c] text-sm">Configure language, timezone, currency, and regional settings for {shopData.name} - {branchData.name}</p>
        </div>
        <Button
          onClick={handleSaveSettings}
          disabled={isUpdating}
          className="bg-[#e58219] hover:bg-[#d67917] text-white font-bold px-6 h-12"
        >
          <Save className="w-4 h-4 mr-2" />
          {isUpdating ? 'Saving...' : 'Save Settings'}
        </Button>
      </div>

      <div className="max-w-4xl space-y-6">

      {/* Preview Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-600" />
            Preview
          </CardTitle>
          <p className="text-sm text-gray-600">See how your settings will appear</p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <Calendar className="w-6 h-6 text-gray-600 mx-auto mb-2" />
              <div className="font-medium">Date</div>
              <div className="text-sm text-gray-600">{preview.date}</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <Clock className="w-6 h-6 text-gray-600 mx-auto mb-2" />
              <div className="font-medium">Time</div>
              <div className="text-sm text-gray-600">{preview.time}</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <DollarSign className="w-6 h-6 text-gray-600 mx-auto mb-2" />
              <div className="font-medium">Currency</div>
              <div className="text-sm text-gray-600">{preview.currency}</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <Globe className="w-6 h-6 text-gray-600 mx-auto mb-2" />
              <div className="font-medium">Number</div>
              <div className="text-sm text-gray-600">{preview.number}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Language Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Languages className="w-5 h-5" />
            Language Settings
          </CardTitle>
          <p className="text-sm text-gray-600">Configure language preferences</p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="language">Primary Language</Label>
              <Select value={settings.language} onValueChange={(value) => setSettings({ ...settings, language: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent>
                  {languages.map((lang) => (
                    <SelectItem key={lang.code} value={lang.code}>
                      {lang.name} ({lang.nativeName})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="fallbackLanguage">Fallback Language</Label>
              <Select value={settings.fallbackLanguage} onValueChange={(value) => setSettings({ ...settings, fallbackLanguage: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select fallback language" />
                </SelectTrigger>
                <SelectContent>
                  {languages.map((lang) => (
                    <SelectItem key={lang.code} value={lang.code}>
                      {lang.name} ({lang.nativeName})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <Separator />

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Auto-detect Language</h4>
                <p className="text-sm text-gray-600">
                  Automatically detect user language from browser settings
                </p>
              </div>
              <Switch
                checked={settings.autoDetectLanguage}
                onCheckedChange={(checked) => setSettings({ ...settings, autoDetectLanguage: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Right-to-Left Support</h4>
                <p className="text-sm text-gray-600">
                  Enable support for RTL languages like Arabic and Hebrew
                </p>
              </div>
              <Switch
                checked={settings.rtlSupport}
                onCheckedChange={(checked) => setSettings({ ...settings, rtlSupport: checked })}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Regional Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="w-5 h-5" />
            Regional Settings
          </CardTitle>
          <p className="text-sm text-gray-600">Configure regional preferences</p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="region">Region</Label>
              <Select value={settings.region} onValueChange={(value) => setSettings({ ...settings, region: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select region" />
                </SelectTrigger>
                <SelectContent>
                  {regions.map((region) => (
                    <SelectItem key={region.code} value={region.code}>
                      {region.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="timezone">Timezone</Label>
              <Select value={settings.timezone} onValueChange={(value) => setSettings({ ...settings, timezone: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select timezone" />
                </SelectTrigger>
                <SelectContent>
                  {timezones.map((tz) => (
                    <SelectItem key={tz.value} value={tz.value}>
                      {tz.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Format Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            Format Settings
          </CardTitle>
          <p className="text-sm text-gray-600">Configure date, time, and number formats</p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="currency">Currency</Label>
              <Select value={settings.currency} onValueChange={(value) => setSettings({ ...settings, currency: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
                <SelectContent>
                  {currencies.map((currency) => (
                    <SelectItem key={currency.code} value={currency.code}>
                      {currency.symbol} {currency.name} ({currency.code})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="dateFormat">Date Format</Label>
              <Select value={settings.dateFormat} onValueChange={(value) => setSettings({ ...settings, dateFormat: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select date format" />
                </SelectTrigger>
                <SelectContent>
                  {dateFormats.map((format) => (
                    <SelectItem key={format.value} value={format.value}>
                      {format.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="timeFormat">Time Format</Label>
              <Select value={settings.timeFormat} onValueChange={(value: '12h' | '24h') => setSettings({ ...settings, timeFormat: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select time format" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="12h">12-hour (2:30 PM)</SelectItem>
                  <SelectItem value="24h">24-hour (14:30)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="firstDayOfWeek">First Day of Week</Label>
              <Select value={settings.firstDayOfWeek} onValueChange={(value: 'sunday' | 'monday') => setSettings({ ...settings, firstDayOfWeek: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select first day of week" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sunday">Sunday</SelectItem>
                  <SelectItem value="monday">Monday</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Number Format */}
      <Card>
        <CardHeader>
          <CardTitle>Number Format</CardTitle>
          <p className="text-sm text-gray-600">Configure how numbers are displayed</p>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="numberFormat">Number Format Locale</Label>
            <Select value={settings.numberFormat} onValueChange={(value) => setSettings({ ...settings, numberFormat: value })}>
              <SelectTrigger>
                <SelectValue placeholder="Select number format" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en-US">English (US) - 1,234.56</SelectItem>
                <SelectItem value="en-GB">English (UK) - 1,234.56</SelectItem>
                <SelectItem value="de-DE">German - 1.234,56</SelectItem>
                <SelectItem value="fr-FR">French - 1 234,56</SelectItem>
                <SelectItem value="es-ES">Spanish - 1.234,56</SelectItem>
                <SelectItem value="it-IT">Italian - 1.234,56</SelectItem>
                <SelectItem value="ja-JP">Japanese - 1,234.56</SelectItem>
                <SelectItem value="ko-KR">Korean - 1,234.56</SelectItem>
                <SelectItem value="zh-CN">Chinese - 1,234.56</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>
      </div>
    </div>
  );
}
