'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { 
  Users, 
  ArrowLeft,
  Plus, 
  Search,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  Target,
  TrendingUp,
  DollarSign,
  Calendar,
  Star,
  Crown,
  Heart,
  Clock,
  RefreshCw,
  Download,
  Filter
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

// Types
interface CustomerSegment {
  id: string;
  name: string;
  description: string;
  criteria: SegmentCriteria;
  customerCount: number;
  totalValue: number;
  averageOrderValue: number;
  lastUpdated: Date;
  color: string;
  icon: React.ReactNode;
  growthRate: number;
  conversionRate: number;
}

interface SegmentCriteria {
  type: 'automatic' | 'manual';
  rules: SegmentRule[];
}

interface SegmentRule {
  field: string;
  operator: string;
  value: string | number;
}

interface SegmentStats {
  totalSegments: number;
  totalCustomersSegmented: number;
  highValueSegments: number;
  averageSegmentSize: number;
}

export default function CustomerSegmentsPage() {
  const t = useTranslations('customers');
  const params = useParams();
  const { slugShop, slugBranch } = params;

  // State
  const [segments, setSegments] = useState<CustomerSegment[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  // Mock data
  const mockSegments: CustomerSegment[] = [
    {
      id: '1',
      name: 'VIP Customers',
      description: 'High-value customers with total spend over $2000',
      criteria: {
        type: 'automatic',
        rules: [
          { field: 'totalSpent', operator: 'greater_than', value: 2000 },
          { field: 'status', operator: 'equals', value: 'vip' }
        ]
      },
      customerCount: 45,
      totalValue: 125000,
      averageOrderValue: 85.50,
      lastUpdated: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      color: 'bg-purple-100 text-purple-800',
      icon: <Crown className="w-4 h-4" />,
      growthRate: 15.2,
      conversionRate: 92.5,
    },
    {
      id: '2',
      name: 'Frequent Diners',
      description: 'Customers who visit more than twice per month',
      criteria: {
        type: 'automatic',
        rules: [
          { field: 'visitFrequency', operator: 'greater_than', value: 8 },
          { field: 'lastVisit', operator: 'within_days', value: 30 }
        ]
      },
      customerCount: 128,
      totalValue: 89500,
      averageOrderValue: 42.30,
      lastUpdated: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      color: 'bg-green-100 text-green-800',
      icon: <Heart className="w-4 h-4" />,
      growthRate: 8.7,
      conversionRate: 78.3,
    },
    {
      id: '3',
      name: 'New Customers',
      description: 'Customers who joined in the last 30 days',
      criteria: {
        type: 'automatic',
        rules: [
          { field: 'joinDate', operator: 'within_days', value: 30 }
        ]
      },
      customerCount: 67,
      totalValue: 12400,
      averageOrderValue: 28.90,
      lastUpdated: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      color: 'bg-blue-100 text-blue-800',
      icon: <Star className="w-4 h-4" />,
      growthRate: 22.1,
      conversionRate: 45.2,
    },
    {
      id: '4',
      name: 'At-Risk Customers',
      description: 'Customers who haven\'t visited in over 60 days',
      criteria: {
        type: 'automatic',
        rules: [
          { field: 'lastVisit', operator: 'more_than_days_ago', value: 60 },
          { field: 'totalOrders', operator: 'greater_than', value: 5 }
        ]
      },
      customerCount: 34,
      totalValue: 45600,
      averageOrderValue: 52.80,
      lastUpdated: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      color: 'bg-red-100 text-red-800',
      icon: <Clock className="w-4 h-4" />,
      growthRate: -12.5,
      conversionRate: 15.8,
    },
    {
      id: '5',
      name: 'Pizza Lovers',
      description: 'Customers who frequently order pizza items',
      criteria: {
        type: 'manual',
        rules: [
          { field: 'favoriteCategory', operator: 'contains', value: 'pizza' }
        ]
      },
      customerCount: 89,
      totalValue: 34200,
      averageOrderValue: 38.40,
      lastUpdated: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      color: 'bg-orange-100 text-orange-800',
      icon: <Target className="w-4 h-4" />,
      growthRate: 5.3,
      conversionRate: 68.9,
    },
    {
      id: '6',
      name: 'Weekend Warriors',
      description: 'Customers who primarily visit on weekends',
      criteria: {
        type: 'automatic',
        rules: [
          { field: 'visitPattern', operator: 'equals', value: 'weekend' }
        ]
      },
      customerCount: 156,
      totalValue: 67800,
      averageOrderValue: 45.20,
      lastUpdated: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000),
      color: 'bg-yellow-100 text-yellow-800',
      icon: <Calendar className="w-4 h-4" />,
      growthRate: 3.8,
      conversionRate: 72.1,
    },
  ];

  const mockStats: SegmentStats = {
    totalSegments: mockSegments.length,
    totalCustomersSegmented: mockSegments.reduce((sum, s) => sum + s.customerCount, 0),
    highValueSegments: mockSegments.filter(s => s.averageOrderValue > 50).length,
    averageSegmentSize: mockSegments.reduce((sum, s) => sum + s.customerCount, 0) / mockSegments.length,
  };

  // Load data
  useEffect(() => {
    const timer = setTimeout(() => {
      setSegments(mockSegments);
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Filter segments
  const filteredSegments = segments.filter(segment => {
    const matchesSearch = segment.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         segment.description.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesSearch;
  });

  // Format date
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Get growth trend icon
  const getGrowthTrendIcon = (growthRate: number) => {
    if (growthRate > 0) {
      return <TrendingUp className="w-4 h-4 text-green-600" />;
    } else {
      return <TrendingUp className="w-4 h-4 text-red-600 rotate-180" />;
    }
  };

  // Get segment type badge
  const getSegmentTypeBadge = (type: 'automatic' | 'manual') => {
    const typeConfig = {
      automatic: { color: 'bg-blue-100 text-blue-800', label: 'Auto' },
      manual: { color: 'bg-gray-100 text-gray-800', label: 'Manual' },
    };

    const config = typeConfig[type];

    return (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/customers`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Customers
            </Button>
          </Link>
          <div className="flex items-center gap-3">
            <Target className="w-8 h-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Customer Segments</h1>
              <p className="text-gray-600">Group customers by behavior and characteristics</p>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" className="flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export
          </Button>
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/customers/segments/create`}>
            <Button className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Create Segment
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Segments</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.totalSegments}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Customers Segmented</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.totalCustomersSegmented}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">High Value Segments</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.highValueSegments}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Segment Size</CardTitle>
            <Users className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Math.round(mockStats.averageSegmentSize)}</div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <div className="flex items-center gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search segments..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
      </div>

      {/* Segments Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredSegments.map((segment) => (
          <Card key={segment.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className={cn('p-2 rounded-lg', segment.color)}>
                    {segment.icon}
                  </div>
                  <div>
                    <h3 className="font-semibold">{segment.name}</h3>
                    {getSegmentTypeBadge(segment.criteria.type)}
                  </div>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuItem asChild>
                      <Link href={`/app/restaurant/${slugShop}/${slugBranch}/customers/segments/${segment.id}`}>
                        <Eye className="mr-2 h-4 w-4" />
                        View Details
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href={`/app/restaurant/${slugShop}/${slugBranch}/customers/segments/${segment.id}/edit`}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Segment
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Filter className="mr-2 h-4 w-4" />
                      Export Customers
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem className="text-red-600">
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete Segment
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              <p className="text-sm text-gray-600">{segment.description}</p>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Key Metrics */}
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {segment.customerCount}
                  </div>
                  <div className="text-sm text-gray-600">Customers</div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    ${segment.averageOrderValue.toFixed(0)}
                  </div>
                  <div className="text-sm text-gray-600">Avg Order</div>
                </div>
              </div>

              {/* Performance Metrics */}
              <div className="space-y-3">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Growth Rate</span>
                    <div className="flex items-center gap-1">
                      {getGrowthTrendIcon(segment.growthRate)}
                      <span className={cn(
                        "font-medium",
                        segment.growthRate > 0 ? "text-green-600" : "text-red-600"
                      )}>
                        {Math.abs(segment.growthRate)}%
                      </span>
                    </div>
                  </div>
                  <Progress 
                    value={Math.abs(segment.growthRate)} 
                    className="h-2"
                  />
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Conversion Rate</span>
                    <span className="font-medium">{segment.conversionRate}%</span>
                  </div>
                  <Progress 
                    value={segment.conversionRate} 
                    className="h-2"
                  />
                </div>
              </div>

              {/* Total Value */}
              <div className="pt-3 border-t">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Total Value</span>
                  <span className="font-bold text-lg">
                    ${segment.totalValue.toLocaleString()}
                  </span>
                </div>
              </div>

              {/* Last Updated */}
              <div className="text-xs text-gray-500">
                Last updated: {formatDate(segment.lastUpdated)}
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2 pt-2">
                <Link 
                  href={`/app/restaurant/${slugShop}/${slugBranch}/customers/segments/${segment.id}`}
                  className="flex-1"
                >
                  <Button variant="outline" className="w-full" size="sm">
                    <Eye className="w-4 h-4 mr-2" />
                    View
                  </Button>
                </Link>
                <Link 
                  href={`/app/restaurant/${slugShop}/${slugBranch}/marketing/campaigns/create?segment=${segment.id}`}
                  className="flex-1"
                >
                  <Button className="w-full" size="sm">
                    <Target className="w-4 h-4 mr-2" />
                    Target
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredSegments.length === 0 && (
        <div className="text-center py-12">
          <Target className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No customer segments found</h3>
          <p className="text-gray-600 mb-4">
            {searchQuery
              ? 'Try adjusting your search terms'
              : 'Create your first customer segment to group customers by behavior'}
          </p>
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/customers/segments/create`}>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Create First Segment
            </Button>
          </Link>
        </div>
      )}

      {/* Segment Performance Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Segment Performance Overview</CardTitle>
          <p className="text-sm text-gray-600">Compare performance across all segments</p>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Segment</TableHead>
                  <TableHead>Customers</TableHead>
                  <TableHead>Total Value</TableHead>
                  <TableHead>Avg Order</TableHead>
                  <TableHead>Growth</TableHead>
                  <TableHead>Conversion</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSegments.map((segment) => (
                  <TableRow key={segment.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className={cn('p-1 rounded', segment.color)}>
                          {segment.icon}
                        </div>
                        {segment.name}
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">
                      {segment.customerCount}
                    </TableCell>
                    <TableCell className="font-medium">
                      ${segment.totalValue.toLocaleString()}
                    </TableCell>
                    <TableCell>
                      ${segment.averageOrderValue.toFixed(2)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        {getGrowthTrendIcon(segment.growthRate)}
                        <span className={cn(
                          "font-medium",
                          segment.growthRate > 0 ? "text-green-600" : "text-red-600"
                        )}>
                          {segment.growthRate > 0 ? '+' : ''}{segment.growthRate}%
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="font-medium">{segment.conversionRate}%</span>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
