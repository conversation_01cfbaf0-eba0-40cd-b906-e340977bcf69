import React from 'react'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import TablesPage from '../page'

// Mock @/i18n/navigation
jest.mock('@/i18n/navigation', () => ({
  Link: ({ children, href, ...props }: any) => {
    return React.createElement('a', { href, ...props }, children)
  },
  useRouter: jest.fn(() => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  })),
  usePathname: jest.fn(() => '/'),
}))

// Mock the APIs
const mockMerchantApi = {
  useGetMerchantsQuery: jest.fn(),
}

const mockTablesApi = {
  useGetTablesQuery: jest.fn(),
  useGetTableAreasQuery: jest.fn(),
}

const mockReservationsApi = {
  useGetReservationsQuery: jest.fn(),
}

jest.mock('@/lib/redux/api/endpoints/restaurant/shopApi', () => ({
  useGetMerchantsQuery: () => mockMerchantApi.useGetMerchantsQuery(),
}))

jest.mock('@/lib/redux/api/endpoints/restaurant/tablesApi', () => ({
  useGetTablesQuery: () => mockTablesApi.useGetTablesQuery(),
  useGetTableAreasQuery: () => mockTablesApi.useGetTableAreasQuery(),
}))

jest.mock('@/lib/redux/api/endpoints/restaurant/reservationsApi', () => ({
  useGetReservationsQuery: () => mockReservationsApi.useGetReservationsQuery(),
}))

// Mock data
const mockMerchant = {
  id: 'merchant-1',
  slug: 'test-restaurant',
  name: 'Test Restaurant',
  branches: [
    {
      id: 'branch-1',
      slug: 'main-branch',
      name: 'Main Branch',
      address: '123 Test St',
    },
  ],
}

const mockTables = [
  {
    id: 'table-1',
    number: 1,
    name: 'Table 1',
    capacity: 4,
    status: 'available',
    area: 'dining',
  },
  {
    id: 'table-2',
    number: 2,
    name: 'Table 2',
    capacity: 2,
    status: 'occupied',
    area: 'dining',
  },
  {
    id: 'table-3',
    number: 3,
    name: 'Table 3',
    capacity: 6,
    status: 'reserved',
    area: 'outdoor',
  },
]

const mockAreas = [
  {
    id: 'area-1',
    name: 'Dining Area',
    description: 'Main dining area',
  },
  {
    id: 'area-2',
    name: 'Outdoor Patio',
    description: 'Outdoor seating area',
  },
]

const mockReservations = [
  {
    id: 'reservation-1',
    time: '7:00 PM',
    customerName: 'John Doe',
    partySize: 4,
    tableId: 'table-1',
    tableName: 'Table 1',
    status: 'confirmed',
    date: '2024-01-15',
  },
  {
    id: 'reservation-2',
    time: '8:00 PM',
    customerName: 'Jane Smith',
    partySize: 2,
    tableId: 'table-2',
    tableName: 'Table 2',
    status: 'confirmed',
    date: '2024-01-15',
  },
]

// Create a mock store
const createMockStore = () => {
  return configureStore({
    reducer: {
      api: () => ({}),
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
      }),
  })
}

const renderWithProvider = (component: React.ReactElement) => {
  const store = createMockStore()
  return render(<Provider store={store}>{component}</Provider>)
}

describe('TablesPage', () => {
  const mockParams = Promise.resolve({
    slugShop: 'test-restaurant',
    slugBranch: 'main-branch',
  })

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Loading States', () => {
    it('shows loading spinner when data is loading', () => {
      mockMerchantApi.useGetMerchantsQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      })
      mockTablesApi.useGetTablesQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      })
      mockTablesApi.useGetTableAreasQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      })
      mockReservationsApi.useGetReservationsQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      })

      renderWithProvider(<TablesPage params={mockParams} />)

      expect(screen.getByTestId('app-loading')).toBeInTheDocument()
    })
  })

  describe('Error States', () => {
    it('shows error message when API calls fail', async () => {
      mockMerchantApi.useGetMerchantsQuery.mockReturnValue({
        data: { data: [mockMerchant] },
        isLoading: false,
        error: { message: 'Network error' },
      })
      mockTablesApi.useGetTablesQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: { message: 'Failed to fetch tables' },
      })
      mockTablesApi.useGetTableAreasQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: null,
      })
      mockReservationsApi.useGetReservationsQuery.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: null,
      })

      renderWithProvider(<TablesPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Error Loading Data')).toBeInTheDocument()
        expect(
          screen.getByText('There was an error loading the table data. Please try again.')
        ).toBeInTheDocument()
      })
    })

    it('shows restaurant not found when merchant does not exist', async () => {
      mockMerchantApi.useGetMerchantsQuery.mockReturnValue({
        data: { data: [] },
        isLoading: false,
        error: null,
      })

      renderWithProvider(<TablesPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Restaurant Not Found')).toBeInTheDocument()
        expect(
          screen.getByText('The restaurant or branch you are looking for does not exist.')
        ).toBeInTheDocument()
      })
    })
  })

  describe('Successful Data Loading', () => {
    beforeEach(() => {
      mockMerchantApi.useGetMerchantsQuery.mockReturnValue({
        data: { data: [mockMerchant] },
        isLoading: false,
        error: null,
      })
      mockTablesApi.useGetTablesQuery.mockReturnValue({
        data: mockTables,
        isLoading: false,
        error: null,
      })
      mockTablesApi.useGetTableAreasQuery.mockReturnValue({
        data: mockAreas,
        isLoading: false,
        error: null,
      })
      mockReservationsApi.useGetReservationsQuery.mockReturnValue({
        data: { data: mockReservations },
        isLoading: false,
        error: null,
      })
    })

    it('renders page title and description', async () => {
      renderWithProvider(<TablesPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Tables & Reservations')).toBeInTheDocument()
        expect(
          screen.getByText("Manage your restaurant's table layout and reservations.")
        ).toBeInTheDocument()
      })
    })

    it('renders tabs for floor plan and reservations', async () => {
      renderWithProvider(<TablesPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Floor Plan')).toBeInTheDocument()
        expect(screen.getByText('Reservations')).toBeInTheDocument()
      })
    })

    it('renders tables grouped by areas', async () => {
      renderWithProvider(<TablesPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Dining Area')).toBeInTheDocument()
        expect(screen.getByText('Outdoor Patio')).toBeInTheDocument()
        expect(screen.getByText('Table 1')).toBeInTheDocument()
        expect(screen.getByText('Table 2')).toBeInTheDocument()
        expect(screen.getByText('Table 3')).toBeInTheDocument()
      })
    })

    it('displays table status indicators correctly', async () => {
      renderWithProvider(<TablesPage params={mockParams} />)

      await waitFor(() => {
        const tableCards = screen.getAllByText(/Table \d/)
        expect(tableCards).toHaveLength(3)

        // Check for status text
        expect(screen.getByText('Status: available')).toBeInTheDocument()
        expect(screen.getByText('Status: occupied')).toBeInTheDocument()
        expect(screen.getByText('Status: reserved')).toBeInTheDocument()
      })
    })

    it('switches to reservations tab and displays reservations', async () => {
      renderWithProvider(<TablesPage params={mockParams} />)

      await waitFor(() => {
        const reservationsTab = screen.getByText('Reservations')
        fireEvent.click(reservationsTab)
      })

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
        expect(screen.getByText('Jane Smith')).toBeInTheDocument()
        expect(screen.getByText('7:00 PM')).toBeInTheDocument()
        expect(screen.getByText('8:00 PM')).toBeInTheDocument()
      })
    })

    it('displays empty state when no tables exist', async () => {
      mockTablesApi.useGetTablesQuery.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      })

      renderWithProvider(<TablesPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('No tables found. Add tables to get started.')).toBeInTheDocument()
      })
    })

    it('displays empty state when no reservations exist', async () => {
      mockReservationsApi.useGetReservationsQuery.mockReturnValue({
        data: { data: [] },
        isLoading: false,
        error: null,
      })

      renderWithProvider(<TablesPage params={mockParams} />)

      await waitFor(() => {
        const reservationsTab = screen.getByText('Reservations')
        fireEvent.click(reservationsTab)
      })

      await waitFor(() => {
        expect(screen.getByText('No reservations found')).toBeInTheDocument()
      })
    })
  })
})
