'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import {
  Megaphone,
  Plus,
  TrendingUp,
  Users,
  DollarSign,
  Eye,
  Calendar,
  Target,
  Mail,
  MessageSquare,
  Share2,
  Gift,
  Star,
  BarChart3,
  RefreshCw,
  Download,
  Edit,
  Trash2,
  MoreHorizontal
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Types
interface Campaign {
  id: string;
  name: string;
  type: 'email' | 'social' | 'sms' | 'promotion' | 'loyalty';
  status: 'draft' | 'active' | 'paused' | 'completed' | 'scheduled';
  startDate: Date;
  endDate?: Date;
  budget: number;
  spent: number;
  reach: number;
  engagement: number;
  conversions: number;
  revenue: number;
  description: string;
}

interface MarketingStats {
  totalCampaigns: number;
  activeCampaigns: number;
  totalReach: number;
  totalRevenue: number;
  avgROI: number;
  customerAcquisition: number;
}

export default function MarketingPage() {
  const t = useTranslations('marketing');
  const params = useParams();
  const { slugShop, slugBranch } = params;

  // State
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);

  // Mock data
  const mockCampaigns: Campaign[] = [
    {
      id: '1',
      name: 'Summer Special Menu',
      type: 'email',
      status: 'active',
      startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      endDate: new Date(Date.now() + 23 * 24 * 60 * 60 * 1000),
      budget: 500,
      spent: 320,
      reach: 2450,
      engagement: 18.5,
      conversions: 156,
      revenue: 3240,
      description: 'Promote new summer menu items with 20% discount'
    },
    {
      id: '2',
      name: 'Happy Hour Social Media',
      type: 'social',
      status: 'active',
      startDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
      endDate: new Date(Date.now() + 16 * 24 * 60 * 60 * 1000),
      budget: 300,
      spent: 180,
      reach: 5200,
      engagement: 12.3,
      conversions: 89,
      revenue: 1780,
      description: 'Daily happy hour promotion on social media'
    },
    {
      id: '3',
      name: 'Loyalty Program Launch',
      type: 'loyalty',
      status: 'completed',
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      budget: 1000,
      spent: 950,
      reach: 3800,
      engagement: 25.7,
      conversions: 342,
      revenue: 8500,
      description: 'Launch new customer loyalty rewards program'
    },
    {
      id: '4',
      name: 'Weekend Brunch Promo',
      type: 'sms',
      status: 'scheduled',
      startDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
      endDate: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000),
      budget: 200,
      spent: 0,
      reach: 0,
      engagement: 0,
      conversions: 0,
      revenue: 0,
      description: 'SMS campaign for weekend brunch special offers'
    },
  ];

  const mockStats: MarketingStats = {
    totalCampaigns: mockCampaigns.length,
    activeCampaigns: mockCampaigns.filter(c => c.status === 'active').length,
    totalReach: mockCampaigns.reduce((sum, c) => sum + c.reach, 0),
    totalRevenue: mockCampaigns.reduce((sum, c) => sum + c.revenue, 0),
    avgROI: 420, // percentage
    customerAcquisition: 89,
  };

  // Load data
  useEffect(() => {
    const timer = setTimeout(() => {
      setCampaigns(mockCampaigns);
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Get campaign type icon
  const getCampaignTypeIcon = (type: Campaign['type']) => {
    const icons = {
      email: Mail,
      social: Share2,
      sms: MessageSquare,
      promotion: Gift,
      loyalty: Star,
    };
    const Icon = icons[type];
    return <Icon className="w-4 h-4" />;
  };

  // Get status badge
  const getStatusBadge = (status: Campaign['status']) => {
    const statusConfig = {
      draft: { color: 'bg-muted text-muted-foreground border-border' },
      active: { color: 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300' },
      paused: { color: 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300' },
      completed: { color: 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300' },
      scheduled: { color: 'bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-950 dark:text-purple-300' },
    };

    const config = statusConfig[status];

    return (
      <Badge className={config.color}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  // Calculate ROI
  const calculateROI = (revenue: number, spent: number) => {
    if (spent === 0) return 0;
    return ((revenue - spent) / spent * 100);
  };

  // Format date
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Megaphone className="w-8 h-8 text-primary" />
          <div>
            <h1 className="text-2xl font-bold text-foreground">Marketing Dashboard</h1>
            <p className="text-muted-foreground">Manage campaigns, promotions, and customer engagement</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" className="flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export
          </Button>
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/marketing/campaigns/create`}>
            <Button className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              New Campaign
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Campaigns</CardTitle>
            <Megaphone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.totalCampaigns}</div>
            <p className="text-xs text-muted-foreground">All campaigns</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <Target className="h-4 w-4 text-green-600 dark:text-green-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">{mockStats.activeCampaigns}</div>
            <p className="text-xs text-muted-foreground">Running now</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Reach</CardTitle>
            <Users className="h-4 w-4 text-blue-600 dark:text-blue-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.totalReach.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">People reached</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600 dark:text-green-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${mockStats.totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Generated</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg ROI</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600 dark:text-green-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">{mockStats.avgROI}%</div>
            <p className="text-xs text-muted-foreground">Return on investment</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">New Customers</CardTitle>
            <Users className="h-4 w-4 text-purple-600 dark:text-purple-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{mockStats.customerAcquisition}</div>
            <p className="text-xs text-muted-foreground">This month</p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/marketing/campaigns`}>
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="flex items-center p-6">
              <Megaphone className="h-8 w-8 text-blue-600 dark:text-blue-400 mr-4" />
              <div>
                <h3 className="font-semibold">Campaigns</h3>
                <p className="text-sm text-muted-foreground">Manage all campaigns</p>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/marketing/promotions`}>
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="flex items-center p-6">
              <Gift className="h-8 w-8 text-green-600 dark:text-green-400 mr-4" />
              <div>
                <h3 className="font-semibold">Promotions</h3>
                <p className="text-sm text-muted-foreground">Create special offers</p>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/marketing/loyalty`}>
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="flex items-center p-6">
              <Star className="h-8 w-8 text-yellow-600 dark:text-yellow-400 mr-4" />
              <div>
                <h3 className="font-semibold">Loyalty Program</h3>
                <p className="text-sm text-muted-foreground">Reward customers</p>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/marketing/analytics`}>
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="flex items-center p-6">
              <BarChart3 className="h-8 w-8 text-purple-600 dark:text-purple-400 mr-4" />
              <div>
                <h3 className="font-semibold">Analytics</h3>
                <p className="text-sm text-muted-foreground">Performance insights</p>
              </div>
            </CardContent>
          </Card>
        </Link>
      </div>

      {/* Recent Campaigns */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Recent Campaigns</CardTitle>
            <Link href={`/app/restaurant/${slugShop}/${slugBranch}/marketing/campaigns`}>
              <Button variant="outline" size="sm">
                View All
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Campaign</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Period</TableHead>
                <TableHead>Reach</TableHead>
                <TableHead>Revenue</TableHead>
                <TableHead>ROI</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {campaigns.slice(0, 5).map((campaign) => (
                <TableRow key={campaign.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{campaign.name}</div>
                      <div className="text-sm text-muted-foreground truncate max-w-xs">
                        {campaign.description}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getCampaignTypeIcon(campaign.type)}
                      <span className="capitalize">{campaign.type}</span>
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(campaign.status)}</TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {formatDate(campaign.startDate)}
                      {campaign.endDate && (
                        <span> - {formatDate(campaign.endDate)}</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">
                      {campaign.reach.toLocaleString()}
                    </div>
                    {campaign.engagement > 0 && (
                      <div className="text-sm text-muted-foreground">
                        {campaign.engagement}% engagement
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">
                      ${campaign.revenue.toLocaleString()}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      ${campaign.spent} spent
                    </div>
                  </TableCell>
                  <TableCell>
                    {campaign.spent > 0 ? (
                      <div className={cn(
                        "font-medium",
                        calculateROI(campaign.revenue, campaign.spent) > 0
                          ? "text-green-600 dark:text-green-400"
                          : "text-red-600 dark:text-red-400"
                      )}>
                        {calculateROI(campaign.revenue, campaign.spent).toFixed(0)}%
                      </div>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem asChild>
                          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/marketing/campaigns/${campaign.id}`}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </Link>
                        </DropdownMenuItem>
                        {campaign.status === 'draft' && (
                          <DropdownMenuItem asChild>
                            <Link href={`/app/restaurant/${slugShop}/${slugBranch}/marketing/campaigns/${campaign.id}/edit`}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Campaign
                            </Link>
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-red-600">
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete Campaign
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Performance Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Campaign Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {campaigns.filter(c => c.status === 'active' || c.status === 'completed').map((campaign) => (
                <div key={campaign.id} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                  <div className="flex items-center gap-3">
                    {getCampaignTypeIcon(campaign.type)}
                    <div>
                      <div className="font-medium">{campaign.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {campaign.reach.toLocaleString()} reach • {campaign.conversions} conversions
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium text-green-600 dark:text-green-400">
                      ${campaign.revenue.toLocaleString()}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {calculateROI(campaign.revenue, campaign.spent).toFixed(0)}% ROI
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Channel Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {['email', 'social', 'sms', 'loyalty'].map((type) => {
                const typeCampaigns = campaigns.filter(c => c.type === type);
                const totalRevenue = typeCampaigns.reduce((sum, c) => sum + c.revenue, 0);
                const totalSpent = typeCampaigns.reduce((sum, c) => sum + c.spent, 0);
                const totalReach = typeCampaigns.reduce((sum, c) => sum + c.reach, 0);

                return (
                  <div key={type} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <div className="flex items-center gap-3">
                      {getCampaignTypeIcon(type as Campaign['type'])}
                      <div>
                        <div className="font-medium capitalize">{type}</div>
                        <div className="text-sm text-muted-foreground">
                          {totalReach.toLocaleString()} total reach
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">
                        ${totalRevenue.toLocaleString()}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {totalSpent > 0 ? `${((totalRevenue - totalSpent) / totalSpent * 100).toFixed(0)}% ROI` : '-'}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
