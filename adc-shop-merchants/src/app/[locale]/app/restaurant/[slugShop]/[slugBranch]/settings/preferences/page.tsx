'use client';

import React, { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { ArrowLeft } from 'lucide-react';
import { AppLoading } from '@/components/ui/app-loading';
import { toast } from 'sonner';
import { useGetShopBySlugQuery, useGetBranchBySlugQuery, useGetBranchSettingsQuery, useUpdateBranchSettingsMutation } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import type { Shop, Branch, BranchSettings } from '@/lib/types/shop';

interface PreferencesPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

interface RestaurantPreferences {
  restaurantName: string;
  restaurantAddress: string;
  contactPhone: string;
  contactEmail: string;
  openingHours: {
    monday: string;
    tuesday: string;
    wednesday: string;
    thursday: string;
    friday: string;
    saturday: string;
    sunday: string;
  };
  paymentMethods: {
    cash: boolean;
    creditCard: boolean;
    debitCard: boolean;
    mobilePayment: boolean;
    giftCard: boolean;
  };
  tableLayout: {
    numberOfTables: string;
    maximumPartySize: string;
  };
  onlineOrdering: 'enabled' | 'disabled';
}

export default function PreferencesPage({ params }: PreferencesPageProps) {
  const { slugShop, slugBranch } = React.use(params);

  // Get shop and branch data from backend using slug-based queries
  const { data: shopData, isLoading: isShopLoading, error: shopError } = useGetShopBySlugQuery(slugShop) as {
    data: Shop | undefined;
    isLoading: boolean;
    error: unknown;
  };
  const { data: branchData, isLoading: isBranchLoading, error: branchError } = useGetBranchBySlugQuery({
    shopSlug: slugShop,
    branchSlug: slugBranch
  }) as {
    data: Branch | undefined;
    isLoading: boolean;
    error: unknown;
  };

  // Get branch settings using the actual shop and branch IDs
  const {
    data: branchSettings,
    isLoading: isLoadingSettings,
    error: settingsError
  } = useGetBranchSettingsQuery(
    { shopId: shopData?.id || '', branchId: branchData?.id || '' },
    { skip: !shopData?.id || !branchData?.id }
  ) as {
    data: BranchSettings | undefined;
    isLoading: boolean;
    error: unknown;
  };

  // Update branch settings mutation
  const [updateBranchSettings, { isLoading: isUpdating }] = useUpdateBranchSettingsMutation();

  const [preferences, setPreferences] = useState<RestaurantPreferences>({
    restaurantName: '',
    restaurantAddress: '',
    contactPhone: '',
    contactEmail: '',
    openingHours: {
      monday: '9:00 AM - 10:00 PM',
      tuesday: '9:00 AM - 10:00 PM',
      wednesday: '9:00 AM - 10:00 PM',
      thursday: '9:00 AM - 10:00 PM',
      friday: '9:00 AM - 11:00 PM',
      saturday: '9:00 AM - 11:00 PM',
      sunday: '10:00 AM - 9:00 PM',
    },
    paymentMethods: {
      cash: true,
      creditCard: true,
      debitCard: true,
      mobilePayment: false,
      giftCard: false,
    },
    tableLayout: {
      numberOfTables: '20',
      maximumPartySize: '8',
    },
    onlineOrdering: 'enabled',
  });

  const isLoading = isShopLoading || isBranchLoading || isLoadingSettings;
  const hasError = shopError || branchError || settingsError;

  // Load branch data and settings into form
  useEffect(() => {
    if (shopData && branchData) {
      setPreferences(prev => ({
        ...prev,
        restaurantName: shopData.name,
        restaurantAddress: branchData.address?.street || '',
        contactPhone: branchData.phone || '',
        contactEmail: branchData.email || '',
      }));
    }

    if (branchSettings) {
      setPreferences(prev => ({
        ...prev,
        // Map business hours from branch data (convert from backend format to frontend format)
        openingHours: branchData?.business_hours ? {
          monday: branchData.business_hours.monday || '9:00 AM - 10:00 PM',
          tuesday: branchData.business_hours.tuesday || '9:00 AM - 10:00 PM',
          wednesday: branchData.business_hours.wednesday || '9:00 AM - 10:00 PM',
          thursday: branchData.business_hours.thursday || '9:00 AM - 10:00 PM',
          friday: branchData.business_hours.friday || '9:00 AM - 11:00 PM',
          saturday: branchData.business_hours.saturday || '9:00 AM - 11:00 PM',
          sunday: branchData.business_hours.sunday || '10:00 AM - 9:00 PM',
        } : prev.openingHours,
        // Map payment methods from settings
        paymentMethods: {
          cash: branchSettings.payment_methods?.includes('cash') || false,
          creditCard: branchSettings.payment_methods?.includes('credit_card') || false,
          debitCard: branchSettings.payment_methods?.includes('debit_card') || false,
          mobilePayment: branchSettings.payment_methods?.includes('mobile_payment') || false,
          giftCard: branchSettings.payment_methods?.includes('gift_card') || false,
        },
        // Map features from settings
        onlineOrdering: branchSettings.online_ordering ? 'enabled' : 'disabled',
      }));
    }
  }, [shopData, branchData, branchSettings]);

  const handleInputChange = (field: keyof RestaurantPreferences, value: string) => {
    setPreferences(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleOpeningHoursChange = (day: keyof RestaurantPreferences['openingHours'], value: string) => {
    setPreferences(prev => ({
      ...prev,
      openingHours: {
        ...prev.openingHours,
        [day]: value,
      },
    }));
  };

  const handlePaymentMethodChange = (method: keyof RestaurantPreferences['paymentMethods'], checked: boolean) => {
    setPreferences(prev => ({
      ...prev,
      paymentMethods: {
        ...prev.paymentMethods,
        [method]: checked,
      },
    }));
  };

  const handleTableLayoutChange = (field: keyof RestaurantPreferences['tableLayout'], value: string) => {
    setPreferences(prev => ({
      ...prev,
      tableLayout: {
        ...prev.tableLayout,
        [field]: value,
      },
    }));
  };

  const handleSavePreferences = async () => {
    if (!shopData?.id || !branchData?.id) {
      toast.error('Missing shop or branch information');
      return;
    }

    try {
      // Prepare payment methods array
      const paymentMethods = Object.entries(preferences.paymentMethods)
        .filter(([, enabled]) => enabled)
        .map(([method]) => {
          switch (method) {
            case 'creditCard': return 'credit_card';
            case 'debitCard': return 'debit_card';
            case 'mobilePayment': return 'mobile_payment';
            case 'giftCard': return 'gift_card';
            default: return method;
          }
        });

      // Prepare settings update to match backend BranchSettings structure
      const settingsUpdate = {
        payment_methods: paymentMethods,
        online_ordering: preferences.onlineOrdering === 'enabled',
        table_reservations: true, // Default to enabled
        delivery_enabled: false, // Default to disabled
      };

      // Update branch settings using real API
      await updateBranchSettings({
        shopId: shopData.id,
        branchId: branchData.id,
        settings: settingsUpdate,
      }).unwrap();

      toast.success('Preferences saved successfully!');
    } catch (error) {
      console.error('Error saving preferences:', error);
      toast.error('Failed to save preferences. Please try again.');
    }
  };

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (hasError || !shopData || !branchData) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Restaurant Not Found</h1>
          <p className="text-[#8a745c] text-sm">The restaurant or branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/settings`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Settings
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Restaurant Preferences</h1>
          <p className="text-[#8a745c] text-sm">Configure your restaurant settings for {shopData.name} - {branchData.name}</p>
        </div>
      </div>

      <div className="max-w-2xl">
        {/* General Settings */}
        <h3 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] pb-2 pt-4">General</h3>

        <div className="space-y-4 mb-6">
          <div>
            <Label htmlFor="restaurantName" className="text-[#181510] text-base font-medium leading-normal">
              Restaurant Name
            </Label>
            <Input
              id="restaurantName"
              value={preferences.restaurantName}
              onChange={(e) => handleInputChange('restaurantName', e.target.value)}
              className="mt-2 border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 text-base"
              placeholder="Enter restaurant name"
            />
          </div>

          <div>
            <Label htmlFor="restaurantAddress" className="text-[#181510] text-base font-medium leading-normal">
              Restaurant Address
            </Label>
            <Input
              id="restaurantAddress"
              value={preferences.restaurantAddress}
              onChange={(e) => handleInputChange('restaurantAddress', e.target.value)}
              className="mt-2 border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 text-base"
              placeholder="Enter restaurant address"
            />
          </div>

          <div>
            <Label htmlFor="contactPhone" className="text-[#181510] text-base font-medium leading-normal">
              Contact Phone
            </Label>
            <Input
              id="contactPhone"
              value={preferences.contactPhone}
              onChange={(e) => handleInputChange('contactPhone', e.target.value)}
              className="mt-2 border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 text-base"
              placeholder="Enter contact phone"
            />
          </div>

          <div>
            <Label htmlFor="contactEmail" className="text-[#181510] text-base font-medium leading-normal">
              Contact Email
            </Label>
            <Input
              id="contactEmail"
              type="email"
              value={preferences.contactEmail}
              onChange={(e) => handleInputChange('contactEmail', e.target.value)}
              className="mt-2 border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 text-base"
              placeholder="Enter contact email"
            />
          </div>
        </div>

        {/* Opening Hours */}
        <h3 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] pb-2 pt-4">Opening Hours</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {Object.entries(preferences.openingHours).map(([day, hours]) => (
            <div key={day}>
              <Label htmlFor={day} className="text-[#181510] text-base font-medium leading-normal capitalize">
                {day}
              </Label>
              <Input
                id={day}
                value={hours}
                onChange={(e) => handleOpeningHoursChange(day as keyof RestaurantPreferences['openingHours'], e.target.value)}
                className="mt-2 border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 text-base"
                placeholder="e.g., 9:00 AM - 10:00 PM"
              />
            </div>
          ))}
        </div>

        {/* Payment Methods */}
        <h3 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] pb-2 pt-4">Payment Methods</h3>

        <div className="space-y-3 mb-6">
          {Object.entries(preferences.paymentMethods).map(([method, checked]) => (
            <div key={method} className="flex items-center space-x-3">
              <Checkbox
                id={method}
                checked={checked}
                onCheckedChange={(checked) => handlePaymentMethodChange(method as keyof RestaurantPreferences['paymentMethods'], checked as boolean)}
                className="h-5 w-5 border-[#e5e1dc] border-2 data-[state=checked]:bg-[#e58219] data-[state=checked]:border-[#e58219]"
              />
              <Label htmlFor={method} className="text-[#181510] text-base font-normal leading-normal capitalize">
                {method === 'creditCard' ? 'Credit Card' :
                 method === 'debitCard' ? 'Debit Card' :
                 method === 'mobilePayment' ? 'Mobile Payment' :
                 method === 'giftCard' ? 'Gift Card' :
                 method}
              </Label>
            </div>
          ))}
        </div>

        {/* Table Layout */}
        <h3 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] pb-2 pt-4">Table Layout</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <Label htmlFor="numberOfTables" className="text-[#181510] text-base font-medium leading-normal">
              Number of Tables
            </Label>
            <Input
              id="numberOfTables"
              type="number"
              value={preferences.tableLayout.numberOfTables}
              onChange={(e) => handleTableLayoutChange('numberOfTables', e.target.value)}
              className="mt-2 border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 text-base"
              placeholder="Enter number of tables"
            />
          </div>

          <div>
            <Label htmlFor="maximumPartySize" className="text-[#181510] text-base font-medium leading-normal">
              Maximum Party Size
            </Label>
            <Input
              id="maximumPartySize"
              type="number"
              value={preferences.tableLayout.maximumPartySize}
              onChange={(e) => handleTableLayoutChange('maximumPartySize', e.target.value)}
              className="mt-2 border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 text-base"
              placeholder="Enter maximum party size"
            />
          </div>
        </div>

        {/* Online Ordering */}
        <h3 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] pb-2 pt-4">Online Ordering</h3>

        <RadioGroup
          value={preferences.onlineOrdering}
          onValueChange={(value) => setPreferences(prev => ({ ...prev, onlineOrdering: value as 'enabled' | 'disabled' }))}
          className="space-y-3 mb-6"
        >
          <div className="flex items-center space-x-4 rounded-xl border border-[#e5e1dc] p-4">
            <RadioGroupItem
              value="enabled"
              id="enabled"
              className="h-5 w-5 border-2 border-[#e5e1dc] text-[#181510]"
            />
            <Label htmlFor="enabled" className="text-[#181510] text-sm font-medium leading-normal">
              Enabled
            </Label>
          </div>
          <div className="flex items-center space-x-4 rounded-xl border border-[#e5e1dc] p-4">
            <RadioGroupItem
              value="disabled"
              id="disabled"
              className="h-5 w-5 border-2 border-[#e5e1dc] text-[#181510]"
            />
            <Label htmlFor="disabled" className="text-[#181510] text-sm font-medium leading-normal">
              Disabled
            </Label>
          </div>
        </RadioGroup>

        {/* Save Button */}
        <div className="flex justify-end pt-4">
          <Button
            onClick={handleSavePreferences}
            disabled={isUpdating}
            className="bg-[#e58219] hover:bg-[#d67917] text-[#181510] font-bold px-6 h-10"
          >
            {isUpdating ? 'Saving...' : 'Save Preferences'}
          </Button>
        </div>
      </div>
    </div>
  );
}
