import React from 'react'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import OrdersPage from '../page'

// Mock @/i18n/navigation
jest.mock('@/i18n/navigation', () => ({
  Link: ({ children, href, ...props }: any) => {
    return React.createElement('a', { href, ...props }, children)
  },
}))

// Mock the APIs and hooks
const mockMerchantApi = {
  useGetMerchantsQuery: jest.fn(),
}

const mockUseOrders = {
  orders: [],
  activeOrders: [
    {
      id: '#ORD-001',
      status: 'preparing',
      customer: { name: '<PERSON>', phone: '+**********' },
      total: 25.99,
      createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
      orderType: 'dine-in',
      tableId: 'Table 5',
      estimatedTime: 15,
      items: [
        { name: 'Burger Deluxe', quantity: 1, price: 15.99, total: 15.99 },
        { name: 'French Fries', quantity: 1, price: 5.99, total: 5.99 },
        { name: 'Coke', quantity: 1, price: 2.99, total: 2.99 },
      ],
    },
    {
      id: '#ORD-002',
      status: 'pending',
      customer: { name: 'Jane Smith', phone: '+**********' },
      total: 18.50,
      createdAt: new Date(Date.now() - 10 * 60 * 1000).toISOString(), // 10 minutes ago
      orderType: 'takeout',
      items: [
        { name: 'Caesar Salad', quantity: 2, price: 9.25, total: 18.50 },
      ],
    },
  ],
  completedOrders: [
    {
      id: '#ORD-003',
      status: 'completed',
      customer: { name: 'Bob Wilson', phone: '+1234567892' },
      total: 32.75,
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
      completedAt: new Date(Date.now() - 90 * 60 * 1000).toISOString(), // 90 minutes ago
      orderType: 'dine-in',
      tableId: 'Table 3',
      items: [
        { name: 'Steak Dinner', quantity: 1, price: 28.99, total: 28.99 },
        { name: 'Wine', quantity: 1, price: 3.76, total: 3.76 },
      ],
    },
  ],
  isLoading: false,
  isLoadingActive: false,
  isLoadingCompleted: false,
  getStatusColor: jest.fn((status) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'preparing': return 'bg-blue-100 text-blue-800'
      case 'ready': return 'bg-green-100 text-green-800'
      case 'completed': return 'bg-gray-100 text-gray-800'
      default: return 'bg-red-100 text-red-800'
    }
  }),
  getStatusText: jest.fn((status) => status),
  formatCurrency: jest.fn((amount) => `$${amount.toFixed(2)}`),
  refetch: jest.fn(),
  refetchActive: jest.fn(),
  refetchCompleted: jest.fn(),
}

jest.mock('@/lib/redux/api/endpoints/restaurant/shopApi', () => ({
  useGetMerchantsQuery: () => mockMerchantApi.useGetMerchantsQuery(),
}))

jest.mock('@/hooks/useOrders', () => ({
  useOrders: jest.fn(() => mockUseOrders),
}))

// Mock components
jest.mock('@/components/ui/app-loading', () => ({
  AppLoading: () => <div data-testid="app-loading">Loading...</div>,
}))

// Mock data
const mockMerchant = {
  id: 'merchant-1',
  slug: 'test-restaurant',
  name: 'Test Restaurant',
  branches: [
    {
      id: 'branch-1',
      slug: 'main-branch',
      name: 'Main Branch',
      address: '123 Test St',
    },
  ],
}

const createMockStore = () => {
  return configureStore({
    reducer: {
      api: () => ({}),
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
      }),
  })
}

const renderWithProvider = (component: React.ReactElement) => {
  const store = createMockStore()
  return render(<Provider store={store}>{component}</Provider>)
}

describe('OrdersPage', () => {
  const mockParams = Promise.resolve({
    slugShop: 'test-restaurant',
    slugBranch: 'main-branch',
  })

  beforeEach(() => {
    jest.clearAllMocks()
    mockMerchantApi.useGetMerchantsQuery.mockReturnValue({
      data: { data: [mockMerchant] },
      isLoading: false,
      error: null,
    })
  })

  describe('Loading States', () => {
    it('shows loading spinner when merchant data is loading', () => {
      mockMerchantApi.useGetMerchantsQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      })

      renderWithProvider(<OrdersPage params={mockParams} />)

      expect(screen.getByTestId('app-loading')).toBeInTheDocument()
    })

    it('shows loading spinner when orders are loading', () => {
      const { useOrders } = require('@/hooks/useOrders')
      useOrders.mockReturnValue({
        ...mockUseOrders,
        isLoading: true,
      })

      renderWithProvider(<OrdersPage params={mockParams} />)

      expect(screen.getByTestId('app-loading')).toBeInTheDocument()
    })
  })

  describe('Error States', () => {
    it('shows restaurant not found when merchant does not exist', async () => {
      mockMerchantApi.useGetMerchantsQuery.mockReturnValue({
        data: { data: [] },
        isLoading: false,
        error: null,
      })

      renderWithProvider(<OrdersPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Restaurant Not Found')).toBeInTheDocument()
        expect(
          screen.getByText('The restaurant or branch you are looking for does not exist.')
        ).toBeInTheDocument()
      })
    })
  })

  describe('Orders Content', () => {
    it('renders orders management title and description', async () => {
      renderWithProvider(<OrdersPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Orders Management')).toBeInTheDocument()
        expect(screen.getByText('Manage orders for your restaurant')).toBeInTheDocument()
      })
    })

    it('renders back to dashboard button', async () => {
      renderWithProvider(<OrdersPage params={mockParams} />)

      await waitFor(() => {
        const backButton = screen.getByText('Back to Dashboard')
        expect(backButton).toBeInTheDocument()
        expect(backButton.closest('a')).toHaveAttribute(
          'href',
          '/app/restaurant/test-restaurant/main-branch/dashboard'
        )
      })
    })

    it('renders refresh button', async () => {
      renderWithProvider(<OrdersPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Refresh')).toBeInTheDocument()
      })
    })

    it('renders search input', async () => {
      renderWithProvider(<OrdersPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByPlaceholderText('Search orders...')).toBeInTheDocument()
      })
    })
  })

  describe('Tabs and Order Display', () => {
    it('displays active and completed tabs with correct counts', async () => {
      renderWithProvider(<OrdersPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Active Orders (2)')).toBeInTheDocument()
        expect(screen.getByText('Completed Orders (1)')).toBeInTheDocument()
      })
    })

    it('displays active orders by default', async () => {
      renderWithProvider(<OrdersPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('#ORD-001')).toBeInTheDocument()
        expect(screen.getByText('#ORD-002')).toBeInTheDocument()
        expect(screen.getByText('John Doe')).toBeInTheDocument()
        expect(screen.getByText('Jane Smith')).toBeInTheDocument()
      })
    })

    it('switches to completed orders when completed tab is clicked', async () => {
      const user = userEvent.setup()
      renderWithProvider(<OrdersPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Completed Orders (1)')).toBeInTheDocument()
      })

      await user.click(screen.getByText('Completed Orders (1)'))

      await waitFor(() => {
        expect(screen.getByText('#ORD-003')).toBeInTheDocument()
        expect(screen.getByText('Bob Wilson')).toBeInTheDocument()
        expect(screen.queryByText('#ORD-001')).not.toBeInTheDocument()
      })
    })

    it('displays order details correctly', async () => {
      renderWithProvider(<OrdersPage params={mockParams} />)

      await waitFor(() => {
        // Check order status
        expect(screen.getByText('preparing')).toBeInTheDocument()
        expect(screen.getByText('pending')).toBeInTheDocument()

        // Check customer info
        expect(screen.getByText('John Doe • +**********')).toBeInTheDocument()
        expect(screen.getByText('Jane Smith • +**********')).toBeInTheDocument()

        // Check totals
        expect(screen.getByText('$25.99')).toBeInTheDocument()
        expect(screen.getByText('$18.50')).toBeInTheDocument()

        // Check order types
        expect(screen.getByText('dine-in')).toBeInTheDocument()
        expect(screen.getByText('takeout')).toBeInTheDocument()

        // Check table info
        expect(screen.getByText('Table 5')).toBeInTheDocument()

        // Check estimated time
        expect(screen.getByText('15 min')).toBeInTheDocument()
      })
    })

    it('displays order items correctly', async () => {
      renderWithProvider(<OrdersPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('1x Burger Deluxe')).toBeInTheDocument()
        expect(screen.getByText('1x French Fries')).toBeInTheDocument()
        expect(screen.getByText('1x Coke')).toBeInTheDocument()
        expect(screen.getByText('2x Caesar Salad')).toBeInTheDocument()
      })
    })

    it('displays view details links', async () => {
      renderWithProvider(<OrdersPage params={mockParams} />)

      await waitFor(() => {
        const viewButtons = screen.getAllByText('View Details')
        expect(viewButtons).toHaveLength(2)

        expect(viewButtons[0].closest('a')).toHaveAttribute(
          'href',
          '/app/restaurant/test-restaurant/main-branch/orders/ORD-001'
        )
        expect(viewButtons[1].closest('a')).toHaveAttribute(
          'href',
          '/app/restaurant/test-restaurant/main-branch/orders/ORD-002'
        )
      })
    })
  })

  describe('Search Functionality', () => {
    it('filters orders by order ID', async () => {
      const user = userEvent.setup()
      renderWithProvider(<OrdersPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('#ORD-001')).toBeInTheDocument()
        expect(screen.getByText('#ORD-002')).toBeInTheDocument()
      })

      const searchInput = screen.getByPlaceholderText('Search orders...')
      await user.type(searchInput, 'ORD-001')

      await waitFor(() => {
        expect(screen.getByText('#ORD-001')).toBeInTheDocument()
        expect(screen.queryByText('#ORD-002')).not.toBeInTheDocument()
      })
    })

    it('filters orders by customer name', async () => {
      const user = userEvent.setup()
      renderWithProvider(<OrdersPage params={mockParams} />)

      const searchInput = screen.getByPlaceholderText('Search orders...')
      await user.type(searchInput, 'jane')

      await waitFor(() => {
        expect(screen.getByText('Jane Smith')).toBeInTheDocument()
        expect(screen.queryByText('John Doe')).not.toBeInTheDocument()
      })
    })

    it('filters orders by item name', async () => {
      const user = userEvent.setup()
      renderWithProvider(<OrdersPage params={mockParams} />)

      const searchInput = screen.getByPlaceholderText('Search orders...')
      await user.type(searchInput, 'burger')

      await waitFor(() => {
        expect(screen.getByText('#ORD-001')).toBeInTheDocument()
        expect(screen.queryByText('#ORD-002')).not.toBeInTheDocument()
      })
    })

    it('shows no results message when search has no matches', async () => {
      const user = userEvent.setup()
      renderWithProvider(<OrdersPage params={mockParams} />)

      const searchInput = screen.getByPlaceholderText('Search orders...')
      await user.type(searchInput, 'nonexistent')

      await waitFor(() => {
        expect(screen.getByText('No active orders match your search')).toBeInTheDocument()
      })
    })

    it('updates tab counts when filtering', async () => {
      const user = userEvent.setup()
      renderWithProvider(<OrdersPage params={mockParams} />)

      const searchInput = screen.getByPlaceholderText('Search orders...')
      await user.type(searchInput, 'ORD-001')

      await waitFor(() => {
        expect(screen.getByText('Active Orders (1)')).toBeInTheDocument()
      })
    })
  })

  describe('User Interactions', () => {
    it('calls refetch functions when refresh button is clicked', async () => {
      const user = userEvent.setup()
      renderWithProvider(<OrdersPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Refresh')).toBeInTheDocument()
      })

      await user.click(screen.getByText('Refresh'))

      expect(mockUseOrders.refetchActive).toHaveBeenCalled()
      expect(mockUseOrders.refetchCompleted).toHaveBeenCalled()
    })
  })

  describe('Empty States', () => {
    it('shows empty state when no active orders exist', async () => {
      const { useOrders } = require('@/hooks/useOrders')
      useOrders.mockReturnValue({
        ...mockUseOrders,
        activeOrders: [],
      })

      renderWithProvider(<OrdersPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('No active orders')).toBeInTheDocument()
      })
    })

    it('shows empty state when no completed orders exist', async () => {
      const user = userEvent.setup()
      const { useOrders } = require('@/hooks/useOrders')
      useOrders.mockReturnValue({
        ...mockUseOrders,
        completedOrders: [],
      })

      renderWithProvider(<OrdersPage params={mockParams} />)

      await user.click(screen.getByText('Completed Orders (0)'))

      await waitFor(() => {
        expect(screen.getByText('No completed orders')).toBeInTheDocument()
      })
    })
  })

  describe('Time Display', () => {
    it('displays time ago correctly', async () => {
      renderWithProvider(<OrdersPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('30m ago')).toBeInTheDocument()
        expect(screen.getByText('10m ago')).toBeInTheDocument()
      })
    })
  })
})
