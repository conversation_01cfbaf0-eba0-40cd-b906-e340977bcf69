import React from 'react'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'

// Simple mock component that mimics the tables page behavior
const MockTablesPage = ({ 
  merchantSlug, 
  branchSlug, 
  isLoading = false, 
  hasError = false,
  merchantData = null,
  tablesData = null,
  reservationsData = null
}: {
  merchantSlug: string
  branchSlug: string
  isLoading?: boolean
  hasError?: boolean
  merchantData?: any
  tablesData?: any
  reservationsData?: any
}) => {
  const [activeTab, setActiveTab] = React.useState<'floor-plan' | 'reservations' | 'waitlist'>('floor-plan')

  if (isLoading) {
    return <div data-testid="app-loading">Loading...</div>
  }

  if (hasError) {
    return (
      <div data-testid="error-state">
        <h1>Error Loading Data</h1>
        <p>There was an error loading the table data. Please try again.</p>
      </div>
    )
  }

  if (!merchantData) {
    return (
      <div data-testid="not-found">
        <h1>Restaurant Not Found</h1>
        <p>The restaurant or branch you are looking for does not exist.</p>
      </div>
    )
  }

  return (
    <div data-testid="tables-page" className="p-6">
      {/* Header */}
      <div className="mb-6">
        <h1 data-testid="page-title">Table Layout</h1>
        <p data-testid="page-description">Manage your restaurant's table layout and reservations.</p>
      </div>

      {/* Tabs */}
      <div className="mb-6">
        <div className="flex border-b">
          <button
            data-testid="floor-plan-tab"
            onClick={() => setActiveTab('floor-plan')}
            className={`px-4 py-2 ${activeTab === 'floor-plan' ? 'border-b-2 border-blue-500' : ''}`}
          >
            Floor Plan
          </button>
          <button
            data-testid="reservations-tab"
            onClick={() => setActiveTab('reservations')}
            className={`px-4 py-2 ${activeTab === 'reservations' ? 'border-b-2 border-blue-500' : ''}`}
          >
            Reservations
          </button>
          <button
            data-testid="waitlist-tab"
            onClick={() => setActiveTab('waitlist')}
            className={`px-4 py-2 ${activeTab === 'waitlist' ? 'border-b-2 border-blue-500' : ''}`}
          >
            Waitlist
          </button>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'floor-plan' && (
        <div data-testid="floor-plan-content">
          <div className="mb-4 flex gap-3">
            <a href={`/app/restaurant/${merchantSlug}/${branchSlug}/tables/layout-editor`}>
              <button data-testid="edit-layout-btn">Edit Layout</button>
            </a>
            <a href={`/app/restaurant/${merchantSlug}/${branchSlug}/reservations/new`}>
              <button data-testid="add-reservation-btn">Add Reservation</button>
            </a>
          </div>
          
          {tablesData?.areas?.map((area: any) => (
            <div key={area.id} data-testid={`area-${area.id}`} className="mb-6">
              <h3 data-testid={`area-name-${area.id}`}>{area.name}</h3>
              <div className="grid grid-cols-4 gap-4">
                {area.tables?.map((table: any) => (
                  <div key={table.id} data-testid={`table-${table.id}`} className="border rounded p-4">
                    <h4 data-testid={`table-name-${table.id}`}>{table.name}</h4>
                    <p data-testid={`table-capacity-${table.id}`}>Seats {table.capacity}</p>
                    <span 
                      data-testid={`table-status-${table.id}`}
                      className={`px-2 py-1 rounded text-xs ${
                        table.status === 'available' ? 'bg-green-100 text-green-800' :
                        table.status === 'occupied' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}
                    >
                      {table.status}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}

      {activeTab === 'reservations' && (
        <div data-testid="reservations-content">
          {reservationsData?.length === 0 ? (
            <div data-testid="no-reservations" className="text-center py-8">
              <p>No reservations found</p>
            </div>
          ) : (
            <div className="space-y-4">
              {reservationsData?.map((reservation: any) => (
                <div key={reservation.id} data-testid={`reservation-${reservation.id}`} className="border rounded p-4">
                  <h4 data-testid={`reservation-customer-${reservation.id}`}>{reservation.customerName}</h4>
                  <p data-testid={`reservation-time-${reservation.id}`}>{reservation.time}</p>
                  <p data-testid={`reservation-table-${reservation.id}`}>{reservation.tableName}</p>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {activeTab === 'waitlist' && (
        <div data-testid="waitlist-content">
          <p>Waitlist management coming soon.</p>
        </div>
      )}
    </div>
  )
}

// Mock data
const mockMerchantData = {
  id: 'merchant-1',
  slug: 'test-restaurant',
  name: 'Test Restaurant',
  branches: [
    {
      id: 'branch-1',
      slug: 'main-branch',
      name: 'Main Branch',
    },
  ],
}

const mockTablesData = {
  areas: [
    {
      id: 'area-1',
      name: 'Dining Area',
      tables: [
        {
          id: 'table-1',
          name: 'Table 1',
          capacity: 4,
          status: 'available',
        },
        {
          id: 'table-2',
          name: 'Table 2',
          capacity: 2,
          status: 'occupied',
        },
      ],
    },
    {
      id: 'area-2',
      name: 'Patio',
      tables: [
        {
          id: 'table-3',
          name: 'Table 3',
          capacity: 6,
          status: 'reserved',
        },
      ],
    },
  ],
}

const mockReservationsData = [
  {
    id: 'res-1',
    customerName: 'John Doe',
    time: '7:00 PM',
    tableName: 'Table 1',
  },
  {
    id: 'res-2',
    customerName: 'Jane Smith',
    time: '8:00 PM',
    tableName: 'Table 2',
  },
]

const createMockStore = () => {
  return configureStore({
    reducer: {
      api: () => ({}),
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
      }),
  })
}

const renderWithProvider = (component: React.ReactElement) => {
  const store = createMockStore()
  return render(<Provider store={store}>{component}</Provider>)
}

describe('TablesPage (Fixed)', () => {
  const defaultProps = {
    merchantSlug: 'test-restaurant',
    branchSlug: 'main-branch',
  }

  describe('Loading States', () => {
    it('shows loading spinner when data is loading', () => {
      renderWithProvider(
        <MockTablesPage {...defaultProps} isLoading={true} />
      )

      expect(screen.getByTestId('app-loading')).toBeInTheDocument()
      expect(screen.getByText('Loading...')).toBeInTheDocument()
    })
  })

  describe('Error States', () => {
    it('shows error message when there is an error', () => {
      renderWithProvider(
        <MockTablesPage {...defaultProps} hasError={true} />
      )

      expect(screen.getByTestId('error-state')).toBeInTheDocument()
      expect(screen.getByText('Error Loading Data')).toBeInTheDocument()
      expect(screen.getByText('There was an error loading the table data. Please try again.')).toBeInTheDocument()
    })

    it('shows restaurant not found when merchant does not exist', () => {
      renderWithProvider(
        <MockTablesPage {...defaultProps} merchantData={null} />
      )

      expect(screen.getByTestId('not-found')).toBeInTheDocument()
      expect(screen.getByText('Restaurant Not Found')).toBeInTheDocument()
      expect(screen.getByText('The restaurant or branch you are looking for does not exist.')).toBeInTheDocument()
    })
  })

  describe('Successful Data Loading', () => {
    it('renders page title and description', () => {
      renderWithProvider(
        <MockTablesPage 
          {...defaultProps} 
          merchantData={mockMerchantData}
          tablesData={mockTablesData}
        />
      )

      expect(screen.getByTestId('page-title')).toHaveTextContent('Table Layout')
      expect(screen.getByTestId('page-description')).toHaveTextContent("Manage your restaurant's table layout and reservations.")
    })

    it('renders all tabs', () => {
      renderWithProvider(
        <MockTablesPage 
          {...defaultProps} 
          merchantData={mockMerchantData}
          tablesData={mockTablesData}
        />
      )

      expect(screen.getByTestId('floor-plan-tab')).toBeInTheDocument()
      expect(screen.getByTestId('reservations-tab')).toBeInTheDocument()
      expect(screen.getByTestId('waitlist-tab')).toBeInTheDocument()
    })

    it('shows floor plan content by default', () => {
      renderWithProvider(
        <MockTablesPage 
          {...defaultProps} 
          merchantData={mockMerchantData}
          tablesData={mockTablesData}
        />
      )

      expect(screen.getByTestId('floor-plan-content')).toBeInTheDocument()
      expect(screen.getByTestId('edit-layout-btn')).toBeInTheDocument()
      expect(screen.getByTestId('add-reservation-btn')).toBeInTheDocument()
    })

    it('renders tables grouped by areas', () => {
      renderWithProvider(
        <MockTablesPage 
          {...defaultProps} 
          merchantData={mockMerchantData}
          tablesData={mockTablesData}
        />
      )

      // Check areas
      expect(screen.getByTestId('area-area-1')).toBeInTheDocument()
      expect(screen.getByTestId('area-area-2')).toBeInTheDocument()
      expect(screen.getByTestId('area-name-area-1')).toHaveTextContent('Dining Area')
      expect(screen.getByTestId('area-name-area-2')).toHaveTextContent('Patio')

      // Check tables
      expect(screen.getByTestId('table-table-1')).toBeInTheDocument()
      expect(screen.getByTestId('table-table-2')).toBeInTheDocument()
      expect(screen.getByTestId('table-table-3')).toBeInTheDocument()
      expect(screen.getByTestId('table-name-table-1')).toHaveTextContent('Table 1')
      expect(screen.getByTestId('table-capacity-table-1')).toHaveTextContent('Seats 4')
      expect(screen.getByTestId('table-status-table-1')).toHaveTextContent('available')
    })
  })

  describe('Tab Navigation', () => {
    it('switches to reservations tab when clicked', async () => {
      const user = userEvent.setup()
      renderWithProvider(
        <MockTablesPage 
          {...defaultProps} 
          merchantData={mockMerchantData}
          tablesData={mockTablesData}
          reservationsData={mockReservationsData}
        />
      )

      // Initially floor plan should be active
      expect(screen.getByTestId('floor-plan-content')).toBeInTheDocument()

      // Click reservations tab
      await user.click(screen.getByTestId('reservations-tab'))

      // Should show reservations content
      expect(screen.getByTestId('reservations-content')).toBeInTheDocument()
      expect(screen.getByTestId('reservation-res-1')).toBeInTheDocument()
      expect(screen.getByTestId('reservation-customer-res-1')).toHaveTextContent('John Doe')
      expect(screen.getByTestId('reservation-time-res-1')).toHaveTextContent('7:00 PM')
    })

    it('switches to waitlist tab when clicked', async () => {
      const user = userEvent.setup()
      renderWithProvider(
        <MockTablesPage 
          {...defaultProps} 
          merchantData={mockMerchantData}
          tablesData={mockTablesData}
        />
      )

      await user.click(screen.getByTestId('waitlist-tab'))

      expect(screen.getByTestId('waitlist-content')).toBeInTheDocument()
      expect(screen.getByText('Waitlist management coming soon.')).toBeInTheDocument()
    })

    it('highlights active tab correctly', async () => {
      const user = userEvent.setup()
      renderWithProvider(
        <MockTablesPage 
          {...defaultProps} 
          merchantData={mockMerchantData}
          tablesData={mockTablesData}
        />
      )

      // Floor plan should be active by default
      expect(screen.getByTestId('floor-plan-tab')).toHaveClass('border-b-2', 'border-blue-500')
      expect(screen.getByTestId('reservations-tab')).not.toHaveClass('border-b-2', 'border-blue-500')

      // Switch to reservations
      await user.click(screen.getByTestId('reservations-tab'))

      expect(screen.getByTestId('reservations-tab')).toHaveClass('border-b-2', 'border-blue-500')
      expect(screen.getByTestId('floor-plan-tab')).not.toHaveClass('border-b-2', 'border-blue-500')
    })
  })

  describe('Empty States', () => {
    it('shows empty state when no reservations exist', async () => {
      const user = userEvent.setup()
      renderWithProvider(
        <MockTablesPage 
          {...defaultProps} 
          merchantData={mockMerchantData}
          tablesData={mockTablesData}
          reservationsData={[]}
        />
      )

      await user.click(screen.getByTestId('reservations-tab'))

      expect(screen.getByTestId('no-reservations')).toBeInTheDocument()
      expect(screen.getByText('No reservations found')).toBeInTheDocument()
    })
  })

  describe('Action Links', () => {
    it('renders correct links for actions', () => {
      renderWithProvider(
        <MockTablesPage 
          {...defaultProps} 
          merchantData={mockMerchantData}
          tablesData={mockTablesData}
        />
      )

      const editLayoutBtn = screen.getByTestId('edit-layout-btn')
      const addReservationBtn = screen.getByTestId('add-reservation-btn')

      expect(editLayoutBtn.closest('a')).toHaveAttribute(
        'href',
        '/app/restaurant/test-restaurant/main-branch/tables/layout-editor'
      )
      expect(addReservationBtn.closest('a')).toHaveAttribute(
        'href',
        '/app/restaurant/test-restaurant/main-branch/reservations/new'
      )
    })
  })
})
