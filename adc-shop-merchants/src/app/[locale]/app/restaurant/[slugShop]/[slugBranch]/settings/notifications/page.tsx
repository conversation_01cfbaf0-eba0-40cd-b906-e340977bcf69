'use client';

import React, { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useGetShopBySlugQuery, useGetBranchBySlugQuery, useGetBranchSettingsQuery, useUpdateBranchSettingsMutation } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import type { Shop } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import type { Branch } from '@/lib/types/shop';
import { AppLoading } from '@/components/ui/app-loading';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel
} from '@/components/ui/form';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ArrowLeft,
  Bell,
  Mail,
  MessageSquare,
  Calendar,
  ShoppingCart,
  Star,
  Users,
  AlertCircle,
  Save
} from 'lucide-react';
import { toast } from 'sonner';

// Define the notification settings schema
const notificationSettingsSchema = z.object({
  // In-app notifications
  inAppEnabled: z.boolean().default(true),
  inAppOrders: z.boolean().default(true),
  inAppReservations: z.boolean().default(true),
  inAppReviews: z.boolean().default(true),
  inAppStaff: z.boolean().default(true),
  inAppSystem: z.boolean().default(true),

  // Email notifications
  emailEnabled: z.boolean().default(true),
  emailOrders: z.boolean().default(true),
  emailReservations: z.boolean().default(true),
  emailReviews: z.boolean().default(false),
  emailStaff: z.boolean().default(true),
  emailSystem: z.boolean().default(true),
  emailDigest: z.boolean().default(true),

  // Push notifications (mobile)
  pushEnabled: z.boolean().default(true),
  pushOrders: z.boolean().default(true),
  pushReservations: z.boolean().default(true),
  pushReviews: z.boolean().default(false),
  pushStaff: z.boolean().default(false),
  pushSystem: z.boolean().default(true),
});

type NotificationSettings = z.infer<typeof notificationSettingsSchema>;

interface NotificationsSettingsPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function NotificationsSettingsPage({ params }: NotificationsSettingsPageProps) {
  const { slugShop, slugBranch } = React.use(params);
  const [activeTab, setActiveTab] = useState<'in-app' | 'email' | 'push'>('in-app');

  // Get shop and branch data from backend using slug-based queries
  const { data: shopData, isLoading: isShopLoading, error: shopError } = useGetShopBySlugQuery(slugShop) as {
    data: Shop | undefined;
    isLoading: boolean;
    error: unknown;
  };
  const { data: branchData, isLoading: isBranchLoading, error: branchError } = useGetBranchBySlugQuery({
    shopSlug: slugShop,
    branchSlug: slugBranch
  }) as {
    data: Branch | undefined;
    isLoading: boolean;
    error: unknown;
  };

  // Get branch settings
  const {
    data: branchSettings,
    isLoading: isLoadingSettings,
  } = useGetBranchSettingsQuery(
    { shopId: shopData?.id || '', branchId: branchData?.id || '' },
    { skip: !shopData?.id || !branchData?.id }
  );

  // Update branch settings mutation
  const [updateBranchSettings, { isLoading: isUpdating }] = useUpdateBranchSettingsMutation();

  // Initialize form with default values
  const form = useForm<NotificationSettings>({
    resolver: zodResolver(notificationSettingsSchema),
    defaultValues: {
      inAppEnabled: true,
      inAppOrders: true,
      inAppReservations: true,
      inAppReviews: true,
      inAppStaff: true,
      inAppSystem: true,
      emailEnabled: true,
      emailOrders: true,
      emailReservations: true,
      emailReviews: false,
      emailStaff: true,
      emailSystem: true,
      emailDigest: true,
      pushEnabled: true,
      pushOrders: true,
      pushReservations: true,
      pushReviews: false,
      pushStaff: false,
      pushSystem: true,
    },
  });

  const isLoading = isShopLoading || isBranchLoading || isLoadingSettings;
  const hasError = shopError || branchError;

  // Watch for changes to enabled toggles
  const inAppEnabled = form.watch('inAppEnabled');
  const emailEnabled = form.watch('emailEnabled');
  const pushEnabled = form.watch('pushEnabled');

  // Load notification settings from branch settings
  useEffect(() => {
    if (branchSettings?.notifications) {
      const notifications = branchSettings.notifications;

      // Map branch notification settings to form values
      form.reset({
        // In-app notifications
        inAppEnabled: notifications.inAppEnabled ?? true,
        inAppOrders: notifications.inAppOrders ?? true,
        inAppReservations: notifications.inAppReservations ?? true,
        inAppReviews: notifications.inAppReviews ?? true,
        inAppStaff: notifications.inAppStaff ?? true,
        inAppSystem: notifications.inAppSystem ?? true,

        // Email notifications
        emailEnabled: notifications.emailEnabled ?? true,
        emailOrders: notifications.emailOrders ?? true,
        emailReservations: notifications.emailReservations ?? true,
        emailReviews: notifications.emailReviews ?? false,
        emailStaff: notifications.emailStaff ?? true,
        emailSystem: notifications.emailSystem ?? true,
        emailDigest: notifications.emailDigest ?? true,

        // Push notifications
        pushEnabled: notifications.pushEnabled ?? true,
        pushOrders: notifications.pushOrders ?? true,
        pushReservations: notifications.pushReservations ?? true,
        pushReviews: notifications.pushReviews ?? false,
        pushStaff: notifications.pushStaff ?? false,
        pushSystem: notifications.pushSystem ?? true,
      });
    }
  }, [branchSettings, form]);

  // Handle form submission
  const onSubmit = async (data: NotificationSettings) => {
    if (!shopData?.id || !branchData?.id) {
      toast.error('Missing shop or branch information');
      return;
    }

    try {
      // Prepare notification settings update
      const notificationSettings = {
        // In-app notifications
        inAppEnabled: data.inAppEnabled,
        inAppOrders: data.inAppOrders,
        inAppReservations: data.inAppReservations,
        inAppReviews: data.inAppReviews,
        inAppStaff: data.inAppStaff,
        inAppSystem: data.inAppSystem,

        // Email notifications
        emailEnabled: data.emailEnabled,
        emailOrders: data.emailOrders,
        emailReservations: data.emailReservations,
        emailReviews: data.emailReviews,
        emailStaff: data.emailStaff,
        emailSystem: data.emailSystem,
        emailDigest: data.emailDigest,

        // Push notifications
        pushEnabled: data.pushEnabled,
        pushOrders: data.pushOrders,
        pushReservations: data.pushReservations,
        pushReviews: data.pushReviews,
        pushStaff: data.pushStaff,
        pushSystem: data.pushSystem,
      };

      // Update branch settings using real API
      await updateBranchSettings({
        shopId: shopData.id,
        branchId: branchData.id,
        settings: {
          notifications: notificationSettings,
        },
      }).unwrap();

      toast.success('Notification settings saved', {
        description: 'Your notification preferences have been updated.',
      });
    } catch (error) {
      toast.error('Failed to save settings', {
        description: 'There was an error saving your notification preferences.',
      });
      console.error('Error saving notification settings:', error);
    }
  };

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (hasError || !shopData || !branchData) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Restaurant Not Found</h1>
          <p className="text-[#8a745c] text-sm">The restaurant or branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/settings`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Settings
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Notification Settings</h1>
          <p className="text-[#8a745c] text-sm">Manage how you receive notifications for {shopData.name} - {branchData.name}</p>
        </div>
        <Button
          type="submit"
          form="notification-form"
          disabled={isUpdating}
          className="bg-[#e58219] hover:bg-[#d67917] text-white font-bold px-6 h-12"
        >
          <Save className="w-4 h-4 mr-2" />
          {isUpdating ? 'Saving...' : 'Save Settings'}
        </Button>
      </div>

    <div className="max-w-4xl">

      <Form {...form}>
        <form id="notification-form" onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Tabs
            value={activeTab}
            onValueChange={(value) => setActiveTab(value as 'in-app' | 'email' | 'push')}
            className="w-full"
          >
            <TabsList className="grid grid-cols-3 w-full max-w-md mb-6">
              <TabsTrigger value="in-app" className="flex items-center gap-2">
                <Bell className="h-4 w-4" />
                <span>In-App</span>
              </TabsTrigger>
              <TabsTrigger value="email" className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                <span>Email</span>
              </TabsTrigger>
              <TabsTrigger value="push" className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4" />
                <span>Push</span>
              </TabsTrigger>
            </TabsList>

            {/* In-App Notifications Tab */}
            <TabsContent value="in-app" className="space-y-6">
              <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Bell className="h-5 w-5 text-[#8a745c]" />
                      <CardTitle>In-App Notifications</CardTitle>
                    </div>
                    <FormField
                      control={form.control}
                      name="inAppEnabled"
                      render={({ field }) => (
                        <FormItem className="flex items-center space-x-2 space-y-0">
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                  <CardDescription>
                    Configure notifications that appear within the application
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="inAppOrders"
                    render={({ field }) => (
                      <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                        <div className="space-y-0.5">
                          <div className="flex items-center gap-2">
                            <ShoppingCart className="h-4 w-4 text-blue-500" />
                            <FormLabel className="font-medium text-[#181510]">Order Notifications</FormLabel>
                          </div>
                          <FormDescription className="text-[#8a745c]">
                            Receive notifications about new orders and order status changes
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={!inAppEnabled}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="inAppReservations"
                    render={({ field }) => (
                      <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                        <div className="space-y-0.5">
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-green-500" />
                            <FormLabel className="font-medium text-[#181510]">Reservation Notifications</FormLabel>
                          </div>
                          <FormDescription className="text-[#8a745c]">
                            Receive notifications about new, updated, or canceled reservations
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={!inAppEnabled}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="inAppReviews"
                    render={({ field }) => (
                      <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                        <div className="space-y-0.5">
                          <div className="flex items-center gap-2">
                            <Star className="h-4 w-4 text-yellow-500" />
                            <FormLabel className="font-medium text-[#181510]">Review Notifications</FormLabel>
                          </div>
                          <FormDescription className="text-[#8a745c]">
                            Receive notifications when customers leave reviews
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={!inAppEnabled}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="inAppStaff"
                    render={({ field }) => (
                      <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                        <div className="space-y-0.5">
                          <div className="flex items-center gap-2">
                            <Users className="h-4 w-4 text-purple-500" />
                            <FormLabel className="font-medium text-[#181510]">Staff Notifications</FormLabel>
                          </div>
                          <FormDescription className="text-[#8a745c]">
                            Receive notifications about staff schedule changes and requests
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={!inAppEnabled}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="inAppSystem"
                    render={({ field }) => (
                      <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                        <div className="space-y-0.5">
                          <div className="flex items-center gap-2">
                            <AlertCircle className="h-4 w-4 text-red-500" />
                            <FormLabel className="font-medium text-[#181510]">System Notifications</FormLabel>
                          </div>
                          <FormDescription className="text-[#8a745c]">
                            Receive important system alerts and updates
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={!inAppEnabled}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            {/* Email Notifications Tab */}
            <TabsContent value="email" className="space-y-6">
              <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Mail className="h-5 w-5 text-[#8a745c]" />
                      <CardTitle>Email Notifications</CardTitle>
                    </div>
                    <FormField
                      control={form.control}
                      name="emailEnabled"
                      render={({ field }) => (
                        <FormItem className="flex items-center space-x-2 space-y-0">
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                  <CardDescription>
                    Configure notifications sent to your email address
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="emailOrders"
                    render={({ field }) => (
                      <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                        <div className="space-y-0.5">
                          <div className="flex items-center gap-2">
                            <ShoppingCart className="h-4 w-4 text-blue-500" />
                            <FormLabel className="font-medium text-[#181510]">Order Emails</FormLabel>
                          </div>
                          <FormDescription className="text-[#8a745c]">
                            Receive email notifications about new orders and order status changes
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={!emailEnabled}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="emailReservations"
                    render={({ field }) => (
                      <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                        <div className="space-y-0.5">
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-green-500" />
                            <FormLabel className="font-medium text-[#181510]">Reservation Emails</FormLabel>
                          </div>
                          <FormDescription className="text-[#8a745c]">
                            Receive email notifications about new, updated, or canceled reservations
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={!emailEnabled}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="emailReviews"
                    render={({ field }) => (
                      <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                        <div className="space-y-0.5">
                          <div className="flex items-center gap-2">
                            <Star className="h-4 w-4 text-yellow-500" />
                            <FormLabel className="font-medium text-[#181510]">Review Emails</FormLabel>
                          </div>
                          <FormDescription className="text-[#8a745c]">
                            Receive email notifications when customers leave reviews
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={!emailEnabled}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="emailStaff"
                    render={({ field }) => (
                      <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                        <div className="space-y-0.5">
                          <div className="flex items-center gap-2">
                            <Users className="h-4 w-4 text-purple-500" />
                            <FormLabel className="font-medium text-[#181510]">Staff Emails</FormLabel>
                          </div>
                          <FormDescription className="text-[#8a745c]">
                            Receive email notifications about staff schedule changes and requests
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={!emailEnabled}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="emailSystem"
                    render={({ field }) => (
                      <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                        <div className="space-y-0.5">
                          <div className="flex items-center gap-2">
                            <AlertCircle className="h-4 w-4 text-red-500" />
                            <FormLabel className="font-medium text-[#181510]">System Emails</FormLabel>
                          </div>
                          <FormDescription className="text-[#8a745c]">
                            Receive important system alerts and updates via email
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={!emailEnabled}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="emailDigest"
                    render={({ field }) => (
                      <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                        <div className="space-y-0.5">
                          <div className="flex items-center gap-2">
                            <Mail className="h-4 w-4 text-[#8a745c]" />
                            <FormLabel className="font-medium text-[#181510]">Daily Digest</FormLabel>
                          </div>
                          <FormDescription className="text-[#8a745c]">
                            Receive a daily summary of all activity in your restaurant
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={!emailEnabled}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            {/* Push Notifications Tab */}
            <TabsContent value="push" className="space-y-6">
              <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <MessageSquare className="h-5 w-5 text-[#8a745c]" />
                      <CardTitle>Push Notifications</CardTitle>
                    </div>
                    <FormField
                      control={form.control}
                      name="pushEnabled"
                      render={({ field }) => (
                        <FormItem className="flex items-center space-x-2 space-y-0">
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                  <CardDescription>
                    Configure notifications sent to your mobile device
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="pushOrders"
                    render={({ field }) => (
                      <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                        <div className="space-y-0.5">
                          <div className="flex items-center gap-2">
                            <ShoppingCart className="h-4 w-4 text-blue-500" />
                            <FormLabel className="font-medium text-[#181510]">Order Notifications</FormLabel>
                          </div>
                          <FormDescription className="text-[#8a745c]">
                            Receive push notifications about new orders and order status changes
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={!pushEnabled}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="pushReservations"
                    render={({ field }) => (
                      <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                        <div className="space-y-0.5">
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-green-500" />
                            <FormLabel className="font-medium text-[#181510]">Reservation Notifications</FormLabel>
                          </div>
                          <FormDescription className="text-[#8a745c]">
                            Receive push notifications about new, updated, or canceled reservations
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={!pushEnabled}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="pushReviews"
                    render={({ field }) => (
                      <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                        <div className="space-y-0.5">
                          <div className="flex items-center gap-2">
                            <Star className="h-4 w-4 text-yellow-500" />
                            <FormLabel className="font-medium text-[#181510]">Review Notifications</FormLabel>
                          </div>
                          <FormDescription className="text-[#8a745c]">
                            Receive push notifications when customers leave reviews
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={!pushEnabled}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="pushStaff"
                    render={({ field }) => (
                      <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                        <div className="space-y-0.5">
                          <div className="flex items-center gap-2">
                            <Users className="h-4 w-4 text-purple-500" />
                            <FormLabel className="font-medium text-[#181510]">Staff Notifications</FormLabel>
                          </div>
                          <FormDescription className="text-[#8a745c]">
                            Receive push notifications about staff schedule changes and requests
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={!pushEnabled}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="pushSystem"
                    render={({ field }) => (
                      <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                        <div className="space-y-0.5">
                          <div className="flex items-center gap-2">
                            <AlertCircle className="h-4 w-4 text-red-500" />
                            <FormLabel className="font-medium text-[#181510]">System Notifications</FormLabel>
                          </div>
                          <FormDescription className="text-[#8a745c]">
                            Receive important system alerts and updates on your mobile device
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={!pushEnabled}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>


        </form>
      </Form>
      </div>
    </div>
  );
}
