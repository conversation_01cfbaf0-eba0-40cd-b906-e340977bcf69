import React from 'react'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import StaffPage from '../page'

// Mock @/i18n/navigation
jest.mock('@/i18n/navigation', () => ({
  Link: ({ children, href, ...props }: any) => {
    return React.createElement('a', { href, ...props }, children)
  },
}))

// Mock the APIs and hooks
const mockMerchantApi = {
  useGetMerchantsQuery: jest.fn(),
}

const mockUseStaff = {
  staff: [
    {
      id: 'staff-1',
      slug: 'john-doe',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      position: 'Manager',
      roleName: 'Manager',
      status: 'active',
      schedule: [
        { dayOfWeek: 'Monday', startTime: '09:00', endTime: '17:00' },
        { dayOfWeek: 'Tuesday', startTime: '09:00', endTime: '17:00' },
      ],
    },
    {
      id: 'staff-2',
      slug: 'jane-smith',
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      position: 'Server',
      roleName: 'Server',
      status: 'inactive',
      schedule: [],
    },
    {
      id: 'staff-3',
      slug: 'bob-wilson',
      firstName: 'Bob',
      lastName: 'Wilson',
      email: '<EMAIL>',
      position: 'Chef',
      roleName: 'Chef',
      status: 'active',
      schedule: [
        { dayOfWeek: 'Wednesday', startTime: '10:00', endTime: '18:00' },
      ],
    },
  ],
  roles: [
    {
      id: 'role-1',
      name: 'Manager',
      description: 'Restaurant manager with full access',
      permissions: ['manage_staff', 'manage_orders', 'view_reports'],
    },
    {
      id: 'role-2',
      name: 'Server',
      description: 'Front-of-house server',
      permissions: ['take_orders', 'view_tables'],
    },
    {
      id: 'role-3',
      name: 'Chef',
      description: 'Kitchen chef',
      permissions: ['manage_menu', 'view_orders'],
    },
  ],
  permissions: [
    {
      id: 'perm-1',
      name: 'manage_staff',
      description: 'Can manage staff members',
      category: 'Staff',
      isActive: true,
    },
    {
      id: 'perm-2',
      name: 'manage_orders',
      description: 'Can manage customer orders',
      category: 'Orders',
      isActive: true,
    },
    {
      id: 'perm-3',
      name: 'view_reports',
      description: 'Can view analytics reports',
      category: 'Reports',
      isActive: false,
    },
  ],
  isLoading: false,
  isError: false,
  error: null,
}

jest.mock('@/lib/redux/api/endpoints/restaurant/shopApi', () => ({
  useGetMerchantsQuery: () => mockMerchantApi.useGetMerchantsQuery(),
}))

jest.mock('@/hooks/useStaff', () => ({
  useStaff: jest.fn(() => mockUseStaff),
}))

// Mock components
jest.mock('@/components/ui/app-loading', () => ({
  AppLoading: () => <div data-testid="app-loading">Loading...</div>,
}))

// Mock data
const mockMerchant = {
  id: 'merchant-1',
  slug: 'test-restaurant',
  name: 'Test Restaurant',
  branches: [
    {
      id: 'branch-1',
      slug: 'main-branch',
      name: 'Main Branch',
      address: '123 Test St',
    },
  ],
}

const createMockStore = () => {
  return configureStore({
    reducer: {
      api: () => ({}),
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
      }),
  })
}

const renderWithProvider = (component: React.ReactElement) => {
  const store = createMockStore()
  return render(<Provider store={store}>{component}</Provider>)
}

describe('StaffPage', () => {
  const mockParams = Promise.resolve({
    slugShop: 'test-restaurant',
    slugBranch: 'main-branch',
  })

  beforeEach(() => {
    jest.clearAllMocks()
    mockMerchantApi.useGetMerchantsQuery.mockReturnValue({
      data: { data: [mockMerchant] },
      isLoading: false,
      error: null,
    })
  })

  describe('Loading States', () => {
    it('shows loading spinner when merchant data is loading', () => {
      mockMerchantApi.useGetMerchantsQuery.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
      })

      renderWithProvider(<StaffPage params={mockParams} />)

      expect(screen.getByTestId('app-loading')).toBeInTheDocument()
    })

    it('shows loading spinner when staff data is loading', () => {
      const { useStaff } = require('@/hooks/useStaff')
      useStaff.mockReturnValue({
        ...mockUseStaff,
        isLoading: true,
      })

      renderWithProvider(<StaffPage params={mockParams} />)

      expect(screen.getByTestId('app-loading')).toBeInTheDocument()
    })
  })

  describe('Error States', () => {
    it('shows branch not found when merchant does not exist', async () => {
      mockMerchantApi.useGetMerchantsQuery.mockReturnValue({
        data: { data: [] },
        isLoading: false,
        error: null,
      })

      renderWithProvider(<StaffPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Branch Not Found')).toBeInTheDocument()
        expect(screen.getByText('The branch you are looking for does not exist.')).toBeInTheDocument()
      })
    })

    it('shows error message when staff data fails to load', async () => {
      const { useStaff } = require('@/hooks/useStaff')
      useStaff.mockReturnValue({
        ...mockUseStaff,
        isError: true,
        error: { data: { message: 'Failed to load staff data' } },
      })

      renderWithProvider(<StaffPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Error Loading Staff')).toBeInTheDocument()
        expect(screen.getByText('Failed to load staff data')).toBeInTheDocument()
      })
    })
  })

  describe('Staff Content', () => {
    it('renders staff management title and description', async () => {
      renderWithProvider(<StaffPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Staff Management')).toBeInTheDocument()
        expect(screen.getByText('Manage staff for Test Restaurant - Main Branch')).toBeInTheDocument()
      })
    })

    it('renders back to dashboard button', async () => {
      renderWithProvider(<StaffPage params={mockParams} />)

      await waitFor(() => {
        const backButton = screen.getByText('Back to Dashboard')
        expect(backButton).toBeInTheDocument()
        expect(backButton.closest('a')).toHaveAttribute(
          'href',
          '/app/restaurant/test-restaurant/main-branch/dashboard'
        )
      })
    })

    it('renders add staff member button', async () => {
      renderWithProvider(<StaffPage params={mockParams} />)

      await waitFor(() => {
        const addButton = screen.getByText('Add Staff Member')
        expect(addButton).toBeInTheDocument()
        expect(addButton.closest('a')).toHaveAttribute(
          'href',
          '/app/restaurant/test-restaurant/main-branch/staff/add'
        )
      })
    })

    it('renders all tabs', async () => {
      renderWithProvider(<StaffPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Staff List')).toBeInTheDocument()
        expect(screen.getByText('Schedules')).toBeInTheDocument()
        expect(screen.getByText('Roles')).toBeInTheDocument()
        expect(screen.getByText('Permissions')).toBeInTheDocument()
      })
    })
  })

  describe('Staff List Tab', () => {
    it('displays staff members correctly', async () => {
      renderWithProvider(<StaffPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
        expect(screen.getByText('Jane Smith')).toBeInTheDocument()
        expect(screen.getByText('Bob Wilson')).toBeInTheDocument()
        expect(screen.getByText('Manager')).toBeInTheDocument()
        expect(screen.getByText('Server')).toBeInTheDocument()
        expect(screen.getByText('Chef')).toBeInTheDocument()
      })
    })

    it('displays staff status badges correctly', async () => {
      renderWithProvider(<StaffPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Active')).toBeInTheDocument()
        expect(screen.getByText('Inactive')).toBeInTheDocument()
      })
    })

    it('displays schedule information correctly', async () => {
      renderWithProvider(<StaffPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('Monday: 09:00-17:00, Tuesday: 09:00-17:00')).toBeInTheDocument()
        expect(screen.getByText('No schedule set')).toBeInTheDocument()
        expect(screen.getByText('Wednesday: 10:00-18:00')).toBeInTheDocument()
      })
    })

    it('displays view and edit buttons for each staff member', async () => {
      renderWithProvider(<StaffPage params={mockParams} />)

      await waitFor(() => {
        const viewButtons = screen.getAllByText('View')
        const editButtons = screen.getAllByText('Edit')

        expect(viewButtons).toHaveLength(3)
        expect(editButtons).toHaveLength(3)

        // Check first staff member links
        expect(viewButtons[0].closest('a')).toHaveAttribute(
          'href',
          '/app/restaurant/test-restaurant/main-branch/staff/john-doe'
        )
        expect(editButtons[0].closest('a')).toHaveAttribute(
          'href',
          '/app/restaurant/test-restaurant/main-branch/staff/john-doe/edit'
        )
      })
    })
  })

  describe('Roles Tab', () => {
    it('switches to roles tab and displays roles', async () => {
      const user = userEvent.setup()
      renderWithProvider(<StaffPage params={mockParams} />)

      await user.click(screen.getByText('Roles'))

      await waitFor(() => {
        expect(screen.getByText('Manager')).toBeInTheDocument()
        expect(screen.getByText('Server')).toBeInTheDocument()
        expect(screen.getByText('Chef')).toBeInTheDocument()
        expect(screen.getByText('Restaurant manager with full access')).toBeInTheDocument()
        expect(screen.getByText('Front-of-house server')).toBeInTheDocument()
        expect(screen.getByText('Kitchen chef')).toBeInTheDocument()
      })
    })

    it('displays permission counts for roles', async () => {
      const user = userEvent.setup()
      renderWithProvider(<StaffPage params={mockParams} />)

      await user.click(screen.getByText('Roles'))

      await waitFor(() => {
        expect(screen.getByText('3 permissions')).toBeInTheDocument()
        expect(screen.getByText('2 permissions')).toBeInTheDocument()
      })
    })
  })

  describe('Permissions Tab', () => {
    it('switches to permissions tab and displays permissions', async () => {
      const user = userEvent.setup()
      renderWithProvider(<StaffPage params={mockParams} />)

      await user.click(screen.getByText('Permissions'))

      await waitFor(() => {
        expect(screen.getByText('manage_staff')).toBeInTheDocument()
        expect(screen.getByText('manage_orders')).toBeInTheDocument()
        expect(screen.getByText('view_reports')).toBeInTheDocument()
        expect(screen.getByText('Can manage staff members')).toBeInTheDocument()
        expect(screen.getByText('Can manage customer orders')).toBeInTheDocument()
        expect(screen.getByText('Can view analytics reports')).toBeInTheDocument()
      })
    })

    it('displays permission status correctly', async () => {
      const user = userEvent.setup()
      renderWithProvider(<StaffPage params={mockParams} />)

      await user.click(screen.getByText('Permissions'))

      await waitFor(() => {
        const activeStatuses = screen.getAllByText('Active')
        const inactiveStatuses = screen.getAllByText('Inactive')

        expect(activeStatuses).toHaveLength(2) // 2 active permissions
        expect(inactiveStatuses).toHaveLength(1) // 1 inactive permission
      })
    })
  })

  describe('Schedules Tab', () => {
    it('switches to schedules tab and shows coming soon message', async () => {
      const user = userEvent.setup()
      renderWithProvider(<StaffPage params={mockParams} />)

      await user.click(screen.getByText('Schedules'))

      await waitFor(() => {
        expect(screen.getByText('Staff Schedules')).toBeInTheDocument()
        expect(screen.getByText('Schedule management features coming soon.')).toBeInTheDocument()
      })
    })
  })

  describe('Search Functionality', () => {
    it('filters staff by name', async () => {
      const user = userEvent.setup()
      renderWithProvider(<StaffPage params={mockParams} />)

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
        expect(screen.getByText('Jane Smith')).toBeInTheDocument()
      })

      const searchInput = screen.getByPlaceholderText('Search staff...')
      await user.type(searchInput, 'john')

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
        expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument()
        expect(screen.queryByText('Bob Wilson')).not.toBeInTheDocument()
      })
    })

    it('filters staff by position', async () => {
      const user = userEvent.setup()
      renderWithProvider(<StaffPage params={mockParams} />)

      const searchInput = screen.getByPlaceholderText('Search staff...')
      await user.type(searchInput, 'chef')

      await waitFor(() => {
        expect(screen.getByText('Bob Wilson')).toBeInTheDocument()
        expect(screen.queryByText('John Doe')).not.toBeInTheDocument()
        expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument()
      })
    })

    it('changes search placeholder based on active tab', async () => {
      const user = userEvent.setup()
      renderWithProvider(<StaffPage params={mockParams} />)

      // Default staff list tab
      expect(screen.getByPlaceholderText('Search staff...')).toBeInTheDocument()

      // Switch to roles tab
      await user.click(screen.getByText('Roles'))
      expect(screen.getByPlaceholderText('Search roles...')).toBeInTheDocument()

      // Switch to permissions tab
      await user.click(screen.getByText('Permissions'))
      expect(screen.getByPlaceholderText('Search permissions...')).toBeInTheDocument()
    })

    it('filters roles by name', async () => {
      const user = userEvent.setup()
      renderWithProvider(<StaffPage params={mockParams} />)

      await user.click(screen.getByText('Roles'))

      const searchInput = screen.getByPlaceholderText('Search roles...')
      await user.type(searchInput, 'manager')

      await waitFor(() => {
        expect(screen.getByText('Manager')).toBeInTheDocument()
        expect(screen.queryByText('Server')).not.toBeInTheDocument()
        expect(screen.queryByText('Chef')).not.toBeInTheDocument()
      })
    })

    it('filters permissions by name', async () => {
      const user = userEvent.setup()
      renderWithProvider(<StaffPage params={mockParams} />)

      await user.click(screen.getByText('Permissions'))

      const searchInput = screen.getByPlaceholderText('Search permissions...')
      await user.type(searchInput, 'manage_staff')

      await waitFor(() => {
        expect(screen.getByText('manage_staff')).toBeInTheDocument()
        expect(screen.queryByText('manage_orders')).not.toBeInTheDocument()
        expect(screen.queryByText('view_reports')).not.toBeInTheDocument()
      })
    })
  })
})
