'use client';

import React from 'react';
import { Link } from '@/i18n/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { TableImage } from '@/components/ui/image-with-fallback';
import { Table, TableArea } from '@/lib/redux/api/endpoints/restaurant/tablesApi';
import { getTableImageByNumber } from '@/data/sample-table-images';

interface FloorPlanTabProps {
  tables: Table[];
  areas: TableArea[];
  slugShop: string;
  slugBranch: string;
}

export function FloorPlanTab({ tables, areas, slugShop, slugBranch }: FloorPlanTabProps) {
  // Group tables by area
  const tablesByArea: Record<string, Table[]> = areas.reduce((acc: Record<string, Table[]>, area: TableArea) => {
    // Match tables to areas by area ID or name
    acc[area.name] = tables.filter((table: Table) =>
      table.area?.id === area.id || table.area?.name === area.name
    );
    return acc;
  }, {});

  // If no areas, create a default area with all tables
  if (areas.length === 0 && tables.length > 0) {
    tablesByArea['Main Dining Area'] = tables;
  }

  return (
    <div>
      {Object.entries(tablesByArea).map(([areaName, areaTables]) => (
        areaTables.length > 0 && (
          <div key={areaName} className="mb-8">
            <h2 className="text-foreground text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">
              {areaName}
            </h2>
            <div className="grid grid-cols-[repeat(auto-fit,minmax(200px,1fr))] gap-4 p-4">
              {areaTables.map((table: Table) => (
                <Link key={table.id} href={`/app/restaurant/${slugShop}/${slugBranch}/tables/${table.id}`}>
                  <Card className="cursor-pointer hover:shadow-lg transition-shadow overflow-hidden h-[200px] relative">
                    {/* Table Image with Fallback */}
                    <TableImage
                      src={table.image_url || getTableImageByNumber(parseInt(table.number) || 1)}
                      alt={`${table.name} - Table ${table.number}`}
                      className="w-full h-full object-cover"
                      containerClassName="absolute inset-0"
                      onError={() => console.log('Image failed to load:', table.image_url || getTableImageByNumber(parseInt(table.number) || 1))}
                    />

                    {/* Overlay for better text readability */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>

                    <CardContent className="relative z-10 p-4 h-full flex flex-col justify-between">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="text-primary-foreground text-lg font-bold drop-shadow-lg">{table.name}</h3>
                          <p className="text-primary-foreground/90 text-sm drop-shadow">Table {table.number}</p>
                        </div>
                        <Badge
                          variant={
                            table.status === 'available' ? 'default' :
                            table.status === 'occupied' ? 'destructive' :
                            table.status === 'reserved' ? 'secondary' :
                            'outline'
                          }
                          className="text-xs bg-background/90 text-foreground border-0"
                        >
                          {table.status}
                        </Badge>
                      </div>

                      <div className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <span className="text-white/90 drop-shadow">Capacity:</span>
                          <span className="text-white font-medium drop-shadow">{table.capacity} people</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-white/90 drop-shadow">Shape:</span>
                          <span className="text-white font-medium capitalize drop-shadow">{table.shape}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          </div>
        )
      ))}

      {/* Show message when no real data */}
      {tables.length === 0 && (
        <div className="text-center py-12">
          <p className="text-muted-foreground text-lg mb-4">No tables found</p>
          <p className="text-muted-foreground text-sm">Add tables to get started with your restaurant layout.</p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end gap-3 px-4 py-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/tables/layout-editor`}>
          <Button variant="outline">
            Edit Layout
          </Button>
        </Link>
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/reservations/new`}>
          <Button variant="secondary">
            Add Reservation
          </Button>
        </Link>
      </div>
    </div>
  );
}
