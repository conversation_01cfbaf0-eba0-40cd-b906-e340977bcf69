'use client';

import React, { useState } from 'react';
import { Link } from '@/i18n/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Plus, ArrowLeft, ChevronUp, ChevronDown, RefreshCw } from 'lucide-react';
import { AppLoading } from '@/components/ui/app-loading';
import { useStaff } from '@/hooks/useStaff';

interface StaffPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function StaffPage({ params }: StaffPageProps) {
  const { slugShop, slugBranch } = React.use(params);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('staff-list');

  // Use staff hook for data fetching with backend-driven filtering
  const {
    staff: staffMembers,
    roles,
    permissions,
    pagination,
    staffStats,
    filters,
    updateFilters,
    isLoading,
    isError,
    error,
    refetch,
  } = useStaff({
    shopSlug: slugShop,
    branchSlug: slugBranch,
  });

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (isError) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/dashboard`}>
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-foreground text-[32px] font-bold leading-tight mb-2">Error Loading Staff</h1>
          <p className="text-muted-foreground text-sm">
            {error && 'data' in error && typeof error.data === 'object' && error.data && 'message' in error.data
              ? (error.data as { message: string }).message
              : 'Failed to load staff data'}
          </p>
        </div>
      </div>
    );
  }

  // Handle search input changes with backend filtering
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    updateFilters({
      search: value || undefined,
      page: 1 // Reset to first page when searching
    });
  };

  // Handle sorting
  const handleSort = (sortBy: 'first_name' | 'last_name' | 'position' | 'department' | 'status' | 'hire_date' | 'created_at') => {
    const currentSortOrder = filters.sort_by === sortBy && filters.sort_order === 'asc' ? 'desc' : 'asc';
    updateFilters({
      sort_by: sortBy,
      sort_order: currentSortOrder,
      page: 1 // Reset to first page when sorting
    });
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    updateFilters({ page });
  };

  // Get sort icon for table headers
  const getSortIcon = (column: string) => {
    if (filters.sort_by !== column) return null;
    return filters.sort_order === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />;
  };

  // Filter data for different tabs (roles and permissions still use frontend filtering for now)
  const filteredPermissions = (permissions || []).filter(permission => {
    return (
      permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (permission.category || '').toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  // Group permissions by category for better organization
  const groupedPermissions = filteredPermissions.reduce((acc, permission) => {
    const category = permission.category || 'Other';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(permission);
    return acc;
  }, {} as Record<string, typeof filteredPermissions>);

  const filteredRoles = (roles || []).filter(role => {
    return (
      role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      role.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      role.permissions.join(', ').toLowerCase().includes(searchTerm.toLowerCase())
    );
  });



  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/dashboard`}>
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-foreground text-[32px] font-bold leading-tight">Staff Management</h1>
          <p className="text-muted-foreground text-sm font-normal leading-normal">
            Manage staff members, roles, and permissions
          </p>
        </div>
        <div className="flex items-end">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff/add`}>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Staff Member
            </Button>
          </Link>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <div className="flex justify-between items-center">
          <TabsList className="p-1">
            <TabsTrigger value="staff-list">Staff List</TabsTrigger>
            <TabsTrigger value="schedules">Schedules</TabsTrigger>
            <TabsTrigger value="roles">Roles</TabsTrigger>
            <TabsTrigger value="permissions">Permissions</TabsTrigger>
          </TabsList>

          <div className="flex-1 max-w-sm ml-4">
            <Input
              placeholder={
                activeTab === 'permissions'
                  ? "Search permissions..."
                  : activeTab === 'roles'
                    ? "Search roles..."
                    : activeTab === 'staff-list'
                      ? "Search staff..."
                      : "Search..."
              }
              value={searchTerm}
              onChange={(e) => {
                if (activeTab === 'staff-list') {
                  handleSearchChange(e.target.value);
                } else {
                  setSearchTerm(e.target.value);
                }
              }}
            />
          </div>
        </div>

        <TabsContent value="staff-list" className="space-y-4">
          {/* Stats Summary */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-card p-4 rounded-lg border border-border">
              <div className="text-2xl font-bold text-foreground">{staffStats?.totalStaff || 0}</div>
              <div className="text-sm text-muted-foreground">Total Staff</div>
            </div>
            <div className="bg-card p-4 rounded-lg border border-border">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">{staffStats?.activeStaff || 0}</div>
              <div className="text-sm text-muted-foreground">Active</div>
            </div>
            <div className="bg-card p-4 rounded-lg border border-border">
              <div className="text-2xl font-bold text-red-600 dark:text-red-400">{staffStats?.inactiveStaff || 0}</div>
              <div className="text-sm text-muted-foreground">Inactive</div>
            </div>
            <div className="bg-card p-4 rounded-lg border border-border">
              <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{staffStats?.suspendedStaff || 0}</div>
              <div className="text-sm text-muted-foreground">Suspended</div>
            </div>
          </div>

          <div className="px-4 py-3 @container">
            <div className="flex overflow-hidden rounded-lg border border-border bg-card">
              <table className="flex-1">
                <thead>
                  <tr className="bg-card">
                    <th className="table-staff-120 px-4 py-3 text-left text-foreground w-[400px] text-sm font-medium leading-normal">
                      <button
                        onClick={() => handleSort('first_name')}
                        className="flex items-center gap-1 hover:text-muted-foreground transition-colors"
                      >
                        Name
                        {getSortIcon('first_name')}
                      </button>
                    </th>
                    <th className="table-staff-240 px-4 py-3 text-left text-foreground w-[400px] text-sm font-medium leading-normal">
                      <button
                        onClick={() => handleSort('position')}
                        className="flex items-center gap-1 hover:text-muted-foreground transition-colors"
                      >
                        Position
                        {getSortIcon('position')}
                      </button>
                    </th>
                    <th className="table-staff-360 px-4 py-3 text-left text-foreground w-60 text-sm font-medium leading-normal">
                      <button
                        onClick={() => handleSort('status')}
                        className="flex items-center gap-1 hover:text-muted-foreground transition-colors"
                      >
                        Status
                        {getSortIcon('status')}
                      </button>
                    </th>
                    <th className="table-staff-480 px-4 py-3 text-left text-foreground w-[400px] text-sm font-medium leading-normal">
                      Schedule
                    </th>
                    <th className="table-staff-600 px-4 py-3 text-left text-foreground w-[400px] text-sm font-medium leading-normal">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {staffMembers.map((staff) => (
                    <tr key={staff.id} className="border-t border-border">
                      <td className="table-staff-120 px-4 py-3 text-foreground text-sm font-medium leading-normal">
                        {staff.firstName} {staff.lastName}
                      </td>
                      <td className="table-staff-240 px-4 py-3 text-muted-foreground text-sm font-normal leading-normal">
                        {staff.position}
                      </td>
                      <td className="table-staff-360 px-4 py-3">
                        <Badge
                          className={`${
                            staff.status === 'active'
                              ? 'bg-green-50 text-green-700 hover:bg-green-50 dark:bg-green-950 dark:text-green-300'
                              : staff.status === 'inactive'
                              ? 'bg-red-50 text-red-700 hover:bg-red-50 dark:bg-red-950 dark:text-red-300'
                              : 'bg-yellow-50 text-yellow-700 hover:bg-yellow-50 dark:bg-yellow-950 dark:text-yellow-300'
                          }`}
                        >
                          {staff.status.charAt(0).toUpperCase() + staff.status.slice(1)}
                        </Badge>
                      </td>
                      <td className="table-staff-480 px-4 py-3 text-muted-foreground text-sm font-normal leading-normal">
                        {staff.schedule && staff.schedule.length > 0
                          ? staff.schedule.map(s => `${s.dayOfWeek}: ${s.startTime}-${s.endTime}`).join(', ')
                          : 'No schedule set'
                        }
                      </td>
                      <td className="table-staff-600 px-4 py-3">
                        <div className="flex space-x-2">
                          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff/${staff.slug}`}>
                            <Button variant="outline" size="sm" className="h-8">
                              View
                            </Button>
                          </Link>
                          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff/${staff.slug}/edit`}>
                            <Button variant="outline" size="sm" className="h-8">
                              Edit
                            </Button>
                          </Link>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination and Stats */}
            <div className="flex justify-between items-center mt-4">
              <div className="text-sm text-muted-foreground">
                Showing {((pagination?.currentPage || 1) - 1) * (pagination?.itemsPerPage || 20) + 1} to{' '}
                {Math.min((pagination?.currentPage || 1) * (pagination?.itemsPerPage || 20), pagination?.totalItems || 0)} of{' '}
                {pagination?.totalItems || 0} staff members
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange((pagination?.currentPage || 1) - 1)}
                  disabled={!pagination?.hasPreviousPage}
                >
                  Previous
                </Button>

                <span className="text-sm text-muted-foreground">
                  Page {pagination?.currentPage || 1} of {pagination?.totalPages || 1}
                </span>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange((pagination?.currentPage || 1) + 1)}
                  disabled={!pagination?.hasNextPage}
                >
                  Next
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => refetch()}
                  className="ml-2"
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="schedules" className="space-y-4">
          <div className="bg-card p-6 rounded-lg border border-border">
            <h2 className="text-lg font-medium text-foreground mb-4">Staff Schedules</h2>
            <p className="text-muted-foreground">Schedule management features coming soon.</p>
          </div>
        </TabsContent>

        <TabsContent value="roles" className="space-y-4">
          <div className="px-4 py-3 @container">
            <div className="flex overflow-hidden rounded-lg border border-border bg-card">
              <table className="flex-1">
                <thead>
                  <tr className="bg-card">
                    <th className="table-roles-120 px-4 py-3 text-left text-foreground w-[400px] text-sm font-medium leading-normal">
                      Role
                    </th>
                    <th className="table-roles-240 px-4 py-3 text-left text-foreground w-[400px] text-sm font-medium leading-normal">
                      Description
                    </th>
                    <th className="table-roles-360 px-4 py-3 text-left text-foreground w-60 text-sm font-medium leading-normal">
                      Permissions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {filteredRoles.map((role) => (
                    <tr key={role.id} className="border-t border-border">
                      <td className="table-roles-120 h-[72px] px-4 py-2 w-[400px] text-foreground text-sm font-normal leading-normal">
                        {role.name}
                      </td>
                      <td className="table-roles-240 h-[72px] px-4 py-2 w-[400px] text-muted-foreground text-sm font-normal leading-normal">
                        {role.description}
                      </td>
                      <td className="table-roles-360 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-muted text-foreground text-sm font-medium leading-normal w-full transition-colors hover:bg-muted/80"
                        >
                          <span className="truncate">
                            {role.permissions.length > 0 ? `${role.permissions.length} permissions` : 'No permissions'}
                          </span>
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <style>
              {`
                @container(max-width:120px){.table-roles-120{display: none;}}
                @container(max-width:240px){.table-roles-240{display: none;}}
                @container(max-width:360px){.table-roles-360{display: none;}}
              `}
            </style>
          </div>
        </TabsContent>

        <TabsContent value="permissions" className="space-y-4">
          <div className="px-4 py-3 @container">
            <div className="flex overflow-hidden rounded-lg border border-border bg-card">
              <table className="flex-1">
                <thead>
                  <tr className="bg-card">
                    <th className="table-permissions-120 px-4 py-3 text-left text-foreground w-[400px] text-sm font-medium leading-normal">
                      Permission
                    </th>
                    <th className="table-permissions-240 px-4 py-3 text-left text-foreground w-[400px] text-sm font-medium leading-normal">
                      Description
                    </th>
                    <th className="table-permissions-360 px-4 py-3 text-left text-foreground w-60 text-sm font-medium leading-normal">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {Object.entries(groupedPermissions).map(([category, categoryPermissions]) => (
                    <React.Fragment key={category}>
                      {/* Category Header */}
                      <tr className="bg-muted/50 border-t border-border">
                        <td colSpan={3} className="h-[48px] px-4 py-2 text-foreground text-sm font-semibold leading-normal">
                          {category} ({categoryPermissions.length})
                        </td>
                      </tr>
                      {/* Category Permissions */}
                      {categoryPermissions.map((permission, index) => (
                        <tr key={permission.id || permission.name || `${category}-${index}`} className="border-t border-border">
                          <td className="table-permissions-120 h-[72px] px-4 py-2 w-[400px] text-foreground text-sm font-normal leading-normal pl-8">
                            {permission.name}
                          </td>
                          <td className="table-permissions-240 h-[72px] px-4 py-2 w-[400px] text-muted-foreground text-sm font-normal leading-normal">
                            {permission.description}
                          </td>
                          <td className="table-permissions-360 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                            <div
                              className={`flex min-w-[84px] max-w-[480px] items-center justify-center overflow-hidden rounded-full h-8 px-4 text-sm font-medium leading-normal w-full ${
                                permission.isActive !== false
                                  ? 'bg-green-50 text-green-700 dark:bg-green-950 dark:text-green-300'
                                  : 'bg-muted text-muted-foreground'
                              }`}
                            >
                              <span className="truncate">
                                {permission.isActive !== false ? 'Active' : 'Inactive'}
                              </span>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </React.Fragment>
                  ))}
                </tbody>
              </table>
            </div>
            <style>
              {`
                @container(max-width:120px){.table-permissions-120{display: none;}}
                @container(max-width:240px){.table-permissions-240{display: none;}}
                @container(max-width:360px){.table-permissions-360{display: none;}}
              `}
            </style>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
