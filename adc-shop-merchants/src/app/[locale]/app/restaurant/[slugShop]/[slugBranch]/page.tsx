'use client';

import { use } from 'react';
import { Link } from '@/i18n/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Users, Utensils, Calendar, Star, BarChart3, DollarSign, Settings, TrendingUp, TrendingDown } from 'lucide-react';
import { AppLoading } from '@/components/ui/app-loading';
import { useGetShopBySlugQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { useDashboardStats } from '@/hooks/useDashboardStats';
import { usePerformanceMonitor } from '@/hooks/usePerformanceMonitor';
import React from 'react';

interface DashboardPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function DashboardPage({ params }: DashboardPageProps) {
  const { slugShop, slugBranch } = use(params);

  // Get shop data from backend by slug
  const { data: shop, isLoading: isLoadingShop } = useGetShopBySlugQuery(slugShop);

  // Monitor performance of optimized API call
  usePerformanceMonitor('GetShopBySlug', isLoadingShop, shop);

  const {
    dashboardStats,
    isLoading,
    refetch
  } = useDashboardStats(slugShop, slugBranch);

  if (isLoadingShop || isLoading) {
    return <AppLoading />;
  }

  if (!shop) {
    return (
      <div className="font-be-vietnam">
        <div className="text-center py-12">
          <h1 className="text-foreground text-[32px] font-bold leading-tight mb-2">Restaurant Not Found</h1>
          <p className="text-muted-foreground text-sm">The restaurant you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  // Helper function to format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Helper function to format growth percentage
  const formatGrowth = (growth: number) => {
    const isPositive = growth >= 0;
    return (
      <span className={`text-xs mt-1 flex items-center ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
        {isPositive ? <TrendingUp className="h-3 w-3 mr-1" /> : <TrendingDown className="h-3 w-3 mr-1" />}
        {isPositive ? '+' : ''}{growth.toFixed(1)}% from yesterday
      </span>
    );
  };

  // Stats card data
  const statsCards = [
    {
      title: "Today's Orders",
      value: dashboardStats?.todayOrders || 0,
      icon: DollarSign,
      growth: dashboardStats?.ordersGrowth,
      formatter: (value: number) => value.toString()
    },
    {
      title: "Today's Revenue",
      value: dashboardStats?.todayRevenue || 0,
      icon: BarChart3,
      growth: dashboardStats?.revenueGrowth,
      formatter: formatCurrency
    },
    {
      title: "Reservations",
      value: dashboardStats?.todayReservations || 0,
      icon: Calendar,
      growth: dashboardStats?.reservationsGrowth,
      formatter: (value: number) => value.toString()
    },
    {
      title: "Active Staff",
      value: dashboardStats?.activeStaff || 0,
      icon: Users,
      subtitle: "Currently on duty",
      formatter: (value: number) => value.toString()
    }
  ];

  // Navigation card data
  const navigationCards = [
    {
      title: "Tables",
      description: "Manage tables, layout, and QR codes",
      icon: Utensils,
      href: `/app/restaurant/${slugShop}/${slugBranch}/tables`
    },
    {
      title: "Menu",
      description: "Manage menu items, categories, and pricing",
      icon: Utensils,
      href: `/app/restaurant/${slugShop}/${slugBranch}/menu`
    },
    {
      title: "Orders",
      description: "Manage orders, payments, and deliveries",
      icon: DollarSign,
      href: `/app/restaurant/${slugShop}/${slugBranch}/orders`
    },
    {
      title: "Reservations",
      description: "Manage bookings, waitlist, and availability",
      icon: Calendar,
      href: `/app/restaurant/${slugShop}/${slugBranch}/reservations`
    },
    {
      title: "Staff",
      description: "Manage employees, roles, and schedules",
      icon: Users,
      href: `/app/restaurant/${slugShop}/${slugBranch}/staff`
    },
    {
      title: "Reviews",
      description: "Manage customer feedback and ratings",
      icon: Star,
      href: `/app/restaurant/${slugShop}/${slugBranch}/reviews`
    },
    {
      title: "Reports",
      description: "View analytics and performance reports",
      icon: BarChart3,
      href: `/app/restaurant/${slugShop}/${slugBranch}/reports`
    },
    {
      title: "Settings",
      description: "Configure branch settings and preferences",
      icon: Settings,
      href: `/app/restaurant/${slugShop}/${slugBranch}/settings`
    }
  ];

  return (
    <div className="font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}`}>
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Restaurant
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-foreground text-[32px] font-bold leading-tight">
            Restaurant Dashboard
          </h1>
          <p className="text-muted-foreground text-sm font-normal leading-normal">
            Real-time insights and management tools for your restaurant
          </p>
        </div>
        <div className="flex items-center">
          <Button
            variant="outline"
            onClick={refetch}
          >
            Refresh Data
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        {statsCards.map((stat, index) => {
          const IconComponent = stat.icon;
          return (
            <Card key={index}>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">{stat.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <IconComponent className="h-8 w-8 text-primary mr-2" />
                  <div className="text-2xl font-bold text-foreground">
                    {stat.formatter(stat.value)}
                  </div>
                </div>
                {stat.growth !== undefined && formatGrowth(stat.growth)}
                {stat.subtitle && (
                  <p className="text-xs text-muted-foreground mt-1">{stat.subtitle}</p>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {navigationCards.map((nav, index) => {
          const IconComponent = nav.icon;
          return (
            <Link key={index} href={nav.href} className="block">
              <Card className="hover:shadow-md transition-shadow h-full">
                <CardContent className="p-6 flex flex-col items-center text-center">
                  <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mb-4">
                    <IconComponent className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-foreground mb-2">{nav.title}</CardTitle>
                  <p className="text-muted-foreground text-sm">{nav.description}</p>
                </CardContent>
              </Card>
            </Link>
          );
        })}
      </div>
    </div>
  );
}
