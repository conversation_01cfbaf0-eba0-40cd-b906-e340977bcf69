'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { 
  ArrowLeft,
  Zap,
  Plus,
  Settings,
  ExternalLink,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Key,
  Webhook,
  Database,
  CreditCard,
  Truck,
  MessageSquare,
  Mail,
  BarChart3,
  Calendar,
  Users,
  ShoppingCart,
  Smartphone
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Ta<PERSON>,
  Ta<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from '@/components/ui/tabs';

// Types
interface Integration {
  id: string;
  name: string;
  description: string;
  category: 'payment' | 'delivery' | 'pos' | 'marketing' | 'analytics' | 'communication' | 'accounting';
  icon: React.ReactNode;
  status: 'connected' | 'disconnected' | 'error' | 'pending';
  isPopular: boolean;
  isPremium: boolean;
  website: string;
  features: string[];
  connectedAt?: Date;
  lastSync?: Date;
  config?: Record<string, any>;
}

interface APIKey {
  id: string;
  name: string;
  key: string;
  permissions: string[];
  createdAt: Date;
  lastUsed?: Date;
  isActive: boolean;
}

export default function IntegrationsSettingsPage() {
  const t = useTranslations('settings');
  const params = useParams();
  const { slugShop, slugBranch } = params;

  // State
  const [integrations, setIntegrations] = useState<Integration[]>([]);
  const [apiKeys, setApiKeys] = useState<APIKey[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [isApiKeyDialogOpen, setIsApiKeyDialogOpen] = useState(false);
  const [newApiKey, setNewApiKey] = useState({
    name: '',
    permissions: [] as string[],
  });

  // Available categories
  const categories = [
    { id: 'all', name: 'All Integrations', icon: <Zap className="w-4 h-4" /> },
    { id: 'payment', name: 'Payment', icon: <CreditCard className="w-4 h-4" /> },
    { id: 'delivery', name: 'Delivery', icon: <Truck className="w-4 h-4" /> },
    { id: 'pos', name: 'POS Systems', icon: <Smartphone className="w-4 h-4" /> },
    { id: 'marketing', name: 'Marketing', icon: <MessageSquare className="w-4 h-4" /> },
    { id: 'analytics', name: 'Analytics', icon: <BarChart3 className="w-4 h-4" /> },
    { id: 'communication', name: 'Communication', icon: <Mail className="w-4 h-4" /> },
    { id: 'accounting', name: 'Accounting', icon: <Database className="w-4 h-4" /> },
  ];

  // Mock data
  const mockIntegrations: Integration[] = [
    {
      id: 'stripe',
      name: 'Stripe',
      description: 'Accept online payments with Stripe\'s secure payment processing',
      category: 'payment',
      icon: <CreditCard className="w-6 h-6" />,
      status: 'connected',
      isPopular: true,
      isPremium: false,
      website: 'https://stripe.com',
      features: ['Credit Cards', 'Digital Wallets', 'Recurring Payments', 'Fraud Protection'],
      connectedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      lastSync: new Date(Date.now() - 2 * 60 * 60 * 1000),
    },
    {
      id: 'ubereats',
      name: 'Uber Eats',
      description: 'Reach more customers with Uber Eats delivery platform',
      category: 'delivery',
      icon: <Truck className="w-6 h-6" />,
      status: 'connected',
      isPopular: true,
      isPremium: false,
      website: 'https://ubereats.com',
      features: ['Order Management', 'Menu Sync', 'Real-time Tracking', 'Analytics'],
      connectedAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
      lastSync: new Date(Date.now() - 30 * 60 * 1000),
    },
    {
      id: 'doordash',
      name: 'DoorDash',
      description: 'Expand your delivery reach with DoorDash',
      category: 'delivery',
      icon: <Truck className="w-6 h-6" />,
      status: 'disconnected',
      isPopular: true,
      isPremium: false,
      website: 'https://doordash.com',
      features: ['Order Management', 'Menu Sync', 'Delivery Tracking', 'Customer Reviews'],
    },
    {
      id: 'mailchimp',
      name: 'Mailchimp',
      description: 'Email marketing and customer engagement platform',
      category: 'marketing',
      icon: <Mail className="w-6 h-6" />,
      status: 'connected',
      isPopular: true,
      isPremium: false,
      website: 'https://mailchimp.com',
      features: ['Email Campaigns', 'Customer Segmentation', 'Automation', 'Analytics'],
      connectedAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000),
      lastSync: new Date(Date.now() - 4 * 60 * 60 * 1000),
    },
    {
      id: 'square',
      name: 'Square POS',
      description: 'Complete point-of-sale system for in-person payments',
      category: 'pos',
      icon: <Smartphone className="w-6 h-6" />,
      status: 'error',
      isPopular: true,
      isPremium: false,
      website: 'https://squareup.com',
      features: ['POS Terminal', 'Inventory Management', 'Staff Management', 'Reports'],
      connectedAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
    },
    {
      id: 'quickbooks',
      name: 'QuickBooks',
      description: 'Accounting and financial management software',
      category: 'accounting',
      icon: <Database className="w-6 h-6" />,
      status: 'disconnected',
      isPopular: true,
      isPremium: true,
      website: 'https://quickbooks.intuit.com',
      features: ['Expense Tracking', 'Invoicing', 'Tax Preparation', 'Financial Reports'],
    },
    {
      id: 'google-analytics',
      name: 'Google Analytics',
      description: 'Web analytics and customer insights',
      category: 'analytics',
      icon: <BarChart3 className="w-6 h-6" />,
      status: 'connected',
      isPopular: true,
      isPremium: false,
      website: 'https://analytics.google.com',
      features: ['Website Analytics', 'Customer Behavior', 'Conversion Tracking', 'Custom Reports'],
      connectedAt: new Date(Date.now() - 120 * 24 * 60 * 60 * 1000),
      lastSync: new Date(Date.now() - 1 * 60 * 60 * 1000),
    },
    {
      id: 'twilio',
      name: 'Twilio',
      description: 'SMS and voice communication platform',
      category: 'communication',
      icon: <MessageSquare className="w-6 h-6" />,
      status: 'pending',
      isPopular: false,
      isPremium: true,
      website: 'https://twilio.com',
      features: ['SMS Notifications', 'Voice Calls', 'WhatsApp', 'Verification'],
    },
  ];

  const mockApiKeys: APIKey[] = [
    {
      id: '1',
      name: 'Mobile App API',
      key: 'sk_live_51H...xyz123',
      permissions: ['orders.read', 'orders.write', 'menu.read'],
      createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      lastUsed: new Date(Date.now() - 2 * 60 * 60 * 1000),
      isActive: true,
    },
    {
      id: '2',
      name: 'Analytics Integration',
      key: 'sk_test_4e...abc789',
      permissions: ['analytics.read', 'reports.read'],
      createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
      lastUsed: new Date(Date.now() - 24 * 60 * 60 * 1000),
      isActive: true,
    },
    {
      id: '3',
      name: 'Legacy System',
      key: 'sk_live_3d...def456',
      permissions: ['orders.read'],
      createdAt: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000),
      lastUsed: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      isActive: false,
    },
  ];

  // Load data
  useEffect(() => {
    const timer = setTimeout(() => {
      setIntegrations(mockIntegrations);
      setApiKeys(mockApiKeys);
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Filter integrations
  const filteredIntegrations = integrations.filter(integration => 
    selectedCategory === 'all' || integration.category === selectedCategory
  );

  // Get status badge
  const getStatusBadge = (status: Integration['status']) => {
    const statusConfig = {
      connected: { color: 'bg-green-100 text-green-800', icon: CheckCircle, label: 'Connected' },
      disconnected: { color: 'bg-gray-100 text-gray-800', icon: XCircle, label: 'Disconnected' },
      error: { color: 'bg-red-100 text-red-800', icon: AlertTriangle, label: 'Error' },
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: AlertTriangle, label: 'Pending' },
    };

    const config = statusConfig[status];
    const Icon = config.icon;

    return (
      <Badge className={cn('flex items-center gap-1', config.color)}>
        <Icon className="w-3 h-3" />
        {config.label}
      </Badge>
    );
  };

  // Format date
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Handle connect integration
  const handleConnect = async (integrationId: string) => {
    try {
      // TODO: Implement integration connection logic
      console.log('Connecting integration:', integrationId);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert('Integration connected successfully!');
    } catch (error) {
      console.error('Error connecting integration:', error);
      alert('Failed to connect integration. Please try again.');
    }
  };

  // Handle disconnect integration
  const handleDisconnect = async (integrationId: string) => {
    try {
      // TODO: Implement integration disconnection logic
      console.log('Disconnecting integration:', integrationId);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert('Integration disconnected successfully!');
    } catch (error) {
      console.error('Error disconnecting integration:', error);
      alert('Failed to disconnect integration. Please try again.');
    }
  };

  // Handle create API key
  const handleCreateApiKey = async () => {
    try {
      // TODO: Implement API key creation
      console.log('Creating API key:', newApiKey);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Reset form and close dialog
      setNewApiKey({ name: '', permissions: [] });
      setIsApiKeyDialogOpen(false);
      
      alert('API key created successfully!');
    } catch (error) {
      console.error('Error creating API key:', error);
      alert('Failed to create API key. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/settings`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Settings
            </Button>
          </Link>
          <div className="flex items-center gap-3">
            <Zap className="w-8 h-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Integrations</h1>
              <p className="text-gray-600">Connect third-party services and manage API access</p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="integrations" className="space-y-6">
        <TabsList>
          <TabsTrigger value="integrations">Integrations</TabsTrigger>
          <TabsTrigger value="api-keys">API Keys</TabsTrigger>
          <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
        </TabsList>

        {/* Integrations Tab */}
        <TabsContent value="integrations" className="space-y-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Integrations</CardTitle>
                <Zap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{integrations.length}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Connected</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {integrations.filter(i => i.status === 'connected').length}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Available</CardTitle>
                <Plus className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {integrations.filter(i => i.status === 'disconnected').length}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Errors</CardTitle>
                <AlertTriangle className="h-4 w-4 text-red-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">
                  {integrations.filter(i => i.status === 'error').length}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
                className="flex items-center gap-2"
              >
                {category.icon}
                {category.name}
              </Button>
            ))}
          </div>

          {/* Integrations Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredIntegrations.map((integration) => (
              <Card key={integration.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-gray-100 rounded-lg">
                        {integration.icon}
                      </div>
                      <div>
                        <h3 className="font-semibold flex items-center gap-2">
                          {integration.name}
                          {integration.isPopular && (
                            <Badge variant="secondary" className="text-xs">Popular</Badge>
                          )}
                          {integration.isPremium && (
                            <Badge variant="outline" className="text-xs">Premium</Badge>
                          )}
                        </h3>
                        <p className="text-sm text-gray-600">{integration.description}</p>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Status */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Status</span>
                    {getStatusBadge(integration.status)}
                  </div>

                  {/* Features */}
                  <div>
                    <h4 className="text-sm font-medium mb-2">Features</h4>
                    <div className="flex flex-wrap gap-1">
                      {integration.features.slice(0, 3).map((feature, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                      {integration.features.length > 3 && (
                        <Badge variant="secondary" className="text-xs">
                          +{integration.features.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>

                  {/* Connection Info */}
                  {integration.status === 'connected' && integration.connectedAt && (
                    <div className="text-xs text-gray-500">
                      Connected on {formatDate(integration.connectedAt)}
                      {integration.lastSync && (
                        <div>Last sync: {formatDate(integration.lastSync)}</div>
                      )}
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex gap-2">
                    {integration.status === 'connected' ? (
                      <>
                        <Button variant="outline" size="sm" className="flex-1">
                          <Settings className="w-4 h-4 mr-2" />
                          Configure
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleDisconnect(integration.id)}
                        >
                          Disconnect
                        </Button>
                      </>
                    ) : (
                      <>
                        <Button 
                          size="sm" 
                          className="flex-1"
                          onClick={() => handleConnect(integration.id)}
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          Connect
                        </Button>
                        <Button variant="outline" size="sm" asChild>
                          <a href={integration.website} target="_blank" rel="noopener noreferrer">
                            <ExternalLink className="w-4 h-4" />
                          </a>
                        </Button>
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* API Keys Tab */}
        <TabsContent value="api-keys" className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold">API Keys</h2>
              <p className="text-sm text-gray-600">Manage API keys for external integrations</p>
            </div>
            
            <Dialog open={isApiKeyDialogOpen} onOpenChange={setIsApiKeyDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Create API Key
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New API Key</DialogTitle>
                  <DialogDescription>
                    Generate a new API key for external integrations
                  </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="keyName">Key Name</Label>
                    <Input
                      id="keyName"
                      value={newApiKey.name}
                      onChange={(e) => setNewApiKey({ ...newApiKey, name: e.target.value })}
                      placeholder="Mobile App API"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Permissions</Label>
                    <div className="grid grid-cols-2 gap-2">
                      {['orders.read', 'orders.write', 'menu.read', 'menu.write', 'analytics.read', 'reports.read'].map((permission) => (
                        <div key={permission} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id={permission}
                            checked={newApiKey.permissions.includes(permission)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setNewApiKey({
                                  ...newApiKey,
                                  permissions: [...newApiKey.permissions, permission]
                                });
                              } else {
                                setNewApiKey({
                                  ...newApiKey,
                                  permissions: newApiKey.permissions.filter(p => p !== permission)
                                });
                              }
                            }}
                          />
                          <Label htmlFor={permission} className="text-sm">
                            {permission}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsApiKeyDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateApiKey}>
                    Create Key
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          <Card>
            <CardContent className="p-0">
              <div className="space-y-4 p-6">
                {apiKeys.map((apiKey) => (
                  <div key={apiKey.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <Key className="w-5 h-5 text-blue-600" />
                      </div>
                      <div>
                        <h4 className="font-medium">{apiKey.name}</h4>
                        <p className="text-sm text-gray-600 font-mono">{apiKey.key}</p>
                        <div className="flex items-center gap-4 mt-1">
                          <span className="text-xs text-gray-500">
                            Created {formatDate(apiKey.createdAt)}
                          </span>
                          {apiKey.lastUsed && (
                            <span className="text-xs text-gray-500">
                              Last used {formatDate(apiKey.lastUsed)}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Badge variant={apiKey.isActive ? 'default' : 'secondary'}>
                        {apiKey.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                      <Button variant="outline" size="sm">
                        <Settings className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Webhooks Tab */}
        <TabsContent value="webhooks" className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold">Webhooks</h2>
              <p className="text-sm text-gray-600">Configure webhook endpoints for real-time notifications</p>
            </div>
            
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add Webhook
            </Button>
          </div>

          <Card>
            <CardContent className="p-6">
              <div className="text-center py-12">
                <Webhook className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No webhooks configured</h3>
                <p className="text-gray-600 mb-4">
                  Set up webhooks to receive real-time notifications about orders, payments, and other events.
                </p>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Create First Webhook
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
