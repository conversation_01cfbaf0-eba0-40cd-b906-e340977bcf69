'use client';

import React, { useState } from 'react';
import { Link } from '@/i18n/navigation';
import { useRouter } from '@/i18n/navigation';
import { Trash2, Edit, CheckCircle, UserX, Mail } from 'lucide-react';
import { AppLoading } from '@/components/ui/app-loading';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useGetBranchWithShopQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { useSendConfirmationMutation } from '@/lib/redux/api/endpoints/restaurant/reservationsApi';
import { useReservation } from '@/hooks/useReservations';
import { toast } from 'sonner';

interface ReservationDetailsPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
    slug: string;
  }>;
}

export default function ReservationDetailsPage({ params }: ReservationDetailsPageProps) {
  const { slugShop, slugBranch, slug } = React.use(params);
  const router = useRouter();

  // Get branch data directly by slug (includes shop data)
  const { data: branchWithShop, isLoading: isLoadingBranch } = useGetBranchWithShopQuery({
    shopSlug: slugShop,
    branchSlug: slugBranch
  });

  // Extract shop and branch from the response
  const merchant = branchWithShop?.shop;
  const branch = branchWithShop;

  // Use reservation hook for data fetching and actions
  const {
    reservation,
    isLoading: isLoadingReservation,
    isError,
    error,
    updateReservation,
    cancelReservation,
  } = useReservation(slugShop, slugBranch, slug);

  // Send confirmation mutation
  const [sendConfirmation] = useSendConfirmationMutation();

  // Action states
  const [isCheckingIn, setIsCheckingIn] = useState(false);
  const [isMarkingNoShow, setIsMarkingNoShow] = useState(false);
  const [isSendingConfirmation, setIsSendingConfirmation] = useState(false);

  // Dialog states
  const [showCheckInDialog, setShowCheckInDialog] = useState(false);
  const [showNoShowDialog, setShowNoShowDialog] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);

  const isLoading = isLoadingBranch || isLoadingReservation;

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!merchant || !branch) {
    return (
      <div className="text-center py-12">
        <h1 className="text-[#161412] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
        <p className="text-[#81766a] text-sm">The branch you are looking for does not exist.</p>
      </div>
    );
  }

  if (isError || !reservation) {
    return (
      <div className="text-center py-12">
        <h1 className="text-[#161412] text-[32px] font-bold leading-tight mb-2">Reservation Not Found</h1>
        <p className="text-[#81766a] text-sm">
          {error && 'data' in error && typeof error.data === 'object' && error.data && 'message' in error.data
            ? (error.data as { message: string }).message
            : 'The reservation you are looking for does not exist.'}
        </p>
      </div>
    );
  }

  const handleCheckIn = async () => {
    setIsCheckingIn(true);
    try {
      await updateReservation({ status: 'checked_in' });
      toast.success('Customer checked in successfully!');
      setShowCheckInDialog(false);
    } catch {
      toast.error('Failed to check in customer');
    } finally {
      setIsCheckingIn(false);
    }
  };

  const handleMarkNoShow = async () => {
    setIsMarkingNoShow(true);
    try {
      await updateReservation({ status: 'no_show' });
      toast.success('Reservation marked as no-show');
      setShowNoShowDialog(false);
    } catch {
      toast.error('Failed to mark as no-show');
    } finally {
      setIsMarkingNoShow(false);
    }
  };

  const handleSendConfirmation = async () => {
    setIsSendingConfirmation(true);
    try {
      await sendConfirmation({
        shopSlug: slugShop,
        branchSlug: slugBranch,
        reservationSlug: slug,
        method: reservation.customerEmail ? 'email' : 'sms'
      }).unwrap();
      toast.success('Confirmation sent successfully!');
    } catch {
      toast.error('Failed to send confirmation');
    } finally {
      setIsSendingConfirmation(false);
    }
  };

  const handleModifyReservation = () => {
    router.push(`/app/restaurant/${slugShop}/${slugBranch}/reservations/${slug}/edit`);
  };

  const handleCancelReservation = async () => {
    try {
      await cancelReservation();
      toast.success('Reservation cancelled successfully!');
      setShowCancelDialog(false);
      router.push(`/app/restaurant/${slugShop}/${slugBranch}/reservations`);
    } catch {
      toast.error('Failed to cancel reservation');
    }
  };

  // Helper function to get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return 'default';
      case 'pending':
        return 'secondary';
      case 'cancelled':
        return 'destructive';
      case 'completed':
        return 'outline';
      case 'seated':
        return 'default';
      case 'no-show':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  return (
    <div className="gap-1 px-6 flex flex-1 justify-center py-5">
      <div className="layout-content-container flex flex-col max-w-[920px] flex-1">
        {/* Breadcrumb */}
        <div className="flex flex-wrap gap-2 p-4">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/reservations`} className="text-[#81766a] text-base font-medium leading-normal hover:text-[#161412]">
            Reservations
          </Link>
          <span className="text-[#81766a] text-base font-medium leading-normal">/</span>
          <span className="text-[#161412] text-base font-medium leading-normal">Reservation Details</span>
        </div>

        {/* Page Header */}
        <div className="flex flex-wrap justify-between gap-3 p-4">
          <div className="flex min-w-72 flex-col gap-3">
            <p className="text-[#161412] tracking-light text-[32px] font-bold leading-tight">Reservation Details</p>
            <p className="text-[#81766a] text-sm font-normal leading-normal">View and manage reservation details for {reservation.customerName}</p>
          </div>
        </div>

        {/* Customer Information */}
        <h2 className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Customer Information</h2>
        <div className="p-4 grid grid-cols-[20%_1fr] gap-x-6">
          <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e3e1dd] py-5">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Name</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{reservation.customerName}</p>
          </div>
          <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e3e1dd] py-5">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Email</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{reservation.customerEmail || 'Not provided'}</p>
          </div>
          <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e3e1dd] py-5">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Phone Number</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{reservation.customerPhone}</p>
          </div>
        </div>

        {/* Reservation Details */}
        <h2 className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Reservation Details</h2>
        <div className="p-4 grid grid-cols-[20%_1fr] gap-x-6">
          <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e3e1dd] py-5">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Date</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{new Date(reservation.date).toLocaleDateString()}</p>
          </div>
          <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e3e1dd] py-5">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Time</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{reservation.time}</p>
          </div>
          <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e3e1dd] py-5">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Party Size</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{reservation.partySize}</p>
          </div>
          <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e3e1dd] py-5">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Status</p>
            <div className="text-[#161412] text-sm font-normal leading-normal">
              <Badge variant={getStatusBadgeVariant(reservation.status)}>
                {reservation.status.charAt(0).toUpperCase() + reservation.status.slice(1).replace('_', ' ')}
              </Badge>
            </div>
          </div>
          {reservation.tableId && (
            <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e3e1dd] py-5">
              <p className="text-[#81766a] text-sm font-normal leading-normal">Table</p>
              <p className="text-[#161412] text-sm font-normal leading-normal">Table {reservation.tableId}</p>
            </div>
          )}
        </div>

        {/* Special Requests */}
        <h2 className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Special Requests</h2>
        <p className="text-[#161412] text-base font-normal leading-normal pb-3 pt-1 px-4">
          {reservation.notes || 'No special requests'}
        </p>

        {/* Action Buttons */}
        <div className="flex justify-stretch">
          <div className="flex flex-1 gap-3 flex-wrap px-4 py-3 justify-start">
            <Button
              onClick={handleModifyReservation}
              variant="default"
              className="min-w-[84px]"
            >
              <Edit className="mr-2 h-4 w-4" />
              Modify Reservation
            </Button>
            <Button
              onClick={() => setShowCancelDialog(true)}
              variant="destructive"
              className="min-w-[84px]"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Cancel Reservation
            </Button>
          </div>
        </div>
      </div>

      {/* Sidebar - Reservation Actions */}
      <div className="layout-content-container flex flex-col w-[360px]">
        <h2 className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Reservation Actions</h2>

        <div className="flex items-center gap-4 bg-white px-4 min-h-14 justify-between">
          <p className="text-[#161412] text-base font-normal leading-normal flex-1 truncate">Check In</p>
          <div className="shrink-0">
            <Button
              onClick={() => setShowCheckInDialog(true)}
              disabled={isCheckingIn || reservation.status === 'seated' || reservation.status === 'completed'}
              size="sm"
              variant="outline"
            >
              <CheckCircle className="mr-2 h-4 w-4" />
              {isCheckingIn ? 'Checking...' : 'Check In'}
            </Button>
          </div>
        </div>

        <div className="flex items-center gap-4 bg-white px-4 min-h-14 justify-between">
          <p className="text-[#161412] text-base font-normal leading-normal flex-1 truncate">Mark as No-Show</p>
          <div className="shrink-0">
            <Button
              onClick={() => setShowNoShowDialog(true)}
              disabled={isMarkingNoShow || reservation.status === 'no-show' || reservation.status === 'completed'}
              size="sm"
              variant="outline"
            >
              <UserX className="mr-2 h-4 w-4" />
              {isMarkingNoShow ? 'Marking...' : 'No-Show'}
            </Button>
          </div>
        </div>

        <div className="flex items-center gap-4 bg-white px-4 min-h-14 justify-between">
          <p className="text-[#161412] text-base font-normal leading-normal flex-1 truncate">Send Confirmation</p>
          <div className="shrink-0">
            <Button
              onClick={handleSendConfirmation}
              disabled={isSendingConfirmation}
              size="sm"
              variant="outline"
            >
              <Mail className="mr-2 h-4 w-4" />
              {isSendingConfirmation ? 'Sending...' : 'Send'}
            </Button>
          </div>
        </div>
      </div>

      {/* Confirmation Dialogs */}
      <AlertDialog open={showCheckInDialog} onOpenChange={setShowCheckInDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Check In Customer</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to check in {reservation.customerName}? This will mark the reservation as checked in.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleCheckIn}
              disabled={isCheckingIn}
              className="bg-green-600 hover:bg-green-700"
            >
              {isCheckingIn ? 'Checking In...' : 'Check In'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={showNoShowDialog} onOpenChange={setShowNoShowDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Mark as No-Show</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to mark this reservation as a no-show? This action indicates that {reservation.customerName} did not arrive for their reservation.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleMarkNoShow}
              disabled={isMarkingNoShow}
              className="bg-orange-600 hover:bg-orange-700"
            >
              {isMarkingNoShow ? 'Marking...' : 'Mark No-Show'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Cancel Reservation</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to cancel this reservation for {reservation.customerName}? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleCancelReservation}
              className="bg-red-600 hover:bg-red-700"
            >
              Cancel Reservation
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
