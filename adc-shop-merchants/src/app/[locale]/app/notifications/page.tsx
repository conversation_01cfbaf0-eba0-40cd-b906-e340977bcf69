'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import {
  Bell,
  BellRing,
  Check,
  X,
  Filter,
  Search,
  MoreVertical,
  AlertCircle,
  Info,
  CheckCircle,
  XCircle,
  Clock,
  Star,
  MessageSquare,
  ShoppingCart,
  Users,
  Calendar,
  TrendingUp
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Types
interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'order' | 'reservation' | 'review' | 'system';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  actionUrl?: string;
  actionLabel?: string;
  metadata?: Record<string, any>;
}

type FilterType = 'all' | 'unread' | 'order' | 'reservation' | 'review' | 'system';
type SortType = 'newest' | 'oldest' | 'priority';

export default function NotificationsPage() {
  const t = useTranslations('notifications');

  // State
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [filteredNotifications, setFilteredNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<FilterType>('all');
  const [sortBy, setSortBy] = useState<SortType>('newest');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedNotifications, setSelectedNotifications] = useState<Set<string>>(new Set());

  // Mock data - replace with API call
  useEffect(() => {
    const mockNotifications: Notification[] = [
      {
        id: '1',
        type: 'order',
        title: 'New Order Received',
        message: 'Order #1234 from John Doe - $45.99',
        timestamp: new Date(Date.now() - 5 * 60 * 1000),
        read: false,
        priority: 'high',
        actionUrl: '/app/restaurant/orders/1234',
        actionLabel: 'View Order',
        metadata: { orderId: '1234', amount: 45.99 }
      },
      {
        id: '2',
        type: 'reservation',
        title: 'New Reservation',
        message: 'Table for 4 at 7:00 PM today - Sarah Wilson',
        timestamp: new Date(Date.now() - 15 * 60 * 1000),
        read: false,
        priority: 'medium',
        actionUrl: '/app/restaurant/reservations',
        actionLabel: 'View Reservation'
      },
      {
        id: '3',
        type: 'review',
        title: 'New Review',
        message: '5-star review: "Amazing food and service!"',
        timestamp: new Date(Date.now() - 30 * 60 * 1000),
        read: true,
        priority: 'low',
        actionUrl: '/app/restaurant/reviews',
        actionLabel: 'View Review'
      },
      {
        id: '4',
        type: 'warning',
        title: 'Low Stock Alert',
        message: 'Tomatoes are running low (5 units remaining)',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        read: false,
        priority: 'urgent',
        actionUrl: '/app/restaurant/inventory',
        actionLabel: 'Manage Inventory'
      },
      {
        id: '5',
        type: 'system',
        title: 'System Update',
        message: 'New features available in your dashboard',
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
        read: true,
        priority: 'low',
        actionUrl: '/app/restaurant/settings',
        actionLabel: 'View Updates'
      }
    ];

    setTimeout(() => {
      setNotifications(mockNotifications);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter and sort notifications
  useEffect(() => {
    let filtered = notifications;

    // Apply filter
    if (filter !== 'all') {
      if (filter === 'unread') {
        filtered = filtered.filter(n => !n.read);
      } else {
        filtered = filtered.filter(n => n.type === filter);
      }
    }

    // Apply search
    if (searchQuery) {
      filtered = filtered.filter(n =>
        n.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        n.message.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply sort
    filtered.sort((a, b) => {
      if (sortBy === 'newest') {
        return b.timestamp.getTime() - a.timestamp.getTime();
      } else if (sortBy === 'oldest') {
        return a.timestamp.getTime() - b.timestamp.getTime();
      } else { // priority
        const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      }
    });

    setFilteredNotifications(filtered);
  }, [notifications, filter, searchQuery, sortBy]);

  // Handlers
  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    );
  };

  const markAsUnread = (id: string) => {
    setNotifications(prev =>
      prev.map(n => n.id === id ? { ...n, read: false } : n)
    );
  };

  const deleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
    setSelectedNotifications(prev => {
      const newSet = new Set(prev);
      newSet.delete(id);
      return newSet;
    });
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
  };

  const deleteSelected = () => {
    setNotifications(prev => prev.filter(n => !selectedNotifications.has(n.id)));
    setSelectedNotifications(new Set());
  };

  const toggleSelection = (id: string) => {
    setSelectedNotifications(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const selectAll = () => {
    setSelectedNotifications(new Set(filteredNotifications.map(n => n.id)));
  };

  const clearSelection = () => {
    setSelectedNotifications(new Set());
  };

  // Helper functions
  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'order': return <ShoppingCart className="w-5 h-5" />;
      case 'reservation': return <Calendar className="w-5 h-5" />;
      case 'review': return <Star className="w-5 h-5" />;
      case 'success': return <CheckCircle className="w-5 h-5" />;
      case 'warning': return <AlertCircle className="w-5 h-5" />;
      case 'error': return <XCircle className="w-5 h-5" />;
      case 'system': return <Info className="w-5 h-5" />;
      default: return <Bell className="w-5 h-5" />;
    }
  };

  const getNotificationColor = (type: Notification['type']) => {
    switch (type) {
      case 'order': return 'text-blue-700 bg-blue-50 dark:text-blue-300 dark:bg-blue-950';
      case 'reservation': return 'text-green-700 bg-green-50 dark:text-green-300 dark:bg-green-950';
      case 'review': return 'text-yellow-700 bg-yellow-50 dark:text-yellow-300 dark:bg-yellow-950';
      case 'success': return 'text-green-700 bg-green-50 dark:text-green-300 dark:bg-green-950';
      case 'warning': return 'text-orange-700 bg-orange-50 dark:text-orange-300 dark:bg-orange-950';
      case 'error': return 'text-red-700 bg-red-50 dark:text-red-300 dark:bg-red-950';
      case 'system': return 'text-muted-foreground bg-muted';
      default: return 'text-blue-700 bg-blue-50 dark:text-blue-300 dark:bg-blue-950';
    }
  };

  const getPriorityColor = (priority: Notification['priority']) => {
    switch (priority) {
      case 'urgent': return 'bg-red-500 dark:bg-red-600';
      case 'high': return 'bg-orange-500 dark:bg-orange-600';
      case 'medium': return 'bg-yellow-500 dark:bg-yellow-600';
      case 'low': return 'bg-green-500 dark:bg-green-600';
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return timestamp.toLocaleDateString();
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="relative">
            <BellRing className="w-8 h-8 text-blue-600" />
            {unreadCount > 0 && (
              <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {unreadCount > 99 ? '99+' : unreadCount}
              </span>
            )}
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Notifications</h1>
            <p className="text-gray-600">
              {unreadCount > 0 ? `${unreadCount} unread notifications` : 'All caught up!'}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {selectedNotifications.size > 0 && (
            <>
              <button
                onClick={deleteSelected}
                className="px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
              >
                Delete Selected ({selectedNotifications.size})
              </button>
              <button
                onClick={clearSelection}
                className="px-3 py-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors"
              >
                Clear Selection
              </button>
            </>
          )}
          <button
            onClick={markAllAsRead}
            disabled={unreadCount === 0}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Mark All Read
          </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search notifications..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        <div className="flex gap-2">
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value as FilterType)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All</option>
            <option value="unread">Unread</option>
            <option value="order">Orders</option>
            <option value="reservation">Reservations</option>
            <option value="review">Reviews</option>
            <option value="system">System</option>
          </select>

          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as SortType)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="newest">Newest First</option>
            <option value="oldest">Oldest First</option>
            <option value="priority">By Priority</option>
          </select>
        </div>
      </div>

      {/* Bulk Actions */}
      {filteredNotifications.length > 0 && (
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <button
            onClick={selectAll}
            className="hover:text-blue-600 transition-colors"
          >
            Select All
          </button>
          <span>•</span>
          <span>{filteredNotifications.length} notifications</span>
        </div>
      )}

      {/* Notifications List */}
      <div className="space-y-2">
        {filteredNotifications.length === 0 ? (
          <div className="text-center py-12">
            <Bell className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications found</h3>
            <p className="text-gray-600">
              {searchQuery ? 'Try adjusting your search or filters' : 'You\'re all caught up!'}
            </p>
          </div>
        ) : (
          filteredNotifications.map((notification) => (
            <NotificationCard
              key={notification.id}
              notification={notification}
              isSelected={selectedNotifications.has(notification.id)}
              onToggleSelection={() => toggleSelection(notification.id)}
              onMarkAsRead={() => markAsRead(notification.id)}
              onMarkAsUnread={() => markAsUnread(notification.id)}
              onDelete={() => deleteNotification(notification.id)}
              getIcon={getNotificationIcon}
              getColor={getNotificationColor}
              getPriorityColor={getPriorityColor}
              formatTimestamp={formatTimestamp}
            />
          ))
        )}
      </div>
    </div>
  );
}

// Notification Card Component
interface NotificationCardProps {
  notification: Notification;
  isSelected: boolean;
  onToggleSelection: () => void;
  onMarkAsRead: () => void;
  onMarkAsUnread: () => void;
  onDelete: () => void;
  getIcon: (type: Notification['type']) => React.ReactNode;
  getColor: (type: Notification['type']) => string;
  getPriorityColor: (priority: Notification['priority']) => string;
  formatTimestamp: (timestamp: Date) => string;
}

function NotificationCard({
  notification,
  isSelected,
  onToggleSelection,
  onMarkAsRead,
  onMarkAsUnread,
  onDelete,
  getIcon,
  getColor,
  getPriorityColor,
  formatTimestamp
}: NotificationCardProps) {
  const [showActions, setShowActions] = useState(false);

  return (
    <div
      className={cn(
        'relative p-4 border rounded-lg transition-all duration-200 hover:shadow-md',
        notification.read ? 'bg-card border-border' : 'bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800',
        isSelected && 'ring-2 ring-primary'
      )}
    >
      <div className="flex items-start gap-4">
        {/* Selection Checkbox */}
        <input
          type="checkbox"
          checked={isSelected}
          onChange={onToggleSelection}
          className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />

        {/* Icon */}
        <div className={cn('p-2 rounded-lg', getColor(notification.type))}>
          {getIcon(notification.type)}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h3 className={cn(
                  'font-medium',
                  notification.read ? 'text-gray-900' : 'text-gray-900 font-semibold'
                )}>
                  {notification.title}
                </h3>
                {/* Priority Indicator */}
                <div className={cn('w-2 h-2 rounded-full', getPriorityColor(notification.priority))} />
                {!notification.read && (
                  <div className="w-2 h-2 bg-blue-600 rounded-full" />
                )}
              </div>
              <p className="text-gray-600 text-sm mb-2">{notification.message}</p>
              <div className="flex items-center gap-4 text-xs text-gray-500">
                <span>{formatTimestamp(notification.timestamp)}</span>
                <span className="capitalize">{notification.priority} priority</span>
              </div>
            </div>

            {/* Actions */}
            <div className="relative">
              <button
                onClick={() => setShowActions(!showActions)}
                className="p-1 hover:bg-gray-100 rounded transition-colors"
              >
                <MoreVertical className="w-4 h-4 text-gray-400" />
              </button>

              {showActions && (
                <div className="absolute right-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-10 min-w-[150px]">
                  {notification.read ? (
                    <button
                      onClick={() => {
                        onMarkAsUnread();
                        setShowActions(false);
                      }}
                      className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
                    >
                      <Bell className="w-4 h-4" />
                      Mark as Unread
                    </button>
                  ) : (
                    <button
                      onClick={() => {
                        onMarkAsRead();
                        setShowActions(false);
                      }}
                      className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
                    >
                      <Check className="w-4 h-4" />
                      Mark as Read
                    </button>
                  )}
                  <button
                    onClick={() => {
                      onDelete();
                      setShowActions(false);
                    }}
                    className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 text-red-600 flex items-center gap-2"
                  >
                    <X className="w-4 h-4" />
                    Delete
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Action Button */}
          {notification.actionUrl && notification.actionLabel && (
            <div className="mt-3">
              <a
                href={notification.actionUrl}
                className="inline-flex items-center px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                {notification.actionLabel}
              </a>
            </div>
          )}
        </div>
      </div>

      {/* Click overlay to close actions */}
      {showActions && (
        <div
          className="fixed inset-0 z-5"
          onClick={() => setShowActions(false)}
        />
      )}
    </div>
  );
}
