'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { AppLoading } from '@/components/ui/app-loading';
import { Calendar, Clock, Users, BarChart, Settings, Plus, Search, CalendarDays } from 'lucide-react';
import { useGetServicesQuery, useGetAppointmentsQuery } from '@/lib/redux/api/endpoints/serviceApi';
import { useSession } from 'next-auth/react';

// Mock service business data
const serviceBusinessData = {
  name: 'Wellness Spa & Salon',
  description: 'Premium spa and beauty services',
  address: '123 Wellness Avenue, Bangkok, Thailand',
  phone: '+66 2 123 4567',
  email: '<EMAIL>',
  website: 'www.wellnessspa.com',
  openingHours: 'Mon-Sun: 10:00 AM - 8:00 PM',
};

// Mock services data
const mockServices = [
  {
    id: '1',
    name: 'Swedish Massage',
    category: 'Massage',
    duration: '60 min',
    price: '฿1,200',
    availability: 'Available',
    image: 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?q=80&w=2070&auto=format&fit=crop'
  },
  {
    id: '2',
    name: 'Deep Tissue Massage',
    category: 'Massage',
    duration: '90 min',
    price: '฿1,800',
    availability: 'Available',
    image: 'https://images.unsplash.com/photo-1519823551278-64ac92734fb1?q=80&w=2044&auto=format&fit=crop'
  },
  {
    id: '3',
    name: 'Facial Treatment',
    category: 'Skincare',
    duration: '45 min',
    price: '฿1,500',
    availability: 'Limited',
    image: 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?q=80&w=2070&auto=format&fit=crop'
  },
  {
    id: '4',
    name: 'Hair Styling',
    category: 'Hair',
    duration: '60 min',
    price: '฿800',
    availability: 'Available',
    image: 'https://images.unsplash.com/photo-1560066984-138dadb4c035?q=80&w=2074&auto=format&fit=crop'
  },
  {
    id: '5',
    name: 'Manicure & Pedicure',
    category: 'Nails',
    duration: '90 min',
    price: '฿1,000',
    availability: 'Available',
    image: 'https://images.unsplash.com/photo-1610992015732-2449b76344bc?q=80&w=2070&auto=format&fit=crop'
  },
  {
    id: '6',
    name: 'Aromatherapy',
    category: 'Wellness',
    duration: '75 min',
    price: '฿1,600',
    availability: 'Limited',
    image: 'https://images.unsplash.com/photo-1540555700478-4be289fbecef?q=80&w=2070&auto=format&fit=crop'
  },
];

// Mock appointments data
const mockAppointments = [
  { id: 'APT-001', customer: 'Sarah Johnson', service: 'Swedish Massage', date: '2023-10-15', time: '10:00 AM', status: 'Confirmed' },
  { id: 'APT-002', customer: 'Michael Chen', service: 'Facial Treatment', date: '2023-10-15', time: '2:30 PM', status: 'Pending' },
  { id: 'APT-003', customer: 'Emma Wilson', service: 'Deep Tissue Massage', date: '2023-10-16', time: '11:15 AM', status: 'Confirmed' },
  { id: 'APT-004', customer: 'David Kim', service: 'Hair Styling', date: '2023-10-16', time: '4:00 PM', status: 'Cancelled' },
];

// Mock staff data
const mockStaff = [
  { id: '1', name: 'Lisa Wong', role: 'Massage Therapist', services: ['Swedish Massage', 'Deep Tissue Massage'], image: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?q=80&w=1961&auto=format&fit=crop' },
  { id: '2', name: 'James Taylor', role: 'Hair Stylist', services: ['Hair Styling', 'Hair Coloring'], image: 'https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?q=80&w=1974&auto=format&fit=crop' },
  { id: '3', name: 'Nina Patel', role: 'Esthetician', services: ['Facial Treatment', 'Skincare Consultation'], image: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?q=80&w=1964&auto=format&fit=crop' },
  { id: '4', name: 'Robert Chen', role: 'Nail Technician', services: ['Manicure', 'Pedicure'], image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=1974&auto=format&fit=crop' },
];

export default function ServicePage() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const { data: session } = useSession();

  // Get merchant ID from session
  const merchantId = session?.user?.id || 'default-merchant-id';

  // Fetch real service data
  const {
    data: services,
    isLoading: isLoadingServices,
    isError: isErrorServices
  } = useGetServicesQuery(merchantId);

  const {
    data: appointments,
    isLoading: isLoadingAppointments,
    isError: isErrorAppointments
  } = useGetAppointmentsQuery(merchantId);

  // Show loading state
  if (isLoadingServices || isLoadingAppointments) {
    return <AppLoading />;
  }

  // Show error state if API fails
  if (isErrorServices || isErrorAppointments) {
    return (
      <div className="container mx-auto p-4">
        <div className="text-center text-red-600">
          <p>Error loading service data. Please check your backend connection.</p>
        </div>
      </div>
    );
  }

  // Use only real data from API
  const servicesList = services || [];
  const appointmentsList = appointments || [];

  // If no services, show empty state
  if (servicesList.length === 0) {
    return (
      <div className="container mx-auto p-4">
        <div className="text-center text-gray-600">
          <p>No services found. Please add services to your business.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="font-be-vietnam">
      {/* Header */}
      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div>
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">{serviceBusinessData.name}</h1>
          <p className="text-[#8a745c] text-sm">{serviceBusinessData.description}</p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            className="bg-[#f1edea] text-[#181510] hover:bg-[#e2dcd4] border-none"
          >
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="pb-3">
          <TabsList className="flex border-b border-[#e2dcd4] px-4 gap-8 bg-transparent h-auto">
            <TabsTrigger
              value="dashboard"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Dashboard</p>
            </TabsTrigger>
            <TabsTrigger
              value="services"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Services</p>
            </TabsTrigger>
            <TabsTrigger
              value="appointments"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Appointments</p>
            </TabsTrigger>
            <TabsTrigger
              value="staff"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Staff</p>
            </TabsTrigger>
          </TabsList>
        </div>

        {/* Dashboard Tab Content */}
        <TabsContent value="dashboard" className="mt-0">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader className="pb-2">
                <CardTitle className="text-[#181510] text-lg flex items-center">
                  <Calendar className="mr-2 h-5 w-5 text-[#8a745c]" />
                  Today's Appointments
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold text-[#181510]">8</p>
                <p className="text-[#8a745c] text-sm">2 pending confirmation</p>
              </CardContent>
            </Card>
            <Card className="bg-card border-border">
              <CardHeader className="pb-2">
                <CardTitle className="text-foreground text-lg flex items-center">
                  <Users className="mr-2 h-5 w-5 text-primary" />
                  Active Clients
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold text-foreground">124</p>
                <p className="text-muted-foreground text-sm">12 new this month</p>
              </CardContent>
            </Card>
            <Card className="bg-card border-border">
              <CardHeader className="pb-2">
                <CardTitle className="text-foreground text-lg flex items-center">
                  <BarChart className="mr-2 h-5 w-5 text-primary" />
                  Monthly Revenue
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold text-foreground">฿78,450</p>
                <p className="text-muted-foreground text-sm">+8% from last month</p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader>
                <CardTitle className="text-[#181510]">Today's Schedule</CardTitle>
                <CardDescription className="text-[#8a745c]">Upcoming appointments</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {appointmentsList.slice(0, 3).map((appointment) => (
                    <div key={appointment.id} className="flex items-center justify-between border-b border-[#e5e1dc] pb-3">
                      <div>
                        <p className="text-[#181510] font-medium">{appointment.time} - {appointment.customer}</p>
                        <p className="text-[#8a745c] text-xs">{appointment.service}</p>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        appointment.status === 'Confirmed' ? 'bg-green-100 text-green-800' :
                        appointment.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {appointment.status}
                      </span>
                    </div>
                  ))}
                  <div className="mt-4 text-right">
                    <Link href="/app/service/appointments" className="text-[#8a745c] hover:text-[#6d5a48] text-sm">
                      View full schedule →
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader>
                <CardTitle className="text-[#181510]">Popular Services</CardTitle>
                <CardDescription className="text-[#8a745c]">Most booked services this month</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {servicesList.slice(0, 3).map((service) => (
                    <div key={service.id} className="flex items-center justify-between border-b border-[#e5e1dc] pb-3">
                      <div className="flex items-center">
                        <div className="w-10 h-10 rounded-md overflow-hidden mr-3">
                          <img src={service.image} alt={service.name} className="w-full h-full object-cover" />
                        </div>
                        <div>
                          <p className="text-[#181510] font-medium">{service.name}</p>
                          <p className="text-[#8a745c] text-xs">{service.duration} • {service.price}</p>
                        </div>
                      </div>
                      <div>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          service.availability === 'Available' ? 'bg-green-100 text-green-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {service.availability}
                        </span>
                      </div>
                    </div>
                  ))}
                  <div className="mt-4 text-right">
                    <Link href="/app/service/services" className="text-[#8a745c] hover:text-[#6d5a48] text-sm">
                      View all services →
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Services Tab Content */}
        <TabsContent value="services" className="mt-0">
          <div className="flex justify-between items-center mb-6">
            <div className="relative w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#8a745c] h-4 w-4" />
              <input
                type="text"
                placeholder="Search services..."
                className="w-full pl-10 pr-4 py-2 rounded-lg border-none bg-[#f1edea] text-[#181510] placeholder:text-[#8a745c] focus:outline-none focus:ring-2 focus:ring-[#e5ccb2]"
              />
            </div>
            <Button className="bg-[#8a745c] hover:bg-[#6d5a48] text-white">
              <Plus className="mr-2 h-4 w-4" />
              Add Service
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {servicesList.map((service) => (
              <Link href={`/app/service/services/${service.id}`} key={service.id}>
                <Card className="h-full overflow-hidden hover:shadow-md transition-shadow bg-[#fbfaf9] border-[#e5e1dc]">
                  <div
                    className="h-40 bg-cover bg-center"
                    style={{ backgroundImage: `url(${service.image})` }}
                  />
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-[#181510]">{service.name}</CardTitle>
                        <CardDescription className="text-[#8a745c]">{service.category}</CardDescription>
                      </div>
                      <div className="text-[#181510] font-bold">{service.price}</div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-between items-center">
                      <span className="text-[#8a745c] text-sm flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        {service.duration}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        service.availability === 'Available' ? 'bg-green-100 text-green-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {service.availability}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </TabsContent>

        {/* Appointments Tab Content */}
        <TabsContent value="appointments" className="mt-0">
          <div className="flex justify-between items-center mb-6">
            <div className="relative w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#8a745c] h-4 w-4" />
              <input
                type="text"
                placeholder="Search appointments..."
                className="w-full pl-10 pr-4 py-2 rounded-lg border-none bg-[#f1edea] text-[#181510] placeholder:text-[#8a745c] focus:outline-none focus:ring-2 focus:ring-[#e5ccb2]"
              />
            </div>
            <Button className="bg-[#8a745c] hover:bg-[#6d5a48] text-white">
              <CalendarDays className="mr-2 h-4 w-4" />
              New Appointment
            </Button>
          </div>

          <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-[#e5e1dc]">
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">ID</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Customer</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Service</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Date</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Time</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Status</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {appointmentsList.map((appointment) => (
                      <tr key={appointment.id} className="border-b border-[#e5e1dc] hover:bg-[#f1edea]">
                        <td className="p-4 text-[#181510]">{appointment.id}</td>
                        <td className="p-4 text-[#181510]">{appointment.customer}</td>
                        <td className="p-4 text-[#181510]">{appointment.service}</td>
                        <td className="p-4 text-[#181510]">{appointment.date}</td>
                        <td className="p-4 text-[#181510]">{appointment.time}</td>
                        <td className="p-4">
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            appointment.status === 'Confirmed' ? 'bg-green-100 text-green-800' :
                            appointment.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {appointment.status}
                          </span>
                        </td>
                        <td className="p-4">
                          <Link href={`/app/service/appointments/${appointment.id}`} className="text-[#8a745c] hover:text-[#6d5a48]">
                            View
                          </Link>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Staff Tab Content */}
        <TabsContent value="staff" className="mt-0">
          <div className="flex justify-between items-center mb-6">
            <div className="relative w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#8a745c] h-4 w-4" />
              <input
                type="text"
                placeholder="Search staff..."
                className="w-full pl-10 pr-4 py-2 rounded-lg border-none bg-[#f1edea] text-[#181510] placeholder:text-[#8a745c] focus:outline-none focus:ring-2 focus:ring-[#e5ccb2]"
              />
            </div>
            <Button className="bg-[#8a745c] hover:bg-[#6d5a48] text-white">
              <Plus className="mr-2 h-4 w-4" />
              Add Staff Member
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {mockStaff.map((staff) => (
              <Link href={`/app/service/staff/${staff.id}`} key={staff.id}>
                <Card className="h-full overflow-hidden hover:shadow-md transition-shadow bg-[#fbfaf9] border-[#e5e1dc]">
                  <div className="p-4 flex justify-center">
                    <div className="w-24 h-24 rounded-full overflow-hidden">
                      <img src={staff.image} alt={staff.name} className="w-full h-full object-cover" />
                    </div>
                  </div>
                  <CardHeader className="pb-2 text-center">
                    <CardTitle className="text-[#181510]">{staff.name}</CardTitle>
                    <CardDescription className="text-[#8a745c]">{staff.role}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center">
                      <p className="text-[#8a745c] text-sm mb-2">Services:</p>
                      <div className="flex flex-wrap justify-center gap-1">
                        {staff.services.map((service, index) => (
                          <span key={index} className="px-2 py-1 bg-[#f1edea] rounded-full text-xs text-[#181510]">
                            {service}
                          </span>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
