'use client';

import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import RestaurantDashboard from '@/components/admin/dashboard/RestaurantDashboard';
import ServiceDashboard from '@/components/admin/dashboard/ServiceDashboard';
import RetailDashboard from '@/components/admin/dashboard/RetailDashboard';
import DigitalDashboard from '@/components/admin/dashboard/DigitalDashboard';
import Link from 'next/link';

interface MerchantPageProps {
  params: {
    type: string;
    id: string;
  };
}

export default function MerchantPage({ params }: MerchantPageProps) {
  const { type, id } = params;
  const { data: merchantsData, isLoading, error } = useGetMerchantsQuery({
    page: 1,
    limit: 50,
    sort_by: 'created_at',
    sort_order: 'desc'
  });
  const merchants = merchantsData?.data || [];

  if (isLoading) {
    return <div className="p-6">Loading merchant data...</div>;
  }

  if (error) {
    return <div className="p-6 text-red-500">Error loading merchant: {JSON.stringify(error)}</div>;
  }

  const merchant = merchants?.find(m => m.id === id);

  if (!merchant) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-yellow-400">⚠️</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Merchant not found. The merchant may have been deleted or you may not have permission to view it.
              </p>
            </div>
          </div>
        </div>
        <Link href="/admin/merchants" className="text-blue-600 hover:underline">
          ← Back to merchants
        </Link>
      </div>
    );
  }

  // Verify that the merchant type matches the URL type
  if (merchant.type !== type) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-yellow-400">⚠️</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Merchant type mismatch. This merchant is of type "{merchant.type}" but you're viewing the "{type}" dashboard.
              </p>
            </div>
          </div>
        </div>
        <Link href={`/admin/merchants/${merchant.type}/${merchant.id}`} className="text-blue-600 hover:underline">
          Go to correct dashboard
        </Link>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-4 flex justify-between items-center">
        <Link href="/admin/merchants" className="text-blue-600 hover:underline">
          ← Back to merchants
        </Link>

        <div className="flex space-x-2">
          <Link
            href={`/admin/merchants/${type}/${id}/edit`}
            className="px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
          >
            Edit
          </Link>
          <Link
            href={`/admin/merchants/${type}/${id}/settings`}
            className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Settings
          </Link>
        </div>
      </div>

      {/* Render the appropriate dashboard based on merchant type */}
      {type === 'restaurant' && <RestaurantDashboard merchantId={id} />}
      {type === 'service' && <ServiceDashboard merchantId={id} />}
      {type === 'retail' && <RetailDashboard merchantId={id} />}
      {type === 'digital' && <DigitalDashboard merchantId={id} />}
      {type === 'convenience' && <div>Convenience Dashboard (Coming Soon)</div>}
      {type === 'custom' && <div>Custom Dashboard (Coming Soon)</div>}
    </div>
  );
}
