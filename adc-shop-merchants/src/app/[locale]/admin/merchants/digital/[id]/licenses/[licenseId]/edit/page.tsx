'use client';

import { useRouter } from 'next/navigation';
import Link from 'next/link';
import LicenseForm from '@/components/admin/forms/LicenseForm';
import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { useGetLicensesQuery } from '@/lib/redux/api/endpoints/digitalApi';

interface EditLicensePageProps {
  params: {
    id: string;
    licenseId: string;
  };
}

export default function EditLicensePage({ params }: EditLicensePageProps) {
  const { id, licenseId } = params;
  const router = useRouter();

  const { data: merchants, isLoading: isLoadingMerchants } = useGetMerchantsQuery();
  const { data: licenses, isLoading: isLoadingLicenses } = useGetLicensesQuery({ merchantId: id });

  const isLoading = isLoadingMerchants || isLoadingLicenses;

  // Find the merchant
  const merchant = merchants?.find(m => m.id === id && m.type === 'digital');

  // Find the license
  const license = licenses?.find(l => l.id === licenseId);

  const handleSuccess = () => {
    // Redirect to the licenses list
    router.push(`/admin/merchants/digital/${id}/licenses`);
  };

  if (isLoading) {
    return <div className="p-6">Loading data...</div>;
  }

  if (!merchant) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-yellow-400">⚠️</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Digital store not found. The merchant may have been deleted or you may not have permission to view it.
              </p>
            </div>
          </div>
        </div>
        <Link href="/admin/merchants" className="text-blue-600 hover:underline">
          ← Back to merchants
        </Link>
      </div>
    );
  }

  if (!license) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-yellow-400">⚠️</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                License not found. The license may have been deleted or you may not have permission to view it.
              </p>
            </div>
          </div>
        </div>
        <Link href={`/admin/merchants/digital/${id}/licenses`} className="text-blue-600 hover:underline">
          ← Back to licenses
        </Link>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <Link href={`/admin/merchants/digital/${id}/licenses`} className="text-blue-600 hover:underline">
          ← Back to licenses
        </Link>
      </div>

      <h1 className="text-2xl font-bold mb-6">Edit License - {license.licenseKey}</h1>

      <LicenseForm
        merchantId={id}
        initialData={license}
        mode="edit"
        onSuccess={handleSuccess}
      />
    </div>
  );
}
