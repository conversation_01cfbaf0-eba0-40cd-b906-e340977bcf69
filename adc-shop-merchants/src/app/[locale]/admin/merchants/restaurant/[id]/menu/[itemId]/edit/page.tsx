'use client';

import { useRouter } from 'next/navigation';
import Link from 'next/link';
import MenuItemForm from '@/components/admin/forms/MenuItemForm';
import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { useGetMenuItemsQuery } from '@/lib/redux/api/endpoints/restaurant/restaurantApi';

interface EditMenuItemPageProps {
  params: {
    id: string;
    itemId: string;
  };
}

export default function EditMenuItemPage({ params }: EditMenuItemPageProps) {
  const { id, itemId } = params;
  const router = useRouter();

  const { data: merchants, isLoading: isLoadingMerchants } = useGetMerchantsQuery();
  const { data: menuItems, isLoading: isLoadingMenuItems } = useGetMenuItemsQuery(id);

  const isLoading = isLoadingMerchants || isLoadingMenuItems;

  // Find the merchant
  const merchant = merchants?.find(m => m.id === id && m.type === 'restaurant');

  // Find the menu item
  const menuItem = menuItems?.find(item => item.id === itemId);

  const handleSuccess = () => {
    // Redirect to the menu items list
    router.push(`/admin/merchants/restaurant/${id}`);
  };

  if (isLoading) {
    return <div className="p-6">Loading data...</div>;
  }

  if (!merchant) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-yellow-400">⚠️</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Restaurant not found. The merchant may have been deleted or you may not have permission to view it.
              </p>
            </div>
          </div>
        </div>
        <Link href="/admin/merchants" className="text-blue-600 hover:underline">
          ← Back to merchants
        </Link>
      </div>
    );
  }

  if (!menuItem) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-yellow-400">⚠️</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Menu item not found. The item may have been deleted or you may not have permission to view it.
              </p>
            </div>
          </div>
        </div>
        <Link href={`/admin/merchants/restaurant/${id}`} className="text-blue-600 hover:underline">
          ← Back to restaurant
        </Link>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <Link href={`/admin/merchants/restaurant/${id}`} className="text-blue-600 hover:underline">
          ← Back to restaurant
        </Link>
      </div>

      <h1 className="text-2xl font-bold mb-6">Edit Menu Item - {menuItem.name}</h1>

      <MenuItemForm
        merchantId={id}
        initialData={menuItem}
        mode="edit"
        onSuccess={handleSuccess}
      />
    </div>
  );
}
