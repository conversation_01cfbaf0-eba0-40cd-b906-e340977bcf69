'use client';

import { useRouter } from 'next/navigation';
import Link from 'next/link';
import AppointmentForm from '@/components/admin/forms/AppointmentForm';
import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';

interface CreateAppointmentPageProps {
  params: {
    id: string;
  };
}

export default function CreateAppointmentPage({ params }: CreateAppointmentPageProps) {
  const { id } = params;
  const router = useRouter();
  const { data: merchants, isLoading } = useGetMerchantsQuery();

  // Find the merchant
  const merchant = merchants?.find(m => m.id === id && m.type === 'service');

  const handleSuccess = () => {
    // Redirect to the appointments list
    router.push(`/admin/merchants/service/${id}/appointments`);
  };

  if (isLoading) {
    return <div className="p-6">Loading merchant data...</div>;
  }

  if (!merchant) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-yellow-400">⚠️</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Service provider not found. The merchant may have been deleted or you may not have permission to view it.
              </p>
            </div>
          </div>
        </div>
        <Link href="/admin/merchants" className="text-blue-600 hover:underline">
          ← Back to merchants
        </Link>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <Link href={`/admin/merchants/service/${id}/appointments`} className="text-blue-600 hover:underline">
          ← Back to appointments
        </Link>
      </div>

      <h1 className="text-2xl font-bold mb-6">Create Appointment for {merchant.name}</h1>

      <AppointmentForm
        merchantId={id}
        mode="create"
        onSuccess={handleSuccess}
      />
    </div>
  );
}
