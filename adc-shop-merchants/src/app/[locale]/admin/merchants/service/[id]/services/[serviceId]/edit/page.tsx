'use client';

import { useRouter } from 'next/navigation';
import Link from 'next/link';
import ServiceForm from '@/components/admin/forms/ServiceForm';
import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { useGetServicesQuery } from '@/lib/redux/api/endpoints/serviceApi';

interface EditServicePageProps {
  params: {
    id: string;
    serviceId: string;
  };
}

export default function EditServicePage({ params }: EditServicePageProps) {
  const { id, serviceId } = params;
  const router = useRouter();

  const { data: merchants, isLoading: isLoadingMerchants } = useGetMerchantsQuery();
  const { data: services, isLoading: isLoadingServices } = useGetServicesQuery(id);

  const isLoading = isLoadingMerchants || isLoadingServices;

  // Find the merchant
  const merchant = merchants?.find(m => m.id === id && m.type === 'service');

  // Find the service
  const service = services?.find(s => s.id === serviceId);

  const handleSuccess = () => {
    // Redirect to the services list
    router.push(`/admin/merchants/service/${id}`);
  };

  if (isLoading) {
    return <div className="p-6">Loading data...</div>;
  }

  if (!merchant) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-yellow-400">⚠️</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Service provider not found. The merchant may have been deleted or you may not have permission to view it.
              </p>
            </div>
          </div>
        </div>
        <Link href="/admin/merchants" className="text-blue-600 hover:underline">
          ← Back to merchants
        </Link>
      </div>
    );
  }

  if (!service) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-yellow-400">⚠️</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Service not found. The service may have been deleted or you may not have permission to view it.
              </p>
            </div>
          </div>
        </div>
        <Link href={`/admin/merchants/service/${id}`} className="text-blue-600 hover:underline">
          ← Back to service provider
        </Link>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <Link href={`/admin/merchants/service/${id}`} className="text-blue-600 hover:underline">
          ← Back to service provider
        </Link>
      </div>

      <h1 className="text-2xl font-bold mb-6">Edit Service - {service.name}</h1>

      <ServiceForm
        merchantId={id}
        initialData={service}
        mode="edit"
        onSuccess={handleSuccess}
      />
    </div>
  );
}
