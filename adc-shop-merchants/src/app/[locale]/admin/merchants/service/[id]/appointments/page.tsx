'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import {
  useGetAppointmentsQuery,
  useGetServicesQuery,
  useGetStaffQuery,
  useCancelAppointmentMutation
} from '@/lib/redux/api/endpoints/serviceApi';

interface AppointmentsPageProps {
  params: {
    id: string;
  };
}

export default function AppointmentsPage({ params }: AppointmentsPageProps) {
  const { id } = params;
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [dateFilter, setDateFilter] = useState<string | null>(null);

  const { data: merchants, isLoading: isLoadingMerchants } = useGetMerchantsQuery({
    page: 1,
    limit: 50,
    sort_by: 'created_at',
    sort_order: 'desc'
  });
  const { data: appointments, isLoading: isLoadingAppointments } = useGetAppointmentsQuery(id);
  const { data: services, isLoading: isLoadingServices } = useGetServicesQuery(id);
  const { data: staff, isLoading: isLoadingStaff } = useGetStaffQuery(id);
  const [cancelAppointment, { isLoading: isCancelling }] = useCancelAppointmentMutation();

  const isLoading = isLoadingMerchants || isLoadingAppointments || isLoadingServices || isLoadingStaff;

  // Find the merchant
  const merchant = merchants?.find(m => m.id === id && m.type === 'service');

  // Filter appointments based on search term, status, and date
  const filteredAppointments = appointments?.filter(appointment => {
    const matchesSearch = searchTerm === '' ||
      appointment.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.customerEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.customerPhone?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === null || appointment.status === statusFilter;
    const matchesDate = dateFilter === null || appointment.date === dateFilter;

    return matchesSearch && matchesStatus && matchesDate;
  });

  // Get service name by ID
  const getServiceName = (serviceId: string) => {
    const service = services?.find(s => s.id === serviceId);
    return service ? service.name : 'Unknown Service';
  };

  // Get staff name by ID
  const getStaffName = (staffId?: string) => {
    if (!staffId) return 'Any Staff';
    const staffMember = staff?.find(s => s.id === staffId);
    return staffMember ? staffMember.name : 'Unknown Staff';
  };

  // Handle appointment cancellation
  const handleCancelAppointment = async (appointmentId: string) => {
    if (window.confirm('Are you sure you want to cancel this appointment?')) {
      try {
        await cancelAppointment({
          merchantId: id,
          appointmentId,
        }).unwrap();
      } catch (error) {
        console.error('Failed to cancel appointment:', error);
      }
    }
  };

  // Get unique dates from appointments
  const uniqueDates = appointments
    ? [...new Set(appointments.map(a => a.date))].sort()
    : [];

  if (isLoading) {
    return <div className="p-6">Loading data...</div>;
  }

  if (!merchant) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-yellow-400">⚠️</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Service provider not found. The merchant may have been deleted or you may not have permission to view it.
              </p>
            </div>
          </div>
        </div>
        <Link href="/admin/merchants" className="text-blue-600 hover:underline">
          ← Back to merchants
        </Link>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <Link href={`/admin/merchants/service/${id}`} className="text-blue-600 hover:underline">
          ← Back to service provider
        </Link>
      </div>

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Appointments - {merchant.name}</h1>
        <Link
          href={`/admin/merchants/service/${id}/appointments/create`}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Create Appointment
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
        <div className="p-4 border-b">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="relative flex-1">
              <input
                type="text"
                placeholder="Search appointments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <span className="absolute left-3 top-2.5">🔍</span>
            </div>

            <div className="flex gap-2">
              <select
                value={statusFilter || ''}
                onChange={(e) => setStatusFilter(e.target.value === '' ? null : e.target.value)}
                className="px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Statuses</option>
                <option value="scheduled">Scheduled</option>
                <option value="confirmed">Confirmed</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
                <option value="no-show">No Show</option>
              </select>

              <select
                value={dateFilter || ''}
                onChange={(e) => setDateFilter(e.target.value === '' ? null : e.target.value)}
                className="px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Dates</option>
                {uniqueDates.map((date) => (
                  <option key={date} value={date}>
                    {date}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Service
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Staff
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date & Time
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredAppointments && filteredAppointments.length > 0 ? (
                filteredAppointments.map((appointment) => (
                  <tr key={appointment.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{appointment.customerName}</div>
                      <div className="text-sm text-gray-500">{appointment.customerEmail}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {getServiceName(appointment.serviceId)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {getStaffName(appointment.staffId)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div>{appointment.date}</div>
                      <div>{appointment.startTime} - {appointment.endTime}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        appointment.status === 'scheduled'
                          ? 'bg-blue-100 text-blue-800'
                          : appointment.status === 'confirmed'
                          ? 'bg-green-100 text-green-800'
                          : appointment.status === 'completed'
                          ? 'bg-purple-100 text-purple-800'
                          : appointment.status === 'cancelled'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Link
                        href={`/admin/merchants/service/${id}/appointments/${appointment.id}/edit`}
                        className="text-indigo-600 hover:text-indigo-900 mr-4"
                      >
                        Edit
                      </Link>
                      {(appointment.status === 'scheduled' || appointment.status === 'confirmed') && (
                        <button
                          onClick={() => handleCancelAppointment(appointment.id!)}
                          disabled={isCancelling}
                          className="text-red-600 hover:text-red-900"
                        >
                          Cancel
                        </button>
                      )}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                    No appointments found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
