'use client';

import { useTranslations } from 'next-intl';

export default function PageContent() {
  // Specify the namespace explicitly with timeZone
  const t = useTranslations('app');

  return (
    <div className="min-h-screen flex flex-col">
      <header className="bg-card border-b border-border p-4">
        <div className="container mx-auto">
          <h1 className="text-xl font-bold text-foreground">{t('title')}</h1>
        </div>
      </header>

      <main className="flex-1 container mx-auto p-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold mb-6 text-foreground">{t('title')}</h1>
          <p className="text-lg mb-8 text-muted-foreground">{t('description')}</p>
        </div>
      </main>

      <footer className="bg-muted py-6 mt-12 border-t border-border">
        <div className="container mx-auto text-center">
          <p className="text-sm text-muted-foreground">
            © {new Date().getFullYear()}
          </p>
        </div>
      </footer>
    </div>
  );
}
