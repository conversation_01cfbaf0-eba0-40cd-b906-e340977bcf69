import type { Metadata } from "next";
import "@/app/globals.css";

export const metadata: Metadata = {
  title: "ADC Shop Merchants",
  description: "Manage your shop with ease",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Root layout should not render html/body when using locale routing
  // The [locale]/layout.tsx will handle the html/body tags
  return children;
}
