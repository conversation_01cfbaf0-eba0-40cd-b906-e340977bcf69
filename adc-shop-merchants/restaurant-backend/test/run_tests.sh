#!/bin/bash

# Thai Menu Slug Integration Tests
# This script runs the integration tests for Thai character support in menu item slugs

set -e

echo "🧪 Running Thai Menu Slug Integration Tests"
echo "==========================================="

# Change to the restaurant-backend directory
cd "$(dirname "$0")/.."

echo "📁 Current directory: $(pwd)"

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo "❌ Go is not installed or not in PATH"
    exit 1
fi

echo "✅ Go version: $(go version)"

# Download dependencies
echo "📦 Downloading dependencies..."
go mod download

# Run the slug generation unit tests
echo ""
echo "🔧 Running Slug Generation Unit Tests..."
echo "----------------------------------------"
go test -v ./test -run TestSlugify

# Run the Thai menu slug tests
echo ""
echo "🍜 Running Thai Menu Slug Tests..."
echo "----------------------------------"
go test -v ./test -run TestRunThaiMenuSlugTestSuite

# Run the menu API integration tests
echo ""
echo "🌐 Running Menu API Integration Tests..."
echo "---------------------------------------"
go test -v ./test -run TestRunMenuAPIIntegrationTestSuite

# Run benchmarks
echo ""
echo "⚡ Running Performance Benchmarks..."
echo "-----------------------------------"
go test -bench=BenchmarkSlugify ./test -benchmem

echo ""
echo "🎉 All tests completed!"
echo ""
echo "📋 Test Summary:"
echo "- ✅ Slug generation with Thai characters"
echo "- ✅ Menu item creation with Thai names"
echo "- ✅ API retrieval by Thai slugs"
echo "- ✅ Menu listing with mixed languages"
echo "- ✅ Error handling for invalid slugs"
echo "- ✅ Performance benchmarks"
echo ""
echo "🚀 Thai character support is working correctly!"
