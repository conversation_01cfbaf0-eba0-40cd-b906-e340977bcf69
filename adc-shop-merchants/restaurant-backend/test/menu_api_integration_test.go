package test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"restaurant-backend/internal/api/handlers"
	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/services"
	"restaurant-backend/internal/types"
)

// MenuAPIIntegrationTestSuite tests the menu API with Thai character support
type MenuAPIIntegrationTestSuite struct {
	suite.Suite
	db           *gorm.DB
	router       *gin.Engine
	testShopID   uuid.UUID
	testBranchID uuid.UUID
}

func (suite *MenuAPIIntegrationTestSuite) SetupSuite() {
	// Initialize in-memory SQLite database
	var err error
	suite.db, err = gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// Auto-migrate tables
	err = suite.db.AutoMigrate(
		&models.Shop{},
		&models.ShopBranch{},
		&models.MenuCategory{},
		&models.MenuItem{},
	)
	suite.Require().NoError(err)

	// Initialize logger
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	// Initialize repositories
	shopRepo := repositories.NewShopRepository(suite.db)
	categoryRepo := repositories.NewMenuCategoryRepository(suite.db)
	itemRepo := repositories.NewMenuItemRepository(suite.db)

	// Initialize services
	menuService := services.NewMenuService(categoryRepo, itemRepo, shopRepo, logger)

	// Initialize router
	gin.SetMode(gin.TestMode)
	suite.router = gin.New()
	
	// Initialize handlers
	menuHandler := handlers.NewMenuHandler(menuService, nil, logger)

	// Setup routes
	api := suite.router.Group("/api/v1")
	{
		shops := api.Group("/shops")
		{
			shops.GET("/slug/:slug/branches/slug/:branchSlug/menu", menuHandler.GetMenuItemsBySlug)
			shops.GET("/slug/:slug/branches/slug/:branchSlug/menu/:itemSlug", menuHandler.GetMenuItemByShopBranchSlug)
		}
		
		branches := api.Group("/branches/:branchId")
		{
			branches.POST("/menu", menuHandler.CreateMenuItem)
		}
	}
}

func (suite *MenuAPIIntegrationTestSuite) SetupTest() {
	// Clean up database
	suite.db.Exec("DELETE FROM menu_items")
	suite.db.Exec("DELETE FROM menu_categories")
	suite.db.Exec("DELETE FROM shop_branches")
	suite.db.Exec("DELETE FROM shops")

	// Create test shop
	shop := &models.Shop{
		Name:        "Test Thai Restaurant",
		Slug:        "test-thai-restaurant",
		Description: "A test restaurant for Thai menu items",
		IsActive:    true,
	}
	err := suite.db.Create(shop).Error
	suite.Require().NoError(err)
	suite.testShopID = shop.ID

	// Create test branch
	branch := &models.ShopBranch{
		ShopID:       shop.ID,
		Name:         "Main Branch",
		Slug:         "main-branch",
		Address:      "123 Thai Street, Bangkok",
		Phone:        "+66123456789",
		Email:        "<EMAIL>",
		IsActive:     true,
		IsMainBranch: true,
	}
	err = suite.db.Create(branch).Error
	suite.Require().NoError(err)
	suite.testBranchID = branch.ID
}

// TestCreateThaiMenuItemViaAPI tests creating Thai menu items via API
func (suite *MenuAPIIntegrationTestSuite) TestCreateThaiMenuItemViaAPI() {
	testCases := []struct {
		name         string
		menuItemName string
		expectedSlug string
	}{
		{
			name:         "Pure Thai name",
			menuItemName: "ผัดไทย",
			expectedSlug: "ผัดไทย",
		},
		{
			name:         "Mixed language name",
			menuItemName: "Green Curry แกงเขียวหวาน",
			expectedSlug: "green-curry-แกงเขียวหวาน",
		},
		{
			name:         "Thai with special characters",
			menuItemName: "ต้มยำกุ้ง (เผ็ด)",
			expectedSlug: "ต้มยำกุ้ง-เผ็ด",
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			// Create menu item via API
			createReq := types.CreateMenuItemRequest{
				Name:        tc.menuItemName,
				Description: "Delicious Thai dish",
				Price:       180.00,
				Images:      []string{"https://example.com/image.jpg"},
				IsAvailable: true,
			}

			jsonData, err := json.Marshal(createReq)
			suite.Require().NoError(err)

			url := fmt.Sprintf("/api/v1/branches/%s/menu", suite.testBranchID)
			req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
			suite.Require().NoError(err)
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			suite.router.ServeHTTP(w, req)

			suite.Assert().Equal(http.StatusCreated, w.Code)

			var createdItem types.MenuItemResponse
			err = json.Unmarshal(w.Body.Bytes(), &createdItem)
			suite.Require().NoError(err)

			suite.Assert().Equal(tc.menuItemName, createdItem.Name)
			suite.Assert().Equal(tc.expectedSlug, createdItem.Slug)

			// Test retrieving the item by slug
			getURL := fmt.Sprintf("/api/v1/shops/slug/test-thai-restaurant/branches/slug/main-branch/menu/%s", tc.expectedSlug)
			getReq, err := http.NewRequest("GET", getURL, nil)
			suite.Require().NoError(err)

			getW := httptest.NewRecorder()
			suite.router.ServeHTTP(getW, getReq)

			suite.Assert().Equal(http.StatusOK, getW.Code, "Failed to retrieve item with slug: %s", tc.expectedSlug)

			var retrievedItem types.MenuItemResponse
			err = json.Unmarshal(getW.Body.Bytes(), &retrievedItem)
			suite.Require().NoError(err)

			suite.Assert().Equal(tc.menuItemName, retrievedItem.Name)
			suite.Assert().Equal(tc.expectedSlug, retrievedItem.Slug)
		})
	}
}

// TestMenuListingWithThaiItems tests menu listing with Thai items
func (suite *MenuAPIIntegrationTestSuite) TestMenuListingWithThaiItems() {
	// Create multiple menu items with Thai names
	menuItems := []struct {
		name string
		slug string
	}{
		{"ผัดไทย", "ผัดไทย"},
		{"ต้มยำกุ้ง", "ต้มยำกุ้ง"},
		{"Hamburger", "hamburger"},
		{"ส้มตำ Som Tam", "ส้มตำ-som-tam"},
	}

	// Create items via direct database insertion for speed
	for _, item := range menuItems {
		menuItem := &models.MenuItem{
			BranchID:    suite.testBranchID,
			Name:        item.name,
			Slug:        item.slug,
			Description: "Test dish",
			Price:       150.00,
			IsAvailable: true,
		}
		err := suite.db.Create(menuItem).Error
		suite.Require().NoError(err)
	}

	// Test menu listing API
	url := "/api/v1/shops/slug/test-thai-restaurant/branches/slug/main-branch/menu"
	req, err := http.NewRequest("GET", url, nil)
	suite.Require().NoError(err)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	suite.Assert().Equal(http.StatusOK, w.Code)

	var response types.MenuItemsResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	suite.Require().NoError(err)

	suite.Assert().Equal(int64(4), response.Total)
	suite.Assert().Len(response.Data, 4)

	// Verify all items are present with correct slugs
	foundSlugs := make(map[string]bool)
	for _, item := range response.Data {
		foundSlugs[item.Slug] = true
	}

	for _, expectedItem := range menuItems {
		suite.Assert().True(foundSlugs[expectedItem.slug], 
			"Expected slug '%s' not found in response", expectedItem.slug)
	}
}

// TestThaiMenuItemNotFound tests 404 handling for non-existent Thai slugs
func (suite *MenuAPIIntegrationTestSuite) TestThaiMenuItemNotFound() {
	// Try to get a non-existent Thai menu item
	url := "/api/v1/shops/slug/test-thai-restaurant/branches/slug/main-branch/menu/ไม่มีอาหารนี้"
	req, err := http.NewRequest("GET", url, nil)
	suite.Require().NoError(err)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	suite.Assert().Equal(http.StatusNotFound, w.Code)
}

// TestInvalidShopOrBranchSlug tests error handling for invalid shop/branch slugs
func (suite *MenuAPIIntegrationTestSuite) TestInvalidShopOrBranchSlug() {
	// Create a menu item first
	menuItem := &models.MenuItem{
		BranchID:    suite.testBranchID,
		Name:        "ผัดไทย",
		Slug:        "ผัดไทย",
		Description: "Test dish",
		Price:       150.00,
		IsAvailable: true,
	}
	err := suite.db.Create(menuItem).Error
	suite.Require().NoError(err)

	// Test with invalid shop slug
	url := "/api/v1/shops/slug/invalid-shop/branches/slug/main-branch/menu/ผัดไทย"
	req, err := http.NewRequest("GET", url, nil)
	suite.Require().NoError(err)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	suite.Assert().Equal(http.StatusNotFound, w.Code)

	// Test with invalid branch slug
	url = "/api/v1/shops/slug/test-thai-restaurant/branches/slug/invalid-branch/menu/ผัดไทย"
	req, err = http.NewRequest("GET", url, nil)
	suite.Require().NoError(err)

	w = httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	suite.Assert().Equal(http.StatusNotFound, w.Code)
}

// TestRunMenuAPIIntegrationTestSuite runs the test suite
func TestRunMenuAPIIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(MenuAPIIntegrationTestSuite))
}
