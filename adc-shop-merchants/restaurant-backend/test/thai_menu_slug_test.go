package test

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/services"
	"restaurant-backend/internal/types"
)

// ThaiMenuSlugTestSuite tests Thai character support in menu item slugs
type ThaiMenuSlugTestSuite struct {
	suite.Suite
	db              *gorm.DB
	menuService     *services.MenuService
	aiService       *services.AIGenerationService
	testShopID      uuid.UUID
	testBranchID    uuid.UUID
}

func (suite *ThaiMenuSlugTestSuite) SetupSuite() {
	// Initialize in-memory SQLite database for testing
	var err error
	suite.db, err = gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// Auto-migrate tables
	err = suite.db.AutoMigrate(
		&models.Shop{},
		&models.ShopBranch{},
		&models.User{},
		&models.MenuCategory{},
		&models.MenuItem{},
		&models.AIGenerationJob{},
	)
	suite.Require().NoError(err)

	// Initialize logger
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel) // Reduce noise in tests

	// Initialize repositories
	shopRepo := repositories.NewShopRepository(suite.db)
	categoryRepo := repositories.NewMenuCategoryRepository(suite.db)
	itemRepo := repositories.NewMenuItemRepository(suite.db)
	aiRepo := repositories.NewAIGenerationRepository(suite.db)

	// Initialize services
	suite.menuService = services.NewMenuService(categoryRepo, itemRepo, shopRepo, logger)
	suite.aiService = services.NewAIGenerationService(aiRepo, suite.menuService, nil, nil, logger)
}

func (suite *ThaiMenuSlugTestSuite) SetupTest() {
	// Clean up database
	suite.db.Exec("DELETE FROM menu_items")
	suite.db.Exec("DELETE FROM menu_categories")
	suite.db.Exec("DELETE FROM ai_generation_jobs")
	suite.db.Exec("DELETE FROM shop_branches")
	suite.db.Exec("DELETE FROM shops")

	// Create test shop
	shop := &models.Shop{
		Name:        "Test Thai Restaurant",
		Slug:        "test-thai-restaurant",
		Description: "A test restaurant for Thai menu items",
		IsActive:    true,
	}
	err := suite.db.Create(shop).Error
	suite.Require().NoError(err)
	suite.testShopID = shop.ID

	// Create test branch
	branch := &models.ShopBranch{
		ShopID:       shop.ID,
		Name:         "Main Branch",
		Slug:         "main-branch",
		Address:      "123 Thai Street, Bangkok",
		Phone:        "+66123456789",
		Email:        "<EMAIL>",
		IsActive:     true,
		IsMainBranch: true,
	}
	err = suite.db.Create(branch).Error
	suite.Require().NoError(err)
	suite.testBranchID = branch.ID
}

// TestThaiCharacterSlugGeneration tests that Thai characters are preserved in slugs
func (suite *ThaiMenuSlugTestSuite) TestThaiCharacterSlugGeneration() {
	ctx := context.Background()

	testCases := []struct {
		name         string
		expectedSlug string
		description  string
	}{
		{
			name:         "ผัดไทย",
			expectedSlug: "ผัดไทย",
			description:  "Traditional Thai stir-fried noodles",
		},
		{
			name:         "ต้มยำกุ้ง",
			expectedSlug: "ต้มยำกุ้ง",
			description:  "Spicy shrimp soup",
		},
		{
			name:         "แกงเขียวหวาน",
			expectedSlug: "แกงเขียวหวาน",
			description:  "Green curry with coconut milk",
		},
		{
			name:         "Pad Thai ผัดไทย",
			expectedSlug: "pad-thai-ผัดไทย",
			description:  "Mixed language menu item",
		},
		{
			name:         "Som Tam (ส้มตำ)",
			expectedSlug: "som-tam-ส้มตำ",
			description:  "Papaya salad with parentheses",
		},
		{
			name:         "ข้าวผัด Special!",
			expectedSlug: "ข้าวผัด-special",
			description:  "Fried rice with special characters",
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			// Create menu item
			req := types.CreateMenuItemRequest{
				Name:        tc.name,
				Description: tc.description,
				Price:       150.00,
				Images:      []string{"https://example.com/image.jpg"},
				IsAvailable: true,
			}

			menuItem, err := suite.menuService.CreateMenuItem(ctx, suite.testBranchID, req)
			suite.Require().NoError(err)
			
			// Verify slug generation
			suite.Assert().Equal(tc.expectedSlug, menuItem.Slug, 
				"Expected slug '%s' for name '%s', got '%s'", tc.expectedSlug, tc.name, menuItem.Slug)

			// Verify we can retrieve the item by slug
			retrievedItem, err := suite.menuService.GetMenuItemByShopBranchSlug(
				ctx, "test-thai-restaurant", "main-branch", tc.expectedSlug,
			)
			suite.Require().NoError(err)
			suite.Assert().Equal(tc.name, retrievedItem.Name)
			suite.Assert().Equal(tc.expectedSlug, retrievedItem.Slug)
		})
	}
}

// TestAIGeneratedMenuItemSlugs tests AI-generated menu items with Thai names
func (suite *ThaiMenuSlugTestSuite) TestAIGeneratedMenuItemSlugs() {
	ctx := context.Background()

	// Simulate AI-generated menu items with Thai names
	aiMenuItems := []models.AIGeneratedMenuItem{
		{
			Name:            "ผัดไทย",
			Description:     "Traditional Thai stir-fried rice noodles",
			Price:           180.00,
			CategoryName:    "อาหารจานหลัก", // Main dishes in Thai
			ImageURL:        "https://example.com/pad-thai.jpg",
			Ingredients:     []string{"rice noodles", "shrimp", "tofu"},
			IsVegetarian:    false,
			IsSpicy:         true,
			SpiceLevel:      2,
			PreparationTime: 15,
			Tags:            []string{"noodles", "traditional"},
		},
		{
			Name:            "ต้มยำกุ้ง",
			Description:     "Spicy and sour soup with shrimp",
			Price:           220.00,
			CategoryName:    "ซุป", // Soup in Thai
			ImageURL:        "https://example.com/tom-yum.jpg",
			Ingredients:     []string{"shrimp", "mushrooms", "lemongrass"},
			IsVegetarian:    false,
			IsSpicy:         true,
			SpiceLevel:      3,
			PreparationTime: 20,
			Tags:            []string{"soup", "spicy"},
		},
	}

	// Test publishing AI-generated menu items
	publishReq := types.PublishMenuRequest{
		JobID:     uuid.New(),
		BranchID:  suite.testBranchID,
		MenuItems: aiMenuItems,
	}

	err := suite.aiService.PublishMenu(ctx, publishReq)
	suite.Require().NoError(err)

	// Verify menu items were created with proper Thai slugs
	expectedResults := []struct {
		name string
		slug string
	}{
		{"ผัดไทย", "ผัดไทย"},
		{"ต้มยำกุ้ง", "ต้มยำกุ้ง"},
	}

	for _, expected := range expectedResults {
		// Retrieve by slug
		retrievedItem, err := suite.menuService.GetMenuItemByShopBranchSlug(
			ctx, "test-thai-restaurant", "main-branch", expected.slug,
		)
		suite.Require().NoError(err, "Failed to retrieve item with slug: %s", expected.slug)
		suite.Assert().Equal(expected.name, retrievedItem.Name)
		suite.Assert().Equal(expected.slug, retrievedItem.Slug)
	}
}

// TestSlugUniqueness tests that duplicate names generate unique slugs
func (suite *ThaiMenuSlugTestSuite) TestSlugUniqueness() {
	ctx := context.Background()

	// Create first menu item
	req1 := types.CreateMenuItemRequest{
		Name:        "ผัดไทย",
		Description: "First Pad Thai",
		Price:       150.00,
		IsAvailable: true,
	}

	item1, err := suite.menuService.CreateMenuItem(ctx, suite.testBranchID, req1)
	suite.Require().NoError(err)
	suite.Assert().Equal("ผัดไทย", item1.Slug)

	// Create second menu item with same name
	req2 := types.CreateMenuItemRequest{
		Name:        "ผัดไทย",
		Description: "Second Pad Thai",
		Price:       180.00,
		IsAvailable: true,
	}

	item2, err := suite.menuService.CreateMenuItem(ctx, suite.testBranchID, req2)
	suite.Require().NoError(err)
	
	// Second item should have a different slug (implementation dependent)
	suite.Assert().NotEqual(item1.Slug, item2.Slug, "Duplicate slugs should not be allowed")
	
	// Both items should be retrievable
	retrieved1, err := suite.menuService.GetMenuItemByShopBranchSlug(
		ctx, "test-thai-restaurant", "main-branch", item1.Slug,
	)
	suite.Require().NoError(err)
	suite.Assert().Equal("First Pad Thai", retrieved1.Description)

	retrieved2, err := suite.menuService.GetMenuItemByShopBranchSlug(
		ctx, "test-thai-restaurant", "main-branch", item2.Slug,
	)
	suite.Require().NoError(err)
	suite.Assert().Equal("Second Pad Thai", retrieved2.Description)
}

// TestRunThaiMenuSlugTestSuite runs the test suite
func TestRunThaiMenuSlugTestSuite(t *testing.T) {
	suite.Run(t, new(ThaiMenuSlugTestSuite))
}
