package test

import (
	"testing"

	"restaurant-backend/internal/services"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

// AIImageUploadTestSuite tests AI image upload functionality
type AIImageUploadTestSuite struct {
	suite.Suite
	aiService *services.AIGenerationService
}

func (suite *AIImageUploadTestSuite) SetupSuite() {
	// This is a unit test for the helper function only
	// We don't need full service initialization for this test
	suite.aiService = &services.AIGenerationService{}
}

// TestIsExternalImageURL tests the external image URL detection
func (suite *AIImageUploadTestSuite) TestIsExternalImageURL() {
	testCases := []struct {
		name     string
		url      string
		expected bool
	}{
		{
			name:     "OpenAI DALL-E URL",
			url:      "https://oaidalleapiprodscus.blob.core.windows.net/private/org-TaXGfZxU9GbLQGz0gwzOgsSF/user-k3X3tLQr4hvhyz0gsXG9A15I/img-1P89yxXS3N8EgdOHXg7e0VrE.png",
			expected: true,
		},
		{
			name:     "OpenAI CDN URL",
			url:      "https://cdn.openai.com/images/food/pad-thai.jpg",
			expected: true,
		},
		{
			name:     "Unsplash URL",
			url:      "https://images.unsplash.com/photo-1234567890/food.jpg",
			expected: true,
		},
		{
			name:     "Placeholder URL",
			url:      "https://via.placeholder.com/300x200/food.jpg",
			expected: true,
		},
		{
			name:     "Lorem Picsum URL",
			url:      "https://picsum.photos/300/200",
			expected: true,
		},
		{
			name:     "Example.com URL",
			url:      "https://example.com/images/food.jpg",
			expected: true,
		},
		{
			name:     "Google Cloud Storage URL",
			url:      "https://storage.googleapis.com/my-bucket/images/food.jpg",
			expected: false,
		},
		{
			name:     "Google Cloud Storage alternative URL",
			url:      "https://storage.cloud.google.com/my-bucket/images/food.jpg",
			expected: false,
		},
		{
			name:     "Local relative URL",
			url:      "/images/food.jpg",
			expected: false,
		},
		{
			name:     "Local absolute URL",
			url:      "images/food.jpg",
			expected: false,
		},
		{
			name:     "Empty URL",
			url:      "",
			expected: false,
		},
		{
			name:     "Random external URL",
			url:      "https://random-domain.com/image.jpg",
			expected: true,
		},
		{
			name:     "HTTP URL",
			url:      "http://external-site.com/image.jpg",
			expected: true,
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			result := suite.aiService.IsExternalImageURL(tc.url)
			suite.Assert().Equal(tc.expected, result,
				"IsExternalImageURL(%q) = %t, expected %t", tc.url, result, tc.expected)
		})
	}
}

// TestImageUploadFlow tests the conceptual flow of image upload
func (suite *AIImageUploadTestSuite) TestImageUploadFlow() {
	// Test the logic flow without actual HTTP calls
	testCases := []struct {
		name         string
		imageURL     string
		shouldUpload bool
	}{
		{
			name:         "OpenAI DALL-E image should be uploaded",
			imageURL:     "https://oaidalleapiprodscus.blob.core.windows.net/private/org-TaXGfZxU9GbLQGz0gwzOgsSF/user-k3X3tLQr4hvhyz0gsXG9A15I/img-1P89yxXS3N8EgdOHXg7e0VrE.png?st=2025-06-02T03%3A53%3A25Z&se=2025-06-02T05%3A53%3A25Z&sp=r&sv=2024-08-04&sr=b&rscd=inline&rsct=image/png",
			shouldUpload: true,
		},
		{
			name:         "GCS image should not be uploaded",
			imageURL:     "https://storage.googleapis.com/my-bucket/ai-generated/shop123/branch456/image.jpg",
			shouldUpload: false,
		},
		{
			name:         "Local image should not be uploaded",
			imageURL:     "/uploads/images/food.jpg",
			shouldUpload: false,
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			isExternal := suite.aiService.IsExternalImageURL(tc.imageURL)
			suite.Assert().Equal(tc.shouldUpload, isExternal,
				"Image upload decision for %s should be %t", tc.imageURL, tc.shouldUpload)
		})
	}
}

// TestRunAIImageUploadTestSuite runs the test suite
func TestRunAIImageUploadTestSuite(t *testing.T) {
	suite.Run(t, new(AIImageUploadTestSuite))
}

// TestImageURLParsing tests various URL formats that might come from AI providers
func TestImageURLParsing(t *testing.T) {
	aiService := &services.AIGenerationService{}

	// Test the exact URL format from your example
	dalleURL := "https://oaidalleapiprodscus.blob.core.windows.net/private/org-TaXGfZxU9GbLQGz0gwzOgsSF/user-k3X3tLQr4hvhyz0gsXG9A15I/img-1P89yxXS3N8EgdOHXg7e0VrE.png?st=2025-06-02T03%3A53%3A25Z&se=2025-06-02T05%3A53%3A25Z&sp=r&sv=2024-08-04&sr=b&rscd=inline&rsct=image/png&skoid=475fd488-6c59-44a5-9aa9-31c4db451bea&sktid=a48cca56-e6da-484e-a814-9c849652bcb3&skt=2025-06-02T01%3A45%3A38Z&ske=2025-06-03T01%3A45%3A38Z&sks=b&skv=2024-08-04&sig=17XCJXdxmYG%2BVgoytnEl2KwOr1APmFxoF5IGTyHMR5k%3D"

	assert.True(t, aiService.IsExternalImageURL(dalleURL),
		"DALL-E URL should be detected as external")

	// Test various other formats
	testURLs := map[string]bool{
		"https://cdn.openai.com/images/test.jpg":             true,  // OpenAI CDN
		"https://storage.googleapis.com/bucket/image.jpg":    false, // Our GCS
		"https://storage.cloud.google.com/bucket/image.jpg":  false, // Our GCS alt
		"https://images.unsplash.com/photo-123/food.jpg":     true,  // Unsplash
		"https://via.placeholder.com/300x200.png":            true,  // Placeholder
		"https://picsum.photos/300/200":                      true,  // Lorem Picsum
		"https://example.com/test.jpg":                       true,  // Example domain
		"https://random-ai-provider.com/generated/image.png": true,  // Random external
		"/local/path/image.jpg":                              false, // Local path
		"images/food.jpg":                                    false, // Relative path
		"":                                                   false, // Empty
	}

	for url, expected := range testURLs {
		result := aiService.IsExternalImageURL(url)
		assert.Equal(t, expected, result,
			"URL %s should return %t but got %t", url, expected, result)
	}
}

// BenchmarkIsExternalImageURL benchmarks the URL detection function
func BenchmarkIsExternalImageURL(b *testing.B) {
	aiService := &services.AIGenerationService{}
	testURLs := []string{
		"https://oaidalleapiprodscus.blob.core.windows.net/private/org-TaXGfZxU9GbLQGz0gwzOgsSF/user-k3X3tLQr4hvhyz0gsXG9A15I/img-1P89yxXS3N8EgdOHXg7e0VrE.png",
		"https://storage.googleapis.com/bucket/image.jpg",
		"https://cdn.openai.com/images/test.jpg",
		"/local/path/image.jpg",
		"https://random-domain.com/image.png",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		for _, url := range testURLs {
			aiService.IsExternalImageURL(url)
		}
	}
}
