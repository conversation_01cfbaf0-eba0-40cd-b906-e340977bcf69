package integration

import (
	"context"
	"os"
	"testing"
	"time"

	"restaurant-backend/internal/config"
	"restaurant-backend/internal/services"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

type AIGenerationSimpleTestSuite struct {
	suite.Suite
	openaiService *services.OpenAIService
	geminiService *services.GeminiService
	ctx           context.Context
}

func (suite *AIGenerationSimpleTestSuite) SetupSuite() {
	// Setup logger
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	suite.ctx = context.Background()

	// Setup OpenAI service (if API key available)
	openaiConfig := &config.OpenAIConfig{
		APIKey:      os.Getenv("OPENAI_API_KEY"),
		Model:       "gpt-4-vision-preview",
		MaxTokens:   4096,
		Temperature: 0.7,
	}
	if openaiConfig.APIKey != "" {
		suite.openaiService = services.NewOpenAIService(openaiConfig, logger)
	}

	// Setup Gemini service (if API key available)
	geminiConfig := &config.GeminiConfig{
		APIKey:      os.Getenv("GEMINI_API_KEY"),
		ProjectID:   getEnvOrDefault("GEMINI_PROJECT_ID", "scandine-457107"),
		Location:    getEnvOrDefault("GEMINI_LOCATION", "us-central1"),
		Model:       "gemini-1.5-pro",
		MaxTokens:   4096,
		Temperature: 0.7,
		ImageModel:  "imagen-3.0-generate-001",
	}
	if geminiConfig.APIKey != "" {
		var err error
		suite.geminiService, err = services.NewGeminiService(geminiConfig, logger)
		require.NoError(suite.T(), err, "Failed to create Gemini service")
	}
}

func (suite *AIGenerationSimpleTestSuite) TearDownSuite() {
	if suite.geminiService != nil {
		suite.geminiService.Close()
	}
}

func (suite *AIGenerationSimpleTestSuite) TestProviderComparison() {
	if suite.geminiService == nil {
		suite.T().Skip("GEMINI_API_KEY not set, skipping provider comparison tests")
	}

	menuText := "Pad Thai ผัดไทย $12, Green Curry แกงเขียวหวาน $14, Tom Yum Soup ต้มยำกุ้ง $10"
	cuisineType := "Thai"
	priceRange := "mid-range"
	restaurantName := "Thai Garden"

	ctx, cancel := context.WithTimeout(suite.ctx, 60*time.Second)
	defer cancel()

	// Test Gemini text enhancement
	geminiItems, err := suite.geminiService.EnhanceMenuText(ctx, menuText, cuisineType, priceRange, restaurantName)
	assert.NoError(suite.T(), err, "Gemini text enhancement should not return error")
	assert.NotEmpty(suite.T(), geminiItems, "Gemini should return menu items")

	// Validate Gemini results
	for _, item := range geminiItems {
		assert.NotEmpty(suite.T(), item.Name, "Gemini menu item should have a name")
		assert.NotEmpty(suite.T(), item.Description, "Gemini menu item should have a description")
		assert.Greater(suite.T(), item.Price, 0.0, "Gemini menu item should have a positive price")
		assert.NotEmpty(suite.T(), item.Category, "Gemini menu item should have a category")
	}

	// Test OpenAI if available
	if suite.openaiService != nil {
		openaiItems, err := suite.openaiService.EnhanceMenuText(ctx, menuText, cuisineType, priceRange, restaurantName)
		assert.NoError(suite.T(), err, "OpenAI text enhancement should not return error")
		assert.NotEmpty(suite.T(), openaiItems, "OpenAI should return menu items")

		// Compare results
		suite.T().Logf("Gemini returned %d items, OpenAI returned %d items", len(geminiItems), len(openaiItems))

		// Both should return similar number of items (within reasonable range)
		assert.InDelta(suite.T(), len(geminiItems), len(openaiItems), 2, "Both providers should return similar number of items")
	}
}

func (suite *AIGenerationSimpleTestSuite) TestImageGeneration() {
	if suite.geminiService == nil {
		suite.T().Skip("GEMINI_API_KEY not set, skipping image generation tests")
	}

	dishName := "Pad Thai ผัดไทย"
	description := "Traditional Thai stir-fried rice noodles with shrimp, tofu, and bean sprouts"
	cuisineType := "Thai"
	style := "realistic"
	theme := "restaurant"
	quality := "high"
	size := "1024x1024"

	ctx, cancel := context.WithTimeout(suite.ctx, 30*time.Second)
	defer cancel()

	// Test Gemini image generation (placeholder)
	geminiImageURL, err := suite.geminiService.GenerateFoodImageWithOptions(ctx, dishName, description, cuisineType, style, theme, quality, size)
	assert.NoError(suite.T(), err, "Gemini image generation should not return error")
	assert.NotEmpty(suite.T(), geminiImageURL, "Gemini should return an image URL")
	assert.Contains(suite.T(), geminiImageURL, "placeholder", "Should return placeholder URL")

	// Test OpenAI if available
	if suite.openaiService != nil {
		openaiImageURL, err := suite.openaiService.GenerateFoodImageWithOptions(ctx, dishName, description, cuisineType, style, theme, quality, size)
		assert.NoError(suite.T(), err, "OpenAI image generation should not return error")
		assert.NotEmpty(suite.T(), openaiImageURL, "OpenAI should return an image URL")

		suite.T().Logf("Gemini image URL: %s", geminiImageURL)
		suite.T().Logf("OpenAI image URL: %s", openaiImageURL)
	}
}

func (suite *AIGenerationSimpleTestSuite) TestMenuImageAnalysis() {
	if suite.geminiService == nil {
		suite.T().Skip("GEMINI_API_KEY not set, skipping menu image analysis tests")
	}

	imageURL := "https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=800"
	cuisineType := "Italian"
	priceRange := "mid-range"
	restaurantName := "Test Restaurant"

	ctx, cancel := context.WithTimeout(suite.ctx, 45*time.Second)
	defer cancel()

	// Test Gemini menu image analysis
	geminiItems, err := suite.geminiService.AnalyzeMenuImage(ctx, imageURL, cuisineType, priceRange, restaurantName)
	assert.NoError(suite.T(), err, "Gemini menu image analysis should not return error")
	assert.NotEmpty(suite.T(), geminiItems, "Gemini should return menu items from image")

	// Test OpenAI if available
	if suite.openaiService != nil {
		openaiItems, err := suite.openaiService.AnalyzeMenuImage(ctx, imageURL, cuisineType, priceRange, restaurantName)
		assert.NoError(suite.T(), err, "OpenAI menu image analysis should not return error")
		assert.NotEmpty(suite.T(), openaiItems, "OpenAI should return menu items from image")

		suite.T().Logf("Gemini found %d items, OpenAI found %d items", len(geminiItems), len(openaiItems))
	}
}

func (suite *AIGenerationSimpleTestSuite) TestFoodImageAnalysis() {
	if suite.geminiService == nil {
		suite.T().Skip("GEMINI_API_KEY not set, skipping food image analysis tests")
	}

	imageURLs := []string{
		"https://images.unsplash.com/photo-1551782450-a2132b4ba21d?w=800", // Burger
	}
	cuisineType := "American"
	priceRange := "affordable"
	restaurantName := "Test Diner"

	ctx, cancel := context.WithTimeout(suite.ctx, 45*time.Second)
	defer cancel()

	// Test Gemini food images analysis
	geminiItems, err := suite.geminiService.AnalyzeFoodImages(ctx, imageURLs, cuisineType, priceRange, restaurantName)
	assert.NoError(suite.T(), err, "Gemini food images analysis should not return error")
	assert.NotEmpty(suite.T(), geminiItems, "Gemini should return menu items from food images")

	// Test OpenAI if available
	if suite.openaiService != nil {
		openaiItems, err := suite.openaiService.AnalyzeFoodImages(ctx, imageURLs, cuisineType, priceRange, restaurantName)
		assert.NoError(suite.T(), err, "OpenAI food images analysis should not return error")
		assert.NotEmpty(suite.T(), openaiItems, "OpenAI should return menu items from food images")

		suite.T().Logf("Gemini analyzed %d items, OpenAI analyzed %d items", len(geminiItems), len(openaiItems))
	}
}

func (suite *AIGenerationSimpleTestSuite) TestThaiLanguageSupport() {
	if suite.geminiService == nil {
		suite.T().Skip("GEMINI_API_KEY not set, skipping Thai language tests")
	}

	// Test with Thai menu text
	thaiMenuText := `
	ผัดไทย - ก๋วยเตี๋ยวผัดไทยแท้ๆ - 180 บาท
	ต้มยำกุ้ง - ซุปรสเปรี้ยวเผ็ดกับกุ้งสด - 220 บาท
	แกงเขียวหวานไก่ - แกงเขียวหวานไก่แท้รสชาติดั้งเดิม - 200 บาท
	`
	cuisineType := "Thai"
	priceRange := "mid-range"
	restaurantName := "ร้านอาหารไทยแท้"

	ctx, cancel := context.WithTimeout(suite.ctx, 60*time.Second)
	defer cancel()

	menuItems, err := suite.geminiService.EnhanceMenuText(ctx, thaiMenuText, cuisineType, priceRange, restaurantName)
	assert.NoError(suite.T(), err, "Should handle Thai language text")
	assert.NotEmpty(suite.T(), menuItems, "Should return menu items for Thai text")

	// Validate that Thai names are preserved or properly handled
	for _, item := range menuItems {
		assert.NotEmpty(suite.T(), item.Name, "Menu item should have a name")
		assert.NotEmpty(suite.T(), item.Description, "Menu item should have a description")
		assert.Greater(suite.T(), item.Price, 0.0, "Menu item should have a positive price")

		suite.T().Logf("Thai menu item: %s - %s (%.2f)", item.Name, item.Description, item.Price)
	}
}

// getEnvOrDefault is defined in test_config.go

func TestAIGenerationSimpleTestSuite(t *testing.T) {
	suite.Run(t, new(AIGenerationSimpleTestSuite))
}
