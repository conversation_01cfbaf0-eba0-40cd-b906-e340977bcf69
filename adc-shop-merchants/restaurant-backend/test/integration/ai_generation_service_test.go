package integration

import (
	"context"
	"os"
	"testing"
	"time"

	"restaurant-backend/internal/config"
	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/services"
	"restaurant-backend/internal/types"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

type AIGenerationServiceTestSuite struct {
	suite.Suite
	db                  *gorm.DB
	aiGenerationService *services.AIGenerationService
	openaiService       *services.OpenAIService
	geminiService       *services.GeminiService
	ctx                 context.Context
	testBranchID        uuid.UUID
	testUserID          uuid.UUID
}

func (suite *AIGenerationServiceTestSuite) SetupSuite() {
	// Setup in-memory SQLite database for testing
	var err error
	suite.db, err = gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(suite.T(), err, "Failed to connect to test database")

	// Auto-migrate only the essential schema for AI generation tests
	err = suite.db.AutoMigrate(&models.AIGenerationJob{})
	if err != nil {
		// If migration fails, skip the test
		suite.T().Skip("Failed to migrate test database, skipping AI generation service tests")
	}

	// Setup logger
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// Setup test IDs
	suite.testBranchID = uuid.New()
	suite.testUserID = uuid.New()

	// Setup OpenAI service (if API key available)
	openaiConfig := &config.OpenAIConfig{
		APIKey:      os.Getenv("OPENAI_API_KEY"),
		Model:       "gpt-4-vision-preview",
		MaxTokens:   4096,
		Temperature: 0.7,
	}
	if openaiConfig.APIKey != "" {
		suite.openaiService = services.NewOpenAIService(openaiConfig, logger)
	}

	// Setup Gemini service (if API key available)
	geminiConfig := &config.GeminiConfig{
		APIKey:      os.Getenv("GEMINI_API_KEY"),
		ProjectID:   getEnvOrDefault("GEMINI_PROJECT_ID", "scandine-457107"),
		Location:    getEnvOrDefault("GEMINI_LOCATION", "us-central1"),
		Model:       "gemini-1.5-pro",
		MaxTokens:   4096,
		Temperature: 0.7,
		ImageModel:  "imagen-3.0-generate-001",
	}
	if geminiConfig.APIKey != "" {
		suite.geminiService, err = services.NewGeminiService(geminiConfig, logger)
		require.NoError(suite.T(), err, "Failed to create Gemini service")
	}

	// Setup repositories
	aiRepo := repositories.NewAIGenerationRepository(suite.db)
	menuService := &services.MenuService{} // Mock menu service

	// Setup AI Generation Service
	suite.aiGenerationService = services.NewAIGenerationService(
		aiRepo,
		menuService,
		suite.openaiService,
		suite.geminiService,
		nil, // storage service
		nil, // websocket hub
		logger,
	)

	suite.ctx = context.Background()
}

func (suite *AIGenerationServiceTestSuite) TearDownSuite() {
	if suite.geminiService != nil {
		suite.geminiService.Close()
	}
}

func (suite *AIGenerationServiceTestSuite) TestCreateJobWithOpenAI() {
	if suite.openaiService == nil {
		suite.T().Skip("OpenAI API key not set, skipping OpenAI tests")
	}

	req := types.CreateAIGenerationJobRequest{
		Type: "text",
		InputData: types.AIGenerationInputDataRequest{
			MenuText:       "Margherita Pizza $12, Caesar Salad $8, Chicken Alfredo $16",
			CuisineType:    "Italian",
			PriceRange:     "mid-range",
			RestaurantName: "Test Restaurant",
			AIProvider:     "openai",
			GenerateImages: false,
		},
	}

	job, err := suite.aiGenerationService.CreateJob(suite.ctx, suite.testBranchID, suite.testUserID, req)

	assert.NoError(suite.T(), err, "Should create job successfully")
	assert.NotNil(suite.T(), job, "Should return job")
	assert.Equal(suite.T(), "openai", job.InputData.AIProvider, "Should use OpenAI provider")
	assert.Equal(suite.T(), "text", job.Type, "Should be text type")
	assert.Equal(suite.T(), "pending", job.Status, "Should start as pending")
}

func (suite *AIGenerationServiceTestSuite) TestCreateJobWithGemini() {
	if suite.geminiService == nil {
		suite.T().Skip("Gemini API key not set, skipping Gemini tests")
	}

	req := types.CreateAIGenerationJobRequest{
		Type: "text",
		InputData: types.AIGenerationInputDataRequest{
			MenuText:       "Pad Thai $10, Green Curry $12, Tom Yum Soup $8",
			CuisineType:    "Thai",
			PriceRange:     "affordable",
			RestaurantName: "Thai Garden",
			AIProvider:     "gemini",
			GenerateImages: false,
		},
	}

	job, err := suite.aiGenerationService.CreateJob(suite.ctx, suite.testBranchID, suite.testUserID, req)

	assert.NoError(suite.T(), err, "Should create job successfully")
	assert.NotNil(suite.T(), job, "Should return job")
	assert.Equal(suite.T(), "gemini", job.InputData.AIProvider, "Should use Gemini provider")
	assert.Equal(suite.T(), "text", job.Type, "Should be text type")
	assert.Equal(suite.T(), "pending", job.Status, "Should start as pending")
}

func (suite *AIGenerationServiceTestSuite) TestProcessJobWithGemini() {
	if suite.geminiService == nil {
		suite.T().Skip("Gemini API key not set, skipping Gemini processing tests")
	}

	// Create a job
	req := types.CreateAIGenerationJobRequest{
		Type: "text",
		InputData: types.AIGenerationInputDataRequest{
			MenuText:       "Spaghetti Carbonara $14, Margherita Pizza $12, Tiramisu $6",
			CuisineType:    "Italian",
			PriceRange:     "mid-range",
			RestaurantName: "Bella Italia",
			AIProvider:     "gemini",
			GenerateImages: false,
		},
	}

	job, err := suite.aiGenerationService.CreateJob(suite.ctx, suite.testBranchID, suite.testUserID, req)
	require.NoError(suite.T(), err, "Should create job successfully")

	// Process the job
	ctx, cancel := context.WithTimeout(suite.ctx, 60*time.Second)
	defer cancel()

	err = suite.aiGenerationService.ProcessJobAsync(ctx, job.ID)
	assert.NoError(suite.T(), err, "Should process job successfully")

	// Retrieve the processed job
	processedJob, err := suite.aiGenerationService.GetJob(suite.ctx, job.ID)
	require.NoError(suite.T(), err, "Should retrieve processed job")

	assert.Equal(suite.T(), "completed", processedJob.Status, "Job should be completed")
	assert.Equal(suite.T(), 100, processedJob.Progress, "Job should be 100% complete")
	assert.NotEmpty(suite.T(), processedJob.OutputData.MenuItems, "Should have generated menu items")

	// Validate generated menu items
	menuItems := processedJob.OutputData.MenuItems
	assert.GreaterOrEqual(suite.T(), len(menuItems), 2, "Should generate multiple menu items")

	for _, item := range menuItems {
		assert.NotEmpty(suite.T(), item.Name, "Menu item should have a name")
		assert.NotEmpty(suite.T(), item.Description, "Menu item should have a description")
		assert.Greater(suite.T(), item.Price, 0.0, "Menu item should have a positive price")
		assert.NotEmpty(suite.T(), item.Category, "Menu item should have a category")
	}
}

func (suite *AIGenerationServiceTestSuite) TestProcessJobWithImageGeneration() {
	if suite.geminiService == nil {
		suite.T().Skip("Gemini API key not set, skipping image generation tests")
	}

	// Create a job with image generation enabled
	req := types.CreateAIGenerationJobRequest{
		Type: "text",
		InputData: types.AIGenerationInputDataRequest{
			MenuText:       "Beef Burger $15, Fish and Chips $12",
			CuisineType:    "American",
			PriceRange:     "mid-range",
			RestaurantName: "American Diner",
			AIProvider:     "gemini",
			GenerateImages: true,
			ImageStyle:     "realistic",
			ImageTheme:     "restaurant",
			ImageQuality:   "high",
			ImageSize:      "1024x1024",
		},
	}

	job, err := suite.aiGenerationService.CreateJob(suite.ctx, suite.testBranchID, suite.testUserID, req)
	require.NoError(suite.T(), err, "Should create job successfully")

	// Process the job
	ctx, cancel := context.WithTimeout(suite.ctx, 90*time.Second)
	defer cancel()

	err = suite.aiGenerationService.ProcessJobAsync(ctx, job.ID)
	assert.NoError(suite.T(), err, "Should process job with image generation")

	// Retrieve the processed job
	processedJob, err := suite.aiGenerationService.GetJob(suite.ctx, job.ID)
	require.NoError(suite.T(), err, "Should retrieve processed job")

	assert.Equal(suite.T(), "completed", processedJob.Status, "Job should be completed")
	assert.NotEmpty(suite.T(), processedJob.OutputData.MenuItems, "Should have generated menu items")

	// Check that images were generated (placeholder URLs)
	for _, item := range processedJob.OutputData.MenuItems {
		assert.NotEmpty(suite.T(), item.ImageURL, "Menu item should have an image URL")
	}
}

func (suite *AIGenerationServiceTestSuite) TestProviderFallback() {
	// Test with invalid provider
	req := types.CreateAIGenerationJobRequest{
		Type: "text",
		InputData: types.AIGenerationInputDataRequest{
			MenuText:       "Test Menu Item $10",
			AIProvider:     "invalid_provider",
			GenerateImages: false,
		},
	}

	job, err := suite.aiGenerationService.CreateJob(suite.ctx, suite.testBranchID, suite.testUserID, req)
	require.NoError(suite.T(), err, "Should create job even with invalid provider")

	// Processing should fail with unsupported provider error
	err = suite.aiGenerationService.ProcessJobAsync(suite.ctx, job.ID)
	assert.Error(suite.T(), err, "Should fail with unsupported provider")
	assert.Contains(suite.T(), err.Error(), "unsupported AI provider", "Should indicate unsupported provider")
}

func (suite *AIGenerationServiceTestSuite) TestJobValidation() {
	// Test with missing required data
	req := types.CreateAIGenerationJobRequest{
		Type: "text",
		InputData: types.AIGenerationInputDataRequest{
			// Missing MenuText for text type
			AIProvider: "gemini",
		},
	}

	_, err := suite.aiGenerationService.CreateJob(suite.ctx, suite.testBranchID, suite.testUserID, req)
	assert.Error(suite.T(), err, "Should fail validation for missing required data")
}

// Helper function to get environment variable with default (removed - using test_config.go version)

func TestAIGenerationServiceTestSuite(t *testing.T) {
	suite.Run(t, new(AIGenerationServiceTestSuite))
}
