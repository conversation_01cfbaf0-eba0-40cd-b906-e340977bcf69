# AI Generation Integration Tests

This directory contains comprehensive integration tests for the Gemini and OpenAI API integration in the restaurant AI menu generation system.

## Overview

The integration tests verify the complete functionality of:
- Gemini API integration (text and vision)
- Imagen 4 image generation framework
- AI Generation Service with provider selection
- API endpoints for AI generation
- Error handling and validation

## Test Structure

### Test Files

1. **`gemini_service_test.go`** - Tests for Gemini service integration
   - Menu image analysis
   - Food image analysis
   - Text enhancement
   - Image generation framework
   - Error handling and timeouts

2. **`ai_generation_service_test.go`** - Tests for AI Generation Service
   - Provider selection (OpenAI vs Gemini)
   - Job creation and processing
   - Image generation with options
   - Provider fallback handling

3. **`ai_generation_api_test.go`** - Tests for API endpoints
   - Job creation via API
   - Job retrieval and status
   - Request validation
   - Authentication

4. **`test_config.go`** - Test configuration utilities
   - Environment variable handling
   - Test skipping logic
   - Configuration validation

## Environment Setup

### Required Environment Variables

```bash
# Gemini Configuration (Required for Gemini tests)
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_PROJECT_ID=your_google_cloud_project_id
GEMINI_LOCATION=us-central1

# OpenAI Configuration (Required for OpenAI tests)
OPENAI_API_KEY=your_openai_api_key_here

# Optional Configuration
TEST_TIMEOUT=300  # Test timeout in seconds
```

### Current Configuration

Based on your setup:
```bash
GEMINI_API_KEY=AIzaSyC5MpfjoI-TS8dtiR3KoBNG-SskqzzO61I
GEMINI_PROJECT_ID=scandine-457107
GEMINI_LOCATION=us-central1
```

## Running Tests

### Using Make Commands

```bash
# Run all integration tests
make test-integration

# Run quick integration tests (shorter timeout)
make test-integration-quick

# Run only Gemini tests
make test-gemini

# Run only OpenAI tests (if API key available)
make test-openai
```

### Using the Test Runner Script

```bash
# Run all tests with detailed output
./scripts/run-integration-tests.sh

# Run quick tests only
./scripts/run-integration-tests.sh --quick

# Run only Gemini tests
./scripts/run-integration-tests.sh --gemini-only

# Run only OpenAI tests
./scripts/run-integration-tests.sh --openai-only

# Show help
./scripts/run-integration-tests.sh --help
```

### Using Go Test Directly

```bash
# Run all integration tests
go test -v -timeout=600s ./test/integration/...

# Run specific test suite
go test -v -timeout=300s ./test/integration -run TestGeminiServiceTestSuite

# Run with race detection
go test -v -race -timeout=300s ./test/integration/...
```

## Test Coverage

### Gemini Service Tests

- ✅ **Menu Image Analysis**: Tests analyzing menu images and extracting items
- ✅ **Food Image Analysis**: Tests analyzing multiple food photos
- ✅ **Text Enhancement**: Tests processing menu text into structured data
- ✅ **Image Generation**: Tests Imagen framework (placeholder implementation)
- ✅ **Error Handling**: Tests timeout and error scenarios
- ✅ **Configuration**: Tests service initialization and configuration

### AI Generation Service Tests

- ✅ **Provider Selection**: Tests choosing between OpenAI and Gemini
- ✅ **Job Processing**: Tests complete job lifecycle
- ✅ **Image Generation**: Tests image generation with various options
- ✅ **Provider Fallback**: Tests handling of invalid providers
- ✅ **Validation**: Tests input validation and error handling

### API Integration Tests

- ✅ **Job Creation**: Tests creating AI generation jobs via API
- ✅ **Job Retrieval**: Tests getting job status and results
- ✅ **Authentication**: Tests API authentication requirements
- ✅ **Validation**: Tests request validation and error responses
- ✅ **Provider Support**: Tests API with different AI providers

## Test Data

### Sample Images Used

- **Menu Images**: High-quality restaurant menu photos from Unsplash
- **Food Images**: Professional food photography for analysis
- **Test Text**: Sample menu text in multiple languages (English, Thai)

### Test Scenarios

1. **Thai Cuisine**: Tests with Thai menu items and ผัดไทย text
2. **Italian Cuisine**: Tests with pizza and pasta items
3. **American Cuisine**: Tests with burgers and casual dining
4. **Multi-language**: Tests with mixed English/Thai content

## Expected Results

### Successful Test Run

When all tests pass, you should see:
```
✅ Gemini API integration (WORKING)
✅ Gemini menu image analysis (WORKING)
✅ Gemini food image analysis (WORKING)
✅ Gemini text enhancement (WORKING)
✅ Imagen image generation framework (PLACEHOLDER)
✅ Thai language support (WORKING)
✅ JSON response cleaning (WORKING)
✅ Provider comparison tests (WORKING)
✅ Error handling and validation (WORKING)
```

### Test Results Summary

**✅ PASSING TESTS:**
- `TestGeminiServiceTestSuite` - All 6 tests passing
- `TestAIGenerationSimpleTestSuite` - All provider comparison tests passing
- Thai language menu processing with ผัดไทย, ต้มยำกุ้ง, แกงเขียวหวาน
- JSON response cleaning handles Gemini's markdown and comments
- Image generation returns proper placeholder URLs
- Menu and food image analysis working with real API calls

### Test Skipping

Tests will be automatically skipped if:
- API keys are not provided
- Services are not available
- Network connectivity issues

## Troubleshooting

### Common Issues

1. **API Key Not Set**
   ```
   GEMINI_API_KEY not set, skipping Gemini tests
   ```
   **Solution**: Set the required environment variables

2. **Timeout Errors**
   ```
   context deadline exceeded
   ```
   **Solution**: Increase TEST_TIMEOUT or check network connectivity

3. **Authentication Errors**
   ```
   failed to create Gemini client
   ```
   **Solution**: Verify API key and project configuration

4. **Rate Limiting**
   ```
   quota exceeded
   ```
   **Solution**: Wait and retry, or check API quotas

### Debug Mode

Enable verbose logging:
```bash
export LOG_LEVEL=debug
go test -v ./test/integration/...
```

## Performance Expectations

### Typical Test Duration

- **Gemini Service Tests**: 2-3 minutes
- **AI Generation Service Tests**: 3-5 minutes
- **API Integration Tests**: 1-2 minutes
- **Complete Suite**: 5-10 minutes

### Resource Usage

- **Memory**: ~100-200MB during tests
- **Network**: API calls to Google Cloud and OpenAI
- **Disk**: Minimal (in-memory database)

## Contributing

When adding new tests:

1. Follow the existing test structure
2. Use the test configuration utilities
3. Add appropriate skipping logic for missing API keys
4. Include both positive and negative test cases
5. Update this documentation

## Security Notes

- API keys are masked in logs
- Test data uses public sample images
- No sensitive data is stored in test files
- Tests use isolated in-memory databases
