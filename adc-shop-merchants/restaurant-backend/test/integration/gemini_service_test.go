package integration

import (
	"context"
	"os"
	"strings"
	"testing"
	"time"

	"restaurant-backend/internal/config"
	"restaurant-backend/internal/services"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

type GeminiServiceTestSuite struct {
	suite.Suite
	geminiService *services.GeminiService
	ctx           context.Context
}

func (suite *GeminiServiceTestSuite) SetupSuite() {
	// Skip tests if Gemini API key is not provided
	apiKey := os.Getenv("GEMINI_API_KEY")
	if apiKey == "" {
		suite.T().Skip("GEMINI_API_KEY not set, skipping Gemini integration tests")
	}

	// Setup logger
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// Setup Gemini config
	geminiConfig := &config.GeminiConfig{
		APIKey:      apiKey,
		ProjectID:   os.Getenv("GEMINI_PROJECT_ID"),
		Location:    os.Getenv("GEMINI_LOCATION"),
		Model:       "gemini-1.5-pro",
		MaxTokens:   4096,
		Temperature: 0.7,
		ImageModel:  "imagen-3.0-generate-001",
	}

	// Set defaults if not provided
	if geminiConfig.ProjectID == "" {
		geminiConfig.ProjectID = "scandine-457107"
	}
	if geminiConfig.Location == "" {
		geminiConfig.Location = "us-central1"
	}

	// Create Gemini service
	var err error
	suite.geminiService, err = services.NewGeminiService(geminiConfig, logger)
	require.NoError(suite.T(), err, "Failed to create Gemini service")

	suite.ctx = context.Background()
}

func (suite *GeminiServiceTestSuite) TearDownSuite() {
	if suite.geminiService != nil {
		suite.geminiService.Close()
	}
}

func (suite *GeminiServiceTestSuite) TestAnalyzeMenuImage() {
	// Test menu image analysis
	imageURL := "https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=800"
	cuisineType := "Italian"
	priceRange := "mid-range"
	restaurantName := "Test Restaurant"

	ctx, cancel := context.WithTimeout(suite.ctx, 30*time.Second)
	defer cancel()

	menuItems, err := suite.geminiService.AnalyzeMenuImage(ctx, imageURL, cuisineType, priceRange, restaurantName)

	assert.NoError(suite.T(), err, "Menu image analysis should not return error")
	assert.NotEmpty(suite.T(), menuItems, "Should return at least one menu item")

	// Validate menu item structure
	if len(menuItems) > 0 {
		item := menuItems[0]
		assert.NotEmpty(suite.T(), item.Name, "Menu item should have a name")
		assert.NotEmpty(suite.T(), item.Description, "Menu item should have a description")
		assert.Greater(suite.T(), item.Price, 0.0, "Menu item should have a positive price")
		assert.NotEmpty(suite.T(), item.Category, "Menu item should have a category")
	}
}

func (suite *GeminiServiceTestSuite) TestAnalyzeFoodImages() {
	// Test food images analysis
	imageURLs := []string{
		"https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800", // Pizza
		"https://images.unsplash.com/photo-1551782450-a2132b4ba21d?w=800",    // Burger
	}
	cuisineType := "American"
	priceRange := "affordable"
	restaurantName := "Test Diner"

	ctx, cancel := context.WithTimeout(suite.ctx, 45*time.Second)
	defer cancel()

	menuItems, err := suite.geminiService.AnalyzeFoodImages(ctx, imageURLs, cuisineType, priceRange, restaurantName)

	assert.NoError(suite.T(), err, "Food images analysis should not return error")
	assert.NotEmpty(suite.T(), menuItems, "Should return at least one menu item")

	// Should return items for multiple images
	assert.LessOrEqual(suite.T(), len(imageURLs), len(menuItems)+2, "Should process multiple images")

	// Validate menu item structure
	for _, item := range menuItems {
		assert.NotEmpty(suite.T(), item.Name, "Menu item should have a name")
		assert.NotEmpty(suite.T(), item.Description, "Menu item should have a description")
		assert.Greater(suite.T(), item.Price, 0.0, "Menu item should have a positive price")
	}
}

func (suite *GeminiServiceTestSuite) TestEnhanceMenuText() {
	// Test menu text enhancement
	menuText := `
	Margherita Pizza - Classic tomato sauce, mozzarella, basil - $12
	Pepperoni Pizza - Tomato sauce, mozzarella, pepperoni - $14
	Caesar Salad - Romaine lettuce, parmesan, croutons - $8
	Chicken Alfredo - Fettuccine pasta with creamy alfredo sauce - $16
	`
	cuisineType := "Italian"
	priceRange := "mid-range"
	restaurantName := "Mario's Pizzeria"

	ctx, cancel := context.WithTimeout(suite.ctx, 30*time.Second)
	defer cancel()

	menuItems, err := suite.geminiService.EnhanceMenuText(ctx, menuText, cuisineType, priceRange, restaurantName)

	assert.NoError(suite.T(), err, "Menu text enhancement should not return error")
	assert.NotEmpty(suite.T(), menuItems, "Should return at least one menu item")
	assert.GreaterOrEqual(suite.T(), len(menuItems), 3, "Should extract multiple items from text")

	// Validate enhanced menu items
	for _, item := range menuItems {
		assert.NotEmpty(suite.T(), item.Name, "Menu item should have a name")
		assert.NotEmpty(suite.T(), item.Description, "Menu item should have an enhanced description")
		assert.Greater(suite.T(), item.Price, 0.0, "Menu item should have a positive price")
		assert.NotEmpty(suite.T(), item.Category, "Menu item should have a category")
		assert.NotNil(suite.T(), item.Ingredients, "Menu item should have ingredients")
	}
}

func (suite *GeminiServiceTestSuite) TestGenerateFoodImageWithOptions() {
	// Test food image generation (currently returns placeholder)
	dishName := "Margherita Pizza"
	description := "Classic Italian pizza with fresh tomatoes, mozzarella, and basil"
	cuisineType := "Italian"
	style := "realistic"
	theme := "restaurant"
	quality := "high"
	size := "1024x1024"

	ctx, cancel := context.WithTimeout(suite.ctx, 30*time.Second)
	defer cancel()

	imageURL, err := suite.geminiService.GenerateFoodImageWithOptions(ctx, dishName, description, cuisineType, style, theme, quality, size)

	// Currently returns placeholder, so we expect a URL
	assert.NoError(suite.T(), err, "Image generation should not return error")
	assert.NotEmpty(suite.T(), imageURL, "Should return an image URL")
	// Check that the URL contains a version of the dish name (lowercase with hyphens)
	expectedDishInURL := strings.ToLower(strings.ReplaceAll(dishName, " ", "-"))
	assert.Contains(suite.T(), imageURL, expectedDishInURL, "Placeholder URL should contain dish name")
}

func (suite *GeminiServiceTestSuite) TestServiceConfiguration() {
	// Test that service is properly configured
	assert.NotNil(suite.T(), suite.geminiService, "Gemini service should be initialized")
}

func (suite *GeminiServiceTestSuite) TestContextTimeout() {
	// Test behavior with short timeout
	imageURL := "https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=800"

	// Very short timeout to test timeout handling
	ctx, cancel := context.WithTimeout(suite.ctx, 1*time.Millisecond)
	defer cancel()

	_, err := suite.geminiService.AnalyzeMenuImage(ctx, imageURL, "Italian", "mid-range", "Test")

	// Should return context deadline exceeded error
	assert.Error(suite.T(), err, "Should return error for timeout")
	assert.Contains(suite.T(), err.Error(), "context deadline exceeded", "Should be a timeout error")
}

func TestGeminiServiceTestSuite(t *testing.T) {
	suite.Run(t, new(GeminiServiceTestSuite))
}
