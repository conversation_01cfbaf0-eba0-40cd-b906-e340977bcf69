package integration

import (
	"os"
	"testing"
)

// TestConfig holds configuration for integration tests
type TestConfig struct {
	OpenAIAPIKey      string
	Gemini<PERSON><PERSON>Key      string
	GeminiProjectID   string
	GeminiLocation    string
	SkipOpenAI        bool
	SkipGemini        bool
	SkipImageGen      bool
	TestTimeout       int // seconds
}

// GetTestConfig returns test configuration from environment variables
func GetTestConfig() *TestConfig {
	config := &TestConfig{
		OpenAIAPIKey:    os.Getenv("OPENAI_API_KEY"),
		GeminiAPIKey:    os.Getenv("GEMINI_API_KEY"),
		GeminiProjectID: getEnvOrDefault("GEMINI_PROJECT_ID", "scandine-457107"),
		GeminiLocation:  getEnvOrDefault("GEMINI_LOCATION", "us-central1"),
		TestTimeout:     60, // default 60 seconds
	}

	// Set skip flags based on API key availability
	config.SkipOpenAI = config.OpenAIAPIKey == ""
	config.SkipGemini = config.GeminiAPIKey == ""
	config.SkipImageGen = config.SkipOpenAI && config.SkipGemini

	return config
}

// ShouldSkipOpenAI returns true if OpenAI tests should be skipped
func (c *TestConfig) ShouldSkipOpenAI(t *testing.T) bool {
	if c.SkipOpenAI {
		t.Skip("OPENAI_API_KEY not set, skipping OpenAI tests")
		return true
	}
	return false
}

// ShouldSkipGemini returns true if Gemini tests should be skipped
func (c *TestConfig) ShouldSkipGemini(t *testing.T) bool {
	if c.SkipGemini {
		t.Skip("GEMINI_API_KEY not set, skipping Gemini tests")
		return true
	}
	return false
}

// ShouldSkipImageGeneration returns true if image generation tests should be skipped
func (c *TestConfig) ShouldSkipImageGeneration(t *testing.T) bool {
	if c.SkipImageGen {
		t.Skip("No AI provider API keys set, skipping image generation tests")
		return true
	}
	return false
}

// PrintTestConfiguration prints the current test configuration
func (c *TestConfig) PrintTestConfiguration(t *testing.T) {
	t.Logf("=== Integration Test Configuration ===")
	t.Logf("OpenAI API Key: %s", maskAPIKey(c.OpenAIAPIKey))
	t.Logf("Gemini API Key: %s", maskAPIKey(c.GeminiAPIKey))
	t.Logf("Gemini Project ID: %s", c.GeminiProjectID)
	t.Logf("Gemini Location: %s", c.GeminiLocation)
	t.Logf("Skip OpenAI: %v", c.SkipOpenAI)
	t.Logf("Skip Gemini: %v", c.SkipGemini)
	t.Logf("Skip Image Generation: %v", c.SkipImageGen)
	t.Logf("Test Timeout: %d seconds", c.TestTimeout)
	t.Logf("=====================================")
}

// maskAPIKey masks an API key for logging
func maskAPIKey(apiKey string) string {
	if apiKey == "" {
		return "NOT_SET"
	}
	if len(apiKey) <= 8 {
		return "***"
	}
	return apiKey[:4] + "..." + apiKey[len(apiKey)-4:]
}

// getEnvOrDefault returns environment variable value or default
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
