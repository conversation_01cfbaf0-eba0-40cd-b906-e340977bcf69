package integration

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"restaurant-backend/internal/api/routes"
	"restaurant-backend/internal/config"
	"restaurant-backend/internal/models"
	"restaurant-backend/internal/types"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

type AIGenerationAPITestSuite struct {
	suite.Suite
	router         *gin.Engine
	db             *gorm.DB
	testShopSlug   string
	testBranchSlug string
	authToken      string
}

func (suite *AIGenerationAPITestSuite) SetupSuite() {
	// Setup test database
	var err error
	suite.db, err = gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(suite.T(), err, "Failed to connect to test database")

	// Auto-migrate the schema
	err = suite.db.AutoMigrate(
		&models.User{},
		&models.Shop{},
		&models.ShopBranch{},
		&models.AIGenerationJob{},
	)
	require.NoError(suite.T(), err, "Failed to migrate test database")

	// Setup test data
	suite.setupTestData()

	// Setup configuration
	cfg := &config.Config{
		Database: config.DatabaseConfig{
			Host:     "localhost",
			Port:     "5432",
			User:     "test",
			Password: "test",
			Name:     "test",
		},
		Server: config.ServerConfig{
			Port:    "8080",
			Env:     "test",
			GinMode: "test",
		},
		JWT: config.JWTConfig{
			Secret:    "test-secret",
			ExpiresIn: 24 * time.Hour,
		},
		OpenAI: config.OpenAIConfig{
			APIKey:      os.Getenv("OPENAI_API_KEY"),
			Model:       "gpt-4-vision-preview",
			MaxTokens:   4096,
			Temperature: 0.7,
		},
		Gemini: config.GeminiConfig{
			APIKey:      os.Getenv("GEMINI_API_KEY"),
			ProjectID:   getEnvOrDefault("GEMINI_PROJECT_ID", "scandine-457107"),
			Location:    getEnvOrDefault("GEMINI_LOCATION", "us-central1"),
			Model:       "gemini-1.5-pro",
			MaxTokens:   4096,
			Temperature: 0.7,
			ImageModel:  "imagen-3.0-generate-001",
		},
	}

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	suite.router = gin.New()

	// Setup logger for routes
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel) // Reduce noise in tests

	// Setup routes with test database
	routes.SetupRoutes(suite.router, suite.db, cfg, logger)

	// Generate auth token for testing
	suite.authToken = suite.generateTestAuthToken()
}

func (suite *AIGenerationAPITestSuite) setupTestData() {
	// Create test user with proper structure
	testUserID := uuid.New()
	testShopID := uuid.New()
	testRoleID := uuid.New()

	// Create a basic role first
	testRole := models.Role{
		BaseModel:   models.BaseModel{ID: testRoleID},
		ShopID:      testShopID,
		Name:        "Admin",
		Description: "Test admin role",
		IsActive:    true,
	}
	suite.db.Create(&testRole)

	testUser := models.User{
		BaseModel: models.BaseModel{ID: testUserID},
		ShopID:    testShopID,
		Email:     "<EMAIL>",
		FirstName: "Test",
		LastName:  "User",
		RoleID:    testRoleID,
		Status:    "active",
	}
	// Set a password for the user
	testUser.SetPassword("testpassword")
	suite.db.Create(&testUser)

	// Create test shop
	testShop := models.Shop{
		BaseModel:   models.BaseModel{ID: testShopID},
		OwnerID:     testUserID,
		Name:        "Test Restaurant",
		Slug:        "test-restaurant",
		Description: "A test restaurant",
		ShopType:    "restaurant",
		Status:      "active",
		IsActive:    true,
	}
	suite.db.Create(&testShop)
	suite.testShopSlug = testShop.Slug

	// Create test branch
	testBranch := models.ShopBranch{
		BaseModel: models.BaseModel{ID: uuid.New()},
		ShopID:    testShopID,
		Name:      "Main Branch",
		Slug:      "main-branch",
	}
	suite.db.Create(&testBranch)
	suite.testBranchSlug = testBranch.Slug
}

func (suite *AIGenerationAPITestSuite) generateTestAuthToken() string {
	// This is a simplified token generation for testing
	// In a real implementation, you would use your JWT service
	return "Bearer test-token"
}

func (suite *AIGenerationAPITestSuite) makeAuthenticatedRequest(method, url string, body []byte) *httptest.ResponseRecorder {
	req, err := http.NewRequest(method, url, bytes.NewBuffer(body))
	require.NoError(suite.T(), err)

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}
	req.Header.Set("Authorization", suite.authToken)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)
	return w
}

func (suite *AIGenerationAPITestSuite) TestCreateAIGenerationJobWithGemini() {
	if os.Getenv("GEMINI_API_KEY") == "" {
		suite.T().Skip("GEMINI_API_KEY not set, skipping Gemini API tests")
	}

	requestBody := types.CreateAIGenerationJobRequest{
		Type: "text",
		InputData: types.AIGenerationInputDataRequest{
			MenuText:       "Pad Thai ผัดไทย $12, Green Curry แกงเขียวหวาน $14, Tom Yum Soup ต้มยำกุ้ง $10",
			CuisineType:    "Thai",
			PriceRange:     "mid-range",
			RestaurantName: "Thai Garden",
			AIProvider:     "gemini",
			GenerateImages: false,
		},
	}

	jsonBody, err := json.Marshal(requestBody)
	require.NoError(suite.T(), err)

	url := fmt.Sprintf("/api/v1/shops/slug/%s/branches/slug/%s/ai-generation/jobs",
		suite.testShopSlug, suite.testBranchSlug)

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	require.NoError(suite.T(), err)

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", suite.authToken)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusCreated, w.Code, "Should create job successfully")

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)

	data := response["data"].(map[string]interface{})
	assert.Equal(suite.T(), "gemini", data["input_data"].(map[string]interface{})["ai_provider"])
	assert.Equal(suite.T(), "text", data["type"])
	assert.Equal(suite.T(), "pending", data["status"])
}

func (suite *AIGenerationAPITestSuite) TestCreateAIGenerationJobWithOpenAI() {
	if os.Getenv("OPENAI_API_KEY") == "" {
		suite.T().Skip("OPENAI_API_KEY not set, skipping OpenAI API tests")
	}

	requestBody := types.CreateAIGenerationJobRequest{
		Type: "text",
		InputData: types.AIGenerationInputDataRequest{
			MenuText:       "Margherita Pizza $12, Caesar Salad $8, Chicken Alfredo $16",
			CuisineType:    "Italian",
			PriceRange:     "mid-range",
			RestaurantName: "Mario's Pizzeria",
			AIProvider:     "openai",
			GenerateImages: true,
			ImageStyle:     "realistic",
			ImageTheme:     "restaurant",
			ImageQuality:   "high",
			ImageSize:      "1024x1024",
		},
	}

	jsonBody, err := json.Marshal(requestBody)
	require.NoError(suite.T(), err)

	url := fmt.Sprintf("/api/v1/shops/slug/%s/branches/slug/%s/ai-generation/jobs",
		suite.testShopSlug, suite.testBranchSlug)

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	require.NoError(suite.T(), err)

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", suite.authToken)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusCreated, w.Code, "Should create job successfully")

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)

	data := response["data"].(map[string]interface{})
	assert.Equal(suite.T(), "openai", data["input_data"].(map[string]interface{})["ai_provider"])
	assert.Equal(suite.T(), "text", data["type"])
	assert.True(suite.T(), data["input_data"].(map[string]interface{})["generate_images"].(bool))
}

func (suite *AIGenerationAPITestSuite) TestCreateJobWithInvalidProvider() {
	requestBody := types.CreateAIGenerationJobRequest{
		Type: "text",
		InputData: types.AIGenerationInputDataRequest{
			MenuText:       "Test Menu Item $10",
			AIProvider:     "invalid_provider",
			GenerateImages: false,
		},
	}

	jsonBody, err := json.Marshal(requestBody)
	require.NoError(suite.T(), err)

	url := fmt.Sprintf("/api/v1/shops/slug/%s/branches/slug/%s/ai-generation/jobs",
		suite.testShopSlug, suite.testBranchSlug)

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	require.NoError(suite.T(), err)

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", suite.authToken)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Should still create the job, but processing will fail
	assert.Equal(suite.T(), http.StatusCreated, w.Code, "Should create job even with invalid provider")
}

func (suite *AIGenerationAPITestSuite) TestGetAIGenerationJobs() {
	// First create a job
	requestBody := types.CreateAIGenerationJobRequest{
		Type: "text",
		InputData: types.AIGenerationInputDataRequest{
			MenuText:   "Test Menu $10",
			AIProvider: "gemini",
		},
	}

	jsonBody, err := json.Marshal(requestBody)
	require.NoError(suite.T(), err)

	createURL := fmt.Sprintf("/api/v1/shops/slug/%s/branches/slug/%s/ai-generation/jobs",
		suite.testShopSlug, suite.testBranchSlug)

	createReq, err := http.NewRequest("POST", createURL, bytes.NewBuffer(jsonBody))
	require.NoError(suite.T(), err)
	createReq.Header.Set("Content-Type", "application/json")
	createReq.Header.Set("Authorization", suite.authToken)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, createReq)
	require.Equal(suite.T(), http.StatusCreated, w.Code)

	// Now get the jobs
	getURL := fmt.Sprintf("/api/v1/shops/slug/%s/branches/slug/%s/ai-generation/jobs",
		suite.testShopSlug, suite.testBranchSlug)

	getReq, err := http.NewRequest("GET", getURL, nil)
	require.NoError(suite.T(), err)
	getReq.Header.Set("Authorization", suite.authToken)

	w = httptest.NewRecorder()
	suite.router.ServeHTTP(w, getReq)

	assert.Equal(suite.T(), http.StatusOK, w.Code, "Should get jobs successfully")

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)

	data := response["data"].([]interface{})
	assert.GreaterOrEqual(suite.T(), len(data), 1, "Should return at least one job")
}

func (suite *AIGenerationAPITestSuite) TestJobValidation() {
	// Test with missing required fields
	requestBody := types.CreateAIGenerationJobRequest{
		Type: "text",
		InputData: types.AIGenerationInputDataRequest{
			// Missing MenuText for text type
			AIProvider: "gemini",
		},
	}

	jsonBody, err := json.Marshal(requestBody)
	require.NoError(suite.T(), err)

	url := fmt.Sprintf("/api/v1/shops/slug/%s/branches/slug/%s/ai-generation/jobs",
		suite.testShopSlug, suite.testBranchSlug)

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	require.NoError(suite.T(), err)

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", suite.authToken)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusBadRequest, w.Code, "Should return bad request for invalid data")
}

func TestAIGenerationAPITestSuite(t *testing.T) {
	suite.Run(t, new(AIGenerationAPITestSuite))
}
