package test

import (
	"testing"

	"restaurant-backend/internal/utils"

	"github.com/stretchr/testify/assert"
)

// TestSlugifyWithThaiCharacters tests the Slugify function with Thai characters
func TestSlugifyWithThaiCharacters(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Pure Thai text",
			input:    "ผัดไทย",
			expected: "ผัดไทย",
		},
		{
			name:     "Thai with spaces",
			input:    "ต้มยำ กุ้ง",
			expected: "ต้มยำ-กุ้ง",
		},
		{
			name:     "Mixed Thai and English",
			input:    "Pad Thai ผัดไทย",
			expected: "pad-thai-ผัดไทย",
		},
		{
			name:     "Thai with punctuation",
			input:    "ส้มตำ (ไทย)",
			expected: "ส้มตำ-ไทย",
		},
		{
			name:     "Thai with special characters",
			input:    "แกงเขียวหวาน!!!",
			expected: "แกงเขียวหวาน",
		},
		{
			name:     "English only",
			input:    "Hamburger",
			expected: "hamburger",
		},
		{
			name:     "English with spaces",
			input:    "Green Curry",
			expected: "green-curry",
		},
		{
			name:     "Mixed with numbers",
			input:    "ข้าวผัด 2023",
			expected: "ข้าวผัด-2023",
		},
		{
			name:     "Multiple spaces",
			input:    "ผัด   ไทย   พิเศษ",
			expected: "ผัด-ไทย-พิเศษ",
		},
		{
			name:     "Leading and trailing spaces",
			input:    "  ต้มยำกุ้ง  ",
			expected: "ต้มยำกุ้ง",
		},
		{
			name:     "Special characters only",
			input:    "!@#$%",
			expected: "",
		},
		{
			name:     "Empty string",
			input:    "",
			expected: "",
		},
		{
			name:     "Multiple hyphens",
			input:    "ผัด--ไทย",
			expected: "ผัด-ไทย",
		},
		{
			name:     "Complex mixed case",
			input:    "SPICY Tom Yum ต้มยำ (เผ็ด)",
			expected: "spicy-tom-yum-ต้มยำ-เผ็ด",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := utils.Slugify(tc.input)
			assert.Equal(t, tc.expected, result,
				"Slugify(%q) = %q, expected %q", tc.input, result, tc.expected)
		})
	}
}

// TestSlugifyUnicodeSupport tests various Unicode characters
func TestSlugifyUnicodeSupport(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Japanese characters",
			input:    "寿司 Sushi",
			expected: "寿司-sushi",
		},
		{
			name:     "Korean characters",
			input:    "김치 Kimchi",
			expected: "김치-kimchi",
		},
		{
			name:     "Chinese characters",
			input:    "炒饭 Fried Rice",
			expected: "炒饭-fried-rice",
		},
		{
			name:     "Arabic characters",
			input:    "كباب Kebab",
			expected: "كباب-kebab",
		},
		{
			name:     "French with accents",
			input:    "Café Français",
			expected: "café-français",
		},
		{
			name:     "German with umlauts",
			input:    "Schönes Essen",
			expected: "schönes-essen",
		},
		{
			name:     "Spanish with tildes",
			input:    "Niño Pequeño",
			expected: "niño-pequeño",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := utils.Slugify(tc.input)
			assert.Equal(t, tc.expected, result,
				"Slugify(%q) = %q, expected %q", tc.input, result, tc.expected)
		})
	}
}

// TestSlugifyEdgeCases tests edge cases and potential issues
func TestSlugifyEdgeCases(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Very long Thai text",
			input:    "ผัดไทยกุ้งสดใส่ไข่เจียวหอมกรอบพร้อมถั่วงอกสดใสและมะนาวสดใหม่",
			expected: "ผัดไทยกุ้งสดใส่ไข่เจียวหอมกรอบพร้อมถั่วงอกสดใสและมะนาวสดใหม่",
		},
		{
			name:     "Numbers only",
			input:    "12345",
			expected: "12345",
		},
		{
			name:     "Mixed numbers and Thai",
			input:    "ผัดไทย 123 พิเศษ",
			expected: "ผัดไทย-123-พิเศษ",
		},
		{
			name:     "URL-like input",
			input:    "https://example.com/ผัดไทย",
			expected: "httpsexamplecomผัดไทย",
		},
		{
			name:     "Email-like input",
			input:    "test@ผัดไทย.com",
			expected: "testผัดไทยcom",
		},
		{
			name:     "Single character",
			input:    "ก",
			expected: "ก",
		},
		{
			name:     "Single English character",
			input:    "A",
			expected: "a",
		},
		{
			name:     "Whitespace only",
			input:    "   \t\n  ",
			expected: "",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := utils.Slugify(tc.input)
			assert.Equal(t, tc.expected, result,
				"Slugify(%q) = %q, expected %q", tc.input, result, tc.expected)
		})
	}
}

// BenchmarkSlugifyThai benchmarks the Slugify function with Thai text
func BenchmarkSlugifyThai(b *testing.B) {
	testInputs := []string{
		"ผัดไทย",
		"ต้มยำกุ้ง",
		"แกงเขียวหวาน",
		"Pad Thai ผัดไทย",
		"ผัดไทยกุ้งสดใส่ไข่เจียวหอมกรอบพร้อมถั่วงอกสดใสและมะนาวสดใหม่",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		for _, input := range testInputs {
			utils.Slugify(input)
		}
	}
}

// BenchmarkSlugifyEnglish benchmarks the Slugify function with English text
func BenchmarkSlugifyEnglish(b *testing.B) {
	testInputs := []string{
		"Hamburger",
		"Pizza Margherita",
		"Caesar Salad",
		"Grilled Chicken Breast",
		"Traditional Italian Pasta with Fresh Tomato Sauce and Basil",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		for _, input := range testInputs {
			utils.Slugify(input)
		}
	}
}

// TestSlugifyConsistency tests that the function produces consistent results
func TestSlugifyConsistency(t *testing.T) {
	testInput := "ผัดไทย Special (เผ็ด)"

	// Run the function multiple times and ensure consistent results
	results := make([]string, 100)
	for i := 0; i < 100; i++ {
		results[i] = utils.Slugify(testInput)
	}

	// All results should be identical
	expected := results[0]
	for i, result := range results {
		assert.Equal(t, expected, result,
			"Inconsistent result at iteration %d: got %q, expected %q", i, result, expected)
	}
}
