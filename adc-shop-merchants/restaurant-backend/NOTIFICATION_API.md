# Notification API Documentation

## Overview
The notification system provides real-time notifications for restaurant operations with both REST API and WebSocket support.

## REST API Endpoints

### Create Notification (Slug-based)
```http
POST /api/v1/shops/slug/{shopSlug}/branches/slug/{branchSlug}/notifications
```

**Headers:**
- `Content-Type: application/json`
- `Authorization: Bearer <token>` (required)

**Request Body:**
```json
{
  "title": "Notification Title",
  "message": "Detailed notification message",
  "type": "order|reservation|inventory|review|staff|payment|promotion|system",
  "priority": "urgent|high|medium|low",
  "link": "/app/restaurant/shop/branch/page",
  "action_label": "Action Button Text",
  "data": {
    "custom": "metadata",
    "order_id": "ORD-123",
    "amount": 45.99
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "notification-uuid",
    "title": "Notification Title",
    "message": "Detailed notification message",
    "type": "order",
    "priority": "high",
    "is_read": false,
    "timestamp": "2024-05-31T11:22:31Z",
    "link": "/app/restaurant/shop/branch/orders",
    "action_label": "View Order",
    "data": {...}
  }
}
```

### Get Notifications
```http
GET /api/v1/shops/slug/{shopSlug}/branches/slug/{branchSlug}/notifications
```

**Query Parameters:**
- `page` (default: 1)
- `limit` (default: 10)
- `sort_by` (default: timestamp)
- `sort_order` (default: desc)
- `type` (optional filter)
- `priority` (optional filter)
- `is_read` (optional filter)

### Mark as Read
```http
PATCH /api/v1/shops/slug/{shopSlug}/branches/slug/{branchSlug}/notifications/{notificationId}
```

### Mark All as Read
```http
PATCH /api/v1/shops/slug/{shopSlug}/branches/slug/{branchSlug}/notifications/mark-all-read
```

## WebSocket API

### Connection
```javascript
// Basic connection
const ws = new WebSocket('ws://localhost:8200/ws');

// Authenticated connection
const ws = new WebSocket('ws://localhost:8200/ws/auth?token=YOUR_JWT_TOKEN');
```

### Subscribe to Notifications
```javascript
ws.send(JSON.stringify({
  type: 'subscribe',
  data: { channel: 'notifications' }
}));
```

### Broadcast Notification (Admin)
```http
POST http://localhost:8200/api/v1/websocket/broadcast
Content-Type: application/json

{
  "type": "notification",
  "data": {
    "title": "Real-time Alert!",
    "message": "This appears immediately",
    "type": "system",
    "priority": "urgent"
  },
  "channel": "notifications"
}
```

## Notification Types

| Type | Description | Icon | Priority |
|------|-------------|------|----------|
| `order` | New orders, order updates | 🛒 | high |
| `reservation` | Table bookings | 📅 | medium |
| `inventory` | Stock alerts | 📦 | urgent |
| `review` | Customer feedback | ⭐ | low |
| `staff` | Employee updates | 👥 | medium |
| `payment` | Transaction alerts | 💰 | low |
| `promotion` | Marketing updates | 🎉 | low |
| `system` | Technical alerts | ⚙️ | varies |

## Priority Levels

| Priority | Badge Color | Use Case |
|----------|-------------|----------|
| `urgent` | Red | Critical issues, emergencies |
| `high` | Orange | Important actions needed |
| `medium` | Blue | Standard notifications |
| `low` | Gray | Informational updates |

## Testing Commands

### Create Test Notification (Database)
```bash
# Run the test script
./scripts/test_realtime_notifications.sh

# Or create manually
psql $DATABASE_URL -c "
INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, timestamp) 
SELECT s.id, sb.id, 'Test Notification', 'This is a test', 'system', 'urgent', false, NOW()
FROM shops s JOIN shop_branches sb ON s.id = sb.shop_id 
WHERE s.slug = 'weerawat-poseeya' AND sb.slug = 'the-green-terrace';
"
```

### Send WebSocket Notification
```bash
curl -X POST "http://localhost:8200/api/v1/websocket/broadcast" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "notification",
    "data": {
      "title": "Live Test!",
      "message": "Real-time notification",
      "type": "system",
      "priority": "urgent"
    },
    "channel": "notifications"
  }'
```

## Current Status

✅ **Database**: 38 total notifications, 29 unread  
✅ **REST API**: Fully functional with authentication  
✅ **WebSocket**: Real-time broadcasting working  
✅ **Frontend**: Notification bell integrated  
✅ **Real-time**: Live updates via WebSocket  

## Frontend Integration

The notification bell automatically:
- Fetches notifications via authenticated API
- Shows unread count badge
- Updates in real-time via WebSocket
- Provides click-to-read functionality
- Links to relevant pages

Check the notification bell at: http://localhost:4000/en/app/restaurant/weerawat-poseeya/the-green-terrace
