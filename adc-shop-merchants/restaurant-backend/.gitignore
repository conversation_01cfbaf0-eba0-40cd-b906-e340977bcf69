# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Environment variables
.env
.env.local
.env.production

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Application specific
tmp/
logs/
uploads/
*.log
build-errors.log

# Database files
*.db
*.sqlite
*.sqlite3

# Coverage files
coverage.out
coverage.html

# Binary files
restaurant-api
restaurant-api_*
main

# Docker volumes
postgres_data/
redis_data/
pgadmin_data/

# Swagger generated files
docs/docs.go
docs/swagger.json
docs/swagger.yaml

# Air temporary files
tmp/

# Test files
testdata/

# Backup files
*.bak
*.backup

# Certificate files
*.pem
*.key
*.crt

# Temporary files
*.tmp
*.temp
