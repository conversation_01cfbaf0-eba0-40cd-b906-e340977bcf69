-- Fix foreign key constraints by migrating from branches to shop_branches

-- First, get a valid shop_branches ID to use as default
DO $$
DECLARE
    default_branch_id UUID;
    old_branch_id UUID;
BEGIN
    -- Get the first available shop branch ID
    SELECT id INTO default_branch_id FROM shop_branches ORDER BY created_at LIMIT 1;
    
    -- Get the old branch ID
    SELECT id INTO old_branch_id FROM branches ORDER BY created_at LIMIT 1;
    
    IF default_branch_id IS NULL THEN
        RAISE EXCEPTION 'No shop branches found in database';
    END IF;
    
    RAISE NOTICE 'Using default shop branch ID: %', default_branch_id;
    RAISE NOTICE 'Old branch ID: %', old_branch_id;
    
    -- Drop all old foreign key constraints pointing to branches table
    ALTER TABLE menu_categories DROP CONSTRAINT IF EXISTS fk_branches_menu_categories;
    ALTER TABLE menu_items DROP CONSTRAINT IF EXISTS fk_branches_menu_items;
    ALTER TABLE orders DROP CONSTRAINT IF EXISTS fk_branches_orders;
    ALTER TABLE reservations DROP CONSTRAINT IF EXISTS fk_branches_reservations;
    ALTER TABLE reviews DROP CONSTRAINT IF EXISTS fk_branches_reviews;
    ALTER TABLE services DROP CONSTRAINT IF EXISTS fk_services_branch;
    ALTER TABLE staff DROP CONSTRAINT IF EXISTS fk_staff_branch;
    ALTER TABLE table_areas DROP CONSTRAINT IF EXISTS fk_branches_table_areas;
    ALTER TABLE tables DROP CONSTRAINT IF EXISTS fk_branches_tables;
    ALTER TABLE users DROP CONSTRAINT IF EXISTS fk_branches_users;
    
    -- Update all records to use the new shop branch ID
    -- For records that reference the old branch, update to new default
    UPDATE menu_categories SET branch_id = default_branch_id WHERE branch_id = old_branch_id OR branch_id NOT IN (SELECT id FROM shop_branches);
    UPDATE menu_items SET branch_id = default_branch_id WHERE branch_id = old_branch_id OR branch_id NOT IN (SELECT id FROM shop_branches);
    UPDATE table_areas SET branch_id = default_branch_id WHERE branch_id = old_branch_id OR branch_id NOT IN (SELECT id FROM shop_branches);
    UPDATE tables SET branch_id = default_branch_id WHERE branch_id = old_branch_id OR branch_id NOT IN (SELECT id FROM shop_branches);
    
    -- For tables that allow NULL branch_id, set to NULL for invalid references
    UPDATE users SET branch_id = NULL WHERE branch_id IS NOT NULL AND branch_id NOT IN (SELECT id FROM shop_branches);
    
    -- For other tables, update to default branch
    UPDATE orders SET branch_id = default_branch_id WHERE branch_id IS NOT NULL AND (branch_id = old_branch_id OR branch_id NOT IN (SELECT id FROM shop_branches));
    UPDATE reservations SET branch_id = default_branch_id WHERE branch_id IS NOT NULL AND (branch_id = old_branch_id OR branch_id NOT IN (SELECT id FROM shop_branches));
    UPDATE reviews SET branch_id = default_branch_id WHERE branch_id IS NOT NULL AND (branch_id = old_branch_id OR branch_id NOT IN (SELECT id FROM shop_branches));
    UPDATE services SET branch_id = default_branch_id WHERE branch_id IS NOT NULL AND (branch_id = old_branch_id OR branch_id NOT IN (SELECT id FROM shop_branches));
    UPDATE staff SET branch_id = default_branch_id WHERE branch_id IS NOT NULL AND (branch_id = old_branch_id OR branch_id NOT IN (SELECT id FROM shop_branches));
    
END $$;

-- Verify the fix
SELECT 'Verification - checking for orphaned records:' as status;

SELECT 'menu_categories with invalid branch_id:' as check_type, COUNT(*) as count
FROM menu_categories WHERE branch_id IS NOT NULL AND branch_id NOT IN (SELECT id FROM shop_branches);

SELECT 'menu_items with invalid branch_id:' as check_type, COUNT(*) as count
FROM menu_items WHERE branch_id IS NOT NULL AND branch_id NOT IN (SELECT id FROM shop_branches);

SELECT 'table_areas with invalid branch_id:' as check_type, COUNT(*) as count
FROM table_areas WHERE branch_id IS NOT NULL AND branch_id NOT IN (SELECT id FROM shop_branches);

SELECT 'tables with invalid branch_id:' as check_type, COUNT(*) as count
FROM tables WHERE branch_id IS NOT NULL AND branch_id NOT IN (SELECT id FROM shop_branches);

SELECT 'users with invalid branch_id:' as check_type, COUNT(*) as count
FROM users WHERE branch_id IS NOT NULL AND branch_id NOT IN (SELECT id FROM shop_branches);

SELECT 'All constraints dropped and data migrated successfully!' as result;
