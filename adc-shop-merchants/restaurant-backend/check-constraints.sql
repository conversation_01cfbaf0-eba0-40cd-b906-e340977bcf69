-- Check what tables and constraints exist

-- List all tables
SELECT 'All tables in database:' as info;
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_type = 'BASE TABLE'
ORDER BY table_name;

-- Check if both branches and shop_branches tables exist
SELECT 'Checking branches table:' as info;
SELECT COUNT(*) as branches_count FROM branches;

SELECT 'Checking shop_branches table:' as info;
SELECT COUNT(*) as shop_branches_count FROM shop_branches;

-- List all foreign key constraints
SELECT 'Foreign key constraints:' as info;
SELECT 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    tc.constraint_name
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND tc.table_schema = 'public'
AND (ccu.table_name = 'branches' OR ccu.table_name = 'shop_branches')
ORDER BY tc.table_name, kcu.column_name;
