package websocket

import (
	"encoding/json"
	"time"

	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
)

const (
	// Time allowed to write a message to the peer
	writeWait = 10 * time.Second

	// Time allowed to read the next pong message from the peer
	pongWait = 60 * time.Second

	// Send pings to peer with this period. Must be less than pongWait
	pingPeriod = (pongWait * 9) / 10

	// Maximum message size allowed from peer
	maxMessageSize = 512
)

// readPump pumps messages from the websocket connection to the hub
func (c *Client) readPump() {
	defer func() {
		c.hub.unregister <- c
		c.conn.Close()
	}()

	c.conn.SetReadLimit(maxMessageSize)
	c.conn.SetReadDeadline(time.Now().Add(pongWait))
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(pongWait))
		return nil
	})

	for {
		_, message, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				c.hub.logger.WithError(err).Error("WebSocket error")
			}
			break
		}

		// Handle incoming messages from client
		c.handleIncomingMessage(message)
	}
}

// writePump pumps messages from the hub to the websocket connection
func (c *Client) writePump() {
	ticker := time.NewTicker(pingPeriod)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.send:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if !ok {
				// The hub closed the channel
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// Add queued chat messages to the current websocket message
			n := len(c.send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-c.send)
			}

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleIncomingMessage processes messages received from the client
func (c *Client) handleIncomingMessage(message []byte) {
	var msg Message
	if err := json.Unmarshal(message, &msg); err != nil {
		c.hub.logger.WithError(err).Error("Failed to unmarshal client message")
		c.sendError("Invalid message format")
		return
	}

	// Add client information to the message
	msg.UserID = &c.userID
	msg.BranchID = &c.branchID
	msg.Timestamp = getCurrentTimestamp()

	// Handle different message types
	switch msg.Type {
	case "ping":
		c.handlePing()
	case "subscribe":
		c.handleSubscribe(msg)
	case "unsubscribe":
		c.handleUnsubscribe(msg)
	case "order_status_request":
		c.handleOrderStatusRequest(msg)
	case "table_status_request":
		c.handleTableStatusRequest(msg)
	default:
		c.hub.logger.WithFields(logrus.Fields{
			"type":    msg.Type,
			"user_id": c.userID,
		}).Warn("Unknown message type received")
		c.sendError("Unknown message type")
	}
}

// handlePing responds to ping messages
func (c *Client) handlePing() {
	response := Message{
		Type:      "pong",
		Data:      map[string]interface{}{"status": "ok"},
		Timestamp: getCurrentTimestamp(),
	}
	c.sendMessage(response)
}

// handleSubscribe handles subscription requests
func (c *Client) handleSubscribe(msg Message) {
	data, ok := msg.Data.(map[string]interface{})
	if !ok {
		c.sendError("Invalid subscribe data")
		return
	}

	channel, ok := data["channel"].(string)
	if !ok {
		c.sendError("Channel not specified")
		return
	}

	// Add subscription to client metadata
	if c.metadata["subscriptions"] == nil {
		c.metadata["subscriptions"] = make(map[string]bool)
	}
	subscriptions := c.metadata["subscriptions"].(map[string]bool)
	subscriptions[channel] = true

	c.hub.logger.WithFields(logrus.Fields{
		"user_id": c.userID,
		"channel": channel,
	}).Info("Client subscribed to channel")

	response := Message{
		Type: "subscribed",
		Data: map[string]interface{}{
			"channel": channel,
			"status":  "success",
		},
		Timestamp: getCurrentTimestamp(),
	}
	c.sendMessage(response)
}

// handleUnsubscribe handles unsubscription requests
func (c *Client) handleUnsubscribe(msg Message) {
	data, ok := msg.Data.(map[string]interface{})
	if !ok {
		c.sendError("Invalid unsubscribe data")
		return
	}

	channel, ok := data["channel"].(string)
	if !ok {
		c.sendError("Channel not specified")
		return
	}

	// Remove subscription from client metadata
	if c.metadata["subscriptions"] != nil {
		subscriptions := c.metadata["subscriptions"].(map[string]bool)
		delete(subscriptions, channel)
	}

	c.hub.logger.WithFields(logrus.Fields{
		"user_id": c.userID,
		"channel": channel,
	}).Info("Client unsubscribed from channel")

	response := Message{
		Type: "unsubscribed",
		Data: map[string]interface{}{
			"channel": channel,
			"status":  "success",
		},
		Timestamp: getCurrentTimestamp(),
	}
	c.sendMessage(response)
}

// handleOrderStatusRequest handles requests for order status updates
func (c *Client) handleOrderStatusRequest(msg Message) {
	// This would typically fetch current order statuses from the database
	// and send them to the client
	response := Message{
		Type: "order_status_response",
		Data: map[string]interface{}{
			"message": "Order status request received",
			"status":  "processing",
		},
		Timestamp: getCurrentTimestamp(),
	}
	c.sendMessage(response)
}

// handleTableStatusRequest handles requests for table status updates
func (c *Client) handleTableStatusRequest(msg Message) {
	// This would typically fetch current table statuses from the database
	// and send them to the client
	response := Message{
		Type: "table_status_response",
		Data: map[string]interface{}{
			"message": "Table status request received",
			"status":  "processing",
		},
		Timestamp: getCurrentTimestamp(),
	}
	c.sendMessage(response)
}

// sendMessage sends a message to the client
func (c *Client) sendMessage(msg Message) {
	if data, err := json.Marshal(msg); err == nil {
		select {
		case c.send <- data:
		default:
			// Client's send channel is full, close the connection
			close(c.send)
		}
	} else {
		c.hub.logger.WithError(err).Error("Failed to marshal message")
	}
}

// sendError sends an error message to the client
func (c *Client) sendError(errorMsg string) {
	response := Message{
		Type: "error",
		Data: map[string]interface{}{
			"error":   errorMsg,
			"status":  "error",
		},
		Timestamp: getCurrentTimestamp(),
	}
	c.sendMessage(response)
}

// isSubscribedTo checks if the client is subscribed to a specific channel
func (c *Client) isSubscribedTo(channel string) bool {
	if c.metadata["subscriptions"] == nil {
		return false
	}
	subscriptions := c.metadata["subscriptions"].(map[string]bool)
	return subscriptions[channel]
}

// GetSubscriptions returns all channels the client is subscribed to
func (c *Client) GetSubscriptions() []string {
	var channels []string
	if c.metadata["subscriptions"] != nil {
		subscriptions := c.metadata["subscriptions"].(map[string]bool)
		for channel := range subscriptions {
			channels = append(channels, channel)
		}
	}
	return channels
}

// UpdateMetadata updates client metadata
func (c *Client) UpdateMetadata(key string, value interface{}) {
	c.metadata[key] = value
}

// GetMetadata retrieves client metadata
func (c *Client) GetMetadata(key string) interface{} {
	return c.metadata[key]
}
