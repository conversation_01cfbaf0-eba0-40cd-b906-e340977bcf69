package websocket

import (
	"encoding/json"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// HandleWebSocketConnection handles basic WebSocket connections without authentication
func HandleWebSocketConnection(hub *Hub, db *gorm.DB, logger *logrus.Logger, c *gin.Context) {
	// For basic connections, we'll create a guest user
	userID := uuid.New()
	branchID := uuid.New() // Default branch or extract from query params

	// Check if branch_id is provided in query params
	if branchIDParam := c.Query("branch_id"); branchIDParam != "" {
		if parsedBranchID, err := uuid.Parse(branchIDParam); err == nil {
			branchID = parsedBranchID
		}
	}

	// Set context values for the hub handler
	c.Set("user_id", userID)
	c.Set("branch_id", branchID)
	c.Set("role", "guest")

	// Use the existing hub handler
	hub.HandleWebSocket(c)
}

// HandleAuthenticatedWebSocketConnection handles WebSocket connections with JWT authentication
func HandleAuthenticatedWebSocketConnection(hub *Hub, db *gorm.DB, logger *logrus.Logger, c *gin.Context) {
	// Extract JWT token from Authorization header or query parameter
	token := extractToken(c)
	if token == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "No authentication token provided"})
		return
	}

	// Validate JWT token and extract user information
	// This is a simplified version - in production, use proper JWT validation
	userID, branchID, role, err := validateTokenAndExtractUserInfo(token, db, logger)
	if err != nil {
		logger.WithError(err).Error("Failed to validate token")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid authentication token"})
		return
	}

	// Set context values for the hub handler
	c.Set("user_id", userID)
	c.Set("branch_id", branchID)
	c.Set("role", role)

	// Use the existing hub handler
	hub.HandleWebSocket(c)
}

// extractToken extracts JWT token from Authorization header or query parameter
func extractToken(c *gin.Context) string {
	// Try Authorization header first
	authHeader := c.GetHeader("Authorization")
	if authHeader != "" {
		// Bearer token format: "Bearer <token>"
		parts := strings.Split(authHeader, " ")
		if len(parts) == 2 && parts[0] == "Bearer" {
			return parts[1]
		}
	}

	// Try query parameter as fallback
	return c.Query("token")
}

// validateTokenAndExtractUserInfo validates JWT token and extracts user information
// This is a simplified implementation - replace with proper JWT validation
func validateTokenAndExtractUserInfo(token string, db *gorm.DB, logger *logrus.Logger) (uuid.UUID, uuid.UUID, string, error) {
	// TODO: Implement proper JWT validation here
	// For now, return dummy values

	// In a real implementation, you would:
	// 1. Parse and validate the JWT token
	// 2. Extract user ID from token claims
	// 3. Query database to get user details and branch association
	// 4. Return the user information

	userID := uuid.New()
	branchID := uuid.New()
	role := "user"

	return userID, branchID, role, nil
}

// GetCurrentTimestamp returns current Unix timestamp
func GetCurrentTimestamp() int64 {
	return getCurrentTimestamp()
}

// BroadcastToChannel broadcasts a message to all clients subscribed to a specific channel
func (h *Hub) BroadcastToChannel(channel string, message Message) {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	for client := range h.clients {
		// Check if client is subscribed to the channel
		if client.isSubscribedTo(channel) {
			if data, err := json.Marshal(message); err == nil {
				select {
				case client.send <- data:
				default:
					h.unregisterClient(client)
				}
			}
		}
	}
}

// Broadcast broadcasts a message to all connected clients
func (h *Hub) Broadcast(message Message) {
	if data, err := json.Marshal(message); err == nil {
		h.broadcast <- data
	}
}

// SendToUser sends a message to a specific user
func (h *Hub) SendToUser(userIDStr string, message Message) bool {
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return false
	}

	h.mutex.RLock()
	defer h.mutex.RUnlock()

	found := false
	for client := range h.clients {
		if client.userID == userID {
			if data, err := json.Marshal(message); err == nil {
				select {
				case client.send <- data:
					found = true
				default:
					h.unregisterClient(client)
				}
			}
		}
	}
	return found
}

// GetConnectedClientsInfo returns detailed information about connected clients
func (h *Hub) GetConnectedClientsInfo() []map[string]interface{} {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	var clients []map[string]interface{}
	for client := range h.clients {
		clientInfo := map[string]interface{}{
			"user_id":       client.userID.String(),
			"branch_id":     client.branchID.String(),
			"role":          client.role,
			"subscriptions": client.GetSubscriptions(),
		}
		clients = append(clients, clientInfo)
	}
	return clients
}
