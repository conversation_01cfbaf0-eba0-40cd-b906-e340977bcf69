package repositories

import (
	"context"
	"fmt"
	"strings"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/types"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// TableRepository defines the interface for table data access
type TableRepository interface {
	Create(ctx context.Context, table *models.Table) error
	GetByID(ctx context.Context, id uuid.UUID) (*models.Table, error)
	GetByBranchID(ctx context.Context, branchID uuid.UUID, filters types.TableFilters) ([]models.Table, int64, error)
	Update(ctx context.Context, table *models.Table) error
	Delete(ctx context.Context, id uuid.UUID) error
	UpdateStatus(ctx context.Context, id uuid.UUID, status string) error
	GetByNumber(ctx context.Context, branchID uuid.UUID, number string) (*models.Table, error)
}

type tableRepository struct {
	db *gorm.DB
}

func NewTableRepository(db *gorm.DB) TableRepository {
	return &tableRepository{db: db}
}

func (r *tableRepository) Create(ctx context.Context, table *models.Table) error {
	return r.db.WithContext(ctx).Create(table).Error
}

func (r *tableRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.Table, error) {
	var table models.Table
	err := r.db.WithContext(ctx).Preload("Branch").Preload("Area").First(&table, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("table not found")
		}
		return nil, err
	}
	return &table, nil
}

func (r *tableRepository) GetByNumber(ctx context.Context, branchID uuid.UUID, number string) (*models.Table, error) {
	var table models.Table
	err := r.db.WithContext(ctx).Preload("Branch").Preload("Area").
		Where("branch_id = ? AND number = ?", branchID, number).First(&table).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("table not found")
		}
		return nil, err
	}
	return &table, nil
}

func (r *tableRepository) GetByBranchID(ctx context.Context, branchID uuid.UUID, filters types.TableFilters) ([]models.Table, int64, error) {
	var tables []models.Table
	var total int64

	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	query := r.db.WithContext(ctx).Model(&models.Table{}).Where("branch_id = ?", branchID)

	// Apply filters
	if filters.AreaID != nil {
		query = query.Where("area_id = ?", *filters.AreaID)
	}
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.IsActive != nil {
		query = query.Where("is_active = ?", *filters.IsActive)
	}
	if filters.Search != "" {
		searchTerm := "%" + strings.ToLower(filters.Search) + "%"
		query = query.Where(
			"LOWER(number) LIKE ? OR LOWER(name) LIKE ?",
			searchTerm, searchTerm,
		)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and get results
	offset := (filters.Page - 1) * filters.Limit
	err := query.Preload("Branch").Preload("Area").
		Offset(offset).Limit(filters.Limit).
		Order("number ASC").
		Find(&tables).Error

	if err != nil {
		return nil, 0, err
	}

	return tables, total, nil
}

func (r *tableRepository) Update(ctx context.Context, table *models.Table) error {
	return r.db.WithContext(ctx).Save(table).Error
}

func (r *tableRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.Table{}, id).Error
}

func (r *tableRepository) UpdateStatus(ctx context.Context, id uuid.UUID, status string) error {
	return r.db.WithContext(ctx).Model(&models.Table{}).
		Where("id = ?", id).Update("status", status).Error
}

// AreaRepository defines the interface for area data access
type AreaRepository interface {
	Create(ctx context.Context, area *models.TableArea) error
	GetByID(ctx context.Context, id uuid.UUID) (*models.TableArea, error)
	GetByBranchID(ctx context.Context, branchID uuid.UUID, filters types.AreaFilters) ([]models.TableArea, int64, error)
	Update(ctx context.Context, area *models.TableArea) error
	Delete(ctx context.Context, id uuid.UUID) error
}

type areaRepository struct {
	db *gorm.DB
}

func NewAreaRepository(db *gorm.DB) AreaRepository {
	return &areaRepository{db: db}
}

func (r *areaRepository) Create(ctx context.Context, area *models.TableArea) error {
	return r.db.WithContext(ctx).Create(area).Error
}

func (r *areaRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.TableArea, error) {
	var area models.TableArea
	err := r.db.WithContext(ctx).Preload("Branch").First(&area, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("area not found")
		}
		return nil, err
	}
	return &area, nil
}

func (r *areaRepository) GetByBranchID(ctx context.Context, branchID uuid.UUID, filters types.AreaFilters) ([]models.TableArea, int64, error) {
	var areas []models.TableArea
	var total int64

	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	query := r.db.WithContext(ctx).Model(&models.TableArea{}).Where("branch_id = ?", branchID)

	// Apply filters
	if filters.IsActive != nil {
		query = query.Where("is_active = ?", *filters.IsActive)
	}
	if filters.Search != "" {
		searchTerm := "%" + strings.ToLower(filters.Search) + "%"
		query = query.Where(
			"LOWER(name) LIKE ? OR LOWER(description) LIKE ?",
			searchTerm, searchTerm,
		)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and get results
	offset := (filters.Page - 1) * filters.Limit
	err := query.Preload("Branch").
		Offset(offset).Limit(filters.Limit).
		Order("name ASC").
		Find(&areas).Error

	if err != nil {
		return nil, 0, err
	}

	return areas, total, nil
}

func (r *areaRepository) Update(ctx context.Context, area *models.TableArea) error {
	return r.db.WithContext(ctx).Save(area).Error
}

func (r *areaRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.TableArea{}, id).Error
}

// Layout functionality will be implemented later when the Layout model is created
