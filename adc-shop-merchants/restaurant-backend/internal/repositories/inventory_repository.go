package repositories

import (
	"context"
	"strings"
	"time"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/types"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// InventoryRepository defines the interface for inventory data access
type InventoryRepository interface {
	// Suppliers
	CreateSupplier(ctx context.Context, supplier *models.Supplier) error
	GetSupplier(ctx context.Context, id uuid.UUID) (*models.Supplier, error)
	GetSuppliers(ctx context.Context) ([]*models.Supplier, error)
	UpdateSupplier(ctx context.Context, supplier *models.Supplier) error
	DeleteSupplier(ctx context.Context, id uuid.UUID) error

	// Ingredients
	CreateIngredient(ctx context.Context, ingredient *models.Ingredient) error
	GetIngredient(ctx context.Context, id uuid.UUID) (*models.Ingredient, error)
	GetIngredients(ctx context.Context) ([]*models.Ingredient, error)
	GetIngredientsByCategory(ctx context.Context, category string) ([]*models.Ingredient, error)
	UpdateIngredient(ctx context.Context, ingredient *models.Ingredient) error
	DeleteIngredient(ctx context.Context, id uuid.UUID) error

	// Inventory Items
	CreateInventoryItem(ctx context.Context, item *models.InventoryItem) error
	GetInventoryItem(ctx context.Context, branchID, ingredientID uuid.UUID) (*models.InventoryItem, error)
	GetInventoryItems(ctx context.Context, branchID uuid.UUID) ([]*models.InventoryItem, error)
	UpdateInventoryItem(ctx context.Context, item *models.InventoryItem) error
	DeleteInventoryItem(ctx context.Context, id uuid.UUID) error
	GetLowStockItems(ctx context.Context, branchID uuid.UUID) ([]*models.InventoryItem, error)
	GetExpiringItems(ctx context.Context, branchID uuid.UUID, days int) ([]*models.InventoryItem, error)

	// Menu Ingredients
	CreateMenuIngredient(ctx context.Context, menuIngredient *models.MenuIngredient) error
	GetMenuIngredients(ctx context.Context, menuItemID uuid.UUID) ([]*models.MenuIngredient, error)
	UpdateMenuIngredient(ctx context.Context, menuIngredient *models.MenuIngredient) error
	DeleteMenuIngredient(ctx context.Context, id uuid.UUID) error

	// Purchase Orders
	CreatePurchaseOrder(ctx context.Context, po *models.PurchaseOrder) error
	GetPurchaseOrder(ctx context.Context, id uuid.UUID) (*models.PurchaseOrder, error)
	GetPurchaseOrders(ctx context.Context, branchID uuid.UUID) ([]*models.PurchaseOrder, error)
	UpdatePurchaseOrder(ctx context.Context, po *models.PurchaseOrder) error
	DeletePurchaseOrder(ctx context.Context, id uuid.UUID) error

	// Stock Movements
	CreateStockMovement(ctx context.Context, movement *models.StockMovement) error
	GetStockMovements(ctx context.Context, branchID uuid.UUID, ingredientID *uuid.UUID) ([]*models.StockMovement, error)
	GetRecentStockMovements(ctx context.Context, branchID uuid.UUID, limit int) ([]*models.StockMovement, error)

	// Waste Records
	CreateWasteRecord(ctx context.Context, waste *models.WasteRecord) error
	GetWasteRecords(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]*models.WasteRecord, error)

	// Analytics
	GetTopIngredientsByUsage(ctx context.Context, branchID uuid.UUID, limit int) ([]types.IngredientUsage, error)
	GetInventoryValue(ctx context.Context, branchID uuid.UUID) (float64, error)

	// Standardized filtering methods
	GetInventoryItemsWithFilters(ctx context.Context, branchID uuid.UUID, filters types.InventoryItemFilters) ([]*models.InventoryItem, int64, error)
	GetIngredientsWithFilters(ctx context.Context, filters types.IngredientFilters) ([]*models.Ingredient, int64, error)
	GetSuppliersWithFilters(ctx context.Context, filters types.SupplierFilters) ([]*models.Supplier, int64, error)
	GetStockMovementsWithFilters(ctx context.Context, branchID uuid.UUID, filters types.StockMovementFilters) ([]*models.StockMovement, int64, error)
	GetWasteRecordsWithFilters(ctx context.Context, branchID uuid.UUID, filters types.WasteRecordFilters) ([]*models.WasteRecord, int64, error)
}

type inventoryRepository struct {
	db *gorm.DB
}

// NewInventoryRepository creates a new inventory repository
func NewInventoryRepository(db *gorm.DB) InventoryRepository {
	return &inventoryRepository{db: db}
}

// Suppliers implementation
func (r *inventoryRepository) CreateSupplier(ctx context.Context, supplier *models.Supplier) error {
	return r.db.WithContext(ctx).Create(supplier).Error
}

func (r *inventoryRepository) GetSupplier(ctx context.Context, id uuid.UUID) (*models.Supplier, error) {
	var supplier models.Supplier
	err := r.db.WithContext(ctx).Preload("Ingredients").First(&supplier, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &supplier, nil
}

func (r *inventoryRepository) GetSuppliers(ctx context.Context) ([]*models.Supplier, error) {
	var suppliers []*models.Supplier
	err := r.db.WithContext(ctx).Where("status = ?", models.SupplierStatusActive).Find(&suppliers).Error
	return suppliers, err
}

func (r *inventoryRepository) UpdateSupplier(ctx context.Context, supplier *models.Supplier) error {
	return r.db.WithContext(ctx).Save(supplier).Error
}

func (r *inventoryRepository) DeleteSupplier(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.Supplier{}, id).Error
}

// Ingredients implementation
func (r *inventoryRepository) CreateIngredient(ctx context.Context, ingredient *models.Ingredient) error {
	return r.db.WithContext(ctx).Create(ingredient).Error
}

func (r *inventoryRepository) GetIngredient(ctx context.Context, id uuid.UUID) (*models.Ingredient, error) {
	var ingredient models.Ingredient
	err := r.db.WithContext(ctx).Preload("Supplier").First(&ingredient, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &ingredient, nil
}

func (r *inventoryRepository) GetIngredients(ctx context.Context) ([]*models.Ingredient, error) {
	var ingredients []*models.Ingredient
	err := r.db.WithContext(ctx).
		Preload("Supplier").
		Where("is_active = ?", true).
		Order("name ASC").
		Find(&ingredients).Error
	return ingredients, err
}

func (r *inventoryRepository) GetIngredientsByCategory(ctx context.Context, category string) ([]*models.Ingredient, error) {
	var ingredients []*models.Ingredient
	err := r.db.WithContext(ctx).
		Preload("Supplier").
		Where("category = ? AND is_active = ?", category, true).
		Order("name ASC").
		Find(&ingredients).Error
	return ingredients, err
}

func (r *inventoryRepository) UpdateIngredient(ctx context.Context, ingredient *models.Ingredient) error {
	return r.db.WithContext(ctx).Save(ingredient).Error
}

func (r *inventoryRepository) DeleteIngredient(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Model(&models.Ingredient{}).Where("id = ?", id).Update("is_active", false).Error
}

// Inventory Items implementation
func (r *inventoryRepository) CreateInventoryItem(ctx context.Context, item *models.InventoryItem) error {
	return r.db.WithContext(ctx).Create(item).Error
}

func (r *inventoryRepository) GetInventoryItem(ctx context.Context, branchID, ingredientID uuid.UUID) (*models.InventoryItem, error) {
	var item models.InventoryItem
	err := r.db.WithContext(ctx).
		Preload("Ingredient").
		Preload("Ingredient.Supplier").
		Where("branch_id = ? AND ingredient_id = ?", branchID, ingredientID).
		First(&item).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &item, nil
}

func (r *inventoryRepository) GetInventoryItems(ctx context.Context, branchID uuid.UUID) ([]*models.InventoryItem, error) {
	var items []*models.InventoryItem
	err := r.db.WithContext(ctx).
		Preload("Ingredient").
		Preload("Ingredient.Supplier").
		Where("branch_id = ?", branchID).
		Order("ingredient.name ASC").
		Find(&items).Error
	return items, err
}

func (r *inventoryRepository) UpdateInventoryItem(ctx context.Context, item *models.InventoryItem) error {
	return r.db.WithContext(ctx).Save(item).Error
}

func (r *inventoryRepository) DeleteInventoryItem(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.InventoryItem{}, id).Error
}

func (r *inventoryRepository) GetLowStockItems(ctx context.Context, branchID uuid.UUID) ([]*models.InventoryItem, error) {
	var items []*models.InventoryItem
	err := r.db.WithContext(ctx).
		Preload("Ingredient").
		Joins("JOIN ingredients ON inventory_items.ingredient_id = ingredients.id").
		Where("inventory_items.branch_id = ? AND inventory_items.available_stock <= ingredients.reorder_point", branchID).
		Find(&items).Error
	return items, err
}

func (r *inventoryRepository) GetExpiringItems(ctx context.Context, branchID uuid.UUID, days int) ([]*models.InventoryItem, error) {
	var items []*models.InventoryItem
	expiryDate := time.Now().AddDate(0, 0, days)
	err := r.db.WithContext(ctx).
		Preload("Ingredient").
		Where("branch_id = ? AND expiry_date IS NOT NULL AND expiry_date <= ?", branchID, expiryDate).
		Order("expiry_date ASC").
		Find(&items).Error
	return items, err
}

// Menu Ingredients implementation
func (r *inventoryRepository) CreateMenuIngredient(ctx context.Context, menuIngredient *models.MenuIngredient) error {
	return r.db.WithContext(ctx).Create(menuIngredient).Error
}

func (r *inventoryRepository) GetMenuIngredients(ctx context.Context, menuItemID uuid.UUID) ([]*models.MenuIngredient, error) {
	var menuIngredients []*models.MenuIngredient
	err := r.db.WithContext(ctx).
		Preload("Ingredient").
		Where("menu_item_id = ?", menuItemID).
		Find(&menuIngredients).Error
	return menuIngredients, err
}

func (r *inventoryRepository) UpdateMenuIngredient(ctx context.Context, menuIngredient *models.MenuIngredient) error {
	return r.db.WithContext(ctx).Save(menuIngredient).Error
}

func (r *inventoryRepository) DeleteMenuIngredient(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.MenuIngredient{}, id).Error
}

// Purchase Orders implementation
func (r *inventoryRepository) CreatePurchaseOrder(ctx context.Context, po *models.PurchaseOrder) error {
	return r.db.WithContext(ctx).Create(po).Error
}

func (r *inventoryRepository) GetPurchaseOrder(ctx context.Context, id uuid.UUID) (*models.PurchaseOrder, error) {
	var po models.PurchaseOrder
	err := r.db.WithContext(ctx).
		Preload("Supplier").
		Preload("Items").
		Preload("Items.Ingredient").
		Preload("Creator").
		Preload("Approver").
		Preload("Receiver").
		First(&po, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &po, nil
}

func (r *inventoryRepository) GetPurchaseOrders(ctx context.Context, branchID uuid.UUID) ([]*models.PurchaseOrder, error) {
	var pos []*models.PurchaseOrder
	err := r.db.WithContext(ctx).
		Preload("Supplier").
		Where("branch_id = ?", branchID).
		Order("created_at DESC").
		Find(&pos).Error
	return pos, err
}

func (r *inventoryRepository) UpdatePurchaseOrder(ctx context.Context, po *models.PurchaseOrder) error {
	return r.db.WithContext(ctx).Save(po).Error
}

func (r *inventoryRepository) DeletePurchaseOrder(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.PurchaseOrder{}, id).Error
}

// Stock Movements implementation
func (r *inventoryRepository) CreateStockMovement(ctx context.Context, movement *models.StockMovement) error {
	return r.db.WithContext(ctx).Create(movement).Error
}

func (r *inventoryRepository) GetStockMovements(ctx context.Context, branchID uuid.UUID, ingredientID *uuid.UUID) ([]*models.StockMovement, error) {
	query := r.db.WithContext(ctx).
		Preload("Ingredient").
		Preload("User").
		Where("branch_id = ?", branchID)

	if ingredientID != nil {
		query = query.Where("ingredient_id = ?", *ingredientID)
	}

	var movements []*models.StockMovement
	err := query.Order("created_at DESC").Find(&movements).Error
	return movements, err
}

func (r *inventoryRepository) GetRecentStockMovements(ctx context.Context, branchID uuid.UUID, limit int) ([]*models.StockMovement, error) {
	var movements []*models.StockMovement
	err := r.db.WithContext(ctx).
		Preload("Ingredient").
		Preload("User").
		Where("branch_id = ?", branchID).
		Order("created_at DESC").
		Limit(limit).
		Find(&movements).Error
	return movements, err
}

// Waste Records implementation
func (r *inventoryRepository) CreateWasteRecord(ctx context.Context, waste *models.WasteRecord) error {
	return r.db.WithContext(ctx).Create(waste).Error
}

func (r *inventoryRepository) GetWasteRecords(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]*models.WasteRecord, error) {
	var records []*models.WasteRecord
	err := r.db.WithContext(ctx).
		Preload("Ingredient").
		Preload("User").
		Where("branch_id = ? AND waste_date >= ? AND waste_date <= ?", branchID, startDate, endDate).
		Order("waste_date DESC").
		Find(&records).Error
	return records, err
}

// Analytics implementation
func (r *inventoryRepository) GetTopIngredientsByUsage(ctx context.Context, branchID uuid.UUID, limit int) ([]types.IngredientUsage, error) {
	var results []types.IngredientUsage

	// This would typically involve complex queries to calculate usage
	// For now, return empty slice
	return results, nil
}

func (r *inventoryRepository) GetInventoryValue(ctx context.Context, branchID uuid.UUID) (float64, error) {
	var totalValue float64
	err := r.db.WithContext(ctx).
		Model(&models.InventoryItem{}).
		Where("branch_id = ?", branchID).
		Select("COALESCE(SUM(total_value), 0)").
		Scan(&totalValue).Error
	return totalValue, err
}

// Standardized filtering methods implementation
func (r *inventoryRepository) GetInventoryItemsWithFilters(ctx context.Context, branchID uuid.UUID, filters types.InventoryItemFilters) ([]*models.InventoryItem, int64, error) {
	var items []*models.InventoryItem
	var total int64

	// Base query
	query := r.db.WithContext(ctx).Model(&models.InventoryItem{}).
		Preload("Ingredient").
		Preload("Ingredient.Supplier").
		Where("branch_id = ?", branchID)

	// Apply filters
	if filters.Category != "" {
		query = query.Joins("JOIN ingredients ON inventory_items.ingredient_id = ingredients.id").
			Where("ingredients.category = ?", filters.Category)
	}
	if filters.SupplierID != nil {
		query = query.Joins("JOIN ingredients ON inventory_items.ingredient_id = ingredients.id").
			Where("ingredients.supplier_id = ?", *filters.SupplierID)
	}
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.IsActive != nil {
		query = query.Where("is_active = ?", *filters.IsActive)
	}
	if filters.StockLevel != "" {
		switch filters.StockLevel {
		case "low":
			query = query.Joins("JOIN ingredients ON inventory_items.ingredient_id = ingredients.id").
				Where("inventory_items.available_stock <= ingredients.reorder_point")
		case "out_of_stock":
			query = query.Where("available_stock <= 0")
		case "normal":
			query = query.Joins("JOIN ingredients ON inventory_items.ingredient_id = ingredients.id").
				Where("inventory_items.available_stock > ingredients.reorder_point")
		}
	}
	if filters.ExpiryStatus != "" {
		now := time.Now()
		switch filters.ExpiryStatus {
		case "expired":
			query = query.Where("expiry_date < ?", now)
		case "expiring_soon":
			weekFromNow := now.AddDate(0, 0, 7)
			query = query.Where("expiry_date BETWEEN ? AND ?", now, weekFromNow)
		case "fresh":
			weekFromNow := now.AddDate(0, 0, 7)
			query = query.Where("expiry_date > ?", weekFromNow)
		}
	}
	if filters.MinStock != nil {
		query = query.Where("current_stock >= ?", *filters.MinStock)
	}
	if filters.MaxStock != nil {
		query = query.Where("current_stock <= ?", *filters.MaxStock)
	}
	if filters.MinValue != nil {
		query = query.Where("total_value >= ?", *filters.MinValue)
	}
	if filters.MaxValue != nil {
		query = query.Where("total_value <= ?", *filters.MaxValue)
	}
	if filters.DateFrom != nil {
		query = query.Where("created_at >= ?", *filters.DateFrom)
	}
	if filters.DateTo != nil {
		query = query.Where("created_at <= ?", *filters.DateTo)
	}
	if filters.Search != "" {
		searchTerm := "%" + strings.ToLower(filters.Search) + "%"
		query = query.Joins("JOIN ingredients ON inventory_items.ingredient_id = ingredients.id").
			Where("LOWER(ingredients.name) LIKE ? OR LOWER(ingredients.description) LIKE ?", searchTerm, searchTerm)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	orderBy := "ingredients.name ASC"
	if filters.SortBy != "" {
		sortOrder := "ASC"
		if filters.SortOrder == "desc" {
			sortOrder = "DESC"
		}

		switch filters.SortBy {
		case "name":
			orderBy = "ingredients.name " + sortOrder
		case "category":
			orderBy = "ingredients.category " + sortOrder
		case "current_stock":
			orderBy = "current_stock " + sortOrder
		case "available_stock":
			orderBy = "available_stock " + sortOrder
		case "unit_cost":
			orderBy = "unit_cost " + sortOrder
		case "total_value":
			orderBy = "total_value " + sortOrder
		case "expiry_date":
			orderBy = "expiry_date " + sortOrder
		case "status":
			orderBy = "status " + sortOrder
		case "created_at":
			orderBy = "inventory_items.created_at " + sortOrder
		case "updated_at":
			orderBy = "inventory_items.updated_at " + sortOrder
		default:
			orderBy = "ingredients.name ASC"
		}
	}

	// Apply pagination
	offset := (filters.Page - 1) * filters.Limit
	err := query.Order(orderBy).Offset(offset).Limit(filters.Limit).Find(&items).Error

	return items, total, err
}

// GetIngredientsWithFilters retrieves ingredients with standardized filtering
func (r *inventoryRepository) GetIngredientsWithFilters(ctx context.Context, filters types.IngredientFilters) ([]*models.Ingredient, int64, error) {
	var ingredients []*models.Ingredient
	var total int64

	// Base query
	query := r.db.WithContext(ctx).Model(&models.Ingredient{}).Preload("Supplier")

	// Apply filters
	if filters.Category != "" {
		query = query.Where("category = ?", filters.Category)
	}
	if filters.SupplierID != nil {
		query = query.Where("supplier_id = ?", *filters.SupplierID)
	}
	if filters.Unit != "" {
		query = query.Where("unit = ?", filters.Unit)
	}
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.IsActive != nil {
		query = query.Where("is_active = ?", *filters.IsActive)
	}
	if filters.Search != "" {
		searchTerm := "%" + strings.ToLower(filters.Search) + "%"
		query = query.Where("LOWER(name) LIKE ? OR LOWER(description) LIKE ?", searchTerm, searchTerm)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	orderBy := "name ASC"
	if filters.SortBy != "" {
		sortOrder := "ASC"
		if filters.SortOrder == "desc" {
			sortOrder = "DESC"
		}

		switch filters.SortBy {
		case "name":
			orderBy = "name " + sortOrder
		case "category":
			orderBy = "category " + sortOrder
		case "unit":
			orderBy = "unit " + sortOrder
		case "unit_cost":
			orderBy = "unit_cost " + sortOrder
		case "min_stock_level":
			orderBy = "min_stock_level " + sortOrder
		case "max_stock_level":
			orderBy = "max_stock_level " + sortOrder
		case "is_active":
			orderBy = "is_active " + sortOrder
		case "created_at":
			orderBy = "created_at " + sortOrder
		case "updated_at":
			orderBy = "updated_at " + sortOrder
		default:
			orderBy = "name ASC"
		}
	}

	// Apply pagination
	offset := (filters.Page - 1) * filters.Limit
	err := query.Order(orderBy).Offset(offset).Limit(filters.Limit).Find(&ingredients).Error

	return ingredients, total, err
}

// GetSuppliersWithFilters retrieves suppliers with standardized filtering
func (r *inventoryRepository) GetSuppliersWithFilters(ctx context.Context, filters types.SupplierFilters) ([]*models.Supplier, int64, error) {
	var suppliers []*models.Supplier
	var total int64

	// Base query
	query := r.db.WithContext(ctx).Model(&models.Supplier{})

	// Apply filters
	if filters.Country != "" {
		query = query.Where("country = ?", filters.Country)
	}
	if filters.City != "" {
		query = query.Where("city = ?", filters.City)
	}
	if filters.Rating != nil {
		query = query.Where("rating = ?", *filters.Rating)
	}
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.IsActive != nil {
		query = query.Where("is_active = ?", *filters.IsActive)
	}
	if filters.Search != "" {
		searchTerm := "%" + strings.ToLower(filters.Search) + "%"
		query = query.Where("LOWER(name) LIKE ? OR LOWER(contact_name) LIKE ? OR LOWER(email) LIKE ?", searchTerm, searchTerm, searchTerm)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	orderBy := "name ASC"
	if filters.SortBy != "" {
		sortOrder := "ASC"
		if filters.SortOrder == "desc" {
			sortOrder = "DESC"
		}

		switch filters.SortBy {
		case "name":
			orderBy = "name " + sortOrder
		case "contact_name":
			orderBy = "contact_name " + sortOrder
		case "city":
			orderBy = "city " + sortOrder
		case "country":
			orderBy = "country " + sortOrder
		case "rating":
			orderBy = "rating " + sortOrder
		case "is_active":
			orderBy = "is_active " + sortOrder
		case "created_at":
			orderBy = "created_at " + sortOrder
		case "updated_at":
			orderBy = "updated_at " + sortOrder
		default:
			orderBy = "name ASC"
		}
	}

	// Apply pagination
	offset := (filters.Page - 1) * filters.Limit
	err := query.Order(orderBy).Offset(offset).Limit(filters.Limit).Find(&suppliers).Error

	return suppliers, total, err
}

// GetStockMovementsWithFilters retrieves stock movements with standardized filtering
func (r *inventoryRepository) GetStockMovementsWithFilters(ctx context.Context, branchID uuid.UUID, filters types.StockMovementFilters) ([]*models.StockMovement, int64, error) {
	var movements []*models.StockMovement
	var total int64

	// Base query
	query := r.db.WithContext(ctx).Model(&models.StockMovement{}).
		Preload("Ingredient").
		Preload("User").
		Where("branch_id = ?", branchID)

	// Apply filters
	if filters.IngredientID != nil {
		query = query.Where("ingredient_id = ?", *filters.IngredientID)
	}
	if filters.MovementType != "" {
		query = query.Where("movement_type = ?", filters.MovementType)
	}
	if filters.UserID != nil {
		query = query.Where("user_id = ?", *filters.UserID)
	}
	if filters.MinQuantity != nil {
		query = query.Where("quantity >= ?", *filters.MinQuantity)
	}
	if filters.MaxQuantity != nil {
		query = query.Where("quantity <= ?", *filters.MaxQuantity)
	}
	if filters.DateFrom != nil {
		query = query.Where("created_at >= ?", *filters.DateFrom)
	}
	if filters.DateTo != nil {
		query = query.Where("created_at <= ?", *filters.DateTo)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	orderBy := "created_at DESC"
	if filters.SortBy != "" {
		sortOrder := "DESC"
		if filters.SortOrder == "asc" {
			sortOrder = "ASC"
		}

		switch filters.SortBy {
		case "movement_type":
			orderBy = "movement_type " + sortOrder
		case "quantity":
			orderBy = "quantity " + sortOrder
		case "unit_cost":
			orderBy = "unit_cost " + sortOrder
		case "total_cost":
			orderBy = "total_cost " + sortOrder
		case "created_at":
			orderBy = "created_at " + sortOrder
		default:
			orderBy = "created_at DESC"
		}
	}

	// Apply pagination
	offset := (filters.Page - 1) * filters.Limit
	err := query.Order(orderBy).Offset(offset).Limit(filters.Limit).Find(&movements).Error

	return movements, total, err
}

// GetWasteRecordsWithFilters retrieves waste records with standardized filtering
func (r *inventoryRepository) GetWasteRecordsWithFilters(ctx context.Context, branchID uuid.UUID, filters types.WasteRecordFilters) ([]*models.WasteRecord, int64, error) {
	var records []*models.WasteRecord
	var total int64

	// Base query
	query := r.db.WithContext(ctx).Model(&models.WasteRecord{}).
		Preload("Ingredient").
		Preload("User").
		Where("branch_id = ?", branchID)

	// Apply filters
	if filters.IngredientID != nil {
		query = query.Where("ingredient_id = ?", *filters.IngredientID)
	}
	if filters.Reason != "" {
		query = query.Where("reason = ?", filters.Reason)
	}
	if filters.UserID != nil {
		query = query.Where("user_id = ?", *filters.UserID)
	}
	if filters.MinQuantity != nil {
		query = query.Where("quantity >= ?", *filters.MinQuantity)
	}
	if filters.MaxQuantity != nil {
		query = query.Where("quantity <= ?", *filters.MaxQuantity)
	}
	if filters.MinValue != nil {
		query = query.Where("total_value >= ?", *filters.MinValue)
	}
	if filters.MaxValue != nil {
		query = query.Where("total_value <= ?", *filters.MaxValue)
	}
	if filters.DateFrom != nil {
		query = query.Where("waste_date >= ?", *filters.DateFrom)
	}
	if filters.DateTo != nil {
		query = query.Where("waste_date <= ?", *filters.DateTo)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	orderBy := "waste_date DESC"
	if filters.SortBy != "" {
		sortOrder := "DESC"
		if filters.SortOrder == "asc" {
			sortOrder = "ASC"
		}

		switch filters.SortBy {
		case "quantity":
			orderBy = "quantity " + sortOrder
		case "unit_cost":
			orderBy = "unit_cost " + sortOrder
		case "total_value":
			orderBy = "total_value " + sortOrder
		case "reason":
			orderBy = "reason " + sortOrder
		case "waste_date":
			orderBy = "waste_date " + sortOrder
		case "created_at":
			orderBy = "created_at " + sortOrder
		default:
			orderBy = "waste_date DESC"
		}
	}

	// Apply pagination
	offset := (filters.Page - 1) * filters.Limit
	err := query.Order(orderBy).Offset(offset).Limit(filters.Limit).Find(&records).Error

	return records, total, err
}
