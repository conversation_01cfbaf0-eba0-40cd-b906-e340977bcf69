package repositories

import (
	"context"
	"fmt"
	"strings"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/types"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// MenuCategoryRepository defines the interface for menu category data access
type MenuCategoryRepository interface {
	Create(ctx context.Context, category *models.MenuCategory) error
	GetByID(ctx context.Context, id uuid.UUID) (*models.MenuCategory, error)
	GetByBranchID(ctx context.Context, branchID uuid.UUID, filters types.CategoryFilters) ([]models.MenuCategory, int64, error)
	Update(ctx context.Context, category *models.MenuCategory) error
	Delete(ctx context.Context, id uuid.UUID) error
	GetBySlug(ctx context.Context, branchID uuid.UUID, slug string) (*models.MenuCategory, error)
}

type menuCategoryRepository struct {
	db *gorm.DB
}

func NewMenuCategoryRepository(db *gorm.DB) MenuCategoryRepository {
	return &menuCategoryRepository{db: db}
}

func (r *menuCategoryRepository) Create(ctx context.Context, category *models.MenuCategory) error {
	return r.db.WithContext(ctx).Create(category).Error
}

func (r *menuCategoryRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.MenuCategory, error) {
	var category models.MenuCategory
	err := r.db.WithContext(ctx).Preload("Branch").First(&category, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("category not found")
		}
		return nil, err
	}
	return &category, nil
}

func (r *menuCategoryRepository) GetBySlug(ctx context.Context, branchID uuid.UUID, slug string) (*models.MenuCategory, error) {
	var category models.MenuCategory
	err := r.db.WithContext(ctx).Preload("Branch").
		Where("branch_id = ? AND slug = ?", branchID, slug).First(&category).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("category not found")
		}
		return nil, err
	}
	return &category, nil
}

func (r *menuCategoryRepository) GetByBranchID(ctx context.Context, branchID uuid.UUID, filters types.CategoryFilters) ([]models.MenuCategory, int64, error) {
	var categories []models.MenuCategory
	var total int64

	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	query := r.db.WithContext(ctx).Model(&models.MenuCategory{}).Where("branch_id = ?", branchID)

	// Apply filters
	if filters.IsActive != nil {
		query = query.Where("is_active = ?", *filters.IsActive)
	}
	if filters.Search != "" {
		searchTerm := "%" + strings.ToLower(filters.Search) + "%"
		query = query.Where("LOWER(name) LIKE ? OR LOWER(description) LIKE ?", searchTerm, searchTerm)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and get results
	offset := (filters.Page - 1) * filters.Limit
	err := query.Preload("Branch").
		Offset(offset).Limit(filters.Limit).
		Order("sort_order ASC, name ASC").
		Find(&categories).Error
	if err != nil {
		return nil, 0, err
	}

	return categories, total, nil
}

func (r *menuCategoryRepository) Update(ctx context.Context, category *models.MenuCategory) error {
	return r.db.WithContext(ctx).Save(category).Error
}

func (r *menuCategoryRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.MenuCategory{}, id).Error
}

// MenuItemRepository defines the interface for menu item data access
type MenuItemRepository interface {
	Create(ctx context.Context, item *models.MenuItem) error
	GetByID(ctx context.Context, id uuid.UUID) (*models.MenuItem, error)
	GetByBranchID(ctx context.Context, branchID uuid.UUID, filters types.MenuItemFilters) ([]models.MenuItem, int64, error)
	Update(ctx context.Context, item *models.MenuItem) error
	Delete(ctx context.Context, id uuid.UUID) error
	GetBySlug(ctx context.Context, branchID uuid.UUID, slug string) (*models.MenuItem, error)
	UpdateAvailability(ctx context.Context, id uuid.UUID, isAvailable bool) error
	GetPopularItems(ctx context.Context, branchID uuid.UUID, limit int) ([]models.MenuItem, error)
}

type menuItemRepository struct {
	db *gorm.DB
}

func NewMenuItemRepository(db *gorm.DB) MenuItemRepository {
	return &menuItemRepository{db: db}
}

func (r *menuItemRepository) Create(ctx context.Context, item *models.MenuItem) error {
	return r.db.WithContext(ctx).Create(item).Error
}

func (r *menuItemRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.MenuItem, error) {
	var item models.MenuItem
	err := r.db.WithContext(ctx).First(&item, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("menu item not found")
		}
		return nil, err
	}
	return &item, nil
}

func (r *menuItemRepository) GetBySlug(ctx context.Context, branchID uuid.UUID, slug string) (*models.MenuItem, error) {
	var item models.MenuItem
	err := r.db.WithContext(ctx).
		Where("branch_id = ? AND slug = ?", branchID, slug).First(&item).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("menu item not found")
		}
		return nil, err
	}
	return &item, nil
}

func (r *menuItemRepository) GetByBranchID(ctx context.Context, branchID uuid.UUID, filters types.MenuItemFilters) ([]models.MenuItem, int64, error) {
	var items []models.MenuItem
	var total int64

	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	query := r.db.WithContext(ctx).Model(&models.MenuItem{}).Where("branch_id = ?", branchID)

	// Apply filters
	if filters.CategoryID != nil {
		query = query.Where("category_id = ?", *filters.CategoryID)
	}
	if filters.IsAvailable != nil {
		query = query.Where("is_available = ?", *filters.IsAvailable)
	}
	if filters.IsVegetarian != nil {
		query = query.Where("is_vegetarian = ?", *filters.IsVegetarian)
	}
	if filters.IsVegan != nil {
		query = query.Where("is_vegan = ?", *filters.IsVegan)
	}
	if filters.IsGlutenFree != nil {
		query = query.Where("is_gluten_free = ?", *filters.IsGlutenFree)
	}
	if filters.IsSpicy != nil {
		query = query.Where("is_spicy = ?", *filters.IsSpicy)
	}
	if filters.MinPrice != nil {
		query = query.Where("price >= ?", *filters.MinPrice)
	}
	if filters.MaxPrice != nil {
		query = query.Where("price <= ?", *filters.MaxPrice)
	}
	if filters.Search != "" {
		searchTerm := "%" + strings.ToLower(filters.Search) + "%"
		query = query.Where(
			"LOWER(name) LIKE ? OR LOWER(description) LIKE ?",
			searchTerm, searchTerm,
		)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and get results
	offset := (filters.Page - 1) * filters.Limit
	err := query.
		Offset(offset).Limit(filters.Limit).
		Order("name ASC").
		Find(&items).Error
	if err != nil {
		return nil, 0, err
	}

	return items, total, nil
}

func (r *menuItemRepository) Update(ctx context.Context, item *models.MenuItem) error {
	return r.db.WithContext(ctx).Save(item).Error
}

func (r *menuItemRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.MenuItem{}, id).Error
}

func (r *menuItemRepository) UpdateAvailability(ctx context.Context, id uuid.UUID, isAvailable bool) error {
	return r.db.WithContext(ctx).Model(&models.MenuItem{}).
		Where("id = ?", id).Update("is_available", isAvailable).Error
}

func (r *menuItemRepository) GetPopularItems(ctx context.Context, branchID uuid.UUID, limit int) ([]models.MenuItem, error) {
	var items []models.MenuItem

	// For now, we'll return available items ordered by name
	// In a real implementation, you might track order counts or ratings
	query := r.db.WithContext(ctx).
		Where("branch_id = ? AND is_available = ?", branchID, true).
		Order("name ASC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&items).Error
	return items, err
}
