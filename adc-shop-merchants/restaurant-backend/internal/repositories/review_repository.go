package repositories

import (
	"context"
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/types"
)

// ReviewRepository defines the interface for review data access
type ReviewRepository interface {
	Create(ctx context.Context, review *models.Review) error
	GetByID(ctx context.Context, id uuid.UUID) (*models.Review, error)
	GetByBranchID(ctx context.Context, branchID uuid.UUID, filters types.ReviewFilters) ([]models.Review, int64, error)
	Update(ctx context.Context, review *models.Review) error
	Delete(ctx context.Context, id uuid.UUID) error
	GetReviewStats(ctx context.Context, branchID uuid.UUID, period string) (*types.ReviewStats, error)
	GetRecentReviews(ctx context.Context, branchID uuid.UUID, limit int) ([]models.Review, error)
	GetPendingReviews(ctx context.Context, branchID uuid.UUID) ([]models.Review, error)
	GetReviewInsights(ctx context.Context, branchID uuid.UUID, period string) (*types.ReviewInsights, error)
	RespondToReview(ctx context.Context, reviewID uuid.UUID, response *models.Response) error
	UpdateReviewStatus(ctx context.Context, reviewID uuid.UUID, status string) error
}

type reviewRepository struct {
	db *gorm.DB
}

func NewReviewRepository(db *gorm.DB) ReviewRepository {
	return &reviewRepository{db: db}
}

func (r *reviewRepository) Create(ctx context.Context, review *models.Review) error {
	return r.db.WithContext(ctx).Create(review).Error
}

func (r *reviewRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.Review, error) {
	var review models.Review
	err := r.db.WithContext(ctx).
		Preload("Branch").
		Preload("Order").
		First(&review, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &review, nil
}

func (r *reviewRepository) GetByBranchID(ctx context.Context, branchID uuid.UUID, filters types.ReviewFilters) ([]models.Review, int64, error) {
	var reviews []models.Review
	var total int64

	query := r.db.WithContext(ctx).Model(&models.Review{}).Where("branch_id = ?", branchID)

	// Apply filters
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}

	if filters.Rating > 0 {
		query = query.Where("rating = ?", filters.Rating)
	}

	if filters.Source != "" {
		query = query.Where("source = ?", filters.Source)
	}

	if filters.Sentiment != "" {
		query = query.Where("sentiment = ?", filters.Sentiment)
	}

	if filters.Search != "" {
		searchTerm := "%" + strings.ToLower(filters.Search) + "%"
		query = query.Where(
			"LOWER(customer_name) LIKE ? OR LOWER(comment) LIKE ? OR LOWER(title) LIKE ?",
			searchTerm, searchTerm, searchTerm,
		)
	}

	if !filters.DateFrom.IsZero() {
		query = query.Where("created_at >= ?", filters.DateFrom)
	}

	if !filters.DateTo.IsZero() {
		query = query.Where("created_at <= ?", filters.DateTo)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	orderBy := "created_at DESC"
	if filters.SortBy != "" {
		switch filters.SortBy {
		case "rating":
			orderBy = "rating"
		case "date":
			orderBy = "created_at"
		case "customer":
			orderBy = "customer_name"
		}

		if filters.SortOrder == "asc" {
			orderBy += " ASC"
		} else {
			orderBy += " DESC"
		}
	}

	// Apply pagination and get results
	offset := (filters.Page - 1) * filters.Limit
	err := query.Preload("Branch").Preload("Order").
		Offset(offset).Limit(filters.Limit).
		Order(orderBy).
		Find(&reviews).Error
	if err != nil {
		return nil, 0, err
	}

	return reviews, total, nil
}

func (r *reviewRepository) Update(ctx context.Context, review *models.Review) error {
	return r.db.WithContext(ctx).Save(review).Error
}

func (r *reviewRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.Review{}, id).Error
}

func (r *reviewRepository) GetReviewStats(ctx context.Context, branchID uuid.UUID, period string) (*types.ReviewStats, error) {
	var stats types.ReviewStats

	// Calculate date range based on period
	var dateFrom time.Time
	now := time.Now()

	switch period {
	case "7d":
		dateFrom = now.AddDate(0, 0, -7)
	case "30d":
		dateFrom = now.AddDate(0, 0, -30)
	case "90d":
		dateFrom = now.AddDate(0, 0, -90)
	case "1y":
		dateFrom = now.AddDate(-1, 0, 0)
	default:
		dateFrom = now.AddDate(0, 0, -30) // Default to 30 days
	}

	// Get total reviews count
	if err := r.db.WithContext(ctx).Model(&models.Review{}).
		Where("branch_id = ? AND created_at >= ?", branchID, dateFrom).
		Count(&stats.TotalReviews).Error; err != nil {
		return nil, err
	}

	// Get average rating
	var avgRating float64
	if err := r.db.WithContext(ctx).Model(&models.Review{}).
		Where("branch_id = ? AND created_at >= ?", branchID, dateFrom).
		Select("COALESCE(AVG(rating), 0)").
		Scan(&avgRating).Error; err != nil {
		return nil, err
	}
	stats.AverageRating = avgRating

	// Get rating distribution
	var ratingCounts []struct {
		Rating int   `json:"rating"`
		Count  int64 `json:"count"`
	}

	if err := r.db.WithContext(ctx).Model(&models.Review{}).
		Where("branch_id = ? AND created_at >= ?", branchID, dateFrom).
		Select("rating, COUNT(*) as count").
		Group("rating").
		Order("rating DESC").
		Scan(&ratingCounts).Error; err != nil {
		return nil, err
	}

	stats.RatingDistribution = make(map[int]int64)
	for _, rc := range ratingCounts {
		stats.RatingDistribution[rc.Rating] = rc.Count
	}

	// Get response rate
	var totalWithResponse int64
	if err := r.db.WithContext(ctx).Model(&models.Review{}).
		Where("branch_id = ? AND created_at >= ? AND response IS NOT NULL", branchID, dateFrom).
		Count(&totalWithResponse).Error; err != nil {
		return nil, err
	}

	if stats.TotalReviews > 0 {
		stats.ResponseRate = float64(totalWithResponse) / float64(stats.TotalReviews) * 100
	}

	return &stats, nil
}

func (r *reviewRepository) GetRecentReviews(ctx context.Context, branchID uuid.UUID, limit int) ([]models.Review, error) {
	var reviews []models.Review
	err := r.db.WithContext(ctx).
		Where("branch_id = ?", branchID).
		Preload("Branch").
		Preload("Order").
		Order("created_at DESC").
		Limit(limit).
		Find(&reviews).Error

	return reviews, err
}

func (r *reviewRepository) GetPendingReviews(ctx context.Context, branchID uuid.UUID) ([]models.Review, error) {
	var reviews []models.Review
	err := r.db.WithContext(ctx).
		Where("branch_id = ? AND status = ?", branchID, models.ReviewStatusPending).
		Preload("Branch").
		Preload("Order").
		Order("created_at DESC").
		Find(&reviews).Error

	return reviews, err
}

func (r *reviewRepository) RespondToReview(ctx context.Context, reviewID uuid.UUID, response *models.Response) error {
	return r.db.WithContext(ctx).Model(&models.Review{}).
		Where("id = ?", reviewID).
		Update("response", response).Error
}

func (r *reviewRepository) UpdateReviewStatus(ctx context.Context, reviewID uuid.UUID, status string) error {
	return r.db.WithContext(ctx).Model(&models.Review{}).
		Where("id = ?", reviewID).
		Update("status", status).Error
}

func (r *reviewRepository) GetReviewInsights(ctx context.Context, branchID uuid.UUID, period string) (*types.ReviewInsights, error) {
	// Calculate date range based on period
	var dateFrom time.Time
	now := time.Now()

	switch period {
	case "7d":
		dateFrom = now.AddDate(0, 0, -7)
	case "30d":
		dateFrom = now.AddDate(0, 0, -30)
	case "90d":
		dateFrom = now.AddDate(0, 0, -90)
	case "1y":
		dateFrom = now.AddDate(-1, 0, 0)
	default:
		dateFrom = now.AddDate(0, 0, -30) // Default to 30 days
	}

	insights := &types.ReviewInsights{
		CommonKeywords:   []types.KeywordInsight{},
		ImprovementAreas: []string{},
		Strengths:        []string{},
	}

	// Get common keywords from comments (simplified implementation)
	var reviews []models.Review
	if err := r.db.WithContext(ctx).
		Where("branch_id = ? AND created_at >= ?", branchID, dateFrom).
		Select("comment, rating, sentiment").
		Find(&reviews).Error; err != nil {
		return nil, err
	}

	// Simple keyword analysis
	keywordMap := make(map[string]*types.KeywordInsight)
	positiveKeywords := []string{"excellent", "great", "amazing", "wonderful", "fantastic", "delicious", "perfect", "outstanding"}
	negativeKeywords := []string{"terrible", "awful", "horrible", "disgusting", "worst", "bad", "poor", "disappointing"}

	for _, review := range reviews {
		comment := strings.ToLower(review.Comment)

		// Check for positive keywords
		for _, keyword := range positiveKeywords {
			if strings.Contains(comment, keyword) {
				if insight, exists := keywordMap[keyword]; exists {
					insight.Count++
				} else {
					keywordMap[keyword] = &types.KeywordInsight{
						Word:      keyword,
						Count:     1,
						Sentiment: "positive",
					}
				}
			}
		}

		// Check for negative keywords
		for _, keyword := range negativeKeywords {
			if strings.Contains(comment, keyword) {
				if insight, exists := keywordMap[keyword]; exists {
					insight.Count++
				} else {
					keywordMap[keyword] = &types.KeywordInsight{
						Word:      keyword,
						Count:     1,
						Sentiment: "negative",
					}
				}
			}
		}
	}

	// Convert map to slice and sort by count
	for _, insight := range keywordMap {
		insights.CommonKeywords = append(insights.CommonKeywords, *insight)
	}

	// Generate improvement areas based on negative feedback
	insights.ImprovementAreas = []string{
		"Service speed",
		"Food quality",
		"Staff friendliness",
		"Cleanliness",
		"Value for money",
	}

	// Generate strengths based on positive feedback
	insights.Strengths = []string{
		"Food taste",
		"Atmosphere",
		"Location",
		"Menu variety",
		"Customer service",
	}

	return insights, nil
}
