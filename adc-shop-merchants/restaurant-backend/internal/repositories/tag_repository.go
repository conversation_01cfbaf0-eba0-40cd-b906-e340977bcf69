package repositories

import (
	"context"
	"strings"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/types"
)

type TagRepository interface {
	// Tag CRUD operations
	CreateTag(ctx context.Context, tag *models.Tag) error
	GetTagByID(ctx context.Context, id uuid.UUID) (*models.Tag, error)
	GetTagBySlug(ctx context.Context, branchID uuid.UUID, slug string) (*models.Tag, error)
	UpdateTag(ctx context.Context, tag *models.Tag) error
	DeleteTag(ctx context.Context, id uuid.UUID) error
	GetTagsByBranchID(ctx context.Context, branchID uuid.UUID, filters types.TagFilters) ([]models.Tag, int64, error)

	// Tag Category CRUD operations
	CreateTagCategory(ctx context.Context, category *models.TagCategory) error
	GetTagCategoryByID(ctx context.Context, id uuid.UUID) (*models.TagCategory, error)
	GetTagCategoryBySlug(ctx context.Context, branchID uuid.UUID, slug string) (*models.TagCategory, error)
	UpdateTagCategory(ctx context.Context, category *models.TagCategory) error
	DeleteTagCategory(ctx context.Context, id uuid.UUID) error
	GetTagCategoriesByBranchID(ctx context.Context, branchID uuid.UUID, filters types.TagCategoryFilters) ([]models.TagCategory, int64, error)

	// Entity Tag operations
	AssignTagsToEntity(ctx context.Context, entityType string, entityID uuid.UUID, tagIDs []uuid.UUID) error
	RemoveTagsFromEntity(ctx context.Context, entityType string, entityID uuid.UUID, tagIDs []uuid.UUID) error
	GetEntityTags(ctx context.Context, entityType string, entityID uuid.UUID) ([]models.EntityTag, error)
	GetEntitiesByTags(ctx context.Context, entityType string, tagIDs []uuid.UUID) ([]uuid.UUID, error)

	// Utility operations
	GetPopularTags(ctx context.Context, branchID uuid.UUID, limit int) ([]models.Tag, error)
	GetTagsByCategory(ctx context.Context, branchID uuid.UUID, category string) ([]models.Tag, error)
	SearchTags(ctx context.Context, branchID uuid.UUID, query string, limit int) ([]models.Tag, error)
	GetTagAnalytics(ctx context.Context, branchID uuid.UUID) (*types.TagAnalyticsResponse, error)
}

type tagRepository struct {
	db *gorm.DB
}

func NewTagRepository(db *gorm.DB) TagRepository {
	return &tagRepository{db: db}
}

// Tag CRUD operations
func (r *tagRepository) CreateTag(ctx context.Context, tag *models.Tag) error {
	return r.db.WithContext(ctx).Create(tag).Error
}

func (r *tagRepository) GetTagByID(ctx context.Context, id uuid.UUID) (*models.Tag, error) {
	var tag models.Tag
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&tag).Error
	if err != nil {
		return nil, err
	}
	return &tag, nil
}

func (r *tagRepository) GetTagBySlug(ctx context.Context, branchID uuid.UUID, slug string) (*models.Tag, error) {
	var tag models.Tag
	err := r.db.WithContext(ctx).Where("branch_id = ? AND slug = ?", branchID, slug).First(&tag).Error
	if err != nil {
		return nil, err
	}
	return &tag, nil
}

func (r *tagRepository) UpdateTag(ctx context.Context, tag *models.Tag) error {
	return r.db.WithContext(ctx).Save(tag).Error
}

func (r *tagRepository) DeleteTag(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Delete entity tag relationships first
		if err := tx.Where("tag_id = ?", id).Delete(&models.EntityTag{}).Error; err != nil {
			return err
		}
		// Delete the tag
		return tx.Delete(&models.Tag{}, id).Error
	})
}

func (r *tagRepository) GetTagsByBranchID(ctx context.Context, branchID uuid.UUID, filters types.TagFilters) ([]models.Tag, int64, error) {
	var tags []models.Tag
	var total int64

	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	query := r.db.WithContext(ctx).Model(&models.Tag{}).Where("branch_id = ?", branchID)

	// Apply filters
	if filters.Category != "" {
		query = query.Where("category = ?", filters.Category)
	}
	if filters.IsActive != nil {
		query = query.Where("is_active = ?", *filters.IsActive)
	}
	if filters.IsSystem != nil {
		query = query.Where("is_system = ?", *filters.IsSystem)
	}
	if filters.Search != "" {
		searchTerm := "%" + strings.ToLower(filters.Search) + "%"
		query = query.Where("LOWER(name) LIKE ? OR LOWER(description) LIKE ?", searchTerm, searchTerm)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	if filters.SortBy != "" {
		order := filters.SortBy
		if filters.SortOrder == "desc" {
			order += " DESC"
		}
		query = query.Order(order)
	} else {
		query = query.Order("name ASC")
	}

	// Apply pagination
	offset := (filters.Page - 1) * filters.Limit
	if err := query.Offset(offset).Limit(filters.Limit).Find(&tags).Error; err != nil {
		return nil, 0, err
	}

	return tags, total, nil
}

// Tag Category CRUD operations
func (r *tagRepository) CreateTagCategory(ctx context.Context, category *models.TagCategory) error {
	return r.db.WithContext(ctx).Create(category).Error
}

func (r *tagRepository) GetTagCategoryByID(ctx context.Context, id uuid.UUID) (*models.TagCategory, error) {
	var category models.TagCategory
	err := r.db.WithContext(ctx).Preload("Tags").Where("id = ?", id).First(&category).Error
	if err != nil {
		return nil, err
	}
	return &category, nil
}

func (r *tagRepository) GetTagCategoryBySlug(ctx context.Context, branchID uuid.UUID, slug string) (*models.TagCategory, error) {
	var category models.TagCategory
	err := r.db.WithContext(ctx).Preload("Tags").Where("branch_id = ? AND slug = ?", branchID, slug).First(&category).Error
	if err != nil {
		return nil, err
	}
	return &category, nil
}

func (r *tagRepository) UpdateTagCategory(ctx context.Context, category *models.TagCategory) error {
	return r.db.WithContext(ctx).Save(category).Error
}

func (r *tagRepository) DeleteTagCategory(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Update tags to remove category reference
		if err := tx.Model(&models.Tag{}).Where("category = (SELECT slug FROM tag_categories WHERE id = ?)", id).Update("category", "").Error; err != nil {
			return err
		}
		// Delete the category
		return tx.Delete(&models.TagCategory{}, id).Error
	})
}

func (r *tagRepository) GetTagCategoriesByBranchID(ctx context.Context, branchID uuid.UUID, filters types.TagCategoryFilters) ([]models.TagCategory, int64, error) {
	var categories []models.TagCategory
	var total int64

	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	query := r.db.WithContext(ctx).Model(&models.TagCategory{}).Where("branch_id = ?", branchID)

	// Apply filters
	if filters.IsActive != nil {
		query = query.Where("is_active = ?", *filters.IsActive)
	}
	if filters.IsSystem != nil {
		query = query.Where("is_system = ?", *filters.IsSystem)
	}
	if filters.Search != "" {
		searchTerm := "%" + strings.ToLower(filters.Search) + "%"
		query = query.Where("LOWER(name) LIKE ? OR LOWER(description) LIKE ?", searchTerm, searchTerm)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	if filters.SortBy != "" {
		order := filters.SortBy
		if filters.SortOrder == "desc" {
			order += " DESC"
		}
		query = query.Order(order)
	} else {
		query = query.Order("sort_order ASC, name ASC")
	}

	// Apply pagination and preload tags
	offset := (filters.Page - 1) * filters.Limit
	if err := query.Preload("Tags", "is_active = ?", true).Offset(offset).Limit(filters.Limit).Find(&categories).Error; err != nil {
		return nil, 0, err
	}

	return categories, total, nil
}

// Entity Tag operations
func (r *tagRepository) AssignTagsToEntity(ctx context.Context, entityType string, entityID uuid.UUID, tagIDs []uuid.UUID) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Remove existing tags for this entity
		if err := tx.Where("entity_type = ? AND entity_id = ?", entityType, entityID).Delete(&models.EntityTag{}).Error; err != nil {
			return err
		}

		// Add new tags
		for _, tagID := range tagIDs {
			entityTag := &models.EntityTag{
				TagID:      tagID,
				EntityType: entityType,
				EntityID:   entityID,
			}
			if err := tx.Create(entityTag).Error; err != nil {
				return err
			}

			// Increment usage count for the tag
			if err := tx.Model(&models.Tag{}).Where("id = ?", tagID).Update("usage_count", gorm.Expr("usage_count + ?", 1)).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

func (r *tagRepository) RemoveTagsFromEntity(ctx context.Context, entityType string, entityID uuid.UUID, tagIDs []uuid.UUID) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Remove entity tag relationships
		if err := tx.Where("entity_type = ? AND entity_id = ? AND tag_id IN ?", entityType, entityID, tagIDs).Delete(&models.EntityTag{}).Error; err != nil {
			return err
		}

		// Decrement usage count for each tag
		for _, tagID := range tagIDs {
			if err := tx.Model(&models.Tag{}).Where("id = ? AND usage_count > 0", tagID).Update("usage_count", gorm.Expr("usage_count - ?", 1)).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

func (r *tagRepository) GetEntityTags(ctx context.Context, entityType string, entityID uuid.UUID) ([]models.EntityTag, error) {
	var entityTags []models.EntityTag
	err := r.db.WithContext(ctx).Preload("Tag").Where("entity_type = ? AND entity_id = ?", entityType, entityID).Find(&entityTags).Error
	return entityTags, err
}

func (r *tagRepository) GetEntitiesByTags(ctx context.Context, entityType string, tagIDs []uuid.UUID) ([]uuid.UUID, error) {
	var entityIDs []uuid.UUID
	err := r.db.WithContext(ctx).Model(&models.EntityTag{}).
		Where("entity_type = ? AND tag_id IN ?", entityType, tagIDs).
		Distinct("entity_id").
		Pluck("entity_id", &entityIDs).Error
	return entityIDs, err
}

// Utility operations
func (r *tagRepository) GetPopularTags(ctx context.Context, branchID uuid.UUID, limit int) ([]models.Tag, error) {
	var tags []models.Tag
	err := r.db.WithContext(ctx).Where("branch_id = ? AND is_active = ?", branchID, true).
		Order("usage_count DESC").
		Limit(limit).
		Find(&tags).Error
	return tags, err
}

func (r *tagRepository) GetTagsByCategory(ctx context.Context, branchID uuid.UUID, category string) ([]models.Tag, error) {
	var tags []models.Tag
	err := r.db.WithContext(ctx).Where("branch_id = ? AND category = ? AND is_active = ?", branchID, category, true).
		Order("name ASC").
		Find(&tags).Error
	return tags, err
}

func (r *tagRepository) SearchTags(ctx context.Context, branchID uuid.UUID, query string, limit int) ([]models.Tag, error) {
	var tags []models.Tag
	searchTerm := "%" + strings.ToLower(query) + "%"
	err := r.db.WithContext(ctx).Where("branch_id = ? AND is_active = ? AND (LOWER(name) LIKE ? OR LOWER(description) LIKE ?)",
		branchID, true, searchTerm, searchTerm).
		Order("usage_count DESC, name ASC").
		Limit(limit).
		Find(&tags).Error
	return tags, err
}

func (r *tagRepository) GetTagAnalytics(ctx context.Context, branchID uuid.UUID) (*types.TagAnalyticsResponse, error) {
	analytics := &types.TagAnalyticsResponse{}

	// Get total counts
	var totalTags, totalCategories int64
	r.db.WithContext(ctx).Model(&models.Tag{}).Where("branch_id = ?", branchID).Count(&totalTags)
	r.db.WithContext(ctx).Model(&models.TagCategory{}).Where("branch_id = ?", branchID).Count(&totalCategories)
	analytics.TotalTags = int(totalTags)
	analytics.TotalCategories = int(totalCategories)

	// Get most used tags
	mostUsedTags, _ := r.GetPopularTags(ctx, branchID, 10)
	for _, tag := range mostUsedTags {
		analytics.MostUsedTags = append(analytics.MostUsedTags, types.TagResponse{
			ID:          tag.ID,
			BranchID:    tag.BranchID,
			Name:        tag.Name,
			Slug:        tag.Slug,
			Description: tag.Description,
			Category:    tag.Category,
			Color:       tag.Color,
			Icon:        tag.Icon,
			UsageCount:  tag.UsageCount,
			IsSystem:    tag.IsSystem,
			IsActive:    tag.IsActive,
			CreatedAt:   tag.CreatedAt,
			UpdatedAt:   tag.UpdatedAt,
		})
	}

	// Get tags by category
	var categoryStats []struct {
		Category string
		Count    int64
	}
	r.db.WithContext(ctx).Model(&models.Tag{}).
		Where("branch_id = ?", branchID).
		Group("category").
		Select("category, COUNT(*) as count").
		Scan(&categoryStats)

	analytics.TagsByCategory = make(map[string]int)
	for _, stat := range categoryStats {
		analytics.TagsByCategory[stat.Category] = int(stat.Count)
	}

	// Get recently created tags
	var recentTags []models.Tag
	r.db.WithContext(ctx).Where("branch_id = ?", branchID).
		Order("created_at DESC").
		Limit(5).
		Find(&recentTags)

	for _, tag := range recentTags {
		analytics.RecentlyCreated = append(analytics.RecentlyCreated, types.TagResponse{
			ID:          tag.ID,
			BranchID:    tag.BranchID,
			Name:        tag.Name,
			Slug:        tag.Slug,
			Description: tag.Description,
			Category:    tag.Category,
			Color:       tag.Color,
			Icon:        tag.Icon,
			UsageCount:  tag.UsageCount,
			IsSystem:    tag.IsSystem,
			IsActive:    tag.IsActive,
			CreatedAt:   tag.CreatedAt,
			UpdatedAt:   tag.UpdatedAt,
		})
	}

	// Get unused tags
	var unusedTags []models.Tag
	r.db.WithContext(ctx).Where("branch_id = ? AND usage_count = 0", branchID).
		Order("created_at DESC").
		Limit(10).
		Find(&unusedTags)

	for _, tag := range unusedTags {
		analytics.UnusedTags = append(analytics.UnusedTags, types.TagResponse{
			ID:          tag.ID,
			BranchID:    tag.BranchID,
			Name:        tag.Name,
			Slug:        tag.Slug,
			Description: tag.Description,
			Category:    tag.Category,
			Color:       tag.Color,
			Icon:        tag.Icon,
			UsageCount:  tag.UsageCount,
			IsSystem:    tag.IsSystem,
			IsActive:    tag.IsActive,
			CreatedAt:   tag.CreatedAt,
			UpdatedAt:   tag.UpdatedAt,
		})
	}

	return analytics, nil
}
