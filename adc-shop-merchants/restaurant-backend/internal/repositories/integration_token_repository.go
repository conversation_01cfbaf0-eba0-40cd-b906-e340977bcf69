package repositories

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"time"

	"restaurant-backend/internal/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// IntegrationTokenRepository handles database operations for integration tokens
type IntegrationTokenRepository struct {
	db *gorm.DB
}

// NewIntegrationTokenRepository creates a new integration token repository
func NewIntegrationTokenRepository(db *gorm.DB) *IntegrationTokenRepository {
	return &IntegrationTokenRepository{db: db}
}

// Create creates a new integration token
func (r *IntegrationTokenRepository) Create(ctx context.Context, token *models.IntegrationToken) error {
	return r.db.WithContext(ctx).Create(token).Error
}

// GetByID retrieves an integration token by ID
func (r *IntegrationTokenRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.IntegrationToken, error) {
	var token models.IntegrationToken
	err := r.db.WithContext(ctx).
		Preload("Shop").
		Preload("Branch").
		Preload("User").
		First(&token, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &token, nil
}

// GetByTokenHash retrieves an integration token by its hash
func (r *IntegrationTokenRepository) GetByTokenHash(ctx context.Context, tokenHash string) (*models.IntegrationToken, error) {
	var token models.IntegrationToken
	err := r.db.WithContext(ctx).
		Preload("Shop").
		Preload("Branch").
		Preload("User").
		First(&token, "token_hash = ? AND status = ?", tokenHash, models.TokenStatusActive).Error
	if err != nil {
		return nil, err
	}
	return &token, nil
}

// GetByShopID retrieves all integration tokens for a shop
func (r *IntegrationTokenRepository) GetByShopID(ctx context.Context, shopID uuid.UUID, limit, offset int) ([]*models.IntegrationToken, int64, error) {
	var tokens []*models.IntegrationToken
	var total int64

	// Count total
	if err := r.db.WithContext(ctx).Model(&models.IntegrationToken{}).
		Where("shop_id = ?", shopID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get tokens with pagination
	err := r.db.WithContext(ctx).
		Preload("Shop").
		Preload("Branch").
		Preload("User").
		Where("shop_id = ?", shopID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&tokens).Error

	return tokens, total, err
}

// GetByUserID retrieves all integration tokens created by a user
func (r *IntegrationTokenRepository) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*models.IntegrationToken, int64, error) {
	var tokens []*models.IntegrationToken
	var total int64

	// Count total
	if err := r.db.WithContext(ctx).Model(&models.IntegrationToken{}).
		Where("user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get tokens with pagination
	err := r.db.WithContext(ctx).
		Preload("Shop").
		Preload("Branch").
		Preload("User").
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&tokens).Error

	return tokens, total, err
}

// Update updates an integration token
func (r *IntegrationTokenRepository) Update(ctx context.Context, token *models.IntegrationToken) error {
	return r.db.WithContext(ctx).Save(token).Error
}

// Delete deletes an integration token
func (r *IntegrationTokenRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.IntegrationToken{}, "id = ?", id).Error
}

// RevokeToken revokes an integration token
func (r *IntegrationTokenRepository) RevokeToken(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Model(&models.IntegrationToken{}).
		Where("id = ?", id).
		Update("status", models.TokenStatusRevoked).Error
}

// GetExpiredTokens retrieves all expired tokens
func (r *IntegrationTokenRepository) GetExpiredTokens(ctx context.Context) ([]*models.IntegrationToken, error) {
	var tokens []*models.IntegrationToken
	now := time.Now()
	
	err := r.db.WithContext(ctx).
		Where("expires_at IS NOT NULL AND expires_at < ? AND status = ?", now, models.TokenStatusActive).
		Find(&tokens).Error
	
	return tokens, err
}

// MarkExpiredTokens marks expired tokens as expired
func (r *IntegrationTokenRepository) MarkExpiredTokens(ctx context.Context) error {
	now := time.Now()
	
	return r.db.WithContext(ctx).Model(&models.IntegrationToken{}).
		Where("expires_at IS NOT NULL AND expires_at < ? AND status = ?", now, models.TokenStatusActive).
		Update("status", models.TokenStatusExpired).Error
}

// ValidateAndGetToken validates a token string and returns the token model
func (r *IntegrationTokenRepository) ValidateAndGetToken(ctx context.Context, tokenString string) (*models.IntegrationToken, error) {
	// Hash the token
	hash := sha256.Sum256([]byte(tokenString))
	tokenHash := hex.EncodeToString(hash[:])
	
	// Get token by hash
	token, err := r.GetByTokenHash(ctx, tokenHash)
	if err != nil {
		return nil, fmt.Errorf("invalid token: %w", err)
	}
	
	// Check if token is active
	if !token.IsActive() {
		return nil, fmt.Errorf("token is not active")
	}
	
	// Check rate limiting
	if token.IsRateLimited() {
		return nil, fmt.Errorf("rate limit exceeded")
	}
	
	return token, nil
}

// CreateTokenUsage creates a new token usage record
func (r *IntegrationTokenRepository) CreateTokenUsage(ctx context.Context, usage *models.IntegrationTokenUsage) error {
	return r.db.WithContext(ctx).Create(usage).Error
}

// GetTokenUsage retrieves token usage statistics
func (r *IntegrationTokenRepository) GetTokenUsage(ctx context.Context, tokenID uuid.UUID, from, to time.Time) ([]*models.IntegrationTokenUsage, error) {
	var usage []*models.IntegrationTokenUsage
	
	err := r.db.WithContext(ctx).
		Where("token_id = ? AND timestamp BETWEEN ? AND ?", tokenID, from, to).
		Order("timestamp DESC").
		Find(&usage).Error
	
	return usage, err
}

// GetTokenUsageStats retrieves aggregated usage statistics for a token
func (r *IntegrationTokenRepository) GetTokenUsageStats(ctx context.Context, tokenID uuid.UUID, from, to time.Time) (map[string]interface{}, error) {
	var stats struct {
		TotalRequests int64   `json:"total_requests"`
		SuccessRate   float64 `json:"success_rate"`
		AvgResponseTime float64 `json:"avg_response_time"`
		ErrorCount    int64   `json:"error_count"`
	}
	
	// Get total requests
	err := r.db.WithContext(ctx).Model(&models.IntegrationTokenUsage{}).
		Where("token_id = ? AND timestamp BETWEEN ? AND ?", tokenID, from, to).
		Count(&stats.TotalRequests).Error
	if err != nil {
		return nil, err
	}
	
	// Get success rate and average response time
	var result struct {
		SuccessCount    int64   `json:"success_count"`
		AvgResponseTime float64 `json:"avg_response_time"`
		ErrorCount      int64   `json:"error_count"`
	}
	
	err = r.db.WithContext(ctx).Model(&models.IntegrationTokenUsage{}).
		Select(`
			COUNT(CASE WHEN status_code >= 200 AND status_code < 400 THEN 1 END) as success_count,
			AVG(response_time) as avg_response_time,
			COUNT(CASE WHEN status_code >= 400 THEN 1 END) as error_count
		`).
		Where("token_id = ? AND timestamp BETWEEN ? AND ?", tokenID, from, to).
		Scan(&result).Error
	if err != nil {
		return nil, err
	}
	
	// Calculate success rate
	if stats.TotalRequests > 0 {
		stats.SuccessRate = float64(result.SuccessCount) / float64(stats.TotalRequests) * 100
	}
	stats.AvgResponseTime = result.AvgResponseTime
	stats.ErrorCount = result.ErrorCount
	
	return map[string]interface{}{
		"total_requests":    stats.TotalRequests,
		"success_rate":      stats.SuccessRate,
		"avg_response_time": stats.AvgResponseTime,
		"error_count":       stats.ErrorCount,
	}, nil
}

// CleanupOldUsageRecords removes old usage records to keep the database clean
func (r *IntegrationTokenRepository) CleanupOldUsageRecords(ctx context.Context, olderThan time.Time) error {
	return r.db.WithContext(ctx).
		Where("timestamp < ?", olderThan).
		Delete(&models.IntegrationTokenUsage{}).Error
}
