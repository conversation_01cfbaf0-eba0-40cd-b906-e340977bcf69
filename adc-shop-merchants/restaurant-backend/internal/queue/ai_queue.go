package queue

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"restaurant-backend/internal/services"

	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// AIJobMessage represents a message for AI generation job processing
type AIJobMessage struct {
	JobID     uuid.UUID `json:"job_id"`
	Type      string    `json:"type"`
	Priority  int       `json:"priority"` // 1=high, 2=normal, 3=low
	CreatedAt time.Time `json:"created_at"`
}

// AIJobQueue handles AI generation job queuing and processing
type AIJobQueue struct {
	redis     *redis.Client
	aiService *services.AIGenerationService
	logger    *logrus.Logger
	workers   int
	queueName string
	pubSubKey string
}

// NewAIJobQueue creates a new AI job queue
func NewAIJobQueue(
	redisClient *redis.Client,
	aiService *services.AIGenerationService,
	logger *logrus.Logger,
	workers int,
) *AIJobQueue {
	return &AIJobQueue{
		redis:     redisClient,
		aiService: aiService,
		logger:    logger,
		workers:   workers,
		queueName: "ai_generation_jobs",
		pubSubKey: "ai_job_notifications",
	}
}

// EnqueueJob adds a new AI generation job to the queue
func (q *AIJobQueue) EnqueueJob(ctx context.Context, jobID uuid.UUID, jobType string, priority int) error {
	message := AIJobMessage{
		JobID:     jobID,
		Type:      jobType,
		Priority:  priority,
		CreatedAt: time.Now(),
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal job message: %w", err)
	}

	// Add to priority queue (lower score = higher priority)
	score := float64(priority)*1000 + float64(time.Now().Unix())
	err = q.redis.ZAdd(ctx, q.queueName, &redis.Z{
		Score:  score,
		Member: string(messageBytes),
	}).Err()
	if err != nil {
		return fmt.Errorf("failed to enqueue job: %w", err)
	}

	// Publish notification for immediate processing
	err = q.redis.Publish(ctx, q.pubSubKey, string(messageBytes)).Err()
	if err != nil {
		q.logger.WithError(err).Warn("Failed to publish job notification")
	}

	q.logger.WithFields(logrus.Fields{
		"job_id":   jobID,
		"type":     jobType,
		"priority": priority,
	}).Info("AI generation job enqueued")

	return nil
}

// StartWorkers starts the specified number of worker goroutines
func (q *AIJobQueue) StartWorkers(ctx context.Context) {
	q.logger.WithField("workers", q.workers).Info("Starting AI job queue workers")

	// Start pub/sub listener for immediate notifications
	go q.startPubSubListener(ctx)

	// Start workers
	for i := 0; i < q.workers; i++ {
		go q.worker(ctx, i)
	}

	// Start periodic queue checker (fallback)
	go q.periodicQueueChecker(ctx)
}

// worker processes jobs from the queue
func (q *AIJobQueue) worker(ctx context.Context, workerID int) {
	logger := q.logger.WithField("worker_id", workerID)
	logger.Info("AI job worker started")

	for {
		select {
		case <-ctx.Done():
			logger.Info("AI job worker stopping")
			return
		default:
			if err := q.processNextJob(ctx, workerID); err != nil {
				logger.WithError(err).Error("Failed to process job")
				time.Sleep(5 * time.Second) // Back off on error
			}
		}
	}
}

// processNextJob gets and processes the next job from the queue
func (q *AIJobQueue) processNextJob(ctx context.Context, workerID int) error {
	// Get highest priority job (lowest score)
	result, err := q.redis.ZPopMin(ctx, q.queueName, 1).Result()
	if err == redis.Nil {
		// No jobs available, wait a bit
		time.Sleep(1 * time.Second)
		return nil
	}
	if err != nil {
		return fmt.Errorf("failed to pop job from queue: %w", err)
	}

	if len(result) == 0 {
		time.Sleep(1 * time.Second)
		return nil
	}

	// Parse job message
	var message AIJobMessage
	if err := json.Unmarshal([]byte(result[0].Member.(string)), &message); err != nil {
		return fmt.Errorf("failed to unmarshal job message: %w", err)
	}

	logger := q.logger.WithFields(logrus.Fields{
		"worker_id": workerID,
		"job_id":    message.JobID,
		"type":      message.Type,
	})

	logger.Info("Processing AI generation job")

	// Process the job
	if err := q.aiService.ProcessJobAsync(ctx, message.JobID); err != nil {
		logger.WithError(err).Error("Failed to process AI generation job")

		// Optionally re-queue with lower priority or move to dead letter queue
		return err
	}

	logger.Info("AI generation job completed successfully")
	return nil
}

// startPubSubListener listens for job notifications for immediate processing
func (q *AIJobQueue) startPubSubListener(ctx context.Context) {
	pubsub := q.redis.Subscribe(ctx, q.pubSubKey)
	defer pubsub.Close()

	q.logger.Info("AI job pub/sub listener started")

	for {
		select {
		case <-ctx.Done():
			q.logger.Info("AI job pub/sub listener stopping")
			return
		default:
			msg, err := pubsub.ReceiveMessage(ctx)
			if err != nil {
				q.logger.WithError(err).Error("Failed to receive pub/sub message")
				time.Sleep(1 * time.Second)
				continue
			}

			q.logger.WithField("message", msg.Payload).Debug("Received job notification")
			// The actual processing will be handled by workers polling the queue
		}
	}
}

// periodicQueueChecker ensures jobs don't get stuck (fallback mechanism)
func (q *AIJobQueue) periodicQueueChecker(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			count, err := q.redis.ZCard(ctx, q.queueName).Result()
			if err != nil {
				q.logger.WithError(err).Error("Failed to check queue size")
				continue
			}

			if count > 0 {
				q.logger.WithField("pending_jobs", count).Info("Pending AI generation jobs in queue")
			}
		}
	}
}

// GetQueueStats returns statistics about the queue
func (q *AIJobQueue) GetQueueStats(ctx context.Context) (map[string]interface{}, error) {
	count, err := q.redis.ZCard(ctx, q.queueName).Result()
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"pending_jobs": count,
		"queue_name":   q.queueName,
		"workers":      q.workers,
	}, nil
}
