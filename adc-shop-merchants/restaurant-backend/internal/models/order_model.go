package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Order represents a customer order
type Order struct {
	BaseModel
	BranchID           uuid.UUID  `json:"branch_id" gorm:"type:uuid;not null;index"`
	OrderNumber        string     `json:"order_number" gorm:"type:varchar(50);uniqueIndex;not null"`
	CustomerID         *uuid.UUID `json:"customer_id" gorm:"type:uuid;index"`
	CustomerName       string     `json:"customer_name" gorm:"type:varchar(255)"`
	CustomerPhone      string     `json:"customer_phone" gorm:"type:varchar(50)"`
	CustomerEmail      string     `json:"customer_email" gorm:"type:varchar(255)"`
	TableID            *uuid.UUID `json:"table_id" gorm:"type:uuid;index"`
	Status             string     `json:"status" gorm:"type:varchar(20);default:'pending'"`
	Type               string     `json:"type" gorm:"type:varchar(20);default:'dine-in'"`
	Subtotal           float64    `json:"subtotal" gorm:"type:decimal(10,2);not null;default:0"`
	Tax                float64    `json:"tax" gorm:"type:decimal(10,2);not null;default:0"`
	ServiceCharge      float64    `json:"service_charge" gorm:"type:decimal(10,2);not null;default:0"`
	Discount           float64    `json:"discount" gorm:"type:decimal(10,2);not null;default:0"`
	Tip                float64    `json:"tip" gorm:"type:decimal(10,2);not null;default:0"`
	Total              float64    `json:"total" gorm:"type:decimal(10,2);not null;default:0"`
	PaymentStatus      string     `json:"payment_status" gorm:"type:varchar(20);default:'pending'"`
	PaymentMethod      string     `json:"payment_method" gorm:"type:varchar(50)"`
	Notes              string     `json:"notes" gorm:"type:text"`
	EstimatedTime      *int       `json:"estimated_time"` // in minutes
	ServedAt           *time.Time `json:"served_at"`
	CompletedAt        *time.Time `json:"completed_at"`
	CancelledAt        *time.Time `json:"cancelled_at"`
	CancellationReason string     `json:"cancellation_reason" gorm:"type:text"`

	// Relationships
	Branch   ShopBranch  `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	Table    *Table      `json:"table,omitempty" gorm:"foreignKey:TableID"`
	Items    []OrderItem `json:"items,omitempty" gorm:"foreignKey:OrderID;constraint:OnDelete:CASCADE"`
	Payments []Payment   `json:"payments,omitempty" gorm:"foreignKey:OrderID;constraint:OnDelete:CASCADE"`
}

// OrderItem represents an item within an order
type OrderItem struct {
	BaseModel
	OrderID       uuid.UUID         `json:"order_id" gorm:"type:uuid;not null;index"`
	MenuItemID    *uuid.UUID        `json:"menu_item_id" gorm:"type:uuid;index"`
	Name          string            `json:"name" gorm:"type:varchar(255);not null"`
	Price         float64           `json:"price" gorm:"type:decimal(10,2);not null"`
	Quantity      int               `json:"quantity" gorm:"not null;default:1"`
	Modifications ModificationsData `json:"modifications" gorm:"type:jsonb"`
	Notes         string            `json:"notes" gorm:"type:text"`
	Total         float64           `json:"total" gorm:"type:decimal(10,2);not null"`
	Status        string            `json:"status" gorm:"type:varchar(20);default:'pending'"`

	// Relationships
	Order    Order     `json:"order,omitempty" gorm:"foreignKey:OrderID"`
	MenuItem *MenuItem `json:"menu_item,omitempty" gorm:"foreignKey:MenuItemID"`
}

// Payment represents a payment for an order
type Payment struct {
	BaseModel
	OrderID       uuid.UUID  `json:"order_id" gorm:"type:uuid;not null;index"`
	Amount        float64    `json:"amount" gorm:"type:decimal(10,2);not null"`
	Method        string     `json:"method" gorm:"type:varchar(50);not null"`
	Status        string     `json:"status" gorm:"type:varchar(20);default:'pending'"`
	TransactionID string     `json:"transaction_id" gorm:"type:varchar(255)"`
	ProcessedAt   *time.Time `json:"processed_at"`
	FailureReason string     `json:"failure_reason" gorm:"type:text"`

	// Relationships
	Order Order `json:"order,omitempty" gorm:"foreignKey:OrderID"`
}

// ModificationsData represents order item modifications
type ModificationsData []OrderModification

// Scan implements the sql.Scanner interface for ModificationsData
func (m *ModificationsData) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, m)
}

// Value implements the driver.Valuer interface for ModificationsData
func (m ModificationsData) Value() (driver.Value, error) {
	return json.Marshal(m)
}

// TableName specifies the table name for Order
func (Order) TableName() string {
	return "orders"
}

// TableName specifies the table name for OrderItem
func (OrderItem) TableName() string {
	return "order_items"
}

// TableName specifies the table name for Payment
func (Payment) TableName() string {
	return "payments"
}

// BeforeCreate hook for Order
func (o *Order) BeforeCreate(tx *gorm.DB) error {
	if err := o.BaseModel.BeforeCreate(tx); err != nil {
		return err
	}

	// Generate order number if not provided
	if o.OrderNumber == "" {
		o.OrderNumber = generateOrderNumber()
	}

	return nil
}

// BeforeCreate hook for OrderItem
func (oi *OrderItem) BeforeCreate(tx *gorm.DB) error {
	if err := oi.BaseModel.BeforeCreate(tx); err != nil {
		return err
	}

	// Initialize modifications if nil
	if oi.Modifications == nil {
		oi.Modifications = ModificationsData{}
	}

	// Calculate total if not set
	if oi.Total == 0 {
		oi.CalculateTotal()
	}

	return nil
}

// CalculateTotal calculates the total for the order
func (o *Order) CalculateTotal() {
	o.Subtotal = 0
	for _, item := range o.Items {
		o.Subtotal += item.Total
	}

	// Apply discount
	discountedSubtotal := o.Subtotal - o.Discount

	// Calculate tax on discounted subtotal
	o.Tax = discountedSubtotal * 0.08 // 8% tax rate (should come from settings)

	// Calculate service charge
	o.ServiceCharge = discountedSubtotal * 0.0 // No service charge by default

	// Calculate total
	o.Total = discountedSubtotal + o.Tax + o.ServiceCharge + o.Tip
}

// CalculateTotal calculates the total for the order item
func (oi *OrderItem) CalculateTotal() {
	baseTotal := oi.Price * float64(oi.Quantity)

	// Add modification costs
	for _, mod := range oi.Modifications {
		baseTotal += mod.Price * float64(oi.Quantity)
	}

	oi.Total = baseTotal
}

// AddItem adds an item to the order
func (o *Order) AddItem(item OrderItem) {
	item.OrderID = o.ID
	item.CalculateTotal()
	o.Items = append(o.Items, item)
	o.CalculateTotal()
}

// RemoveItem removes an item from the order
func (o *Order) RemoveItem(itemID uuid.UUID) {
	for i, item := range o.Items {
		if item.ID == itemID {
			o.Items = append(o.Items[:i], o.Items[i+1:]...)
			break
		}
	}
	o.CalculateTotal()
}

// UpdateItemQuantity updates the quantity of an order item
func (o *Order) UpdateItemQuantity(itemID uuid.UUID, quantity int) {
	for i := range o.Items {
		if o.Items[i].ID == itemID {
			o.Items[i].Quantity = quantity
			o.Items[i].CalculateTotal()
			break
		}
	}
	o.CalculateTotal()
}

// GetItemCount returns the total number of items in the order
func (o *Order) GetItemCount() int {
	count := 0
	for _, item := range o.Items {
		count += item.Quantity
	}
	return count
}

// IsEditable returns true if the order can be edited
func (o *Order) IsEditable() bool {
	return o.Status == OrderStatusPending || o.Status == OrderStatusConfirmed
}

// IsCancellable returns true if the order can be cancelled
func (o *Order) IsCancellable() bool {
	return o.Status != OrderStatusCompleted && o.Status != OrderStatusCancelled
}

// CanBeServed returns true if the order can be marked as served
func (o *Order) CanBeServed() bool {
	return o.Status == OrderStatusReady
}

// CanBeCompleted returns true if the order can be marked as completed
func (o *Order) CanBeCompleted() bool {
	return o.Status == OrderStatusServed
}

// GetEstimatedCompletionTime returns the estimated completion time
func (o *Order) GetEstimatedCompletionTime() *time.Time {
	if o.EstimatedTime == nil {
		return nil
	}

	estimatedTime := o.CreatedAt.Add(time.Duration(*o.EstimatedTime) * time.Minute)
	return &estimatedTime
}

// GetDuration returns the duration from creation to completion
func (o *Order) GetDuration() *time.Duration {
	if o.CompletedAt == nil {
		return nil
	}

	duration := o.CompletedAt.Sub(o.CreatedAt)
	return &duration
}

// GetCustomerInfo returns formatted customer information
func (o *Order) GetCustomerInfo() string {
	if o.CustomerName == "" {
		return "Walk-in Customer"
	}

	info := o.CustomerName
	if o.CustomerPhone != "" {
		info += " (" + o.CustomerPhone + ")"
	}

	return info
}

// GetStatusColor returns a color code for the order status
func (o *Order) GetStatusColor() string {
	switch o.Status {
	case OrderStatusPending:
		return "#FFA500" // Orange
	case OrderStatusConfirmed:
		return "#0066CC" // Blue
	case OrderStatusPreparing:
		return "#FF6600" // Dark Orange
	case OrderStatusReady:
		return "#00CC66" // Green
	case OrderStatusServed:
		return "#6600CC" // Purple
	case OrderStatusCompleted:
		return "#666666" // Gray
	case OrderStatusCancelled:
		return "#CC0000" // Red
	default:
		return "#666666" // Gray
	}
}

// AddModification adds a modification to the order item
func (oi *OrderItem) AddModification(modification OrderModification) {
	oi.Modifications = append(oi.Modifications, modification)
	oi.CalculateTotal()
}

// RemoveModification removes a modification from the order item
func (oi *OrderItem) RemoveModification(optionID uuid.UUID) {
	for i, mod := range oi.Modifications {
		if mod.OptionID == optionID {
			oi.Modifications = append(oi.Modifications[:i], oi.Modifications[i+1:]...)
			break
		}
	}
	oi.CalculateTotal()
}

// GetModificationsCost returns the total cost of modifications
func (oi *OrderItem) GetModificationsCost() float64 {
	cost := 0.0
	for _, mod := range oi.Modifications {
		cost += mod.Price
	}
	return cost * float64(oi.Quantity)
}

// GetFormattedModifications returns a formatted string of modifications
func (oi *OrderItem) GetFormattedModifications() string {
	if len(oi.Modifications) == 0 {
		return ""
	}

	var mods []string
	for _, mod := range oi.Modifications {
		if mod.Price > 0 {
			mods = append(mods, fmt.Sprintf("%s (+$%.2f)", mod.Name, mod.Price))
		} else {
			mods = append(mods, mod.Name)
		}
	}

	return fmt.Sprintf("Modifications: %s", mods)
}

// AddPayment adds a payment to the order
func (o *Order) AddPayment(payment Payment) {
	payment.OrderID = o.ID
	o.Payments = append(o.Payments, payment)
}

// GetTotalPaid returns the total amount paid for the order
func (o *Order) GetTotalPaid() float64 {
	total := 0.0
	for _, payment := range o.Payments {
		if payment.Status == PaymentStatusPaid {
			total += payment.Amount
		}
	}
	return total
}

// GetRemainingBalance returns the remaining balance to be paid
func (o *Order) GetRemainingBalance() float64 {
	return o.Total - o.GetTotalPaid()
}

// IsFullyPaid returns true if the order is fully paid
func (o *Order) IsFullyPaid() bool {
	return o.GetRemainingBalance() <= 0.01 // Allow for small rounding differences
}

// Helper functions

func generateOrderNumber() string {
	// Generate order number with timestamp and random component
	now := time.Now()
	return fmt.Sprintf("ORD-%d%02d%02d-%04d",
		now.Year(), now.Month(), now.Day(),
		now.Hour()*100+now.Minute())
}

// Order status constants (already defined in base.go, but repeated here for clarity)
const (
	// Order item statuses
	OrderItemStatusPending   = "pending"
	OrderItemStatusPreparing = "preparing"
	OrderItemStatusReady     = "ready"
	OrderItemStatusServed    = "served"
	OrderItemStatusCancelled = "cancelled"
)
