package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Tag represents a centralized tag that can be applied to various entities
type Tag struct {
	BaseModel
	BranchID    uuid.UUID `json:"branch_id" gorm:"type:uuid;not null;index"`
	Name        string    `json:"name" gorm:"type:varchar(100);not null"`
	Slug        string    `json:"slug" gorm:"type:varchar(100);not null"`
	Description string    `json:"description" gorm:"type:text"`
	Category    string    `json:"category" gorm:"type:varchar(50);not null"` // dietary, style, popularity, etc.
	Color       string    `json:"color" gorm:"type:varchar(7);default:'#8a745c'"`
	Icon        string    `json:"icon" gorm:"type:varchar(50)"`
	UsageCount  int       `json:"usage_count" gorm:"default:0"`
	IsSystem    bool      `json:"is_system" gorm:"default:false"` // System-generated tags
	IsActive    bool      `json:"is_active" gorm:"default:true"`

	// Relationships
	Branch ShopBranch `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
}

// TagCategory represents predefined tag categories
type TagCategory struct {
	BaseModel
	BranchID    uuid.UUID `json:"branch_id" gorm:"type:uuid;not null;index"`
	Name        string    `json:"name" gorm:"type:varchar(100);not null"`
	Slug        string    `json:"slug" gorm:"type:varchar(100);not null"`
	Description string    `json:"description" gorm:"type:text"`
	Color       string    `json:"color" gorm:"type:varchar(7);default:'#8a745c'"`
	Icon        string    `json:"icon" gorm:"type:varchar(50)"`
	SortOrder   int       `json:"sort_order" gorm:"default:0"`
	IsSystem    bool      `json:"is_system" gorm:"default:false"`
	IsActive    bool      `json:"is_active" gorm:"default:true"`

	// Relationships
	Branch ShopBranch `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
}

// EntityTag represents the many-to-many relationship between tags and entities
type EntityTag struct {
	ID         uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	TagID      uuid.UUID `json:"tag_id" gorm:"type:uuid;not null;index"`
	EntityType string    `json:"entity_type" gorm:"type:varchar(50);not null"` // menu_item, review, service, etc.
	EntityID   uuid.UUID `json:"entity_id" gorm:"type:uuid;not null;index"`
	CreatedAt  time.Time `json:"created_at" gorm:"autoCreateTime"`

	// Relationships
	Tag Tag `json:"tag,omitempty" gorm:"foreignKey:TagID"`
}

// TableName specifies the table name for Tag
func (Tag) TableName() string {
	return "tags"
}

// TableName specifies the table name for TagCategory
func (TagCategory) TableName() string {
	return "tag_categories"
}

// TableName specifies the table name for EntityTag
func (EntityTag) TableName() string {
	return "entity_tags"
}

// BeforeCreate hook for Tag
func (t *Tag) BeforeCreate(tx *gorm.DB) error {
	if err := t.BaseModel.BeforeCreate(tx); err != nil {
		return err
	}

	// Generate slug if not provided
	if t.Slug == "" {
		t.Slug = Slugify(t.Name)
	}

	return nil
}

// BeforeCreate hook for TagCategory
func (tc *TagCategory) BeforeCreate(tx *gorm.DB) error {
	if err := tc.BaseModel.BeforeCreate(tx); err != nil {
		return err
	}

	// Generate slug if not provided
	if tc.Slug == "" {
		tc.Slug = Slugify(tc.Name)
	}

	return nil
}

// IncrementUsage increments the usage count for a tag
func (t *Tag) IncrementUsage(tx *gorm.DB) error {
	return tx.Model(t).Update("usage_count", gorm.Expr("usage_count + ?", 1)).Error
}

// DecrementUsage decrements the usage count for a tag
func (t *Tag) DecrementUsage(tx *gorm.DB) error {
	return tx.Model(t).Where("usage_count > 0").Update("usage_count", gorm.Expr("usage_count - ?", 1)).Error
}

// GetPopularTags returns the most used tags for a branch
func GetPopularTags(tx *gorm.DB, branchID uuid.UUID, limit int) ([]Tag, error) {
	var tags []Tag
	err := tx.Where("branch_id = ? AND is_active = ?", branchID, true).
		Order("usage_count DESC").
		Limit(limit).
		Find(&tags).Error
	return tags, err
}

// GetTagsByCategory returns tags filtered by category
func GetTagsByCategory(tx *gorm.DB, branchID uuid.UUID, category string) ([]Tag, error) {
	var tags []Tag
	err := tx.Where("branch_id = ? AND category = ? AND is_active = ?", branchID, category, true).
		Order("name ASC").
		Find(&tags).Error
	return tags, err
}
