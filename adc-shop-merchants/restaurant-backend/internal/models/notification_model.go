package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
)

// JSONB represents a JSONB field that can be properly scanned from PostgreSQL
type JSONB map[string]interface{}

// <PERSON><PERSON> implements the sql.Scanner interface for JSONB
func (j *JSONB) Scan(value interface{}) error {
	if value == nil {
		*j = make(map[string]interface{})
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into JSONB", value)
	}

	if len(bytes) == 0 {
		*j = make(map[string]interface{})
		return nil
	}

	return json.Unmarshal(bytes, j)
}

// Value implements the driver.Valuer interface for JSONB
func (j JSONB) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Notification represents a notification in the system
type Notification struct {
	BaseModel
	ShopID   uuid.UUID  `json:"shop_id" gorm:"type:uuid;not null;index"`
	BranchID uuid.UUID  `json:"branch_id" gorm:"type:uuid;not null;index"`
	UserID   *uuid.UUID `json:"user_id" gorm:"type:uuid;index"` // Optional - for user-specific notifications

	// Content
	Title   string `json:"title" gorm:"type:varchar(255);not null"`
	Message string `json:"message" gorm:"type:text;not null"`

	// Classification
	Type     string `json:"type" gorm:"type:varchar(50);not null;index"`       // order, reservation, review, system, staff, inventory, payment, promotion
	Priority string `json:"priority" gorm:"type:varchar(20);default:'medium'"` // low, medium, high, urgent

	// Status
	IsRead bool `json:"is_read" gorm:"default:false;index"`

	// Optional fields
	Link        *string `json:"link" gorm:"type:varchar(500)"`         // URL to navigate to
	ActionLabel *string `json:"action_label" gorm:"type:varchar(100)"` // Label for action button
	Data        JSONB   `json:"data" gorm:"type:jsonb"`                // Additional metadata

	// Timestamps
	Timestamp time.Time `json:"timestamp" gorm:"not null;index;default:CURRENT_TIMESTAMP"`

	// Relationships
	Shop   Shop       `json:"shop,omitempty" gorm:"foreignKey:ShopID"`
	Branch ShopBranch `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	User   *User      `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// NotificationStats represents notification statistics
type NotificationStats struct {
	TotalNotifications        int            `json:"total_notifications"`
	UnreadNotifications       int            `json:"unread_notifications"`
	ReadNotifications         int            `json:"read_notifications"`
	UrgentNotifications       int            `json:"urgent_notifications"`
	HighPriorityNotifications int            `json:"high_priority_notifications"`
	ByType                    map[string]int `json:"by_type"`
	ByPriority                map[string]int `json:"by_priority"`
}

// NotificationFilters represents filters for notification queries
type NotificationFilters struct {
	Type      *string    `json:"type"`
	Priority  *string    `json:"priority"`
	IsRead    *bool      `json:"is_read"`
	Search    *string    `json:"search"`
	StartDate *time.Time `json:"start_date"`
	EndDate   *time.Time `json:"end_date"`
	DateRange *string    `json:"date_range"` // today, yesterday, last_7_days, last_30_days
}

// NotificationRequest represents a request to create a notification
type NotificationRequest struct {
	Title       string     `json:"title" binding:"required"`
	Message     string     `json:"message" binding:"required"`
	Type        string     `json:"type" binding:"required"`
	Priority    *string    `json:"priority"`
	Link        *string    `json:"link"`
	ActionLabel *string    `json:"action_label"`
	Data        JSONB      `json:"data"`
	UserID      *uuid.UUID `json:"user_id"`
}

// NotificationUpdateRequest represents a request to update a notification
type NotificationUpdateRequest struct {
	IsRead *bool `json:"is_read"`
}

// BulkNotificationUpdateRequest represents a request to bulk update notifications
type BulkNotificationUpdateRequest struct {
	NotificationIDs []uuid.UUID               `json:"notification_ids" binding:"required"`
	Updates         NotificationUpdateRequest `json:"updates" binding:"required"`
}

// TableName overrides the table name for GORM
func (Notification) TableName() string {
	return "notifications"
}

// ValidateType checks if the notification type is valid
func (n *Notification) ValidateType() bool {
	validTypes := []string{"order", "reservation", "review", "system", "staff", "inventory", "payment", "promotion"}
	for _, validType := range validTypes {
		if n.Type == validType {
			return true
		}
	}
	return false
}

// ValidatePriority checks if the notification priority is valid
func (n *Notification) ValidatePriority() bool {
	validPriorities := []string{"low", "medium", "high", "urgent"}
	for _, validPriority := range validPriorities {
		if n.Priority == validPriority {
			return true
		}
	}
	return false
}

// IsUrgent returns true if the notification is urgent
func (n *Notification) IsUrgent() bool {
	return n.Priority == "urgent"
}

// IsHighPriority returns true if the notification is high priority or urgent
func (n *Notification) IsHighPriority() bool {
	return n.Priority == "high" || n.Priority == "urgent"
}
