package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"

	"github.com/google/uuid"
)

// Floor represents a floor level in a restaurant branch
type Floor struct {
	BaseModel
	BranchID    uuid.UUID `json:"branch_id" gorm:"type:uuid;not null;index"`
	Name        string    `json:"name" gorm:"type:varchar(255);not null"`
	Description string    `json:"description" gorm:"type:text"`
	Order       int       `json:"order" gorm:"not null;default:1"` // Display order (1 = ground floor, 2 = second floor, etc.)
	IsActive    bool      `json:"is_active" gorm:"default:true"`

	// Floor layout configuration
	Layout FloorLayout `json:"layout" gorm:"type:jsonb"`

	// Relationships
	Branch ShopBranch  `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	Areas  []TableArea `json:"areas,omitempty" gorm:"foreignKey:FloorID"`
}

// FloorLayout represents the layout configuration for a floor
type FloorLayout struct {
	Width           float64 `json:"width"`            // Canvas width in pixels
	Height          float64 `json:"height"`           // Canvas height in pixels
	BackgroundImage string  `json:"background_image"` // URL to background image
	GridSize        int     `json:"grid_size"`        // Grid size for snapping
	ShowGrid        bool    `json:"show_grid"`        // Whether to show grid
}

// Scan implements the sql.Scanner interface for FloorLayout
func (fl *FloorLayout) Scan(value interface{}) error {
	if value == nil {
		// Set default values if null
		fl.Width = 800
		fl.Height = 600
		fl.GridSize = 20
		fl.ShowGrid = true
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into FloorLayout", value)
	}

	// Try to unmarshal, if it fails, set default values
	if err := json.Unmarshal(bytes, fl); err != nil {
		// Set default values if JSON is invalid
		fl.Width = 800
		fl.Height = 600
		fl.GridSize = 20
		fl.ShowGrid = true
		return nil
	}

	// Ensure we have valid values
	if fl.Width <= 0 {
		fl.Width = 800
	}
	if fl.Height <= 0 {
		fl.Height = 600
	}
	if fl.GridSize <= 0 {
		fl.GridSize = 20
	}

	return nil
}

// Value implements the driver.Valuer interface for FloorLayout
func (fl FloorLayout) Value() (driver.Value, error) {
	return json.Marshal(fl)
}

// TableName returns the table name for Floor model
func (Floor) TableName() string {
	return "floors"
}
