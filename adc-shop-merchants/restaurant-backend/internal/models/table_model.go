package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// TableArea represents a dining area containing tables
type TableArea struct {
	BaseModel
	BranchID    uuid.UUID  `json:"branch_id" gorm:"type:uuid;not null;index"`
	FloorID     *uuid.UUID `json:"floor_id" gorm:"type:uuid;index"`
	Name        string     `json:"name" gorm:"type:varchar(255);not null"`
	Description string     `json:"description" gorm:"type:text"`
	Color       string     `json:"color" gorm:"type:varchar(7);default:'#8a745c'"`
	IsActive    bool       `json:"is_active" gorm:"default:true"`

	// Relationships
	Branch ShopBranch `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	Floor  *Floor     `json:"floor,omitempty" gorm:"foreignKey:FloorID"`
	Tables []Table    `json:"tables,omitempty" gorm:"foreignKey:AreaID"`
}

// Table represents a dining table
type Table struct {
	BaseModel
	BranchID  uuid.UUID    `json:"branch_id" gorm:"type:uuid;not null;index"`
	AreaID    *uuid.UUID   `json:"area_id" gorm:"type:uuid;index"`
	Name      string       `json:"name" gorm:"type:varchar(100);not null"`
	Number    int          `json:"number" gorm:"not null"`
	Capacity  int          `json:"capacity" gorm:"not null"`
	Status    string       `json:"status" gorm:"type:varchar(20);default:'available'"`
	Position  PositionData `json:"position" gorm:"type:jsonb"`
	Shape     string       `json:"shape" gorm:"type:varchar(20);default:'square'"`
	Size      SizeData     `json:"size" gorm:"type:jsonb"`
	ImageURL  string       `json:"image_url" gorm:"type:varchar(500)"`
	QRCodeURL string       `json:"qr_code_url" gorm:"type:varchar(500)"`
	IsActive  bool         `json:"is_active" gorm:"default:true"`

	// Current state
	CurrentOrder       *TableOrder       `json:"current_order,omitempty" gorm:"-"`
	CurrentReservation *TableReservation `json:"current_reservation,omitempty" gorm:"-"`

	// Relationships
	Branch       ShopBranch    `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	Area         *TableArea    `json:"area,omitempty" gorm:"foreignKey:AreaID"`
	Orders       []Order       `json:"orders,omitempty" gorm:"foreignKey:TableID"`
	Reservations []Reservation `json:"reservations,omitempty" gorm:"foreignKey:TableID"`
}

// Custom types for JSON fields
type (
	PositionData Position
	SizeData     Size
)

// Scan and Value methods for PositionData
func (p *PositionData) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, p)
}

func (p PositionData) Value() (driver.Value, error) {
	return json.Marshal(p)
}

// Scan and Value methods for SizeData
func (s *SizeData) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, s)
}

func (s SizeData) Value() (driver.Value, error) {
	return json.Marshal(s)
}

// TableName specifies the table name for TableArea
func (TableArea) TableName() string {
	return "table_areas"
}

// TableName specifies the table name for Table
func (Table) TableName() string {
	return "tables"
}

// BeforeCreate hook for TableArea
func (ta *TableArea) BeforeCreate(tx *gorm.DB) error {
	return ta.BaseModel.BeforeCreate(tx)
}

// BeforeCreate hook for Table
func (t *Table) BeforeCreate(tx *gorm.DB) error {
	if err := t.BaseModel.BeforeCreate(tx); err != nil {
		return err
	}

	// Initialize position and size if not set
	if t.Position.X == 0 && t.Position.Y == 0 {
		t.Position = PositionData{X: 100, Y: 100}
	}
	if t.Size.Width == 0 && t.Size.Height == 0 {
		t.Size = SizeData{Width: 80, Height: 80}
	}

	return nil
}

// IsAvailable returns true if the table is available
func (t *Table) IsAvailable() bool {
	return t.Status == TableStatusAvailable && t.IsActive
}

// IsOccupied returns true if the table is occupied
func (t *Table) IsOccupied() bool {
	return t.Status == TableStatusOccupied
}

// IsReserved returns true if the table is reserved
func (t *Table) IsReserved() bool {
	return t.Status == TableStatusReserved
}

// CanBeReserved returns true if the table can be reserved
func (t *Table) CanBeReserved() bool {
	return t.IsAvailable() || t.Status == TableStatusCleaning
}

// GetStatusColor returns a color code for the table status
func (t *Table) GetStatusColor() string {
	switch t.Status {
	case TableStatusAvailable:
		return "#00CC66" // Green
	case TableStatusOccupied:
		return "#CC0000" // Red
	case TableStatusReserved:
		return "#0066CC" // Blue
	case TableStatusCleaning:
		return "#FFA500" // Orange
	case TableStatusMaintenance:
		return "#666666" // Gray
	default:
		return "#666666" // Gray
	}
}

// GetCapacityRange returns a description of the table capacity
func (t *Table) GetCapacityRange() string {
	switch {
	case t.Capacity <= 2:
		return "Small (1-2 people)"
	case t.Capacity <= 4:
		return "Medium (3-4 people)"
	case t.Capacity <= 6:
		return "Large (5-6 people)"
	case t.Capacity <= 8:
		return "Extra Large (7-8 people)"
	default:
		return "Group (9+ people)"
	}
}

// GetShapeIcon returns an icon name for the table shape
func (t *Table) GetShapeIcon() string {
	switch t.Shape {
	case TableShapeRound:
		return "circle"
	case TableShapeRectangle:
		return "rectangle"
	case TableShapeSquare:
		return "square"
	default:
		return "square"
	}
}

// GetAreaName returns the area name or "No Area" if not assigned
func (t *Table) GetAreaName() string {
	if t.Area != nil {
		return t.Area.Name
	}
	return "No Area"
}

// GetQRCodeData returns the data that should be encoded in the QR code
func (t *Table) GetQRCodeData(baseURL string) string {
	return baseURL + "/table/" + t.ID.String()
}

// UpdatePosition updates the table position
func (t *Table) UpdatePosition(x, y float64) {
	t.Position = PositionData{X: x, Y: y}
}

// UpdateSize updates the table size
func (t *Table) UpdateSize(width, height float64) {
	t.Size = SizeData{Width: width, Height: height}
}

// GetTableCount returns the number of tables in the area
func (ta *TableArea) GetTableCount() int {
	return len(ta.Tables)
}

// GetActiveTableCount returns the number of active tables in the area
func (ta *TableArea) GetActiveTableCount() int {
	count := 0
	for _, table := range ta.Tables {
		if table.IsActive {
			count++
		}
	}
	return count
}

// GetAvailableTableCount returns the number of available tables in the area
func (ta *TableArea) GetAvailableTableCount() int {
	count := 0
	for _, table := range ta.Tables {
		if table.IsAvailable() {
			count++
		}
	}
	return count
}

// GetTotalCapacity returns the total seating capacity of the area
func (ta *TableArea) GetTotalCapacity() int {
	capacity := 0
	for _, table := range ta.Tables {
		if table.IsActive {
			capacity += table.Capacity
		}
	}
	return capacity
}

// GetAvailableCapacity returns the available seating capacity of the area
func (ta *TableArea) GetAvailableCapacity() int {
	capacity := 0
	for _, table := range ta.Tables {
		if table.IsAvailable() {
			capacity += table.Capacity
		}
	}
	return capacity
}
