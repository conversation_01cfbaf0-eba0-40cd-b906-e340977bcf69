package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Review represents a customer review
type Review struct {
	BaseModel
	BranchID       uuid.UUID     `json:"branch_id" gorm:"type:uuid;not null;index"`
	CustomerID     *uuid.UUID    `json:"customer_id" gorm:"type:uuid;index"`
	OrderID        *uuid.UUID    `json:"order_id" gorm:"type:uuid;index"`
	CustomerName   string        `json:"customer_name" gorm:"type:varchar(255);not null"`
	CustomerEmail  string        `json:"customer_email" gorm:"type:varchar(255)"`
	CustomerAvatar string        `json:"customer_avatar" gorm:"type:varchar(500)"`
	Rating         int           `json:"rating" gorm:"not null;check:rating >= 1 AND rating <= 5"`
	Title          string        `json:"title" gorm:"type:varchar(255)"`
	Comment        string        `json:"comment" gorm:"type:text;not null"`
	Photos         PhotosData    `json:"photos" gorm:"type:jsonb"`
	Source         string        `json:"source" gorm:"type:varchar(20);default:'internal'"`
	Status         string        `json:"status" gorm:"type:varchar(20);default:'pending'"`
	IsVerified     bool          `json:"is_verified" gorm:"default:false"`
	IsPublic       bool          `json:"is_public" gorm:"default:true"`
	Response       *ResponseData `json:"response" gorm:"type:jsonb"`
	Tags           TagsData      `json:"tags" gorm:"type:jsonb"`
	Sentiment      string        `json:"sentiment" gorm:"type:varchar(20)"`

	// Relationships
	Branch ShopBranch `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	Order  *Order     `json:"order,omitempty" gorm:"foreignKey:OrderID"`
}

// Custom types for JSON fields
type (
	PhotosData   []string
	ResponseData Response
)

// Scan and Value methods for PhotosData
func (p *PhotosData) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, p)
}

func (p PhotosData) Value() (driver.Value, error) {
	return json.Marshal(p)
}

// Scan and Value methods for ResponseData
func (r *ResponseData) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, r)
}

func (r ResponseData) Value() (driver.Value, error) {
	return json.Marshal(r)
}

// TableName specifies the table name for Review
func (Review) TableName() string {
	return "reviews"
}

// BeforeCreate hook for Review
func (r *Review) BeforeCreate(tx *gorm.DB) error {
	if err := r.BaseModel.BeforeCreate(tx); err != nil {
		return err
	}

	// Initialize empty slices if nil
	if r.Photos == nil {
		r.Photos = PhotosData{}
	}
	if r.Tags == nil {
		r.Tags = TagsData{}
	}

	// Auto-detect sentiment based on rating if not set
	if r.Sentiment == "" {
		r.Sentiment = r.detectSentiment()
	}

	return nil
}

// HasResponse returns true if the review has a response
func (r *Review) HasResponse() bool {
	return r.Response != nil && r.Response.Message != ""
}

// IsPositive returns true if the review is positive (rating >= 4)
func (r *Review) IsPositive() bool {
	return r.Rating >= 4
}

// IsNegative returns true if the review is negative (rating <= 2)
func (r *Review) IsNegative() bool {
	return r.Rating <= 2
}

// IsNeutral returns true if the review is neutral (rating == 3)
func (r *Review) IsNeutral() bool {
	return r.Rating == 3
}

// GetRatingStars returns a string representation of the rating
func (r *Review) GetRatingStars() string {
	stars := ""
	for i := 1; i <= 5; i++ {
		if i <= r.Rating {
			stars += "★"
		} else {
			stars += "☆"
		}
	}
	return stars
}

// GetRatingColor returns a color code for the rating
func (r *Review) GetRatingColor() string {
	switch {
	case r.Rating >= 4:
		return "#00CC66" // Green
	case r.Rating == 3:
		return "#FFA500" // Orange
	default:
		return "#CC0000" // Red
	}
}

// GetSentimentColor returns a color code for the sentiment
func (r *Review) GetSentimentColor() string {
	switch r.Sentiment {
	case ReviewSentimentPositive:
		return "#00CC66" // Green
	case ReviewSentimentNeutral:
		return "#FFA500" // Orange
	case ReviewSentimentNegative:
		return "#CC0000" // Red
	default:
		return "#666666" // Gray
	}
}

// GetSourceColor returns a color code for the review source
func (r *Review) GetSourceColor() string {
	switch r.Source {
	case ReviewSourceGoogle:
		return "#4285F4" // Google Blue
	case ReviewSourceYelp:
		return "#FF1A1A" // Yelp Red
	case ReviewSourceFacebook:
		return "#1877F2" // Facebook Blue
	case ReviewSourceTripAdvisor:
		return "#00AA6C" // TripAdvisor Green
	case ReviewSourceInternal:
		return "#6B46C1" // Purple
	default:
		return "#666666" // Gray
	}
}

// GetStatusColor returns a color code for the review status
func (r *Review) GetStatusColor() string {
	switch r.Status {
	case ReviewStatusPending:
		return "#FFA500" // Orange
	case ReviewStatusApproved:
		return "#00CC66" // Green
	case ReviewStatusRejected:
		return "#CC0000" // Red
	case ReviewStatusFlagged:
		return "#FF6600" // Dark Orange
	default:
		return "#666666" // Gray
	}
}

// GetPhotoCount returns the number of photos attached to the review
func (r *Review) GetPhotoCount() int {
	return len(r.Photos)
}

// HasPhotos returns true if the review has photos
func (r *Review) HasPhotos() bool {
	return len(r.Photos) > 0
}

// GetFirstPhoto returns the first photo URL or empty string
func (r *Review) GetFirstPhoto() string {
	if len(r.Photos) > 0 {
		return r.Photos[0]
	}
	return ""
}

// AddTag adds a tag to the review if it doesn't already exist
func (r *Review) AddTag(tag string) {
	for _, t := range r.Tags {
		if t == tag {
			return // Tag already exists
		}
	}
	r.Tags = append(r.Tags, tag)
}

// RemoveTag removes a tag from the review
func (r *Review) RemoveTag(tag string) {
	for i, t := range r.Tags {
		if t == tag {
			r.Tags = append(r.Tags[:i], r.Tags[i+1:]...)
			break
		}
	}
}

// HasTag checks if the review has a specific tag
func (r *Review) HasTag(tag string) bool {
	for _, t := range r.Tags {
		if t == tag {
			return true
		}
	}
	return false
}

// CanBeResponded returns true if the review can be responded to
func (r *Review) CanBeResponded() bool {
	return r.Status == ReviewStatusApproved && !r.HasResponse()
}

// CanBeEdited returns true if the review response can be edited
func (r *Review) CanBeEdited() bool {
	return r.Status == ReviewStatusApproved && r.HasResponse()
}

// GetWordCount returns the word count of the review comment
func (r *Review) GetWordCount() int {
	// Simple word count implementation
	words := 0
	inWord := false
	for _, char := range r.Comment {
		if char == ' ' || char == '\t' || char == '\n' {
			inWord = false
		} else if !inWord {
			words++
			inWord = true
		}
	}
	return words
}

// IsLongReview returns true if the review is considered long (>100 words)
func (r *Review) IsLongReview() bool {
	return r.GetWordCount() > 100
}

// detectSentiment automatically detects sentiment based on rating
func (r *Review) detectSentiment() string {
	switch {
	case r.Rating >= 4:
		return ReviewSentimentPositive
	case r.Rating == 3:
		return ReviewSentimentNeutral
	default:
		return ReviewSentimentNegative
	}
}

// GetCustomerInitials returns the customer's initials for avatar fallback
func (r *Review) GetCustomerInitials() string {
	if r.CustomerName == "" {
		return "?"
	}

	names := []rune(r.CustomerName)
	if len(names) == 0 {
		return "?"
	}

	initials := string(names[0])

	// Find the first letter of the last name
	for i, char := range names {
		if char == ' ' && i+1 < len(names) {
			initials += string(names[i+1])
			break
		}
	}

	return initials
}
