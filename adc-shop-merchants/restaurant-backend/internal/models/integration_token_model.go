package models

import (
	"crypto/rand"
	"encoding/hex"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// IntegrationTokenType represents the type of integration token
type IntegrationTokenType string

const (
	TokenTypeAPI     IntegrationTokenType = "api"
	TokenTypeWebhook IntegrationTokenType = "webhook"
	TokenTypeWidget  IntegrationTokenType = "widget"
	TokenTypeService IntegrationTokenType = "service"
)

// IntegrationTokenStatus represents the status of an integration token
type IntegrationTokenStatus string

const (
	TokenStatusActive   IntegrationTokenStatus = "active"
	TokenStatusInactive IntegrationTokenStatus = "inactive"
	TokenStatusRevoked  IntegrationTokenStatus = "revoked"
	TokenStatusExpired  IntegrationTokenStatus = "expired"
)

// IntegrationTokenScope represents the scope/permissions of a token
type IntegrationTokenScope struct {
	Resource     string                 `json:"resource"`     // e.g., "menu", "orders", "reservations"
	Actions      []string               `json:"actions"`      // e.g., ["read", "write", "delete"]
	BranchIDs    []string               `json:"branch_ids"`   // Specific branches (empty = all branches)
	Restrictions map[string]interface{} `json:"restrictions"` // Additional restrictions
}

// IntegrationToken represents an API token for third-party integrations
type IntegrationToken struct {
	BaseModel

	// Basic Information
	Name        string                 `json:"name" gorm:"type:varchar(255);not null"`
	Description string                 `json:"description" gorm:"type:text"`
	Type        IntegrationTokenType   `json:"type" gorm:"type:varchar(50);not null;index"`
	Status      IntegrationTokenStatus `json:"status" gorm:"type:varchar(50);not null;index;default:'active'"`

	// Token Data
	TokenHash   string `json:"-" gorm:"type:varchar(255);uniqueIndex;not null"` // SHA256 hash of the token
	TokenPrefix string `json:"token_prefix" gorm:"type:varchar(20);not null"`   // First 8 chars for identification

	// Ownership
	ShopID   uuid.UUID  `json:"shop_id" gorm:"type:uuid;not null;index"`
	BranchID *uuid.UUID `json:"branch_id" gorm:"type:uuid;index"`        // Optional: specific branch
	UserID   uuid.UUID  `json:"user_id" gorm:"type:uuid;not null;index"` // Creator

	// Permissions and Scopes
	Scopes []IntegrationTokenScope `json:"scopes" gorm:"type:jsonb"`

	// Rate Limiting
	RateLimit       int        `json:"rate_limit" gorm:"default:1000"`        // Requests per hour
	RateLimitWindow int        `json:"rate_limit_window" gorm:"default:3600"` // Window in seconds
	CurrentUsage    int        `json:"current_usage" gorm:"default:0"`        // Current usage count
	UsageResetAt    *time.Time `json:"usage_reset_at"`                        // When usage resets

	// Security
	AllowedIPs     []string `json:"allowed_ips" gorm:"type:jsonb"`     // IP whitelist
	AllowedOrigins []string `json:"allowed_origins" gorm:"type:jsonb"` // CORS origins

	// Expiration
	ExpiresAt *time.Time `json:"expires_at" gorm:"index"` // Optional expiration

	// Usage Tracking
	LastUsedAt    *time.Time `json:"last_used_at"`
	UsageCount    int64      `json:"usage_count" gorm:"default:0"`
	LastUsedIP    string     `json:"last_used_ip" gorm:"type:varchar(45)"`
	LastUserAgent string     `json:"last_user_agent" gorm:"type:varchar(500)"`

	// Relationships
	Shop   Shop        `json:"shop,omitempty" gorm:"foreignKey:ShopID"`
	Branch *ShopBranch `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	User   User        `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// IntegrationTokenUsage tracks token usage for analytics
type IntegrationTokenUsage struct {
	BaseModel

	TokenID   uuid.UUID `json:"token_id" gorm:"type:uuid;not null;index"`
	Endpoint  string    `json:"endpoint" gorm:"type:varchar(255);not null"`
	Method    string    `json:"method" gorm:"type:varchar(10);not null"`
	IPAddress string    `json:"ip_address" gorm:"type:varchar(45)"`
	UserAgent string    `json:"user_agent" gorm:"type:varchar(500)"`

	// Response Information
	StatusCode   int    `json:"status_code"`
	ResponseTime int64  `json:"response_time"` // in milliseconds
	ErrorMessage string `json:"error_message" gorm:"type:text"`

	// Request Data
	RequestSize  int64 `json:"request_size"`  // in bytes
	ResponseSize int64 `json:"response_size"` // in bytes

	// Timestamp
	Timestamp time.Time `json:"timestamp" gorm:"not null;index;default:CURRENT_TIMESTAMP"`

	// Relationships
	Token IntegrationToken `json:"token,omitempty" gorm:"foreignKey:TokenID"`
}

// GenerateToken generates a new secure token
func (t *IntegrationToken) GenerateToken() (string, error) {
	// Generate 32 random bytes (256 bits)
	tokenBytes := make([]byte, 32)
	if _, err := rand.Read(tokenBytes); err != nil {
		return "", err
	}

	// Convert to hex string
	token := hex.EncodeToString(tokenBytes)

	// Add prefix based on type
	var prefix string
	switch t.Type {
	case TokenTypeAPI:
		prefix = "api_"
	case TokenTypeWebhook:
		prefix = "whk_"
	case TokenTypeWidget:
		prefix = "wgt_"
	case TokenTypeService:
		prefix = "svc_"
	default:
		prefix = "tok_"
	}

	fullToken := prefix + token

	// Store prefix for identification
	t.TokenPrefix = fullToken[:12] // Store first 12 chars

	return fullToken, nil
}

// IsExpired checks if the token is expired
func (t *IntegrationToken) IsExpired() bool {
	if t.ExpiresAt == nil {
		return false
	}
	return time.Now().After(*t.ExpiresAt)
}

// IsActive checks if the token is active and not expired
func (t *IntegrationToken) IsActive() bool {
	return t.Status == TokenStatusActive && !t.IsExpired()
}

// CanAccess checks if the token has access to a specific resource and action
func (t *IntegrationToken) CanAccess(resource, action string, branchID *uuid.UUID) bool {
	if !t.IsActive() {
		return false
	}

	for _, scope := range t.Scopes {
		if scope.Resource == resource || scope.Resource == "*" {
			// Check if action is allowed
			actionAllowed := false
			for _, allowedAction := range scope.Actions {
				if allowedAction == action || allowedAction == "*" {
					actionAllowed = true
					break
				}
			}

			if !actionAllowed {
				continue
			}

			// Check branch access
			if branchID != nil && len(scope.BranchIDs) > 0 {
				branchAllowed := false
				branchIDStr := branchID.String()
				for _, allowedBranchID := range scope.BranchIDs {
					if allowedBranchID == branchIDStr || allowedBranchID == "*" {
						branchAllowed = true
						break
					}
				}
				if !branchAllowed {
					continue
				}
			}

			return true
		}
	}

	return false
}

// UpdateUsage updates the token usage statistics
func (t *IntegrationToken) UpdateUsage(db *gorm.DB, ipAddress, userAgent string) error {
	now := time.Now()

	// Reset usage count if window has passed
	if t.UsageResetAt == nil || now.After(*t.UsageResetAt) {
		resetTime := now.Add(time.Duration(t.RateLimitWindow) * time.Second)
		t.UsageResetAt = &resetTime
		t.CurrentUsage = 0
	}

	// Increment usage
	t.CurrentUsage++
	t.UsageCount++
	t.LastUsedAt = &now
	t.LastUsedIP = ipAddress
	t.LastUserAgent = userAgent

	return db.Save(t).Error
}

// IsRateLimited checks if the token has exceeded its rate limit
func (t *IntegrationToken) IsRateLimited() bool {
	if t.RateLimit <= 0 {
		return false // No rate limit
	}

	// Check if usage window has reset
	if t.UsageResetAt == nil || time.Now().After(*t.UsageResetAt) {
		return false // Usage should be reset
	}

	return t.CurrentUsage >= t.RateLimit
}

// TableName returns the table name for GORM
func (IntegrationToken) TableName() string {
	return "integration_tokens"
}

// TableName returns the table name for GORM
func (IntegrationTokenUsage) TableName() string {
	return "integration_token_usage"
}
