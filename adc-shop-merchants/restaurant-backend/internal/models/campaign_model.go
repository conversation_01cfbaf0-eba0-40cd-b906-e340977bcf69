package models

import (
	"time"

	"github.com/google/uuid"
)

// CommunicationCampaign represents a marketing campaign
type CommunicationCampaign struct {
	BaseModel
	MerchantID  uuid.UUID  `json:"merchant_id" gorm:"type:uuid;not null;index"`
	Name        string     `json:"name" gorm:"type:varchar(255);not null"`
	Description string     `json:"description" gorm:"type:text"`
	Type        string     `json:"type" gorm:"type:varchar(20);not null"`          // email, sms, push
	Status      string     `json:"status" gorm:"type:varchar(20);default:'draft'"` // draft, scheduled, running, completed, cancelled
	TemplateID  *uuid.UUID `json:"template_id" gorm:"type:uuid;index"`
	SegmentID   *uuid.UUID `json:"segment_id" gorm:"type:uuid;index"`

	// Campaign content
	Subject string `json:"subject" gorm:"type:varchar(255)"`
	Content string `json:"content" gorm:"type:text;not null"`

	// Scheduling
	ScheduledAt *time.Time `json:"scheduled_at"`
	StartedAt   *time.Time `json:"started_at"`
	CompletedAt *time.Time `json:"completed_at"`

	// Targeting
	TargetAudience string   `json:"target_audience" gorm:"type:varchar(100)"` // all, segment, custom
	Recipients     []string `json:"recipients" gorm:"type:jsonb"`             // for custom targeting

	// Analytics
	TotalRecipients  int `json:"total_recipients" gorm:"default:0"`
	SentCount        int `json:"sent_count" gorm:"default:0"`
	DeliveredCount   int `json:"delivered_count" gorm:"default:0"`
	OpenedCount      int `json:"opened_count" gorm:"default:0"`
	ClickedCount     int `json:"clicked_count" gorm:"default:0"`
	UnsubscribeCount int `json:"unsubscribe_count" gorm:"default:0"`
	BounceCount      int `json:"bounce_count" gorm:"default:0"`

	// Settings
	Settings CampaignSettings `json:"settings" gorm:"type:jsonb"`

	// Relationships
	Shop     Shop                   `json:"shop,omitempty" gorm:"foreignKey:MerchantID"`
	Template *CommunicationTemplate `json:"template,omitempty" gorm:"foreignKey:TemplateID"`
	Segment  *CampaignSegment       `json:"segment,omitempty" gorm:"foreignKey:SegmentID"`
}

// CommunicationTemplate represents a reusable template
type CommunicationTemplate struct {
	BaseModel
	MerchantID  uuid.UUID `json:"merchant_id" gorm:"type:uuid;not null;index"`
	Name        string    `json:"name" gorm:"type:varchar(255);not null"`
	Description string    `json:"description" gorm:"type:text"`
	Type        string    `json:"type" gorm:"type:varchar(20);not null"`     // email, sms, push
	Category    string    `json:"category" gorm:"type:varchar(50);not null"` // appointment_confirmation, marketing, etc.
	Subject     string    `json:"subject" gorm:"type:varchar(255)"`
	Content     string    `json:"content" gorm:"type:text;not null"`
	Variables   []string  `json:"variables" gorm:"type:jsonb"` // available template variables
	IsDefault   bool      `json:"is_default" gorm:"default:false"`
	IsActive    bool      `json:"is_active" gorm:"default:true"`

	// Relationships
	Shop      Shop                    `json:"shop,omitempty" gorm:"foreignKey:MerchantID"`
	Campaigns []CommunicationCampaign `json:"campaigns,omitempty" gorm:"foreignKey:TemplateID"`
}

// CampaignSegment represents a customer segment for targeting
type CampaignSegment struct {
	BaseModel
	MerchantID  uuid.UUID `json:"merchant_id" gorm:"type:uuid;not null;index"`
	Name        string    `json:"name" gorm:"type:varchar(255);not null"`
	Description string    `json:"description" gorm:"type:text"`
	Type        string    `json:"type" gorm:"type:varchar(50);not null"` // demographic, behavioral, geographic

	// Segment criteria
	Criteria SegmentCriteria `json:"criteria" gorm:"type:jsonb"`

	// Analytics
	CustomerCount int       `json:"customer_count" gorm:"default:0"`
	LastUpdated   time.Time `json:"last_updated" gorm:"autoUpdateTime"`

	// Status
	IsActive bool `json:"is_active" gorm:"default:true"`

	// Relationships
	Shop      Shop                    `json:"shop,omitempty" gorm:"foreignKey:MerchantID"`
	Campaigns []CommunicationCampaign `json:"campaigns,omitempty" gorm:"foreignKey:SegmentID"`
}

// CommunicationAnalytics represents analytics for communication campaigns
type CommunicationAnalytics struct {
	BaseModel
	MerchantID uuid.UUID              `json:"merchant_id" gorm:"type:uuid;not null;index"`
	CampaignID *uuid.UUID             `json:"campaign_id" gorm:"type:uuid;index"`
	Type       string                 `json:"type" gorm:"type:varchar(20);not null"`  // email, sms, push
	Event      string                 `json:"event" gorm:"type:varchar(50);not null"` // sent, delivered, opened, clicked, etc.
	Recipient  string                 `json:"recipient" gorm:"type:varchar(255);not null"`
	EventData  map[string]interface{} `json:"event_data" gorm:"type:jsonb"`
	Timestamp  time.Time              `json:"timestamp" gorm:"not null;index"`

	// Relationships
	Shop     Shop                   `json:"shop,omitempty" gorm:"foreignKey:MerchantID"`
	Campaign *CommunicationCampaign `json:"campaign,omitempty" gorm:"foreignKey:CampaignID"`
}

// Supporting types
type CampaignSettings struct {
	SendTime         string            `json:"send_time"` // optimal send time
	Timezone         string            `json:"timezone"`
	TrackOpens       bool              `json:"track_opens"`
	TrackClicks      bool              `json:"track_clicks"`
	AllowUnsubscribe bool              `json:"allow_unsubscribe"`
	ReplyToEmail     string            `json:"reply_to_email"`
	FromName         string            `json:"from_name"`
	CustomFields     map[string]string `json:"custom_fields"`
}

type SegmentCriteria struct {
	// Demographic criteria
	AgeRange *AgeRange `json:"age_range,omitempty"`
	Gender   string    `json:"gender,omitempty"`
	Location string    `json:"location,omitempty"`

	// Behavioral criteria
	LastVisitDays     *int     `json:"last_visit_days,omitempty"` // visited within X days
	TotalOrders       *int     `json:"total_orders,omitempty"`
	TotalSpent        *float64 `json:"total_spent,omitempty"`
	AverageOrderValue *float64 `json:"average_order_value,omitempty"`
	FavoriteCategory  string   `json:"favorite_category,omitempty"`

	// Engagement criteria
	EmailEngagement string `json:"email_engagement,omitempty"` // high, medium, low
	SMSEngagement   string `json:"sms_engagement,omitempty"`
	ReviewsLeft     *int   `json:"reviews_left,omitempty"`
	ReferralsMade   *int   `json:"referrals_made,omitempty"`

	// Custom criteria
	CustomFilters map[string]interface{} `json:"custom_filters,omitempty"`
}

type AgeRange struct {
	Min int `json:"min"`
	Max int `json:"max"`
}

// TableName overrides for GORM
func (CommunicationCampaign) TableName() string {
	return "communication_campaigns"
}

func (CommunicationTemplate) TableName() string {
	return "communication_templates"
}

func (CampaignSegment) TableName() string {
	return "campaign_segments"
}

func (CommunicationAnalytics) TableName() string {
	return "communication_analytics"
}
