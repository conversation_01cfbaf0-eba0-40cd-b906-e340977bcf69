package middleware

import (
	"net/http"
	"strings"
	"time"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// IntegrationTokenAuth middleware validates integration tokens
func IntegrationTokenAuth(tokenService *services.IntegrationTokenService, logger *logrus.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
			c.Abort()
			return
		}

		// Check if header starts with "Bearer "
		if !strings.HasPrefix(authHeader, "Bearer ") {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid authorization header format"})
			c.Abort()
			return
		}

		// Extract token
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == "" {
			c.JSO<PERSON>(http.StatusUnauthorized, gin.H{"error": "Token required"})
			c.Abort()
			return
		}

		// Get client IP and user agent
		ipAddress := c.ClientIP()
		userAgent := c.GetHeader("User-Agent")

		// Validate token
		token, err := tokenService.ValidateToken(c.Request.Context(), tokenString, ipAddress, userAgent)
		if err != nil {
			logger.WithFields(logrus.Fields{
				"error":      err.Error(),
				"ip_address": ipAddress,
				"user_agent": userAgent,
			}).Warn("Invalid integration token")

			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid or expired token"})
			c.Abort()
			return
		}

		// Set token context
		c.Set("integration_token", token)
		c.Set("token_id", token.ID)
		c.Set("shop_id", token.ShopID)
		c.Set("branch_id", token.BranchID)
		c.Set("user_id", token.UserID)
		c.Set("token_type", token.Type)
		c.Set("token_scopes", token.Scopes)
		c.Set("auth_source", "integration_token")

		// Log successful authentication
		logger.WithFields(logrus.Fields{
			"token_id":     token.ID,
			"token_prefix": token.TokenPrefix,
			"shop_id":      token.ShopID,
			"ip_address":   ipAddress,
		}).Debug("Integration token authenticated")

		c.Next()
	}
}

// RequireTokenScope middleware checks if the token has required scope
func RequireTokenScope(resource, action string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from context
		tokenInterface, exists := c.Get("integration_token")
		if !exists {
			c.JSON(http.StatusForbidden, gin.H{"error": "No integration token found"})
			c.Abort()
			return
		}

		token, ok := tokenInterface.(*models.IntegrationToken)
		if !ok {
			c.JSON(http.StatusForbidden, gin.H{"error": "Invalid token context"})
			c.Abort()
			return
		}

		// Get branch ID from URL params if available
		var branchID *uuid.UUID
		if branchParam := c.Param("branchId"); branchParam != "" {
			if parsedBranchID, err := uuid.Parse(branchParam); err == nil {
				branchID = &parsedBranchID
			}
		}

		// Check if token has required scope
		if !token.CanAccess(resource, action, branchID) {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Insufficient permissions",
				"required_scope": gin.H{
					"resource": resource,
					"action":   action,
				},
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// OptionalIntegrationTokenAuth middleware validates integration tokens if present but doesn't require them
func OptionalIntegrationTokenAuth(tokenService *services.IntegrationTokenService, logger *logrus.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Next()
			return
		}

		// Check if header starts with "Bearer "
		if !strings.HasPrefix(authHeader, "Bearer ") {
			c.Next()
			return
		}

		// Extract token
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == "" {
			c.Next()
			return
		}

		// Get client IP and user agent
		ipAddress := c.ClientIP()
		userAgent := c.GetHeader("User-Agent")

		// Validate token
		token, err := tokenService.ValidateToken(c.Request.Context(), tokenString, ipAddress, userAgent)
		if err != nil {
			// Log but don't fail the request
			logger.WithFields(logrus.Fields{
				"error":      err.Error(),
				"ip_address": ipAddress,
				"user_agent": userAgent,
			}).Debug("Optional integration token validation failed")
			c.Next()
			return
		}

		// Set token context
		c.Set("integration_token", token)
		c.Set("token_id", token.ID)
		c.Set("shop_id", token.ShopID)
		c.Set("branch_id", token.BranchID)
		c.Set("user_id", token.UserID)
		c.Set("token_type", token.Type)
		c.Set("token_scopes", token.Scopes)
		c.Set("auth_source", "integration_token")

		c.Next()
	}
}

// RateLimitByToken middleware implements rate limiting per token
func RateLimitByToken() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from context
		tokenInterface, exists := c.Get("integration_token")
		if !exists {
			c.Next()
			return
		}

		token, ok := tokenInterface.(*models.IntegrationToken)
		if !ok {
			c.Next()
			return
		}

		// Check rate limit
		if token.IsRateLimited() {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":       "Rate limit exceeded",
				"rate_limit":  token.RateLimit,
				"window":      token.RateLimitWindow,
				"reset_at":    token.UsageResetAt,
				"retry_after": time.Until(*token.UsageResetAt).Seconds(),
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// ValidateTokenOrigin middleware validates the request origin against allowed origins
func ValidateTokenOrigin() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from context
		tokenInterface, exists := c.Get("integration_token")
		if !exists {
			c.Next()
			return
		}

		token, ok := tokenInterface.(*models.IntegrationToken)
		if !ok {
			c.Next()
			return
		}

		// Skip validation if no allowed origins are set
		if len(token.AllowedOrigins) == 0 {
			c.Next()
			return
		}

		// Get request origin
		origin := c.GetHeader("Origin")
		if origin == "" {
			origin = c.GetHeader("Referer")
		}

		// Check if origin is allowed
		originAllowed := false
		for _, allowedOrigin := range token.AllowedOrigins {
			if allowedOrigin == "*" || allowedOrigin == origin {
				originAllowed = true
				break
			}
		}

		if !originAllowed {
			c.JSON(http.StatusForbidden, gin.H{
				"error":           "Origin not allowed",
				"origin":          origin,
				"allowed_origins": token.AllowedOrigins,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// ValidateTokenIP middleware validates the request IP against allowed IPs
func ValidateTokenIP() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from context
		tokenInterface, exists := c.Get("integration_token")
		if !exists {
			c.Next()
			return
		}

		token, ok := tokenInterface.(*models.IntegrationToken)
		if !ok {
			c.Next()
			return
		}

		// Skip validation if no allowed IPs are set
		if len(token.AllowedIPs) == 0 {
			c.Next()
			return
		}

		// Get client IP
		clientIP := c.ClientIP()

		// Check if IP is allowed
		ipAllowed := false
		for _, allowedIP := range token.AllowedIPs {
			if allowedIP == "*" || allowedIP == clientIP {
				ipAllowed = true
				break
			}
		}

		if !ipAllowed {
			c.JSON(http.StatusForbidden, gin.H{
				"error":       "IP address not allowed",
				"ip_address":  clientIP,
				"allowed_ips": token.AllowedIPs,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
