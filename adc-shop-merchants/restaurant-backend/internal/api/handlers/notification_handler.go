package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/services"
)

// NotificationHandler handles notification-related HTTP requests
type NotificationHandler struct {
	notificationService *services.NotificationService
	logger              *logrus.Logger
}

// NewNotificationHandler creates a new notification handler
func NewNotificationHandler(notificationService *services.NotificationService, logger *logrus.Logger) *NotificationHandler {
	return &NotificationHandler{
		notificationService: notificationService,
		logger:              logger,
	}
}

// GetNotifications godoc
// @Summary Get notifications with filtering, sorting, and pagination
// @Description Retrieve notifications for a specific shop and branch with optional filtering
// @Tags notifications
// @Accept json
// @Produce json
// @Param shopId query string true "Shop ID"
// @Param branchId query string true "Branch ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param sort_by query string false "Sort field" default(timestamp)
// @Param sort_order query string false "Sort order (asc/desc)" default(desc)
// @Param type query string false "Filter by notification type"
// @Param priority query string false "Filter by priority"
// @Param isRead query bool false "Filter by read status"
// @Param search query string false "Search in title and message"
// @Param startDate query string false "Start date filter (ISO format)"
// @Param endDate query string false "End date filter (ISO format)"
// @Param dateRange query string false "Date range filter (today, yesterday, last_7_days, last_30_days)"
// @Success 200 {object} models.PaginatedResponse[models.Notification]
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /notifications [get]
func (h *NotificationHandler) GetNotifications(c *gin.Context) {
	// Parse shop and branch IDs
	shopIDStr := c.Query("shopId")
	branchIDStr := c.Query("branchId")

	if shopIDStr == "" || branchIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "shopId and branchId are required"})
		return
	}

	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	branchID, err := uuid.Parse(branchIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	sortBy := c.DefaultQuery("sort_by", "timestamp")
	sortOrder := c.DefaultQuery("sort_order", "desc")

	// Parse filters
	filters := models.NotificationFilters{}

	if typeFilter := c.Query("type"); typeFilter != "" {
		filters.Type = &typeFilter
	}
	if priorityFilter := c.Query("priority"); priorityFilter != "" {
		filters.Priority = &priorityFilter
	}
	if isReadStr := c.Query("isRead"); isReadStr != "" {
		if isRead, err := strconv.ParseBool(isReadStr); err == nil {
			filters.IsRead = &isRead
		}
	}
	if search := c.Query("search"); search != "" {
		filters.Search = &search
	}
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		if startDate, err := time.Parse(time.RFC3339, startDateStr); err == nil {
			filters.StartDate = &startDate
		}
	}
	if endDateStr := c.Query("endDate"); endDateStr != "" {
		if endDate, err := time.Parse(time.RFC3339, endDateStr); err == nil {
			filters.EndDate = &endDate
		}
	}
	if dateRange := c.Query("dateRange"); dateRange != "" {
		filters.DateRange = &dateRange
	}

	// Get notifications
	result, err := h.notificationService.GetNotifications(c.Request.Context(), shopID, branchID, filters, page, limit, sortBy, sortOrder)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get notifications")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get notifications"})
		return
	}

	// Get stats for the response
	stats, err := h.notificationService.GetNotificationStats(c.Request.Context(), shopID, branchID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get notification stats")
		// Continue without stats rather than failing the whole request
		stats = &models.NotificationStats{
			ByType:     make(map[string]int),
			ByPriority: make(map[string]int),
		}
	}

	// Format response to match frontend expectations
	response := gin.H{
		"data":       result.Data,
		"total":      result.Total,
		"page":       result.Page,
		"limit":      result.Limit,
		"totalPages": result.TotalPages,
		"summary": gin.H{
			"totalNotifications":        stats.TotalNotifications,
			"unreadNotifications":       stats.UnreadNotifications,
			"readNotifications":         stats.ReadNotifications,
			"urgentNotifications":       stats.UrgentNotifications,
			"highPriorityNotifications": stats.HighPriorityNotifications,
			"byType":                    stats.ByType,
			"byPriority":                stats.ByPriority,
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetNotificationByID godoc
// @Summary Get notification by ID
// @Description Retrieve a specific notification by ID
// @Tags notifications
// @Accept json
// @Produce json
// @Param shopId query string true "Shop ID"
// @Param branchId query string true "Branch ID"
// @Param id path string true "Notification ID"
// @Success 200 {object} models.Notification
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /notifications/{id} [get]
func (h *NotificationHandler) GetNotificationByID(c *gin.Context) {
	// Parse shop and branch IDs
	shopIDStr := c.Query("shopId")
	branchIDStr := c.Query("branchId")
	notificationIDStr := c.Param("id")

	if shopIDStr == "" || branchIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "shopId and branchId are required"})
		return
	}

	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	branchID, err := uuid.Parse(branchIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	notificationID, err := uuid.Parse(notificationIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid notification ID"})
		return
	}

	// Get notification
	notification, err := h.notificationService.GetNotificationByID(c.Request.Context(), shopID, branchID, notificationID)
	if err != nil {
		if err.Error() == "notification not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Notification not found"})
			return
		}
		h.logger.WithError(err).Error("Failed to get notification by ID")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get notification"})
		return
	}

	c.JSON(http.StatusOK, notification)
}

// CreateNotification godoc
// @Summary Create a new notification
// @Description Create a new notification for a shop and branch
// @Tags notifications
// @Accept json
// @Produce json
// @Param shopId query string true "Shop ID"
// @Param branchId query string true "Branch ID"
// @Param notification body models.NotificationRequest true "Notification data"
// @Success 201 {object} models.Notification
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /notifications [post]
func (h *NotificationHandler) CreateNotification(c *gin.Context) {
	// Parse shop and branch IDs
	shopIDStr := c.Query("shopId")
	branchIDStr := c.Query("branchId")

	if shopIDStr == "" || branchIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "shopId and branchId are required"})
		return
	}

	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	branchID, err := uuid.Parse(branchIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Parse request body
	var req models.NotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create notification
	notification, err := h.notificationService.CreateNotification(c.Request.Context(), shopID, branchID, req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create notification")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create notification"})
		return
	}

	c.JSON(http.StatusCreated, notification)
}

// UpdateNotification godoc
// @Summary Update a notification
// @Description Update a notification (e.g., mark as read/unread)
// @Tags notifications
// @Accept json
// @Produce json
// @Param shopId query string true "Shop ID"
// @Param branchId query string true "Branch ID"
// @Param id path string true "Notification ID"
// @Param updates body models.NotificationUpdateRequest true "Update data"
// @Success 200 {object} models.Notification
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /notifications/{id} [patch]
func (h *NotificationHandler) UpdateNotification(c *gin.Context) {
	// Parse shop and branch IDs
	shopIDStr := c.Query("shopId")
	branchIDStr := c.Query("branchId")
	notificationIDStr := c.Param("id")

	if shopIDStr == "" || branchIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "shopId and branchId are required"})
		return
	}

	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	branchID, err := uuid.Parse(branchIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	notificationID, err := uuid.Parse(notificationIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid notification ID"})
		return
	}

	// Parse request body
	var req models.NotificationUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Update notification
	notification, err := h.notificationService.UpdateNotification(c.Request.Context(), shopID, branchID, notificationID, req)
	if err != nil {
		if err.Error() == "notification not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Notification not found"})
			return
		}
		h.logger.WithError(err).Error("Failed to update notification")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update notification"})
		return
	}

	c.JSON(http.StatusOK, notification)
}

// BulkUpdateNotifications godoc
// @Summary Bulk update notifications
// @Description Update multiple notifications at once
// @Tags notifications
// @Accept json
// @Produce json
// @Param shopId query string true "Shop ID"
// @Param branchId query string true "Branch ID"
// @Param request body models.BulkNotificationUpdateRequest true "Bulk update data"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /notifications/bulk-update [patch]
func (h *NotificationHandler) BulkUpdateNotifications(c *gin.Context) {
	// Parse shop and branch IDs
	shopIDStr := c.Query("shopId")
	branchIDStr := c.Query("branchId")

	if shopIDStr == "" || branchIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "shopId and branchId are required"})
		return
	}

	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	branchID, err := uuid.Parse(branchIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Parse request body
	var req models.BulkNotificationUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Bulk update notifications
	err = h.notificationService.BulkUpdateNotifications(c.Request.Context(), shopID, branchID, req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to bulk update notifications")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to bulk update notifications"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Notifications updated successfully"})
}

// DeleteNotification godoc
// @Summary Delete a notification
// @Description Delete a specific notification
// @Tags notifications
// @Accept json
// @Produce json
// @Param shopId query string true "Shop ID"
// @Param branchId query string true "Branch ID"
// @Param id path string true "Notification ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /notifications/{id} [delete]
func (h *NotificationHandler) DeleteNotification(c *gin.Context) {
	// Parse shop and branch IDs
	shopIDStr := c.Query("shopId")
	branchIDStr := c.Query("branchId")
	notificationIDStr := c.Param("id")

	if shopIDStr == "" || branchIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "shopId and branchId are required"})
		return
	}

	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	branchID, err := uuid.Parse(branchIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	notificationID, err := uuid.Parse(notificationIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid notification ID"})
		return
	}

	// Delete notification
	err = h.notificationService.DeleteNotification(c.Request.Context(), shopID, branchID, notificationID)
	if err != nil {
		if err.Error() == "notification not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Notification not found"})
			return
		}
		h.logger.WithError(err).Error("Failed to delete notification")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete notification"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Notification deleted successfully"})
}

// MarkAllAsRead godoc
// @Summary Mark all notifications as read
// @Description Mark all notifications as read for a shop and branch
// @Tags notifications
// @Accept json
// @Produce json
// @Param shopId query string true "Shop ID"
// @Param branchId query string true "Branch ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /notifications/mark-all-read [patch]
func (h *NotificationHandler) MarkAllAsRead(c *gin.Context) {
	// Parse shop and branch IDs
	shopIDStr := c.Query("shopId")
	branchIDStr := c.Query("branchId")

	if shopIDStr == "" || branchIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "shopId and branchId are required"})
		return
	}

	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	branchID, err := uuid.Parse(branchIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Mark all as read
	err = h.notificationService.MarkAllAsRead(c.Request.Context(), shopID, branchID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to mark all notifications as read")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to mark all notifications as read"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "All notifications marked as read"})
}

// ClearAllNotifications godoc
// @Summary Clear all notifications
// @Description Delete all notifications for a shop and branch
// @Tags notifications
// @Accept json
// @Produce json
// @Param shopId query string true "Shop ID"
// @Param branchId query string true "Branch ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /notifications/clear-all [delete]
func (h *NotificationHandler) ClearAllNotifications(c *gin.Context) {
	// Parse shop and branch IDs
	shopIDStr := c.Query("shopId")
	branchIDStr := c.Query("branchId")

	if shopIDStr == "" || branchIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "shopId and branchId are required"})
		return
	}

	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	branchID, err := uuid.Parse(branchIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Clear all notifications
	err = h.notificationService.ClearAllNotifications(c.Request.Context(), shopID, branchID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to clear all notifications")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to clear all notifications"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "All notifications cleared"})
}

// GetNotificationStats godoc
// @Summary Get notification statistics
// @Description Get notification statistics for a shop and branch
// @Tags notifications
// @Accept json
// @Produce json
// @Param shopId query string true "Shop ID"
// @Param branchId query string true "Branch ID"
// @Success 200 {object} models.NotificationStats
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /notifications/stats [get]
func (h *NotificationHandler) GetNotificationStats(c *gin.Context) {
	// Parse shop and branch IDs
	shopIDStr := c.Query("shopId")
	branchIDStr := c.Query("branchId")

	if shopIDStr == "" || branchIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "shopId and branchId are required"})
		return
	}

	shopID, err := uuid.Parse(shopIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	branchID, err := uuid.Parse(branchIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Get stats
	stats, err := h.notificationService.GetNotificationStats(c.Request.Context(), shopID, branchID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get notification stats")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get notification stats"})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// Helper method to get shop and branch IDs from slugs - now using service methods
func (h *NotificationHandler) getShopAndBranchIDsFromSlugs(c *gin.Context, shopSlug, branchSlug string) (uuid.UUID, uuid.UUID, error) {
	// This is now handled by the service layer, so we don't need this helper anymore
	// The service methods handle slug-to-ID conversion internally
	return uuid.Nil, uuid.Nil, fmt.Errorf("this method should not be called - use service slug methods directly")
}

// Slug-based notification handlers that extract shop and branch IDs from URL parameters

// GetNotificationsBySlug handles GET /shops/slug/:slug/branches/slug/:branchSlug/notifications
func (h *NotificationHandler) GetNotificationsBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	sortBy := c.DefaultQuery("sort_by", "timestamp")
	sortOrder := c.DefaultQuery("sort_order", "desc")

	// Parse filters
	filters := models.NotificationFilters{}

	if typeFilter := c.Query("type"); typeFilter != "" {
		filters.Type = &typeFilter
	}
	if priorityFilter := c.Query("priority"); priorityFilter != "" {
		filters.Priority = &priorityFilter
	}
	if isReadStr := c.Query("isRead"); isReadStr != "" {
		if isRead, err := strconv.ParseBool(isReadStr); err == nil {
			filters.IsRead = &isRead
		}
	}
	if search := c.Query("search"); search != "" {
		filters.Search = &search
	}
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		if startDate, err := time.Parse(time.RFC3339, startDateStr); err == nil {
			filters.StartDate = &startDate
		}
	}
	if endDateStr := c.Query("endDate"); endDateStr != "" {
		if endDate, err := time.Parse(time.RFC3339, endDateStr); err == nil {
			filters.EndDate = &endDate
		}
	}
	if dateRange := c.Query("dateRange"); dateRange != "" {
		filters.DateRange = &dateRange
	}

	// Get notifications using slug-based service method
	result, err := h.notificationService.GetNotificationsBySlug(c.Request.Context(), shopSlug, branchSlug, filters, page, limit, sortBy, sortOrder)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get notifications by slug")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get notifications"})
		return
	}

	// Get stats for the response
	stats, err := h.notificationService.GetNotificationStatsBySlug(c.Request.Context(), shopSlug, branchSlug)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get notification stats by slug")
		// Continue without stats rather than failing the whole request
		stats = &models.NotificationStats{
			ByType:     make(map[string]int),
			ByPriority: make(map[string]int),
		}
	}

	// Format response to match frontend expectations
	response := gin.H{
		"data":       result.Data,
		"total":      result.Total,
		"page":       result.Page,
		"limit":      result.Limit,
		"totalPages": result.TotalPages,
		"summary": gin.H{
			"totalNotifications":        stats.TotalNotifications,
			"unreadNotifications":       stats.UnreadNotifications,
			"readNotifications":         stats.ReadNotifications,
			"urgentNotifications":       stats.UrgentNotifications,
			"highPriorityNotifications": stats.HighPriorityNotifications,
			"byType":                    stats.ByType,
			"byPriority":                stats.ByPriority,
		},
	}

	c.JSON(http.StatusOK, response)
}

// CreateNotificationBySlug handles POST /shops/slug/:slug/branches/slug/:branchSlug/notifications
func (h *NotificationHandler) CreateNotificationBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	// Parse request body
	var req models.NotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create notification using slug-based service method
	notification, err := h.notificationService.CreateNotificationBySlug(c.Request.Context(), shopSlug, branchSlug, req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create notification by slug")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create notification"})
		return
	}

	c.JSON(http.StatusCreated, notification)
}

// GetNotificationByIDAndSlug handles GET /shops/slug/:slug/branches/slug/:branchSlug/notifications/:id
func (h *NotificationHandler) GetNotificationByIDAndSlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")
	notificationIDStr := c.Param("id")

	notificationID, err := uuid.Parse(notificationIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid notification ID"})
		return
	}

	// Get notification using slug-based service method
	notification, err := h.notificationService.GetNotificationByIDAndSlug(c.Request.Context(), shopSlug, branchSlug, notificationID)
	if err != nil {
		if err.Error() == "notification not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Notification not found"})
			return
		}
		h.logger.WithError(err).Error("Failed to get notification by ID and slug")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get notification"})
		return
	}

	c.JSON(http.StatusOK, notification)
}

// UpdateNotificationBySlug handles PATCH /shops/slug/:slug/branches/slug/:branchSlug/notifications/:id
func (h *NotificationHandler) UpdateNotificationBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")
	notificationIDStr := c.Param("id")

	notificationID, err := uuid.Parse(notificationIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid notification ID"})
		return
	}

	// Parse request body
	var req models.NotificationUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Update notification using slug-based service method
	notification, err := h.notificationService.UpdateNotificationBySlug(c.Request.Context(), shopSlug, branchSlug, notificationID, req)
	if err != nil {
		if err.Error() == "notification not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Notification not found"})
			return
		}
		h.logger.WithError(err).Error("Failed to update notification by slug")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update notification"})
		return
	}

	c.JSON(http.StatusOK, notification)
}

// DeleteNotificationBySlug handles DELETE /shops/slug/:slug/branches/slug/:branchSlug/notifications/:id
func (h *NotificationHandler) DeleteNotificationBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")
	notificationIDStr := c.Param("id")

	notificationID, err := uuid.Parse(notificationIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid notification ID"})
		return
	}

	// Delete notification using slug-based service method
	err = h.notificationService.DeleteNotificationBySlug(c.Request.Context(), shopSlug, branchSlug, notificationID)
	if err != nil {
		if err.Error() == "notification not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Notification not found"})
			return
		}
		h.logger.WithError(err).Error("Failed to delete notification by slug")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete notification"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Notification deleted successfully"})
}

// BulkUpdateNotificationsBySlug handles PATCH /shops/slug/:slug/branches/slug/:branchSlug/notifications/bulk-update
func (h *NotificationHandler) BulkUpdateNotificationsBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	// Parse request body
	var req models.BulkNotificationUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Bulk update notifications using slug-based service method
	err := h.notificationService.BulkUpdateNotificationsBySlug(c.Request.Context(), shopSlug, branchSlug, req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to bulk update notifications by slug")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to bulk update notifications"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Notifications updated successfully"})
}

// MarkAllAsReadBySlug handles PATCH /shops/slug/:slug/branches/slug/:branchSlug/notifications/mark-all-read
func (h *NotificationHandler) MarkAllAsReadBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	// Mark all as read using slug-based service method
	err := h.notificationService.MarkAllAsReadBySlug(c.Request.Context(), shopSlug, branchSlug)
	if err != nil {
		h.logger.WithError(err).Error("Failed to mark all notifications as read by slug")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to mark all notifications as read"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "All notifications marked as read"})
}

// ClearAllNotificationsBySlug handles DELETE /shops/slug/:slug/branches/slug/:branchSlug/notifications/clear-all
func (h *NotificationHandler) ClearAllNotificationsBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	// Clear all notifications using slug-based service method
	err := h.notificationService.ClearAllNotificationsBySlug(c.Request.Context(), shopSlug, branchSlug)
	if err != nil {
		h.logger.WithError(err).Error("Failed to clear all notifications by slug")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to clear all notifications"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "All notifications cleared successfully"})
}

// GetNotificationStatsBySlug handles GET /shops/slug/:slug/branches/slug/:branchSlug/notifications/stats
func (h *NotificationHandler) GetNotificationStatsBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	// Get notification stats using slug-based service method
	stats, err := h.notificationService.GetNotificationStatsBySlug(c.Request.Context(), shopSlug, branchSlug)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get notification stats by slug")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get notification stats"})
		return
	}

	c.JSON(http.StatusOK, stats)
}
