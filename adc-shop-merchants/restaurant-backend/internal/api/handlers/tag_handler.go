package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"restaurant-backend/internal/services"
	"restaurant-backend/internal/types"
)

type TagHandler struct {
	tagService services.TagService
	logger     *logrus.Logger
}

func NewTagHandler(tagService services.TagService, logger *logrus.Logger) *TagHandler {
	return &TagHandler{
		tagService: tagService,
		logger:     logger,
	}
}

// Tag operations

// @Summary Create a new tag
// @Description Create a new tag for a branch
// @Tags tags
// @Accept json
// @Produce json
// @Param branchId path string true "Branch ID"
// @Param tag body types.CreateTagRequest true "Tag data"
// @Success 201 {object} types.TagResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/tags [post]
func (h *TagHandler) CreateTag(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var req types.CreateTagRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	tag, err := h.tagService.CreateTag(c.Request.Context(), branchID, req)
	if err != nil {
		h.logger.Error("Failed to create tag: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create tag"})
		return
	}

	c.JSON(http.StatusCreated, tag)
}

// @Summary Get tag by ID
// @Description Get a specific tag by its ID
// @Tags tags
// @Produce json
// @Param tagId path string true "Tag ID"
// @Success 200 {object} types.TagResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/tags/{tagId} [get]
func (h *TagHandler) GetTag(c *gin.Context) {
	tagID, err := uuid.Parse(c.Param("tagId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tag ID"})
		return
	}

	tag, err := h.tagService.GetTag(c.Request.Context(), tagID)
	if err != nil {
		h.logger.Error("Failed to get tag: ", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Tag not found"})
		return
	}

	c.JSON(http.StatusOK, tag)
}

// @Summary Get tag by slug
// @Description Get a specific tag by its slug
// @Tags tags
// @Produce json
// @Param branchId path string true "Branch ID"
// @Param tagSlug path string true "Tag Slug"
// @Success 200 {object} types.TagResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/tags/slug/{tagSlug} [get]
func (h *TagHandler) GetTagBySlug(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	tagSlug := c.Param("tagSlug")
	if tagSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tag slug is required"})
		return
	}

	tag, err := h.tagService.GetTagBySlug(c.Request.Context(), branchID, tagSlug)
	if err != nil {
		h.logger.Error("Failed to get tag by slug: ", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Tag not found"})
		return
	}

	c.JSON(http.StatusOK, tag)
}

// @Summary Update tag
// @Description Update an existing tag
// @Tags tags
// @Accept json
// @Produce json
// @Param tagId path string true "Tag ID"
// @Param tag body types.UpdateTagRequest true "Updated tag data"
// @Success 200 {object} types.TagResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/tags/{tagId} [put]
func (h *TagHandler) UpdateTag(c *gin.Context) {
	tagID, err := uuid.Parse(c.Param("tagId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tag ID"})
		return
	}

	var req types.UpdateTagRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	tag, err := h.tagService.UpdateTag(c.Request.Context(), tagID, req)
	if err != nil {
		h.logger.Error("Failed to update tag: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update tag"})
		return
	}

	c.JSON(http.StatusOK, tag)
}

// @Summary Delete tag
// @Description Delete a tag
// @Tags tags
// @Param tagId path string true "Tag ID"
// @Success 204
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/tags/{tagId} [delete]
func (h *TagHandler) DeleteTag(c *gin.Context) {
	tagID, err := uuid.Parse(c.Param("tagId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tag ID"})
		return
	}

	if err := h.tagService.DeleteTag(c.Request.Context(), tagID); err != nil {
		h.logger.Error("Failed to delete tag: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete tag"})
		return
	}

	c.Status(http.StatusNoContent)
}

// @Summary Get tags
// @Description Get all tags for a branch with filtering and pagination
// @Tags tags
// @Produce json
// @Param branchId path string true "Branch ID"
// @Param category query string false "Filter by category"
// @Param is_active query boolean false "Filter by active status"
// @Param is_system query boolean false "Filter by system tags"
// @Param search query string false "Search in name and description"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param sort_by query string false "Sort field" default("name")
// @Param sort_order query string false "Sort order (asc, desc)" default("asc")
// @Success 200 {object} types.TagsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/tags [get]
func (h *TagHandler) GetTags(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.TagFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	tags, err := h.tagService.GetTags(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.Error("Failed to get tags: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get tags"})
		return
	}

	c.JSON(http.StatusOK, tags)
}

// @Summary Get popular tags
// @Description Get the most popular tags for a branch
// @Tags tags
// @Produce json
// @Param branchId path string true "Branch ID"
// @Param limit query int false "Number of tags to return" default(10)
// @Success 200 {object} types.PopularTagsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/tags/popular [get]
func (h *TagHandler) GetPopularTags(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	limit := 10
	if limitStr := c.Query("limit"); limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	tags, err := h.tagService.GetPopularTags(c.Request.Context(), branchID, limit)
	if err != nil {
		h.logger.Error("Failed to get popular tags: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get popular tags"})
		return
	}

	c.JSON(http.StatusOK, tags)
}

// @Summary Search tags
// @Description Search for tags by name or description
// @Tags tags
// @Accept json
// @Produce json
// @Param branchId path string true "Branch ID"
// @Param search body types.TagSuggestionRequest true "Search parameters"
// @Success 200 {object} types.TagSuggestionResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/tags/search [post]
func (h *TagHandler) SearchTags(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var req types.TagSuggestionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	tags, err := h.tagService.SearchTags(c.Request.Context(), branchID, req)
	if err != nil {
		h.logger.Error("Failed to search tags: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to search tags"})
		return
	}

	c.JSON(http.StatusOK, tags)
}

// @Summary Get tags by category
// @Description Get all tags in a specific category
// @Tags tags
// @Produce json
// @Param branchId path string true "Branch ID"
// @Param category path string true "Category name"
// @Success 200 {object} types.TagsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/tags/category/{category} [get]
func (h *TagHandler) GetTagsByCategory(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	category := c.Param("category")
	if category == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Category is required"})
		return
	}

	tags, err := h.tagService.GetTagsByCategory(c.Request.Context(), branchID, category)
	if err != nil {
		h.logger.Error("Failed to get tags by category: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get tags by category"})
		return
	}

	c.JSON(http.StatusOK, tags)
}

// @Summary Get tag analytics
// @Description Get analytics data for tags in a branch
// @Tags tags
// @Produce json
// @Param branchId path string true "Branch ID"
// @Success 200 {object} types.TagAnalyticsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/tags/analytics [get]
func (h *TagHandler) GetTagAnalytics(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	analytics, err := h.tagService.GetTagAnalytics(c.Request.Context(), branchID)
	if err != nil {
		h.logger.Error("Failed to get tag analytics: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get tag analytics"})
		return
	}

	c.JSON(http.StatusOK, analytics)
}

// Tag Category operations

// @Summary Create a new tag category
// @Description Create a new tag category for a branch
// @Tags tag-categories
// @Accept json
// @Produce json
// @Param branchId path string true "Branch ID"
// @Param category body types.CreateTagCategoryRequest true "Tag category data"
// @Success 201 {object} types.TagCategoryResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/tag-categories [post]
func (h *TagHandler) CreateTagCategory(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var req types.CreateTagCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	category, err := h.tagService.CreateTagCategory(c.Request.Context(), branchID, req)
	if err != nil {
		h.logger.Error("Failed to create tag category: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create tag category"})
		return
	}

	c.JSON(http.StatusCreated, category)
}

// @Summary Get tag categories
// @Description Get all tag categories for a branch with filtering and pagination
// @Tags tag-categories
// @Produce json
// @Param branchId path string true "Branch ID"
// @Param is_active query boolean false "Filter by active status"
// @Param is_system query boolean false "Filter by system categories"
// @Param search query string false "Search in name and description"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param sort_by query string false "Sort field" default("sort_order")
// @Param sort_order query string false "Sort order (asc, desc)" default("asc")
// @Success 200 {object} types.TagCategoriesResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/tag-categories [get]
func (h *TagHandler) GetTagCategories(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.TagCategoryFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	categories, err := h.tagService.GetTagCategories(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.Error("Failed to get tag categories: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get tag categories"})
		return
	}

	c.JSON(http.StatusOK, categories)
}

// Entity Tag operations

// @Summary Assign tags to entity
// @Description Assign tags to an entity (menu item, review, etc.)
// @Tags entity-tags
// @Accept json
// @Produce json
// @Param entityType path string true "Entity type (menu_item, review, etc.)"
// @Param entityId path string true "Entity ID"
// @Param tags body types.AssignTagsRequest true "Tag IDs to assign"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/entities/{entityType}/{entityId}/tags [post]
func (h *TagHandler) AssignTagsToEntity(c *gin.Context) {
	entityType := c.Param("entityType")
	entityID, err := uuid.Parse(c.Param("entityId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid entity ID"})
		return
	}

	var req types.AssignTagsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	if err := h.tagService.AssignTagsToEntity(c.Request.Context(), entityType, entityID, req); err != nil {
		h.logger.Error("Failed to assign tags to entity: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to assign tags to entity"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Tags assigned successfully"})
}

// @Summary Get entity tags
// @Description Get all tags assigned to an entity
// @Tags entity-tags
// @Produce json
// @Param entityType path string true "Entity type (menu_item, review, etc.)"
// @Param entityId path string true "Entity ID"
// @Success 200 {object} []types.EntityTagResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/entities/{entityType}/{entityId}/tags [get]
func (h *TagHandler) GetEntityTags(c *gin.Context) {
	entityType := c.Param("entityType")
	entityID, err := uuid.Parse(c.Param("entityId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid entity ID"})
		return
	}

	tags, err := h.tagService.GetEntityTags(c.Request.Context(), entityType, entityID)
	if err != nil {
		h.logger.Error("Failed to get entity tags: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get entity tags"})
		return
	}

	c.JSON(http.StatusOK, tags)
}
