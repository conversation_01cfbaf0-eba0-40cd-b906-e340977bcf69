package handlers

import (
	"net/http"
	"strconv"
	"time"

	"restaurant-backend/internal/services"
	"restaurant-backend/internal/types"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// IntegrationTokenHandler handles integration token HTTP requests
type IntegrationTokenHandler struct {
	tokenService *services.IntegrationTokenService
	logger       *logrus.Logger
}

// NewIntegrationTokenHandler creates a new integration token handler
func NewIntegrationTokenHandler(tokenService *services.IntegrationTokenService, logger *logrus.Logger) *IntegrationTokenHandler {
	return &IntegrationTokenHandler{
		tokenService: tokenService,
		logger:       logger,
	}
}

// CreateToken creates a new integration token
// @Summary Create integration token
// @Description Create a new integration token for API access
// @Tags integration-tokens
// @Accept json
// @Produce json
// @Param request body types.CreateIntegrationTokenRequest true "Token creation request"
// @Success 201 {object} types.CreateIntegrationTokenResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/integration-tokens [post]
func (h *IntegrationTokenHandler) CreateToken(c *gin.Context) {
	var req types.CreateIntegrationTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context (from auth middleware)
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Handle different user ID formats (string for OAuth, UUID for JWT)
	var userID uuid.UUID
	switch v := userIDInterface.(type) {
	case string:
		if parsedUUID, err := uuid.Parse(v); err == nil {
			userID = parsedUUID
		} else {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID format"})
			return
		}
	case uuid.UUID:
		userID = v
	default:
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID type"})
		return
	}

	req.UserID = userID

	// Get shop ID from context
	shopIDInterface, exists := c.Get("shop_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Shop not found in context"})
		return
	}

	shopID, ok := shopIDInterface.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid shop ID"})
		return
	}

	req.ShopID = shopID

	// Create token
	response, err := h.tokenService.CreateToken(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create integration token")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create token"})
		return
	}

	c.JSON(http.StatusCreated, response)
}

// GetTokens retrieves integration tokens for a shop
// @Summary Get integration tokens
// @Description Get all integration tokens for a shop
// @Tags integration-tokens
// @Accept json
// @Produce json
// @Param limit query int false "Limit" default(50)
// @Param offset query int false "Offset" default(0)
// @Success 200 {object} types.GetIntegrationTokensResponse
// @Failure 401 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/integration-tokens [get]
func (h *IntegrationTokenHandler) GetTokens(c *gin.Context) {
	// Get user ID from context
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var userID uuid.UUID
	switch v := userIDInterface.(type) {
	case string:
		if parsedUUID, err := uuid.Parse(v); err == nil {
			userID = parsedUUID
		} else {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID format"})
			return
		}
	case uuid.UUID:
		userID = v
	default:
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID type"})
		return
	}

	// Get shop ID from context
	shopIDInterface, exists := c.Get("shop_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Shop not found in context"})
		return
	}

	shopID, ok := shopIDInterface.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid shop ID"})
		return
	}

	// Parse query parameters
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "50"))
	offset, _ := strconv.Atoi(c.DefaultQuery("offset", "0"))

	req := types.GetIntegrationTokensRequest{
		UserID: userID,
		ShopID: shopID,
		Limit:  limit,
		Offset: offset,
	}

	// Get tokens
	response, err := h.tokenService.GetTokens(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get integration tokens")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get tokens"})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetToken retrieves a specific integration token
// @Summary Get integration token
// @Description Get a specific integration token by ID
// @Tags integration-tokens
// @Accept json
// @Produce json
// @Param id path string true "Token ID"
// @Success 200 {object} types.IntegrationTokenResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/integration-tokens/{id} [get]
func (h *IntegrationTokenHandler) GetToken(c *gin.Context) {
	// Parse token ID
	tokenIDStr := c.Param("id")
	tokenID, err := uuid.Parse(tokenIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid token ID"})
		return
	}

	// Get user ID from context
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var userID uuid.UUID
	switch v := userIDInterface.(type) {
	case string:
		if parsedUUID, err := uuid.Parse(v); err == nil {
			userID = parsedUUID
		} else {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID format"})
			return
		}
	case uuid.UUID:
		userID = v
	default:
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID type"})
		return
	}

	req := types.GetIntegrationTokenRequest{
		TokenID: tokenID,
		UserID:  userID,
	}

	// Get token
	response, err := h.tokenService.GetToken(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get integration token")
		c.JSON(http.StatusNotFound, gin.H{"error": "Token not found"})
		return
	}

	c.JSON(http.StatusOK, response)
}

// UpdateToken updates an integration token
// @Summary Update integration token
// @Description Update an existing integration token
// @Tags integration-tokens
// @Accept json
// @Produce json
// @Param id path string true "Token ID"
// @Param request body types.UpdateIntegrationTokenRequest true "Token update request"
// @Success 200 {object} types.IntegrationTokenResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/integration-tokens/{id} [put]
func (h *IntegrationTokenHandler) UpdateToken(c *gin.Context) {
	// Parse token ID
	tokenIDStr := c.Param("id")
	tokenID, err := uuid.Parse(tokenIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid token ID"})
		return
	}

	var req types.UpdateIntegrationTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.TokenID = tokenID

	// Get user ID from context
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var userID uuid.UUID
	switch v := userIDInterface.(type) {
	case string:
		if parsedUUID, err := uuid.Parse(v); err == nil {
			userID = parsedUUID
		} else {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID format"})
			return
		}
	case uuid.UUID:
		userID = v
	default:
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID type"})
		return
	}

	req.UserID = userID

	// Update token
	response, err := h.tokenService.UpdateToken(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update integration token")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update token"})
		return
	}

	c.JSON(http.StatusOK, response)
}

// RevokeToken revokes an integration token
// @Summary Revoke integration token
// @Description Revoke an existing integration token
// @Tags integration-tokens
// @Accept json
// @Produce json
// @Param id path string true "Token ID"
// @Success 204
// @Failure 400 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/integration-tokens/{id}/revoke [post]
func (h *IntegrationTokenHandler) RevokeToken(c *gin.Context) {
	// Parse token ID
	tokenIDStr := c.Param("id")
	tokenID, err := uuid.Parse(tokenIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid token ID"})
		return
	}

	// Get user ID from context
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var userID uuid.UUID
	switch v := userIDInterface.(type) {
	case string:
		if parsedUUID, err := uuid.Parse(v); err == nil {
			userID = parsedUUID
		} else {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID format"})
			return
		}
	case uuid.UUID:
		userID = v
	default:
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID type"})
		return
	}

	req := types.RevokeIntegrationTokenRequest{
		TokenID: tokenID,
		UserID:  userID,
	}

	// Revoke token
	if err := h.tokenService.RevokeToken(c.Request.Context(), req); err != nil {
		h.logger.WithError(err).Error("Failed to revoke integration token")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to revoke token"})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetTokenUsage retrieves token usage statistics
// @Summary Get token usage
// @Description Get usage statistics for an integration token
// @Tags integration-tokens
// @Accept json
// @Produce json
// @Param id path string true "Token ID"
// @Param from query string false "From date (RFC3339)"
// @Param to query string false "To date (RFC3339)"
// @Success 200 {object} types.GetTokenUsageResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/integration-tokens/{id}/usage [get]
func (h *IntegrationTokenHandler) GetTokenUsage(c *gin.Context) {
	// Parse token ID
	tokenIDStr := c.Param("id")
	tokenID, err := uuid.Parse(tokenIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid token ID"})
		return
	}

	// Get user ID from context
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var userID uuid.UUID
	switch v := userIDInterface.(type) {
	case string:
		if parsedUUID, err := uuid.Parse(v); err == nil {
			userID = parsedUUID
		} else {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID format"})
			return
		}
	case uuid.UUID:
		userID = v
	default:
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID type"})
		return
	}

	// Parse time parameters
	var from, to time.Time
	if fromStr := c.Query("from"); fromStr != "" {
		if parsedFrom, err := time.Parse(time.RFC3339, fromStr); err == nil {
			from = parsedFrom
		}
	}
	if toStr := c.Query("to"); toStr != "" {
		if parsedTo, err := time.Parse(time.RFC3339, toStr); err == nil {
			to = parsedTo
		}
	}

	req := types.GetTokenUsageRequest{
		TokenID: tokenID,
		UserID:  userID,
		From:    from,
		To:      to,
	}

	// Get usage
	response, err := h.tokenService.GetTokenUsage(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get token usage")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get token usage"})
		return
	}

	c.JSON(http.StatusOK, response)
}

// ValidateToken validates an integration token (public endpoint)
// @Summary Validate integration token
// @Description Validate an integration token and return token info
// @Tags integration-tokens
// @Accept json
// @Produce json
// @Param request body types.ValidateTokenRequest true "Token validation request"
// @Success 200 {object} types.ValidateTokenResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Router /api/v1/integration-tokens/validate [post]
func (h *IntegrationTokenHandler) ValidateToken(c *gin.Context) {
	var req types.ValidateTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get client IP and user agent if not provided
	if req.IPAddress == "" {
		req.IPAddress = c.ClientIP()
	}
	if req.UserAgent == "" {
		req.UserAgent = c.GetHeader("User-Agent")
	}

	// Validate token
	token, err := h.tokenService.ValidateToken(c.Request.Context(), req.Token, req.IPAddress, req.UserAgent)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"valid":   false,
			"message": err.Error(),
		})
		return
	}

	// Check specific resource/action if provided
	var hasAccess bool = true
	if req.Resource != "" && req.Action != "" {
		var branchID *uuid.UUID
		if req.BranchID != "" {
			if parsedBranchID, err := uuid.Parse(req.BranchID); err == nil {
				branchID = &parsedBranchID
			}
		}
		hasAccess = token.CanAccess(req.Resource, req.Action, branchID)
	}

	if !hasAccess {
		c.JSON(http.StatusForbidden, gin.H{
			"valid":   false,
			"message": "Insufficient permissions for requested resource",
		})
		return
	}

	// Convert scopes
	scopes := make([]types.IntegrationTokenScope, len(token.Scopes))
	for i, scope := range token.Scopes {
		scopes[i] = types.IntegrationTokenScope{
			Resource:     scope.Resource,
			Actions:      scope.Actions,
			BranchIDs:    scope.BranchIDs,
			Restrictions: scope.Restrictions,
		}
	}

	response := types.ValidateTokenResponse{
		Valid: true,
		Token: &types.IntegrationTokenResponse{
			ID:           token.ID,
			Name:         token.Name,
			Description:  token.Description,
			Type:         string(token.Type),
			Status:       string(token.Status),
			TokenPrefix:  token.TokenPrefix,
			Scopes:       scopes,
			RateLimit:    token.RateLimit,
			CurrentUsage: token.CurrentUsage,
			UsageCount:   token.UsageCount,
			LastUsedAt:   token.LastUsedAt,
			ExpiresAt:    token.ExpiresAt,
			CreatedAt:    token.CreatedAt,
			UpdatedAt:    token.UpdatedAt,
		},
		Scopes:   scopes,
		ShopID:   token.ShopID,
		BranchID: token.BranchID,
	}

	c.JSON(http.StatusOK, response)
}
