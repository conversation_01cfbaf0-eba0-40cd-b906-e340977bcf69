package handlers

import (
	"net/http"
	"strings"

	"restaurant-backend/internal/services"
	"restaurant-backend/internal/types"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// TableHandler handles table-related HTTP requests
type TableHandler struct {
	tableService   *services.TableService
	storageService *services.StorageService
	logger         *logrus.Logger
}

func NewTableHandler(tableService *services.TableService, storageService *services.StorageService, logger *logrus.Logger) *TableHandler {
	return &TableHandler{
		tableService:   tableService,
		storageService: storageService,
		logger:         logger,
	}
}

// Table handlers

// GetTables godoc
// @Summary Get all tables for a branch
// @Description Get all tables for a specific branch with filtering and pagination
// @Tags tables
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param area_id query string false "Filter by area ID"
// @Param status query string false "Filter by status"
// @Param is_active query bool false "Filter by active status"
// @Param search query string false "Search term"
// @Param page query int false "Page number"
// @Param limit query int false "Items per page"
// @Success 200 {object} types.TablesResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/tables [get]
func (h *TableHandler) GetTables(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.TableFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	tables, err := h.tableService.GetTables(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.Error("Failed to get tables: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get tables"})
		return
	}

	c.JSON(http.StatusOK, tables)
}

// GetTable godoc
// @Summary Get a specific table
// @Description Get a specific table by ID
// @Tags tables
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param tableId path string true "Table ID"
// @Success 200 {object} types.TableResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/tables/{tableId} [get]
func (h *TableHandler) GetTable(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	tableID, err := uuid.Parse(c.Param("tableId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid table ID"})
		return
	}

	table, err := h.tableService.GetTableByID(c.Request.Context(), branchID, tableID)
	if err != nil {
		h.logger.Error("Failed to get table: ", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Table not found"})
		return
	}

	c.JSON(http.StatusOK, table)
}

// CreateTable godoc
// @Summary Create a new table
// @Description Create a new table for a branch
// @Tags tables
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param table body types.CreateTableRequest true "Table data"
// @Success 201 {object} types.TableResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/tables [post]
func (h *TableHandler) CreateTable(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var req types.CreateTableRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	table, err := h.tableService.CreateTable(c.Request.Context(), branchID, req)
	if err != nil {
		h.logger.Error("Failed to create table: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create table"})
		return
	}

	c.JSON(http.StatusCreated, table)
}

// UpdateTable godoc
// @Summary Update a table
// @Description Update a specific table by ID
// @Tags tables
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param tableId path string true "Table ID"
// @Param table body types.UpdateTableRequest true "Table update data"
// @Success 200 {object} types.TableResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/tables/{tableId} [put]
func (h *TableHandler) UpdateTable(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	tableID, err := uuid.Parse(c.Param("tableId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid table ID"})
		return
	}

	var req types.UpdateTableRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	table, err := h.tableService.UpdateTable(c.Request.Context(), branchID, tableID, req)
	if err != nil {
		h.logger.Error("Failed to update table: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update table"})
		return
	}

	c.JSON(http.StatusOK, table)
}

// DeleteTable godoc
// @Summary Delete a table
// @Description Delete a specific table by ID
// @Tags tables
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param tableId path string true "Table ID"
// @Success 204
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/tables/{tableId} [delete]
func (h *TableHandler) DeleteTable(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	tableID, err := uuid.Parse(c.Param("tableId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid table ID"})
		return
	}

	if err := h.tableService.DeleteTable(c.Request.Context(), branchID, tableID); err != nil {
		h.logger.Error("Failed to delete table: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete table"})
		return
	}

	c.Status(http.StatusNoContent)
}

// UpdateTableStatus godoc
// @Summary Update table status
// @Description Update the status of a specific table
// @Tags tables
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param tableId path string true "Table ID"
// @Param status body types.UpdateTableStatusRequest true "Status data"
// @Success 200 {object} types.TableResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/tables/{tableId}/status [patch]
func (h *TableHandler) UpdateTableStatus(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	tableID, err := uuid.Parse(c.Param("tableId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid table ID"})
		return
	}

	var req types.UpdateTableStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	table, err := h.tableService.UpdateTableStatus(c.Request.Context(), branchID, tableID, req)
	if err != nil {
		h.logger.Error("Failed to update table status: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update table status"})
		return
	}

	c.JSON(http.StatusOK, table)
}

// GenerateQRCode godoc
// @Summary Generate QR code for table
// @Description Generate a QR code for a specific table
// @Tags tables
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param tableId path string true "Table ID"
// @Param qr body types.GenerateQRCodeRequest true "QR code data"
// @Success 200 {object} types.QRCodeResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/tables/{tableId}/qr-code [post]
func (h *TableHandler) GenerateQRCode(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	tableID, err := uuid.Parse(c.Param("tableId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid table ID"})
		return
	}

	var req types.GenerateQRCodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	qrCode, err := h.tableService.GenerateQRCode(c.Request.Context(), branchID, tableID, req)
	if err != nil {
		h.logger.Error("Failed to generate QR code: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate QR code"})
		return
	}

	c.JSON(http.StatusOK, qrCode)
}

// Area handlers

// GetAreas godoc
// @Summary Get all areas for a branch
// @Description Get all areas for a specific branch with filtering and pagination
// @Tags areas
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param is_active query bool false "Filter by active status"
// @Param search query string false "Search term"
// @Param page query int false "Page number"
// @Param limit query int false "Items per page"
// @Success 200 {object} types.AreasResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/areas [get]
func (h *TableHandler) GetAreas(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.AreaFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	areas, err := h.tableService.GetAreas(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.Error("Failed to get areas: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get areas"})
		return
	}

	c.JSON(http.StatusOK, areas)
}

// CreateArea godoc
// @Summary Create a new area
// @Description Create a new area for a branch
// @Tags areas
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param area body types.CreateAreaRequest true "Area data"
// @Success 201 {object} types.AreaResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/areas [post]
func (h *TableHandler) CreateArea(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var req types.CreateAreaRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	area, err := h.tableService.CreateArea(c.Request.Context(), branchID, req)
	if err != nil {
		h.logger.Error("Failed to create area: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create area"})
		return
	}

	c.JSON(http.StatusCreated, area)
}

// UpdateArea godoc
// @Summary Update an area
// @Description Update a specific area by ID
// @Tags areas
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param areaId path string true "Area ID"
// @Param area body types.UpdateAreaRequest true "Area update data"
// @Success 200 {object} types.AreaResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/areas/{areaId} [put]
func (h *TableHandler) UpdateArea(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	areaID, err := uuid.Parse(c.Param("areaId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid area ID"})
		return
	}

	var req types.UpdateAreaRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	area, err := h.tableService.UpdateArea(c.Request.Context(), branchID, areaID, req)
	if err != nil {
		h.logger.Error("Failed to update area: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update area"})
		return
	}

	c.JSON(http.StatusOK, area)
}

// DeleteArea godoc
// @Summary Delete an area
// @Description Delete a specific area by ID
// @Tags areas
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param areaId path string true "Area ID"
// @Success 204
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/areas/{areaId} [delete]
func (h *TableHandler) DeleteArea(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	areaID, err := uuid.Parse(c.Param("areaId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid area ID"})
		return
	}

	if err := h.tableService.DeleteArea(c.Request.Context(), branchID, areaID); err != nil {
		h.logger.Error("Failed to delete area: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete area"})
		return
	}

	c.Status(http.StatusNoContent)
}

// UploadTableImage godoc
// @Summary Upload table image
// @Description Upload an image for a specific table
// @Tags tables
// @Accept multipart/form-data
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param tableId path string true "Table ID"
// @Param image formData file true "Image file"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/tables/{tableId}/image [post]
func (h *TableHandler) UploadTableImage(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	tableID, err := uuid.Parse(c.Param("tableId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid table ID"})
		return
	}

	// Get the uploaded file
	file, header, err := c.Request.FormFile("image")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No image file provided"})
		return
	}
	defer file.Close()

	// Validate file type
	contentType := header.Header.Get("Content-Type")
	if !strings.HasPrefix(contentType, "image/") {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File must be an image"})
		return
	}

	// Validate file size (5MB max)
	if header.Size > 5*1024*1024 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File size must be less than 5MB"})
		return
	}

	// Upload to Google Cloud Storage
	shopID := c.Param("merchantId") // Using merchantId for backward compatibility
	imageURL, err := h.storageService.UploadTableImage(c.Request.Context(), file, header, shopID, branchID.String(), tableID.String())
	if err != nil {
		h.logger.WithError(err).Error("Failed to upload table image")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to upload image"})
		return
	}

	// Update the table with the new image URL
	updateReq := types.UpdateTableRequest{
		ImageURL: &imageURL,
	}

	_, err = h.tableService.UpdateTable(c.Request.Context(), branchID, tableID, updateReq)
	if err != nil {
		h.logger.Error("Failed to update table image: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update table image"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"imageUrl": imageURL,
		"message":  "Image uploaded successfully",
	})
}
