package handlers

import (
	"net/http"
	"strconv"

	"restaurant-backend/internal/services"
	"restaurant-backend/internal/types"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// MenuHandler handles menu-related HTTP requests
type MenuHandler struct {
	menuService    *services.MenuService
	storageService *services.StorageService
	logger         *logrus.Logger
}

func NewMenuHandler(menuService *services.MenuService, storageService *services.StorageService, logger *logrus.Logger) *MenuHandler {
	return &MenuHandler{
		menuService:    menuService,
		storageService: storageService,
		logger:         logger,
	}
}

// Category handlers

// GetCategories godoc
// @Summary Get all menu categories for a branch
// @Description Get all menu categories for a specific branch with filtering and pagination
// @Tags menu-categories
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param is_active query bool false "Filter by active status"
// @Param search query string false "Search term"
// @Param page query int false "Page number"
// @Param limit query int false "Items per page"
// @Success 200 {object} types.CategoriesResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/menu/categories [get]
func (h *MenuHandler) GetCategories(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.CategoryFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	categories, err := h.menuService.GetCategories(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.Error("Failed to get categories: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get categories"})
		return
	}

	c.JSON(http.StatusOK, categories)
}

// CreateCategory godoc
// @Summary Create a new menu category
// @Description Create a new menu category for a branch
// @Tags menu-categories
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param category body types.CreateCategoryRequest true "Category data"
// @Success 201 {object} types.CategoryResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/menu/categories [post]
func (h *MenuHandler) CreateCategory(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var req types.CreateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	category, err := h.menuService.CreateCategory(c.Request.Context(), branchID, req)
	if err != nil {
		h.logger.Error("Failed to create category: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create category"})
		return
	}

	c.JSON(http.StatusCreated, category)
}

// UpdateCategory godoc
// @Summary Update a menu category
// @Description Update a specific menu category by ID
// @Tags menu-categories
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param categoryId path string true "Category ID"
// @Param category body types.UpdateCategoryRequest true "Category update data"
// @Success 200 {object} types.CategoryResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/menu/categories/{categoryId} [put]
func (h *MenuHandler) UpdateCategory(c *gin.Context) {
	categoryID, err := uuid.Parse(c.Param("categoryId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
		return
	}

	var req types.UpdateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	category, err := h.menuService.UpdateCategory(c.Request.Context(), categoryID, req)
	if err != nil {
		h.logger.Error("Failed to update category: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update category"})
		return
	}

	c.JSON(http.StatusOK, category)
}

// DeleteCategory godoc
// @Summary Delete a menu category
// @Description Delete a specific menu category by ID
// @Tags menu-categories
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param categoryId path string true "Category ID"
// @Success 204
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/menu/categories/{categoryId} [delete]
func (h *MenuHandler) DeleteCategory(c *gin.Context) {
	categoryID, err := uuid.Parse(c.Param("categoryId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
		return
	}

	if err := h.menuService.DeleteCategory(c.Request.Context(), categoryID); err != nil {
		h.logger.Error("Failed to delete category: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete category"})
		return
	}

	c.Status(http.StatusNoContent)
}

// Menu Item handlers

// GetMenuItems godoc
// @Summary Get all menu items for a branch
// @Description Get all menu items for a specific branch with filtering and pagination
// @Tags menu-items
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param category_id query string false "Filter by category ID"
// @Param is_available query bool false "Filter by availability"
// @Param is_vegetarian query bool false "Filter by vegetarian"
// @Param is_vegan query bool false "Filter by vegan"
// @Param is_gluten_free query bool false "Filter by gluten free"
// @Param is_spicy query bool false "Filter by spicy"
// @Param min_price query number false "Minimum price filter"
// @Param max_price query number false "Maximum price filter"
// @Param search query string false "Search term"
// @Param page query int false "Page number"
// @Param limit query int false "Items per page"
// @Success 200 {object} types.MenuItemsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/menu/items [get]
func (h *MenuHandler) GetMenuItems(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.MenuItemFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	// Apply defaults
	filters.ApplyDefaults()

	items, err := h.menuService.GetMenuItems(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.Error("Failed to get menu items: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get menu items"})
		return
	}

	c.JSON(http.StatusOK, items)
}

// GetMenuItem godoc
// @Summary Get a specific menu item
// @Description Get a specific menu item by ID
// @Tags menu-items
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param itemId path string true "Menu Item ID"
// @Success 200 {object} types.MenuItemResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/menu/items/{itemId} [get]
func (h *MenuHandler) GetMenuItem(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	itemID, err := uuid.Parse(c.Param("itemId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid item ID"})
		return
	}

	item, err := h.menuService.GetMenuItemByID(c.Request.Context(), branchID, itemID)
	if err != nil {
		h.logger.Error("Failed to get menu item: ", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Menu item not found"})
		return
	}

	c.JSON(http.StatusOK, item)
}

// GetMenuItemBySlug godoc
// @Summary Get a specific menu item by slug
// @Description Get a specific menu item by slug
// @Tags menu-items
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param slug path string true "Menu Item Slug"
// @Success 200 {object} types.MenuItemResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/menu/items/slug/{slug} [get]
func (h *MenuHandler) GetMenuItemBySlug(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	slug := c.Param("slug")
	if slug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Slug is required"})
		return
	}

	item, err := h.menuService.GetMenuItemBySlug(c.Request.Context(), branchID, slug)
	if err != nil {
		h.logger.Error("Failed to get menu item by slug: ", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Menu item not found"})
		return
	}

	c.JSON(http.StatusOK, item)
}

// GetMenuItemByShopBranchSlug godoc
// @Summary Get a specific menu item by slug using shop and branch slugs
// @Description Get a specific menu item by slug using shop and branch slugs
// @Tags menu-items
// @Accept json
// @Produce json
// @Param shopSlug path string true "Shop Slug"
// @Param branchSlug path string true "Branch Slug"
// @Param itemSlug path string true "Menu Item Slug"
// @Success 200 {object} types.MenuItemResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/slug/{shopSlug}/branches/slug/{branchSlug}/menu/items/slug/{itemSlug} [get]
func (h *MenuHandler) GetMenuItemByShopBranchSlug(c *gin.Context) {
	shopSlug := c.Param("slug")         // From /shops/slug/:slug
	branchSlug := c.Param("branchSlug") // From /branches/slug/:branchSlug
	itemSlug := c.Param("itemSlug")     // From /items/slug/:itemSlug

	if shopSlug == "" || branchSlug == "" || itemSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug, branch slug, and item slug are required"})
		return
	}

	item, err := h.menuService.GetMenuItemByShopBranchSlug(c.Request.Context(), shopSlug, branchSlug, itemSlug)
	if err != nil {
		h.logger.Error("Failed to get menu item by shop/branch/item slug: ", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Menu item not found"})
		return
	}

	c.JSON(http.StatusOK, item)
}

// GetMenuItemsBySlug godoc
// @Summary Get all menu items for a branch using shop and branch slugs
// @Description Get all menu items for a specific branch with filtering and pagination using shop and branch slugs
// @Tags menu-items
// @Accept json
// @Produce json
// @Param shopSlug path string true "Shop Slug"
// @Param branchSlug path string true "Branch Slug"
// @Param category_id query string false "Filter by category ID"
// @Param is_available query bool false "Filter by availability"
// @Param is_vegetarian query bool false "Filter by vegetarian"
// @Param is_vegan query bool false "Filter by vegan"
// @Param is_gluten_free query bool false "Filter by gluten free"
// @Param is_spicy query bool false "Filter by spicy"
// @Param min_price query number false "Minimum price filter"
// @Param max_price query number false "Maximum price filter"
// @Param search query string false "Search term"
// @Param page query int false "Page number"
// @Param limit query int false "Items per page"
// @Success 200 {object} types.MenuItemsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/slug/{shopSlug}/branches/slug/{branchSlug}/menu/items [get]
func (h *MenuHandler) GetMenuItemsBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")         // From /shops/slug/:slug
	branchSlug := c.Param("branchSlug") // From /branches/slug/:branchSlug

	h.logger.Info("GetMenuItemsBySlug called with params: ", "shopSlug", shopSlug, "branchSlug", branchSlug)

	if shopSlug == "" || branchSlug == "" {
		h.logger.Error("Missing required parameters: ", "shopSlug", shopSlug, "branchSlug", branchSlug)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug and branch slug are required"})
		return
	}

	// Manually parse query parameters to avoid binding issues
	var filters types.MenuItemFilters

	// Parse pagination
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			filters.Page = page
		}
	}
	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 && limit <= 100 {
			filters.Limit = limit
		}
	}

	// Parse sorting
	filters.SortBy = c.Query("sort_by")
	filters.SortOrder = c.Query("sort_order")

	// Parse search
	filters.Search = c.Query("search")

	// Apply defaults
	filters.ApplyDefaults()

	h.logger.Info("Parsed filters: ", "filters", filters)

	items, err := h.menuService.GetMenuItemsBySlug(c.Request.Context(), shopSlug, branchSlug, filters)
	if err != nil {
		h.logger.Error("Failed to get menu items by slug: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get menu items"})
		return
	}

	c.JSON(http.StatusOK, items)
}

// CreateMenuItem godoc
// @Summary Create a new menu item
// @Description Create a new menu item for a branch
// @Tags menu-items
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param item body types.CreateMenuItemRequest true "Menu item data"
// @Success 201 {object} types.MenuItemResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/menu/items [post]
func (h *MenuHandler) CreateMenuItem(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var req types.CreateMenuItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	item, err := h.menuService.CreateMenuItem(c.Request.Context(), branchID, req)
	if err != nil {
		h.logger.Error("Failed to create menu item: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create menu item"})
		return
	}

	c.JSON(http.StatusCreated, item)
}

// UpdateMenuItem godoc
// @Summary Update a menu item
// @Description Update a specific menu item by ID
// @Tags menu-items
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param itemId path string true "Menu Item ID"
// @Param item body types.UpdateMenuItemRequest true "Menu item update data"
// @Success 200 {object} types.MenuItemResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/menu/items/{itemId} [put]
func (h *MenuHandler) UpdateMenuItem(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	itemID, err := uuid.Parse(c.Param("itemId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid item ID"})
		return
	}

	var req types.UpdateMenuItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	item, err := h.menuService.UpdateMenuItem(c.Request.Context(), branchID, itemID, req)
	if err != nil {
		h.logger.Error("Failed to update menu item: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update menu item"})
		return
	}

	c.JSON(http.StatusOK, item)
}

// DeleteMenuItem godoc
// @Summary Delete a menu item
// @Description Delete a specific menu item by ID
// @Tags menu-items
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param itemId path string true "Menu Item ID"
// @Success 204
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/menu/items/{itemId} [delete]
func (h *MenuHandler) DeleteMenuItem(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	itemID, err := uuid.Parse(c.Param("itemId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid item ID"})
		return
	}

	if err := h.menuService.DeleteMenuItem(c.Request.Context(), branchID, itemID); err != nil {
		h.logger.Error("Failed to delete menu item: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete menu item"})
		return
	}

	c.Status(http.StatusNoContent)
}

// ToggleAvailability godoc
// @Summary Toggle menu item availability
// @Description Toggle the availability status of a specific menu item
// @Tags menu-items
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param itemId path string true "Menu Item ID"
// @Param availability body types.ToggleAvailabilityRequest true "Availability data"
// @Success 200 {object} types.MenuItemResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/menu/items/{itemId}/availability [patch]
func (h *MenuHandler) ToggleAvailability(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	itemID, err := uuid.Parse(c.Param("itemId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid item ID"})
		return
	}

	var req types.ToggleAvailabilityRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	item, err := h.menuService.ToggleAvailability(c.Request.Context(), branchID, itemID, req)
	if err != nil {
		h.logger.Error("Failed to toggle menu item availability: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to toggle menu item availability"})
		return
	}

	c.JSON(http.StatusOK, item)
}

// UploadMenuItemImage godoc
// @Summary Upload menu item image
// @Description Upload an image for a specific menu item
// @Tags menu-items
// @Accept multipart/form-data
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param itemId path string true "Menu Item ID"
// @Param image formData file true "Image file"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/menu/items/{itemId}/image [post]
func (h *MenuHandler) UploadMenuItemImage(c *gin.Context) {
	// Try both parameter names for backward compatibility
	shopID := c.Param("shopId")
	if shopID == "" {
		shopID = c.Param("merchantId")
	}
	branchID := c.Param("branchId")
	itemID := c.Param("itemId")

	// Log the received parameters for debugging
	h.logger.WithFields(logrus.Fields{
		"shopID":   shopID,
		"branchID": branchID,
		"itemID":   itemID,
	}).Info("UploadMenuItemImage: Received parameters")

	// Validate UUIDs
	if _, err := uuid.Parse(shopID); err != nil {
		h.logger.WithError(err).WithField("shopID", shopID).Error("Invalid shop ID format")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}
	if _, err := uuid.Parse(branchID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}
	if _, err := uuid.Parse(itemID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid item ID"})
		return
	}

	// Get the uploaded file
	file, header, err := c.Request.FormFile("image")
	if err != nil {
		h.logger.WithError(err).Error("Failed to get form file")
		c.JSON(http.StatusBadRequest, gin.H{"error": "No image file provided"})
		return
	}
	defer file.Close()

	// Validate file type - only allow specific image types
	contentType := header.Header.Get("Content-Type")
	allowedTypes := []string{"image/jpeg", "image/png", "image/webp"}
	isValidType := false
	for _, allowedType := range allowedTypes {
		if contentType == allowedType {
			isValidType = true
			break
		}
	}
	if !isValidType {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File must be a JPEG, PNG, or WebP image"})
		return
	}

	// Validate file size (5MB max)
	if header.Size > 5*1024*1024 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File size must be less than 5MB"})
		return
	}

	// Upload to Google Cloud Storage
	imageURL, err := h.storageService.UploadMenuItemImage(c.Request.Context(), file, header, shopID, branchID, itemID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to upload menu item image")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to upload image"})
		return
	}

	// Update menu item with new image URL
	itemUUID, _ := uuid.Parse(itemID)
	branchUUID, _ := uuid.Parse(branchID)

	// Get current menu item to preserve existing images
	currentItem, err := h.menuService.GetMenuItemByID(c.Request.Context(), branchUUID, itemUUID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get menu item for image update")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update menu item"})
		return
	}

	// Add new image to the images array
	updatedImages := currentItem.Images
	if updatedImages == nil {
		updatedImages = []string{}
	}
	updatedImages = append(updatedImages, imageURL)

	// Update menu item with new images
	updateReq := types.UpdateMenuItemRequest{
		Images: updatedImages,
	}

	_, err = h.menuService.UpdateMenuItem(c.Request.Context(), branchUUID, itemUUID, updateReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update menu item with new image")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update menu item"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"imageUrl": imageURL,
		"message":  "Image uploaded successfully",
	})
}

// GetPopularItems godoc
// @Summary Get popular menu items
// @Description Get popular menu items for a branch with filtering, sorting, and pagination
// @Tags menu-items
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param category_id query string false "Filter by category ID"
// @Param min_price query number false "Minimum price filter"
// @Param max_price query number false "Maximum price filter"
// @Param is_available query bool false "Filter by availability"
// @Param is_vegetarian query bool false "Filter by vegetarian items"
// @Param is_vegan query bool false "Filter by vegan items"
// @Param search query string false "Search term"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param sort_by query string false "Sort field" default("order_count")
// @Param sort_order query string false "Sort order (asc, desc)" default("desc")
// @Success 200 {object} types.MenuItemsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/reports/popular-items [get]
func (h *MenuHandler) GetPopularItems(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.MenuItemFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	// Apply defaults first
	filters.ApplyDefaults()

	// Set default limit to 10 for popular items if not specified
	if filters.Limit == 0 {
		filters.Limit = 10
	}

	// Set default sort to order_count descending for popular items
	if filters.SortBy == "" {
		filters.SortBy = "order_count"
		filters.SortOrder = "desc"
	}

	items, err := h.menuService.GetMenuItems(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.Error("Failed to get popular items: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get popular items"})
		return
	}

	c.JSON(http.StatusOK, items)
}
