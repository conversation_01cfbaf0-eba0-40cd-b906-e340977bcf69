package handlers

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// HealthHandler handles health check endpoints
type HealthHandler struct {
	db     *gorm.DB
	logger *logrus.Logger
}

// NewHealthHandler creates a new health handler
func NewHealthHandler(db *gorm.DB, logger *logrus.Logger) *HealthHandler {
	return &HealthHandler{
		db:     db,
		logger: logger,
	}
}

// Health godoc
// @Summary Health check
// @Description Check if the service is running
// @Tags health
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /health [get]
func (h *HealthHandler) Health(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "ok",
		"timestamp": time.Now().UTC(),
		"service":   "restaurant-api",
		"version":   "1.0.0",
	})
}

// Ready godoc
// @Summary Readiness check
// @Description Check if the service is ready to serve requests
// @Tags health
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 503 {object} map[string]interface{}
// @Router /ready [get]
func (h *HealthHandler) Ready(c *gin.Context) {
	// Check database connection
	sqlDB, err := h.db.DB()
	if err != nil {
		h.logger.Error("Failed to get database connection: ", err)
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"status":  "not ready",
			"error":   "database connection failed",
			"details": err.Error(),
		})
		return
	}

	if err := sqlDB.Ping(); err != nil {
		h.logger.Error("Database ping failed: ", err)
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"status":  "not ready",
			"error":   "database ping failed",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":    "ready",
		"timestamp": time.Now().UTC(),
		"checks": gin.H{
			"database": "ok",
		},
	})
}
