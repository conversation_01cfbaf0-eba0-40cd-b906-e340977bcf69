package handlers

import (
	"net/http"
	"strings"
	"time"

	"restaurant-backend/internal/services"
	"restaurant-backend/internal/types"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type ReservationHandler struct {
	reservationService *services.ReservationService
	shopService        *services.ShopService
	logger             *logrus.Logger
}

func NewReservationHandler(reservationService *services.ReservationService, shopService *services.ShopService, logger *logrus.Logger) *ReservationHandler {
	return &ReservationHandler{
		reservationService: reservationService,
		shopService:        shopService,
		logger:             logger,
	}
}

// GetReservations retrieves reservations for a branch
// @Summary Get reservations
// @Description Get reservations for a specific branch with optional filters
// @Tags reservations
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param status query string false "Filter by status"
// @Param date query string false "Filter by date (YYYY-MM-DD)"
// @Param start_date query string false "Filter by start date (YYYY-MM-DD)"
// @Param end_date query string false "Filter by end date (YYYY-MM-DD)"
// @Param table_id query string false "Filter by table ID"
// @Param search query string false "Search by customer name, phone, or email"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.ReservationsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/{shopId}/branches/{branchId}/reservations [get]
func (h *ReservationHandler) GetReservations(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.ReservationFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	reservations, err := h.reservationService.GetReservations(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get reservations")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get reservations"})
		return
	}

	c.JSON(http.StatusOK, reservations)
}

// GetReservation retrieves a single reservation
// @Summary Get reservation
// @Description Get a specific reservation by ID
// @Tags reservations
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param reservationId path string true "Reservation ID"
// @Success 200 {object} types.ReservationResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/{shopId}/branches/{branchId}/reservations/{reservationId} [get]
func (h *ReservationHandler) GetReservation(c *gin.Context) {
	reservationID, err := uuid.Parse(c.Param("reservationId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid reservation ID"})
		return
	}

	reservation, err := h.reservationService.GetReservation(c.Request.Context(), reservationID)
	if err != nil {
		if err.Error() == "reservation not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Reservation not found"})
			return
		}
		h.logger.WithError(err).Error("Failed to get reservation")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get reservation"})
		return
	}

	c.JSON(http.StatusOK, reservation)
}

// CreateReservation creates a new reservation
// @Summary Create reservation
// @Description Create a new reservation
// @Tags reservations
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param reservation body types.CreateReservationRequest true "Reservation data"
// @Success 201 {object} types.ReservationResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/{shopId}/branches/{branchId}/reservations [post]
func (h *ReservationHandler) CreateReservation(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var req types.CreateReservationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	reservation, err := h.reservationService.CreateReservation(c.Request.Context(), branchID, req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create reservation")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create reservation"})
		return
	}

	c.JSON(http.StatusCreated, reservation)
}

// UpdateReservation updates an existing reservation
// @Summary Update reservation
// @Description Update an existing reservation
// @Tags reservations
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param reservationId path string true "Reservation ID"
// @Param reservation body types.UpdateReservationRequest true "Reservation data"
// @Success 200 {object} types.ReservationResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/{shopId}/branches/{branchId}/reservations/{reservationId} [put]
func (h *ReservationHandler) UpdateReservation(c *gin.Context) {
	reservationID, err := uuid.Parse(c.Param("reservationId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid reservation ID"})
		return
	}

	var req types.UpdateReservationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	reservation, err := h.reservationService.UpdateReservation(c.Request.Context(), reservationID, req)
	if err != nil {
		if err.Error() == "reservation not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Reservation not found"})
			return
		}
		h.logger.WithError(err).Error("Failed to update reservation")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update reservation"})
		return
	}

	c.JSON(http.StatusOK, reservation)
}

// GetTodayReservations retrieves today's reservations
// @Summary Get today's reservations
// @Description Get all reservations for today for a specific branch
// @Tags reservations
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Success 200 {array} types.ReservationResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/{shopId}/branches/{branchId}/reservations/today [get]
func (h *ReservationHandler) GetTodayReservations(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	reservations, err := h.reservationService.GetTodayReservations(c.Request.Context(), branchID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get today's reservations")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get today's reservations"})
		return
	}

	c.JSON(http.StatusOK, reservations)
}

// GetAvailability retrieves available time slots
// @Summary Get availability
// @Description Get available time slots for a specific date
// @Tags reservations
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param date query string true "Date (YYYY-MM-DD)"
// @Success 200 {object} types.AvailabilityResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/{shopId}/branches/{branchId}/reservations/availability [get]
func (h *ReservationHandler) GetAvailability(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	dateStr := c.Query("date")
	if dateStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Date parameter is required"})
		return
	}

	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid date format. Use YYYY-MM-DD"})
		return
	}

	availability, err := h.reservationService.GetAvailability(c.Request.Context(), branchID, date)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get availability")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get availability"})
		return
	}

	c.JSON(http.StatusOK, availability)
}

// GetReservationStats retrieves reservation statistics
// @Summary Get reservation statistics
// @Description Get reservation statistics for a specific branch
// @Tags reservations
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param period query string false "Time period (7d, 30d, 90d, 1y)" default("30d")
// @Success 200 {object} types.ReservationStats
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/{shopId}/branches/{branchId}/reservations/stats [get]
func (h *ReservationHandler) GetReservationStats(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	period := c.DefaultQuery("period", "30d")

	stats, err := h.reservationService.GetReservationStats(c.Request.Context(), branchID, period)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get reservation stats")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get reservation stats"})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// Slug-based handlers

// GetReservationsBySlug retrieves reservations by shop and branch slug
func (h *ReservationHandler) GetReservationsBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	// Get shop by slug
	shop, err := h.shopService.GetShopBySlug(c.Request.Context(), shopSlug)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get shop by slug")
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	// Find branch by slug
	var branchID uuid.UUID
	for _, branch := range shop.Branches {
		if branch.Slug == branchSlug {
			branchID = branch.ID
			break
		}
	}

	if branchID == uuid.Nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	// Use existing GetReservations logic
	var filters types.ReservationFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	reservations, err := h.reservationService.GetReservations(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get reservations")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get reservations"})
		return
	}

	c.JSON(http.StatusOK, reservations)
}

// GetReservationStatsBySlug retrieves reservation statistics by shop and branch slug
func (h *ReservationHandler) GetReservationStatsBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	// Get shop by slug
	shop, err := h.shopService.GetShopBySlug(c.Request.Context(), shopSlug)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get shop by slug")
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	// Find branch by slug
	var branchID uuid.UUID
	for _, branch := range shop.Branches {
		if branch.Slug == branchSlug {
			branchID = branch.ID
			break
		}
	}

	if branchID == uuid.Nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	period := c.DefaultQuery("period", "30d")

	stats, err := h.reservationService.GetReservationStats(c.Request.Context(), branchID, period)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get reservation stats")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get reservation stats"})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetTodayReservationsBySlug retrieves today's reservations by shop and branch slug
func (h *ReservationHandler) GetTodayReservationsBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	// Get shop by slug
	shop, err := h.shopService.GetShopBySlug(c.Request.Context(), shopSlug)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get shop by slug")
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	// Find branch by slug
	var branchID uuid.UUID
	for _, branch := range shop.Branches {
		if branch.Slug == branchSlug {
			branchID = branch.ID
			break
		}
	}

	if branchID == uuid.Nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	reservations, err := h.reservationService.GetTodayReservations(c.Request.Context(), branchID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get today's reservations")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get today's reservations"})
		return
	}

	c.JSON(http.StatusOK, reservations)
}

// Placeholder methods for other reservation operations
func (h *ReservationHandler) CancelReservation(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Cancel reservation - to be implemented"})
}

// CreateReservationBySlug creates a new reservation using shop and branch slugs
// @Summary Create reservation by slug
// @Description Create a new reservation using shop and branch slugs
// @Tags reservations
// @Accept json
// @Produce json
// @Param slug path string true "Shop Slug"
// @Param branchSlug path string true "Branch Slug"
// @Param reservation body types.CreateReservationRequest true "Reservation data"
// @Success 201 {object} types.ReservationResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/slug/{slug}/branches/slug/{branchSlug}/reservations [post]
func (h *ReservationHandler) CreateReservationBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	// Get shop by slug
	shop, err := h.shopService.GetShopBySlug(c.Request.Context(), shopSlug)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get shop by slug")
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	// Find branch by slug
	var branchID uuid.UUID
	for _, branch := range shop.Branches {
		if branch.Slug == branchSlug {
			branchID = branch.ID
			break
		}
	}

	if branchID == uuid.Nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	var req types.CreateReservationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	reservation, err := h.reservationService.CreateReservation(c.Request.Context(), branchID, req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create reservation")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create reservation"})
		return
	}

	c.JSON(http.StatusCreated, reservation)
}

func (h *ReservationHandler) GetReservationBySlug(c *gin.Context) {
	reservationSlug := c.Param("reservationSlug")

	reservation, err := h.reservationService.GetReservationBySlug(c.Request.Context(), reservationSlug)
	if err != nil {
		if err.Error() == "reservation not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Reservation not found"})
			return
		}
		h.logger.WithError(err).Error("Failed to get reservation by slug")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get reservation"})
		return
	}

	c.JSON(http.StatusOK, reservation)
}

func (h *ReservationHandler) UpdateReservationBySlug(c *gin.Context) {
	reservationSlug := c.Param("reservationSlug")

	var req types.UpdateReservationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	reservation, err := h.reservationService.UpdateReservationBySlug(c.Request.Context(), reservationSlug, req)
	if err != nil {
		if err.Error() == "reservation not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Reservation not found"})
			return
		}
		h.logger.WithError(err).Error("Failed to update reservation by slug")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update reservation"})
		return
	}

	c.JSON(http.StatusOK, reservation)
}

func (h *ReservationHandler) CancelReservationBySlug(c *gin.Context) {
	reservationSlug := c.Param("reservationSlug")
	if reservationSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Reservation slug is required"})
		return
	}

	// Parse request body for cancellation reason (optional)
	var req struct {
		Reason string `json:"reason"`
	}
	// Don't return error if body is empty, reason is optional
	_ = c.ShouldBindJSON(&req)

	reservation, err := h.reservationService.CancelReservationBySlug(c.Request.Context(), reservationSlug, req.Reason)
	if err != nil {
		if err.Error() == "reservation not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Reservation not found"})
			return
		}
		if strings.Contains(err.Error(), "cannot be cancelled") {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		h.logger.WithError(err).Error("Failed to cancel reservation by slug")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cancel reservation"})
		return
	}

	c.JSON(http.StatusOK, reservation)
}

func (h *ReservationHandler) CheckInReservationBySlug(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Check in reservation by slug - to be implemented"})
}

func (h *ReservationHandler) MarkNoShowBySlug(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Mark no show by slug - to be implemented"})
}

func (h *ReservationHandler) GetAvailabilityBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	dateStr := c.Query("date")
	if dateStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Date parameter is required"})
		return
	}

	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid date format. Use YYYY-MM-DD"})
		return
	}

	availability, err := h.reservationService.GetAvailabilityBySlug(c.Request.Context(), shopSlug, branchSlug, date)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get availability by slug")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get availability"})
		return
	}

	c.JSON(http.StatusOK, availability)
}

func (h *ReservationHandler) CheckInReservation(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Check-in reservation - to be implemented"})
}

func (h *ReservationHandler) MarkNoShow(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Mark no-show - to be implemented"})
}
