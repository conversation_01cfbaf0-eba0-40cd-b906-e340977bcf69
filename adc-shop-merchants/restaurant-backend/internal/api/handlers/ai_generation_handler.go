package handlers

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/queue"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/services"
	"restaurant-backend/internal/types"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type AIGenerationHandler struct {
	aiService      *services.AIGenerationService
	storageService *services.StorageService
	shopService    *services.ShopService
	userRepo       repositories.UserRepository
	shopRepo       *repositories.ShopRepository
	roleRepo       repositories.RoleRepository
	aiQueue        *queue.AIJobQueue
	logger         *logrus.Logger
}

func NewAIGenerationHandler(
	aiService *services.AIGenerationService,
	storageService *services.StorageService,
	shopService *services.ShopService,
	userRepo repositories.UserRepository,
	shopRepo *repositories.ShopRepository,
	roleRepo repositories.RoleRepository,
	aiQueue *queue.AIJobQueue,
	logger *logrus.Logger,
) *AIGenerationHandler {
	return &AIGenerationHandler{
		aiService:      aiService,
		storageService: storageService,
		shopService:    shopService,
		userRepo:       userRepo,
		shopRepo:       shopRepo,
		roleRepo:       roleRepo,
		aiQueue:        aiQueue,
		logger:         logger,
	}
}

// CreateJob godoc
// @Summary Create a new AI generation job
// @Description Create a new AI generation job for menu items
// @Tags ai-generation
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param job body types.CreateAIGenerationJobRequest true "AI generation job data"
// @Success 201 {object} types.AIGenerationJobResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/ai-generation/jobs [post]
func (h *AIGenerationHandler) CreateJob(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Get user ID from context - handle both string and UUID formats
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		h.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	h.logger.WithField("userIDInterface", userIDInterface).Info("Retrieved user ID from context")

	var userID uuid.UUID

	// Handle both string (NextAuth) and UUID (legacy JWT) formats
	if userIDStr, ok := userIDInterface.(string); ok {
		h.logger.WithField("userIDStr", userIDStr).Info("Processing string user ID")
		// For NextAuth, user ID might be a string (OAuth provider ID)
		// Try to parse as UUID first, if it fails, create a deterministic UUID from the string
		if parsedUUID, err := uuid.Parse(userIDStr); err == nil {
			userID = parsedUUID
			h.logger.WithField("userID", userID).Info("Successfully parsed user ID as UUID")
		} else {
			// For OAuth provider IDs (like Google), create a deterministic UUID
			// This is a simple approach - in production you might want a more sophisticated mapping
			userID = uuid.NewSHA1(uuid.NameSpaceOID, []byte(userIDStr))
			h.logger.WithFields(logrus.Fields{
				"originalUserID": userIDStr,
				"generatedUUID":  userID,
			}).Info("Generated deterministic UUID from OAuth user ID")

			// Ensure the OAuth user exists in the database before proceeding
			if err := h.ensureOAuthUserExists(c.Request.Context(), userIDStr, userID); err != nil {
				h.logger.WithError(err).Error("Failed to ensure OAuth user exists")
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to validate user"})
				return
			}
		}
	} else if userUUID, ok := userIDInterface.(uuid.UUID); ok {
		userID = userUUID
		h.logger.WithField("userID", userID).Info("Using existing UUID user ID")
	} else {
		h.logger.WithField("userIDType", fmt.Sprintf("%T", userIDInterface)).Error("Invalid user ID format")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID format"})
		return
	}

	var req types.CreateAIGenerationJobRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data", "details": err.Error()})
		return
	}

	job, err := h.aiService.CreateJob(c.Request.Context(), branchID, userID, req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create AI generation job")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create job"})
		return
	}

	// Enqueue the job for processing if queue is available
	if h.aiQueue != nil {
		priority := 2 // Normal priority
		if req.Type == "food_images" {
			priority = 1 // Higher priority for food images
		}

		if err := h.aiQueue.EnqueueJob(c.Request.Context(), job.ID, req.Type, priority); err != nil {
			h.logger.WithError(err).Error("Failed to enqueue AI generation job")
			// Don't fail the request, job is created and can be processed manually
		}
	}

	c.JSON(http.StatusCreated, job)
}

// GetJob godoc
// @Summary Get AI generation job by ID
// @Description Retrieve an AI generation job by its ID
// @Tags ai-generation
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param jobId path string true "Job ID"
// @Success 200 {object} types.AIGenerationJobResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/ai-generation/jobs/{jobId} [get]
func (h *AIGenerationHandler) GetJob(c *gin.Context) {
	jobID, err := uuid.Parse(c.Param("jobId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid job ID"})
		return
	}

	job, err := h.aiService.GetJob(c.Request.Context(), jobID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get AI generation job")
		c.JSON(http.StatusNotFound, gin.H{"error": "Job not found"})
		return
	}

	c.JSON(http.StatusOK, job)
}

// GetJobs godoc
// @Summary Get AI generation jobs for a branch
// @Description Retrieve AI generation jobs for a specific branch with filters
// @Tags ai-generation
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param type query string false "Job type filter"
// @Param status query string false "Job status filter"
// @Param page query int false "Page number"
// @Param limit query int false "Items per page"
// @Success 200 {object} types.AIGenerationJobsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/ai-generation/jobs [get]
func (h *AIGenerationHandler) GetJobs(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.AIGenerationJobFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	jobs, err := h.aiService.GetJobsByBranch(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get AI generation jobs")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get jobs"})
		return
	}

	c.JSON(http.StatusOK, jobs)
}

// PublishMenu godoc
// @Summary Publish AI generated menu
// @Description Publish AI generated menu items to the actual menu
// @Tags ai-generation
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param request body types.PublishAIGeneratedMenuRequest true "Publish menu request"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/ai-generation/publish [post]
func (h *AIGenerationHandler) PublishMenu(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var req types.PublishAIGeneratedMenuRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data", "details": err.Error()})
		return
	}

	err = h.aiService.PublishGeneratedMenu(c.Request.Context(), branchID, req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to publish AI generated menu")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to publish menu"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Menu published successfully"})
}

// UploadFile godoc
// @Summary Upload file for AI generation
// @Description Upload an image file for AI menu generation
// @Tags ai-generation
// @Accept multipart/form-data
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param type formData string true "File type (menu_image or food_image)"
// @Param file formData file true "Image file"
// @Success 200 {object} types.UploadAIFileResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/ai-generation/upload [post]
func (h *AIGenerationHandler) UploadFile(c *gin.Context) {
	shopID := c.Param("shopId")
	branchID := c.Param("branchId")

	// Get file type from form
	fileType := c.PostForm("type")
	if fileType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File type is required"})
		return
	}

	if fileType != "menu_image" && fileType != "food_image" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file type. Must be 'menu_image' or 'food_image'"})
		return
	}

	// Get uploaded file
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No file uploaded"})
		return
	}
	defer file.Close()

	// Upload to storage
	var fileURL string
	folder := fmt.Sprintf("ai-generation/%s/%s", shopID, branchID)
	fileURL, err = h.storageService.UploadFile(c.Request.Context(), file, header, folder)
	if err != nil {
		h.logger.WithError(err).Error("Failed to upload file")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to upload file"})
		return
	}

	response := types.UploadAIFileResponse{
		FileURL: fileURL,
		Message: "File uploaded successfully",
	}

	c.JSON(http.StatusOK, response)
}

// Slug-based handlers

// CreateJobBySlug creates a new AI generation job using shop and branch slugs
func (h *AIGenerationHandler) CreateJobBySlug(c *gin.Context) {
	h.logger.Info("CreateJobBySlug called")
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")
	h.logger.WithFields(logrus.Fields{
		"shopSlug":   shopSlug,
		"branchSlug": branchSlug,
	}).Info("Processing AI generation job creation by slug")

	// Get shop and branch IDs from slugs
	_, branchID, err := h.getShopAndBranchIDsFromSlugs(c, shopSlug, branchSlug)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get shop and branch IDs from slugs")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop or branch"})
		return
	}

	h.logger.WithField("branchID", branchID).Info("Successfully resolved branch ID from slug")

	// Set the branch ID in the context for the main handler
	c.Params = append(c.Params, gin.Param{Key: "branchId", Value: branchID.String()})

	// Call the main handler
	h.CreateJob(c)
}

// GetJobsBySlug retrieves AI generation jobs for a branch using shop and branch slugs
func (h *AIGenerationHandler) GetJobsBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	// Get shop and branch IDs from slugs
	_, branchID, err := h.getShopAndBranchIDsFromSlugs(c, shopSlug, branchSlug)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop or branch"})
		return
	}

	// Set the branch ID in the context for the main handler
	c.Params = append(c.Params, gin.Param{Key: "branchId", Value: branchID.String()})

	// Call the main handler
	h.GetJobs(c)
}

// GetJobBySlug retrieves an AI generation job by ID using shop and branch slugs
func (h *AIGenerationHandler) GetJobBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	// Get shop and branch IDs from slugs
	_, branchID, err := h.getShopAndBranchIDsFromSlugs(c, shopSlug, branchSlug)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop or branch"})
		return
	}

	// Set the branch ID in the context for the main handler
	c.Params = append(c.Params, gin.Param{Key: "branchId", Value: branchID.String()})

	// Call the main handler
	h.GetJob(c)
}

// PublishMenuBySlug publishes AI generated menu using shop and branch slugs
func (h *AIGenerationHandler) PublishMenuBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	// Get shop and branch IDs from slugs
	_, branchID, err := h.getShopAndBranchIDsFromSlugs(c, shopSlug, branchSlug)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop or branch"})
		return
	}

	// Set the branch ID in the context for the main handler
	c.Params = append(c.Params, gin.Param{Key: "branchId", Value: branchID.String()})

	// Call the main handler
	h.PublishMenu(c)
}

// UploadFileBySlug uploads file for AI generation using shop and branch slugs
func (h *AIGenerationHandler) UploadFileBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	// Get shop and branch IDs from slugs (we don't actually need the IDs for upload, but validate them)
	_, _, err := h.getShopAndBranchIDsFromSlugs(c, shopSlug, branchSlug)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop or branch"})
		return
	}

	// Set the shop and branch slugs in the context for the main handler
	c.Params = append(c.Params, gin.Param{Key: "shopId", Value: shopSlug})
	c.Params = append(c.Params, gin.Param{Key: "branchId", Value: branchSlug})

	// Call the main handler
	h.UploadFile(c)
}

// Helper method to get shop and branch IDs from slugs
func (h *AIGenerationHandler) getShopAndBranchIDsFromSlugs(c *gin.Context, shopSlug, branchSlug string) (uuid.UUID, uuid.UUID, error) {
	// Get branch by slug using the shop service
	branch, err := h.shopService.GetBranchBySlug(c.Request.Context(), shopSlug, branchSlug)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"shopSlug":   shopSlug,
			"branchSlug": branchSlug,
		}).Error("Failed to get branch by slug")
		return uuid.Nil, uuid.Nil, fmt.Errorf("branch not found")
	}

	// The IDs are already UUIDs, no need to parse
	return branch.Shop.ID, branch.ID, nil
}

// ensureOAuthUserExists ensures that an OAuth user exists in the database
func (h *AIGenerationHandler) ensureOAuthUserExists(ctx context.Context, oauthUserID string, userID uuid.UUID) error {
	// Check if user already exists
	_, err := h.userRepo.GetByID(ctx, userID)
	if err == nil {
		// User exists, no need to create
		return nil
	}

	// User doesn't exist, create them
	// Get default shop and branch for OAuth users
	shop, err := h.shopRepo.GetShopBySlug(ctx, "weerawat-poseeya")
	if err != nil {
		return fmt.Errorf("failed to get default shop: %w", err)
	}

	branch, err := h.shopRepo.GetBranchBySlug(ctx, "weerawat-poseeya", "the-green-terrace")
	if err != nil {
		return fmt.Errorf("failed to get default branch: %w", err)
	}

	// Get default role for OAuth users - find any suitable role by shop ID
	roles, err := h.roleRepo.GetByShopID(ctx, shop.ID)
	if err != nil {
		return fmt.Errorf("failed to get roles: %w", err)
	}

	h.logger.WithFields(logrus.Fields{
		"shopID":     shop.ID,
		"rolesCount": len(roles),
	}).Info("Retrieved roles for OAuth user creation")

	// If no roles exist, create default roles for the shop
	if len(roles) == 0 {
		h.logger.WithField("shopID", shop.ID).Info("No roles found for shop, creating default roles")
		if err := h.createDefaultRoles(ctx, shop.ID); err != nil {
			return fmt.Errorf("failed to create default roles: %w", err)
		}

		// Retry getting roles after creation
		roles, err = h.roleRepo.GetByShopID(ctx, shop.ID)
		if err != nil {
			return fmt.Errorf("failed to get roles after creation: %w", err)
		}

		h.logger.WithFields(logrus.Fields{
			"shopID":     shop.ID,
			"rolesCount": len(roles),
		}).Info("Retrieved roles after default role creation")
	}

	var role *models.Role
	// Try to find "user" role first
	for _, r := range roles {
		if r.Name == "user" {
			role = &r
			break
		}
	}

	// If "user" role not found, try other common role names
	if role == nil {
		for _, r := range roles {
			if r.Name == "staff" || r.Name == "employee" || r.Name == "member" || r.Name == "Owner/Manager" || r.Name == "Admin" {
				role = &r
				break
			}
		}
	}

	// If still no role found, use the first available role
	if role == nil && len(roles) > 0 {
		role = &roles[0]
		h.logger.WithFields(logrus.Fields{
			"selectedRole": role.Name,
			"shopID":       shop.ID,
		}).Info("Using first available role for OAuth user")
	}

	if role == nil {
		return fmt.Errorf("no roles found for shop %s", shop.ID)
	}

	// Create the OAuth user
	user := &models.User{
		BaseModel: models.BaseModel{ID: userID},
		ShopID:    shop.ID,
		BranchID:  &branch.ID,
		Email:     fmt.Sprintf("<EMAIL>", oauthUserID), // Placeholder email
		FirstName: "OAuth",
		LastName:  "User",
		RoleID:    role.ID,
		Position:  "OAuth User",
		Status:    "active",
		HireDate:  time.Now(),
	}

	// Set a default password (won't be used for OAuth login)
	if err := user.SetPassword("oauth-default-password"); err != nil {
		return fmt.Errorf("failed to set password: %w", err)
	}

	// Create the user in the database
	if err := h.userRepo.Create(ctx, user); err != nil {
		return fmt.Errorf("failed to create OAuth user: %w", err)
	}

	h.logger.WithFields(logrus.Fields{
		"oauthUserID": oauthUserID,
		"userID":      userID,
		"email":       user.Email,
	}).Info("Created OAuth user")

	return nil
}

// createDefaultRoles creates default roles for a shop
func (h *AIGenerationHandler) createDefaultRoles(ctx context.Context, shopID uuid.UUID) error {
	// Using predefined UUIDs for consistency
	ownerRoleID := uuid.MustParse("550e8400-e29b-41d4-a716-************")
	assistantManagerRoleID := uuid.MustParse("550e8400-e29b-41d4-a716-************")
	serverRoleID := uuid.MustParse("550e8400-e29b-41d4-a716-************")
	kitchenStaffRoleID := uuid.MustParse("550e8400-e29b-41d4-a716-************")
	cashierRoleID := uuid.MustParse("550e8400-e29b-41d4-a716-************")
	hostRoleID := uuid.MustParse("550e8400-e29b-41d4-a716-************")
	inventoryManagerRoleID := uuid.MustParse("550e8400-e29b-41d4-a716-************")

	// Create default roles data
	defaultRolesData := []struct {
		ID          uuid.UUID
		Name        string
		Description string
		Permissions []string
	}{
		{
			ID:          ownerRoleID,
			Name:        "Owner/Manager",
			Description: "Full access to all restaurant operations and settings",
			Permissions: []string{
				"dashboard.view", "analytics.view", "analytics.export",
				"staff.view", "staff.create", "staff.edit", "staff.delete", "staff.manage_roles", "staff.manage_schedule",
				"orders.view", "orders.create", "orders.edit", "orders.cancel", "orders.refund", "orders.kitchen_display",
				"menu.view", "menu.create", "menu.edit", "menu.delete", "menu.manage_categories", "menu.manage_pricing",
				"tables.view", "tables.manage", "tables.configure",
				"reservations.view", "reservations.create", "reservations.edit", "reservations.cancel",
				"inventory.view", "inventory.manage", "inventory.reports",
				"reports.view", "reports.export", "reports.financial",
				"settings.view", "settings.edit", "settings.system",
				"ai.generate", "ai.manage",
			},
		},
		{
			ID:          assistantManagerRoleID,
			Name:        "Assistant Manager",
			Description: "Supervisory role with most operational permissions",
			Permissions: []string{
				"dashboard.view", "analytics.view",
				"staff.view", "staff.manage_schedule",
				"orders.view", "orders.create", "orders.edit", "orders.kitchen_display",
				"menu.view", "menu.edit", "menu.manage_pricing",
				"tables.view", "tables.manage",
				"reservations.view", "reservations.create", "reservations.edit",
				"inventory.view", "inventory.manage",
				"reports.view",
				"ai.generate",
			},
		},
		{
			ID:          serverRoleID,
			Name:        "Server/Waiter",
			Description: "Front-of-house staff serving customers",
			Permissions: []string{
				"dashboard.view",
				"orders.view", "orders.create", "orders.edit",
				"menu.view",
				"tables.view",
				"reservations.view", "reservations.create",
			},
		},
		{
			ID:          kitchenStaffRoleID,
			Name:        "Kitchen Staff",
			Description: "Back-of-house staff preparing food",
			Permissions: []string{
				"dashboard.view",
				"orders.view", "orders.kitchen_display",
				"menu.view",
				"inventory.view",
			},
		},
		{
			ID:          cashierRoleID,
			Name:        "Cashier",
			Description: "Staff handling payments and transactions",
			Permissions: []string{
				"dashboard.view",
				"orders.view", "orders.create", "orders.edit",
				"menu.view",
				"tables.view",
			},
		},
		{
			ID:          hostRoleID,
			Name:        "Host/Hostess",
			Description: "Staff managing customer seating and reservations",
			Permissions: []string{
				"dashboard.view",
				"tables.view", "tables.manage",
				"reservations.view", "reservations.create", "reservations.edit",
			},
		},
		{
			ID:          inventoryManagerRoleID,
			Name:        "Inventory Manager",
			Description: "Staff managing inventory and supplies",
			Permissions: []string{
				"dashboard.view",
				"inventory.view", "inventory.manage", "inventory.reports",
				"menu.view",
				"reports.view",
			},
		},
	}

	// Create roles in database
	for _, roleData := range defaultRolesData {
		// First check if role already exists with this ID
		_, err := h.roleRepo.GetByID(ctx, roleData.ID)
		if err == nil {
			// Role already exists, skip
			continue
		}

		// Create new role with predefined ID
		role := &models.Role{
			ShopID:      shopID,
			Name:        roleData.Name,
			Description: roleData.Description,
			Permissions: roleData.Permissions,
			IsActive:    true,
		}

		// Set the ID manually before creation
		role.ID = roleData.ID

		// Try to create the role
		err = h.roleRepo.Create(ctx, role)
		if err != nil {
			h.logger.WithError(err).WithField("role_name", roleData.Name).Error("Failed to create default role")
			// Continue with other roles even if one fails
		} else {
			h.logger.WithFields(logrus.Fields{
				"role_name": roleData.Name,
				"role_id":   roleData.ID,
				"shop_id":   shopID,
			}).Info("Created default role")
		}
	}

	return nil
}
