package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"restaurant-backend/internal/services"
	"restaurant-backend/internal/types"
)

type FloorHandler struct {
	floorService *services.FloorService
	logger       *logrus.Logger
}

func NewFloorHandler(floorService *services.FloorService, logger *logrus.Logger) *FloorHandler {
	return &FloorHandler{
		floorService: floorService,
		logger:       logger,
	}
}

// GetFloors godoc
// @Summary Get all floors for a branch
// @Description Get all floors for a specific branch with filtering, sorting, and pagination
// @Tags floors
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param name query string false "Filter by floor name"
// @Param include_areas query bool false "Include areas in response"
// @Param is_active query bool false "Filter by active status"
// @Param search query string false "Search term"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param sort_by query string false "Sort field" default("order")
// @Param sort_order query string false "Sort order (asc, desc)" default("asc")
// @Success 200 {object} types.FloorsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/floors [get]
func (h *FloorHandler) GetFloors(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.FloorFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	floorsResponse, err := h.floorService.GetFloors(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.Error("Failed to get floors: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get floors"})
		return
	}

	c.JSON(http.StatusOK, floorsResponse)
}

// GetFloor godoc
// @Summary Get a specific floor
// @Description Get a specific floor by ID
// @Tags floors
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param floorId path string true "Floor ID"
// @Success 200 {object} types.FloorResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/floors/{floorId} [get]
func (h *FloorHandler) GetFloor(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	floorID, err := uuid.Parse(c.Param("floorId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid floor ID"})
		return
	}

	floor, err := h.floorService.GetFloor(c.Request.Context(), branchID, floorID)
	if err != nil {
		if err.Error() == "floor not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Floor not found"})
			return
		}
		h.logger.Error("Failed to get floor: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get floor"})
		return
	}

	c.JSON(http.StatusOK, floor)
}

// CreateFloor godoc
// @Summary Create a new floor
// @Description Create a new floor for a branch
// @Tags floors
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param floor body types.CreateFloorRequest true "Floor data"
// @Success 201 {object} types.FloorResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/floors [post]
func (h *FloorHandler) CreateFloor(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var req types.CreateFloorRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	floor, err := h.floorService.CreateFloor(c.Request.Context(), branchID, req)
	if err != nil {
		h.logger.Error("Failed to create floor: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create floor"})
		return
	}

	c.JSON(http.StatusCreated, floor)
}

// UpdateFloor godoc
// @Summary Update a floor
// @Description Update an existing floor
// @Tags floors
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param floorId path string true "Floor ID"
// @Param floor body types.UpdateFloorRequest true "Floor data"
// @Success 200 {object} types.FloorResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/floors/{floorId} [put]
func (h *FloorHandler) UpdateFloor(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	floorID, err := uuid.Parse(c.Param("floorId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid floor ID"})
		return
	}

	var req types.UpdateFloorRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	floor, err := h.floorService.UpdateFloor(c.Request.Context(), branchID, floorID, req)
	if err != nil {
		if err.Error() == "floor not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Floor not found"})
			return
		}
		h.logger.Error("Failed to update floor: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update floor"})
		return
	}

	c.JSON(http.StatusOK, floor)
}

// DeleteFloor godoc
// @Summary Delete a floor
// @Description Delete an existing floor
// @Tags floors
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param floorId path string true "Floor ID"
// @Success 204
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/floors/{floorId} [delete]
func (h *FloorHandler) DeleteFloor(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	floorID, err := uuid.Parse(c.Param("floorId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid floor ID"})
		return
	}

	err = h.floorService.DeleteFloor(c.Request.Context(), branchID, floorID)
	if err != nil {
		if err.Error() == "floor not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Floor not found"})
			return
		}
		h.logger.Error("Failed to delete floor: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete floor"})
		return
	}

	c.Status(http.StatusNoContent)
}
