package types

import (
	"time"

	"github.com/google/uuid"
)

// PopularItemsFilters represents filters for popular items analytics
type PopularItemsFilters struct {
	StandardPagination
	StandardSorting
	StandardDateFilters

	CategoryID   *uuid.UUID `form:"category_id" json:"category_id"`
	MinOrders    *int       `form:"min_orders" binding:"omitempty,min=1" json:"min_orders"`
	Period       string     `form:"period" binding:"oneof=today week month quarter year" json:"period"`
	IsAvailable  *bool      `form:"is_available" json:"is_available"`
	IsVegetarian *bool      `form:"is_vegetarian" json:"is_vegetarian"`
	IsVegan      *bool      `form:"is_vegan" json:"is_vegan"`
}

// CustomerAnalyticsFilters represents filters for customer analytics
type CustomerAnalyticsFilters struct {
	StandardPagination
	StandardSorting
	StandardDateFilters

	CustomerType  string     `form:"customer_type" binding:"oneof=new returning vip" json:"customer_type"`
	MinOrderValue *float64   `form:"min_order_value" binding:"omitempty,min=0" json:"min_order_value"`
	MaxOrderValue *float64   `form:"max_order_value" binding:"omitempty,min=0" json:"max_order_value"`
	MinOrderCount *int       `form:"min_order_count" binding:"omitempty,min=1" json:"min_order_count"`
	MaxOrderCount *int       `form:"max_order_count" binding:"omitempty,min=1" json:"max_order_count"`
	SegmentID     *uuid.UUID `form:"segment_id" json:"segment_id"`
	PaymentMethod string     `form:"payment_method" json:"payment_method"`
}

// StaffPerformanceFilters represents filters for staff performance analytics
type StaffPerformanceFilters struct {
	StandardPagination
	StandardSorting
	StandardDateFilters

	StaffID    *uuid.UUID `form:"staff_id" json:"staff_id"`
	Position   string     `form:"position" json:"position"`
	Department string     `form:"department" json:"department"`
	MinRating  *float64   `form:"min_rating" binding:"omitempty,min=0,max=5" json:"min_rating"`
	MaxRating  *float64   `form:"max_rating" binding:"omitempty,min=0,max=5" json:"max_rating"`
	MetricType string     `form:"metric_type" binding:"oneof=efficiency orders_served customer_rating sales" json:"metric_type"`
}

// TableUtilizationFilters represents filters for table utilization analytics
type TableUtilizationFilters struct {
	StandardPagination
	StandardSorting
	StandardDateFilters

	TableID        *uuid.UUID `form:"table_id" json:"table_id"`
	AreaID         *uuid.UUID `form:"area_id" json:"area_id"`
	MinCapacity    *int       `form:"min_capacity" binding:"omitempty,min=1" json:"min_capacity"`
	MaxCapacity    *int       `form:"max_capacity" binding:"omitempty,min=1" json:"max_capacity"`
	TimeSlot       string     `form:"time_slot" json:"time_slot"`                                     // breakfast, lunch, dinner, late_night
	DayOfWeek      *int       `form:"day_of_week" binding:"omitempty,min=0,max=6" json:"day_of_week"` // 0=Sunday, 6=Saturday
	MinUtilization *float64   `form:"min_utilization" binding:"omitempty,min=0,max=100" json:"min_utilization"`
	MaxUtilization *float64   `form:"max_utilization" binding:"omitempty,min=0,max=100" json:"max_utilization"`
}

// SalesReportFilters represents filters for sales reports
type SalesReportFilters struct {
	StandardPagination
	StandardSorting
	StandardDateFilters

	ReportType     string     `form:"report_type" binding:"oneof=daily weekly monthly yearly" json:"report_type"`
	PaymentMethod  string     `form:"payment_method" json:"payment_method"`
	OrderType      string     `form:"order_type" json:"order_type"`
	TableID        *uuid.UUID `form:"table_id" json:"table_id"`
	StaffID        *uuid.UUID `form:"staff_id" json:"staff_id"`
	MinAmount      *float64   `form:"min_amount" binding:"omitempty,min=0" json:"min_amount"`
	MaxAmount      *float64   `form:"max_amount" binding:"omitempty,min=0" json:"max_amount"`
	IncludeRefunds *bool      `form:"include_refunds" json:"include_refunds"`
}

// InventoryAnalyticsFilters represents filters for inventory analytics
type InventoryAnalyticsFilters struct {
	StandardPagination
	StandardSorting
	StandardDateFilters

	Category     string     `form:"category" json:"category"`
	SupplierID   *uuid.UUID `form:"supplier_id" json:"supplier_id"`
	AnalysisType string     `form:"analysis_type" binding:"oneof=usage waste cost turnover" json:"analysis_type"`
	MinValue     *float64   `form:"min_value" binding:"omitempty,min=0" json:"min_value"`
	MaxValue     *float64   `form:"max_value" binding:"omitempty,min=0" json:"max_value"`
	IncludeWaste *bool      `form:"include_waste" json:"include_waste"`
}

// Response types for analytics endpoints
type PopularItemsAnalyticsResponse struct {
	StandardResponse
	Data []PopularItemAnalytics `json:"data"`
}

type CustomerAnalyticsResponse struct {
	StandardResponse
	Data []CustomerAnalytics `json:"data"`
}

type StaffPerformanceAnalyticsResponse struct {
	StandardResponse
	Data []StaffPerformanceAnalytics `json:"data"`
}

type TableUtilizationAnalyticsResponse struct {
	StandardResponse
	Data []TableUtilizationAnalytics `json:"data"`
}

type SalesReportAnalyticsResponse struct {
	StandardResponse
	Data []SalesReportAnalytics `json:"data"`
}

type InventoryAnalyticsResponse struct {
	StandardResponse
	Data []InventoryAnalytics `json:"data"`
}

// Individual analytics response types
type PopularItemAnalytics struct {
	ItemID       uuid.UUID `json:"item_id"`
	ItemName     string    `json:"item_name"`
	Category     string    `json:"category"`
	OrderCount   int       `json:"order_count"`
	TotalRevenue float64   `json:"total_revenue"`
	AvgRating    float64   `json:"avg_rating"`
	Rank         int       `json:"rank"`
	Period       string    `json:"period"`
}

type CustomerAnalytics struct {
	CustomerID       *uuid.UUID `json:"customer_id"`
	CustomerName     string     `json:"customer_name"`
	CustomerType     string     `json:"customer_type"`
	TotalOrders      int        `json:"total_orders"`
	TotalSpent       float64    `json:"total_spent"`
	AvgOrderValue    float64    `json:"avg_order_value"`
	LastOrderDate    *time.Time `json:"last_order_date"`
	FirstOrderDate   *time.Time `json:"first_order_date"`
	FavoriteItems    []string   `json:"favorite_items"`
	PreferredPayment string     `json:"preferred_payment"`
}

type StaffPerformanceAnalytics struct {
	StaffID         uuid.UUID `json:"staff_id"`
	StaffName       string    `json:"staff_name"`
	Position        string    `json:"position"`
	Department      string    `json:"department"`
	OrdersServed    int       `json:"orders_served"`
	TotalSales      float64   `json:"total_sales"`
	AvgOrderValue   float64   `json:"avg_order_value"`
	CustomerRating  float64   `json:"customer_rating"`
	EfficiencyScore float64   `json:"efficiency_score"`
	WorkingHours    float64   `json:"working_hours"`
	SalesPerHour    float64   `json:"sales_per_hour"`
}

type TableUtilizationAnalytics struct {
	TableID           uuid.UUID `json:"table_id"`
	TableNumber       string    `json:"table_number"`
	AreaName          string    `json:"area_name"`
	Capacity          int       `json:"capacity"`
	TotalReservations int       `json:"total_reservations"`
	TotalHours        float64   `json:"total_hours"`
	UtilizedHours     float64   `json:"utilized_hours"`
	UtilizationRate   float64   `json:"utilization_rate"`
	AvgPartySize      float64   `json:"avg_party_size"`
	TurnoverRate      float64   `json:"turnover_rate"`
	Revenue           float64   `json:"revenue"`
	RevenuePerHour    float64   `json:"revenue_per_hour"`
}

type SalesReportAnalytics struct {
	Date               time.Time          `json:"date"`
	Period             string             `json:"period"`
	TotalOrders        int                `json:"total_orders"`
	TotalRevenue       float64            `json:"total_revenue"`
	AvgOrderValue      float64            `json:"avg_order_value"`
	TotalRefunds       float64            `json:"total_refunds"`
	NetRevenue         float64            `json:"net_revenue"`
	PaymentBreakdown   map[string]float64 `json:"payment_breakdown"`
	OrderTypeBreakdown map[string]int     `json:"order_type_breakdown"`
	HourlyBreakdown    []HourlySales      `json:"hourly_breakdown"`
}

type HourlySales struct {
	Hour    int     `json:"hour"`
	Orders  int     `json:"orders"`
	Revenue float64 `json:"revenue"`
}

type InventoryAnalytics struct {
	IngredientID    uuid.UUID `json:"ingredient_id"`
	IngredientName  string    `json:"ingredient_name"`
	Category        string    `json:"category"`
	TotalUsage      float64   `json:"total_usage"`
	TotalWaste      float64   `json:"total_waste"`
	TotalCost       float64   `json:"total_cost"`
	WastePercentage float64   `json:"waste_percentage"`
	TurnoverRate    float64   `json:"turnover_rate"`
	AvgStockLevel   float64   `json:"avg_stock_level"`
	StockOuts       int       `json:"stock_outs"`
	SupplierName    string    `json:"supplier_name"`
	CostTrend       string    `json:"cost_trend"` // increasing, decreasing, stable
}

// Allowed sort fields for analytics
var PopularItemsAnalyticsSortFields = []string{
	"item_name", "order_count", "total_revenue", "avg_rating", "rank",
}

var CustomerAnalyticsSortFields = []string{
	"customer_name", "total_orders", "total_spent", "avg_order_value",
	"last_order_date", "first_order_date",
}

var StaffPerformanceAnalyticsSortFields = []string{
	"staff_name", "position", "orders_served", "total_sales",
	"avg_order_value", "customer_rating", "efficiency_score",
}

var TableUtilizationAnalyticsSortFields = []string{
	"table_number", "capacity", "total_reservations", "utilization_rate",
	"turnover_rate", "revenue", "revenue_per_hour",
}

var SalesReportAnalyticsSortFields = []string{
	"date", "total_orders", "total_revenue", "avg_order_value",
	"net_revenue",
}

var InventoryAnalyticsSortFields = []string{
	"ingredient_name", "category", "total_usage", "total_waste",
	"total_cost", "waste_percentage", "turnover_rate",
}

// CommunicationAnalyticsFilters for communication analytics filtering
type CommunicationAnalyticsFilters struct {
	StandardPagination
	StandardSorting
	StandardSearch
	StandardDateFilters

	// Communication specific filters
	CampaignID   *uuid.UUID `form:"campaign_id"`
	ChannelType  string     `form:"channel_type" binding:"oneof=email sms push notification"`
	Status       string     `form:"status" binding:"oneof=sent delivered opened clicked failed"`
	SegmentID    *uuid.UUID `form:"segment_id"`
	MinOpenRate  *float64   `form:"min_open_rate" binding:"omitempty,min=0,max=100"`
	MaxOpenRate  *float64   `form:"max_open_rate" binding:"omitempty,min=0,max=100"`
	MinClickRate *float64   `form:"min_click_rate" binding:"omitempty,min=0,max=100"`
	MaxClickRate *float64   `form:"max_click_rate" binding:"omitempty,min=0,max=100"`
}

// CommunicationAnalyticsSortFields defines valid sort fields for communication analytics
var CommunicationAnalyticsSortFields = []string{
	"sent_count", "delivered_count", "open_rate", "click_rate", "campaign_name", "sent_date", "created_at",
}

// CommunicationAnalytics represents communication analytics data
type CommunicationAnalytics struct {
	ID             uuid.UUID  `json:"id"`
	CampaignID     uuid.UUID  `json:"campaign_id"`
	CampaignName   string     `json:"campaign_name"`
	ChannelType    string     `json:"channel_type"`
	SegmentID      *uuid.UUID `json:"segment_id,omitempty"`
	SegmentName    *string    `json:"segment_name,omitempty"`
	SentCount      int        `json:"sent_count"`
	DeliveredCount int        `json:"delivered_count"`
	OpenedCount    int        `json:"opened_count"`
	ClickedCount   int        `json:"clicked_count"`
	FailedCount    int        `json:"failed_count"`
	OpenRate       float64    `json:"open_rate"`     // percentage
	ClickRate      float64    `json:"click_rate"`    // percentage
	DeliveryRate   float64    `json:"delivery_rate"` // percentage
	SentDate       time.Time  `json:"sent_date"`
	CreatedAt      time.Time  `json:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at"`
}

// CommunicationAnalyticsResponse for paginated communication analytics
type CommunicationAnalyticsResponse struct {
	StandardResponse
	Data []CommunicationAnalytics `json:"data"`
}
