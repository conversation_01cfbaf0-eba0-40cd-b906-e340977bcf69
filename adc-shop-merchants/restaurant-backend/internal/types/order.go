package types

import (
	"time"

	"github.com/google/uuid"
)

// Order request types
type CreateOrderRequest struct {
	TableID         *uuid.UUID              `json:"table_id"`
	CustomerName    string                  `json:"customer_name"`
	CustomerPhone   string                  `json:"customer_phone"`
	CustomerEmail   string                  `json:"customer_email"`
	OrderType       string                  `json:"order_type" binding:"required,oneof=dine_in takeaway delivery"`
	Items           []OrderItemRequest      `json:"items" binding:"required,min=1"`
	Notes           string                  `json:"notes"`
	DeliveryAddress *DeliveryAddressRequest `json:"delivery_address"`
	ScheduledFor    *time.Time              `json:"scheduled_for"`
}

type UpdateOrderRequest struct {
	Status        *string            `json:"status" binding:"omitempty,oneof=pending confirmed preparing ready served completed cancelled"`
	Items         []OrderItemRequest `json:"items"`
	Notes         *string            `json:"notes"`
	EstimatedTime *int               `json:"estimated_time"`
}

type OrderItemRequest struct {
	MenuItemID uuid.UUID                `json:"menu_item_id" binding:"required"`
	Quantity   int                      `json:"quantity" binding:"required,min=1"`
	Price      float64                  `json:"price" binding:"required,min=0"`
	Notes      string                   `json:"notes"`
	Options    []OrderItemOptionRequest `json:"options"`
}

type OrderItemOptionRequest struct {
	OptionName  string  `json:"option_name" binding:"required"`
	ChoiceName  string  `json:"choice_name" binding:"required"`
	PriceAdjust float64 `json:"price_adjust"`
}

type DeliveryAddressRequest struct {
	Street  string `json:"street" binding:"required"`
	City    string `json:"city" binding:"required"`
	State   string `json:"state"`
	ZipCode string `json:"zip_code"`
	Country string `json:"country" binding:"required"`
	Notes   string `json:"notes"`
}

type UpdateOrderStatusRequest struct {
	Status        string `json:"status" binding:"required,oneof=pending confirmed preparing ready served completed cancelled"`
	EstimatedTime *int   `json:"estimated_time"`
	Notes         string `json:"notes"`
}

type ProcessPaymentRequest struct {
	PaymentMethod string  `json:"payment_method" binding:"required,oneof=cash card digital_wallet"`
	Amount        float64 `json:"amount" binding:"required,min=0"`
	TipAmount     float64 `json:"tip_amount" binding:"min=0"`
	PaymentRef    string  `json:"payment_ref"`
}

// Order filters
type OrderFilters struct {
	// Filtering
	Status        string     `form:"status"`
	OrderType     string     `form:"order_type"`
	TableID       *uuid.UUID `form:"table_id"`
	CustomerName  string     `form:"customer_name"`
	PaymentStatus string     `form:"payment_status"`
	PaymentMethod string     `form:"payment_method"`
	DateFrom      *time.Time `form:"date_from"`
	DateTo        *time.Time `form:"date_to"`
	MinAmount     float64    `form:"min_amount"`
	MaxAmount     float64    `form:"max_amount"`
	Search        string     `form:"search"` // Search by order number, customer name, phone

	// Sorting
	SortBy    string `form:"sort_by"`    // order_number, customer_name, total_amount, status, created_at, updated_at
	SortOrder string `form:"sort_order"` // asc, desc

	// Pagination
	Page  int `form:"page" binding:"min=1"`
	Limit int `form:"limit" binding:"min=1,max=100"`
}

// Response types
type OrderResponse struct {
	ID              uuid.UUID                `json:"id"`
	BranchID        uuid.UUID                `json:"branch_id"`
	TableID         *uuid.UUID               `json:"table_id"`
	Table           *TableSummary            `json:"table,omitempty"`
	OrderNumber     string                   `json:"order_number"`
	CustomerName    string                   `json:"customer_name"`
	CustomerPhone   string                   `json:"customer_phone"`
	CustomerEmail   string                   `json:"customer_email"`
	OrderType       string                   `json:"order_type"`
	Status          string                   `json:"status"`
	Items           []OrderItemResponse      `json:"items"`
	Subtotal        float64                  `json:"subtotal"`
	TaxAmount       float64                  `json:"tax_amount"`
	TipAmount       float64                  `json:"tip_amount"`
	TotalAmount     float64                  `json:"total_amount"`
	Notes           string                   `json:"notes"`
	DeliveryAddress *DeliveryAddressResponse `json:"delivery_address,omitempty"`
	EstimatedTime   *int                     `json:"estimated_time"`
	ScheduledFor    *time.Time               `json:"scheduled_for"`
	PaymentStatus   string                   `json:"payment_status"`
	PaymentMethod   string                   `json:"payment_method"`
	CreatedAt       time.Time                `json:"created_at"`
	UpdatedAt       time.Time                `json:"updated_at"`
	CompletedAt     *time.Time               `json:"completed_at"`
}

type OrdersResponse struct {
	Data       []OrderResponse `json:"data"`
	Total      int64           `json:"total"`
	Page       int             `json:"page"`
	Limit      int             `json:"limit"`
	TotalPages int             `json:"total_pages"`
}

type OrderItemResponse struct {
	ID         uuid.UUID                 `json:"id"`
	MenuItemID uuid.UUID                 `json:"menu_item_id"`
	MenuItem   *MenuItemSummary          `json:"menu_item,omitempty"`
	Quantity   int                       `json:"quantity"`
	UnitPrice  float64                   `json:"unit_price"`
	TotalPrice float64                   `json:"total_price"`
	Notes      string                    `json:"notes"`
	Options    []OrderItemOptionResponse `json:"options"`
	Status     string                    `json:"status"`
	CreatedAt  time.Time                 `json:"created_at"`
}

type OrderItemOptionResponse struct {
	OptionName  string  `json:"option_name"`
	ChoiceName  string  `json:"choice_name"`
	PriceAdjust float64 `json:"price_adjust"`
}

type DeliveryAddressResponse struct {
	Street  string `json:"street"`
	City    string `json:"city"`
	State   string `json:"state"`
	ZipCode string `json:"zip_code"`
	Country string `json:"country"`
	Notes   string `json:"notes"`
}

type TableSummary struct {
	ID     uuid.UUID `json:"id"`
	Number string    `json:"number"`
	Name   string    `json:"name"`
}

type MenuItemSummary struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Price       float64   `json:"price"`
	ImageURL    string    `json:"image_url"`
}

type PaymentResponse struct {
	ID            uuid.UUID `json:"id"`
	OrderID       uuid.UUID `json:"order_id"`
	Amount        float64   `json:"amount"`
	TipAmount     float64   `json:"tip_amount"`
	TotalAmount   float64   `json:"total_amount"`
	PaymentMethod string    `json:"payment_method"`
	PaymentRef    string    `json:"payment_ref"`
	Status        string    `json:"status"`
	ProcessedAt   time.Time `json:"processed_at"`
}

// Analytics types
type OrderStatsResponse struct {
	TotalOrders       int64            `json:"total_orders"`
	TotalRevenue      float64          `json:"total_revenue"`
	AverageOrderValue float64          `json:"average_order_value"`
	OrdersByStatus    map[string]int64 `json:"orders_by_status"`
	OrdersByType      map[string]int64 `json:"orders_by_type"`
	RevenueByDay      []DailyRevenue   `json:"revenue_by_day"`
	PopularItems      []PopularItem    `json:"popular_items"`
}

type DailyRevenue struct {
	Date    time.Time `json:"date"`
	Revenue float64   `json:"revenue"`
	Orders  int64     `json:"orders"`
}

type PopularItem struct {
	MenuItemID   uuid.UUID `json:"menu_item_id"`
	MenuItemName string    `json:"menu_item_name"`
	Quantity     int64     `json:"quantity"`
	Revenue      float64   `json:"revenue"`
}

type DashboardStatsResponse struct {
	TodayOrders      int64   `json:"today_orders"`
	TodayRevenue     float64 `json:"today_revenue"`
	PendingOrders    int64   `json:"pending_orders"`
	ActiveOrders     int64   `json:"active_orders"`
	CompletedOrders  int64   `json:"completed_orders"`
	AverageOrderTime int     `json:"average_order_time"`
	TableOccupancy   float64 `json:"table_occupancy"`
}

// Frontend-compatible dashboard stats response
type FrontendDashboardStatsResponse struct {
	TodayOrders        int64   `json:"todayOrders"`
	TodayRevenue       float64 `json:"todayRevenue"`
	TodayReservations  int64   `json:"todayReservations"`
	ActiveStaff        int64   `json:"activeStaff"`
	OrdersGrowth       float64 `json:"ordersGrowth"`
	RevenueGrowth      float64 `json:"revenueGrowth"`
	ReservationsGrowth float64 `json:"reservationsGrowth"`
}

type RecentActivityResponse struct {
	Orders []OrderResponse `json:"orders"`
	Limit  int             `json:"limit"`
}

type RealTimeMetricsResponse struct {
	ActiveOrders    int64   `json:"active_orders"`
	TodayRevenue    float64 `json:"today_revenue"`
	OnlineCustomers int64   `json:"online_customers"`
	AverageWaitTime int     `json:"average_wait_time"`
}

// Order status constants
const (
	OrderStatusPending   = "pending"
	OrderStatusConfirmed = "confirmed"
	OrderStatusPreparing = "preparing"
	OrderStatusReady     = "ready"
	OrderStatusServed    = "served"
	OrderStatusCompleted = "completed"
	OrderStatusCancelled = "cancelled"
)

// Order type constants
const (
	OrderTypeDineIn   = "dine_in"
	OrderTypeTakeaway = "takeaway"
	OrderTypeDelivery = "delivery"
)

// Payment status constants
const (
	PaymentStatusPending   = "pending"
	PaymentStatusCompleted = "completed"
	PaymentStatusFailed    = "failed"
	PaymentStatusRefunded  = "refunded"
)

// Payment method constants
const (
	PaymentMethodCash          = "cash"
	PaymentMethodCard          = "card"
	PaymentMethodDigitalWallet = "digital_wallet"
)
