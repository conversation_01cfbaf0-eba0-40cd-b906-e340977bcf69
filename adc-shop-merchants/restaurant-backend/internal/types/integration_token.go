package types

import (
	"time"

	"restaurant-backend/internal/models"

	"github.com/google/uuid"
)

// IntegrationTokenScope represents the scope/permissions of a token
type IntegrationTokenScope struct {
	Resource     string                 `json:"resource"`     // e.g., "menu", "orders", "reservations"
	Actions      []string               `json:"actions"`      // e.g., ["read", "write", "delete"]
	BranchIDs    []string               `json:"branch_ids"`   // Specific branches (empty = all branches)
	Restrictions map[string]interface{} `json:"restrictions"` // Additional restrictions
}

// CreateIntegrationTokenRequest represents a request to create an integration token
type CreateIntegrationTokenRequest struct {
	Name           string                  `json:"name" binding:"required"`
	Description    string                  `json:"description"`
	Type           string                  `json:"type" binding:"required"` // api, webhook, widget, service
	UserID         uuid.UUID               `json:"user_id" binding:"required"`
	ShopID         uuid.UUID               `json:"shop_id" binding:"required"`
	BranchID       *uuid.UUID              `json:"branch_id"`
	Scopes         []IntegrationTokenScope `json:"scopes" binding:"required"`
	RateLimit      int                     `json:"rate_limit"`      // Requests per hour
	RateLimitWindow int                    `json:"rate_limit_window"` // Window in seconds
	AllowedIPs     []string                `json:"allowed_ips"`
	AllowedOrigins []string                `json:"allowed_origins"`
	ExpiresAt      *time.Time              `json:"expires_at"`
}

// CreateIntegrationTokenResponse represents the response when creating an integration token
type CreateIntegrationTokenResponse struct {
	Token       string                  `json:"token"`        // The actual token (only returned once)
	TokenID     uuid.UUID               `json:"token_id"`
	TokenPrefix string                  `json:"token_prefix"` // First few chars for identification
	Name        string                  `json:"name"`
	Type        string                  `json:"type"`
	Status      string                  `json:"status"`
	Scopes      []IntegrationTokenScope `json:"scopes"`
	RateLimit   int                     `json:"rate_limit"`
	ExpiresAt   *time.Time              `json:"expires_at"`
	CreatedAt   time.Time               `json:"created_at"`
}

// GetIntegrationTokensRequest represents a request to get integration tokens
type GetIntegrationTokensRequest struct {
	UserID uuid.UUID `json:"user_id" binding:"required"`
	ShopID uuid.UUID `json:"shop_id" binding:"required"`
	Limit  int       `json:"limit"`
	Offset int       `json:"offset"`
}

// GetIntegrationTokensResponse represents the response when getting integration tokens
type GetIntegrationTokensResponse struct {
	Tokens []IntegrationTokenResponse `json:"tokens"`
	Total  int64                      `json:"total"`
	Limit  int                        `json:"limit"`
	Offset int                        `json:"offset"`
}

// IntegrationTokenResponse represents an integration token in responses
type IntegrationTokenResponse struct {
	ID           uuid.UUID               `json:"id"`
	Name         string                  `json:"name"`
	Description  string                  `json:"description"`
	Type         string                  `json:"type"`
	Status       string                  `json:"status"`
	TokenPrefix  string                  `json:"token_prefix"` // First few chars for identification
	Scopes       []IntegrationTokenScope `json:"scopes"`
	RateLimit    int                     `json:"rate_limit"`
	CurrentUsage int                     `json:"current_usage"`
	UsageCount   int64                   `json:"usage_count"`
	LastUsedAt   *time.Time              `json:"last_used_at"`
	ExpiresAt    *time.Time              `json:"expires_at"`
	CreatedAt    time.Time               `json:"created_at"`
	UpdatedAt    time.Time               `json:"updated_at"`
}

// GetIntegrationTokenRequest represents a request to get a specific integration token
type GetIntegrationTokenRequest struct {
	TokenID uuid.UUID `json:"token_id" binding:"required"`
	UserID  uuid.UUID `json:"user_id" binding:"required"`
}

// UpdateIntegrationTokenRequest represents a request to update an integration token
type UpdateIntegrationTokenRequest struct {
	TokenID        uuid.UUID                `json:"token_id" binding:"required"`
	UserID         uuid.UUID                `json:"user_id" binding:"required"`
	Name           *string                  `json:"name"`
	Description    *string                  `json:"description"`
	Status         *string                  `json:"status"`
	Scopes         *[]IntegrationTokenScope `json:"scopes"`
	RateLimit      *int                     `json:"rate_limit"`
	AllowedIPs     *[]string                `json:"allowed_ips"`
	AllowedOrigins *[]string                `json:"allowed_origins"`
	ExpiresAt      *time.Time               `json:"expires_at"`
}

// RevokeIntegrationTokenRequest represents a request to revoke an integration token
type RevokeIntegrationTokenRequest struct {
	TokenID uuid.UUID `json:"token_id" binding:"required"`
	UserID  uuid.UUID `json:"user_id" binding:"required"`
}

// GetTokenUsageRequest represents a request to get token usage statistics
type GetTokenUsageRequest struct {
	TokenID uuid.UUID `json:"token_id" binding:"required"`
	UserID  uuid.UUID `json:"user_id" binding:"required"`
	From    time.Time `json:"from"`
	To      time.Time `json:"to"`
}

// GetTokenUsageResponse represents the response when getting token usage statistics
type GetTokenUsageResponse struct {
	Usage []*models.IntegrationTokenUsage `json:"usage"`
	Stats map[string]interface{}          `json:"stats"`
	From  time.Time                       `json:"from"`
	To    time.Time                       `json:"to"`
}

// ValidateTokenRequest represents a request to validate a token
type ValidateTokenRequest struct {
	Token     string `json:"token" binding:"required"`
	Resource  string `json:"resource"`
	Action    string `json:"action"`
	BranchID  string `json:"branch_id"`
	IPAddress string `json:"ip_address"`
	UserAgent string `json:"user_agent"`
}

// ValidateTokenResponse represents the response when validating a token
type ValidateTokenResponse struct {
	Valid    bool                        `json:"valid"`
	Token    *IntegrationTokenResponse   `json:"token,omitempty"`
	Scopes   []IntegrationTokenScope     `json:"scopes,omitempty"`
	Message  string                      `json:"message,omitempty"`
	ShopID   uuid.UUID                   `json:"shop_id,omitempty"`
	BranchID *uuid.UUID                  `json:"branch_id,omitempty"`
}

// TokenUsageRequest represents a request to record token usage
type TokenUsageRequest struct {
	TokenID      uuid.UUID `json:"token_id" binding:"required"`
	Endpoint     string    `json:"endpoint" binding:"required"`
	Method       string    `json:"method" binding:"required"`
	IPAddress    string    `json:"ip_address"`
	UserAgent    string    `json:"user_agent"`
	StatusCode   int       `json:"status_code"`
	ResponseTime int64     `json:"response_time"` // in milliseconds
	ErrorMessage string    `json:"error_message"`
	RequestSize  int64     `json:"request_size"`  // in bytes
	ResponseSize int64     `json:"response_size"` // in bytes
}

// Common token scopes for easy reference
var (
	// Menu scopes
	MenuReadScope = IntegrationTokenScope{
		Resource: "menu",
		Actions:  []string{"read"},
	}
	MenuWriteScope = IntegrationTokenScope{
		Resource: "menu",
		Actions:  []string{"read", "write"},
	}
	MenuFullScope = IntegrationTokenScope{
		Resource: "menu",
		Actions:  []string{"*"},
	}

	// Order scopes
	OrderReadScope = IntegrationTokenScope{
		Resource: "orders",
		Actions:  []string{"read"},
	}
	OrderWriteScope = IntegrationTokenScope{
		Resource: "orders",
		Actions:  []string{"read", "write"},
	}
	OrderFullScope = IntegrationTokenScope{
		Resource: "orders",
		Actions:  []string{"*"},
	}

	// Reservation scopes
	ReservationReadScope = IntegrationTokenScope{
		Resource: "reservations",
		Actions:  []string{"read"},
	}
	ReservationWriteScope = IntegrationTokenScope{
		Resource: "reservations",
		Actions:  []string{"read", "write"},
	}
	ReservationFullScope = IntegrationTokenScope{
		Resource: "reservations",
		Actions:  []string{"*"},
	}

	// Analytics scopes
	AnalyticsReadScope = IntegrationTokenScope{
		Resource: "analytics",
		Actions:  []string{"read"},
	}

	// Full access scope
	FullAccessScope = IntegrationTokenScope{
		Resource: "*",
		Actions:  []string{"*"},
	}
)
