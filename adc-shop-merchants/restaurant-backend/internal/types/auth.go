package types

import (
	"time"

	"restaurant-backend/internal/models"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

// LoginRequest represents a login request
type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse represents a login response
type LoginResponse struct {
	Token        string       `json:"token"`
	RefreshToken string       `json:"refresh_token"`
	User         *models.User `json:"user"`
	ExpiresAt    time.Time    `json:"expires_at"`
}

// RegisterRequest represents a registration request
type RegisterRequest struct {
	Email     string `json:"email" binding:"required,email"`
	Password  string `json:"password" binding:"required,min=6"`
	FirstName string `json:"first_name" binding:"required"`
	LastName  string `json:"last_name" binding:"required"`
	Phone     string `json:"phone"`
}

// RegisterResponse represents a registration response
type RegisterResponse struct {
	User    *models.User `json:"user"`
	Message string       `json:"message"`
}

// RefreshTokenRequest represents a refresh token request
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// CreateOAuthUserRequest represents an OAuth user creation request
type CreateOAuthUserRequest struct {
	OAuthUserID string `json:"oauth_user_id" binding:"required"` // OAuth provider user ID
	Email       string `json:"email" binding:"required,email"`
	Name        string `json:"name"`
	FirstName   string `json:"first_name"`
	LastName    string `json:"last_name"`
	AvatarURL   string `json:"avatar_url"`
	Provider    string `json:"provider" binding:"required"` // google, github, facebook, etc.
	ShopSlug    string `json:"shop_slug"`                   // Optional: associate with specific shop
	BranchSlug  string `json:"branch_slug"`                 // Optional: associate with specific branch
}

// Claims represents JWT claims
type Claims struct {
	UserID      uuid.UUID `json:"user_id"`
	ShopID      uuid.UUID `json:"shop_id"`
	BranchID    uuid.UUID `json:"branch_id"`
	Role        string    `json:"role"`
	Permissions []string  `json:"permissions"`
	jwt.RegisteredClaims
}
