package types

import (
	"time"

	"github.com/google/uuid"
)

// Menu Category types
type CreateCategoryRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	ImageURL    string `json:"image_url"`
	SortOrder   int    `json:"sort_order"`
	IsActive    bool   `json:"is_active"`
}

type UpdateCategoryRequest struct {
	Name        *string `json:"name"`
	Description *string `json:"description"`
	ImageURL    *string `json:"image_url"`
	SortOrder   *int    `json:"sort_order"`
	IsActive    *bool   `json:"is_active"`
}

type CategoryFilters struct {
	IsActive *bool  `form:"is_active"`
	Search   string `form:"search"`
	Page     int    `form:"page" binding:"min=1"`
	Limit    int    `form:"limit" binding:"min=1,max=100"`
}

// Menu Item types
type CreateMenuItemRequest struct {
	CategoryID      *uuid.UUID              `json:"category_id"`
	Name            string                  `json:"name" binding:"required"`
	Description     string                  `json:"description"`
	Price           float64                 `json:"price" binding:"required,min=0"`
	Cost            *float64                `json:"cost" binding:"omitempty,min=0"`
	Images          []string                `json:"images"`
	Ingredients     []string                `json:"ingredients"`
	Allergens       []string                `json:"allergens"`
	NutritionalInfo *NutritionalInfoRequest `json:"nutritional_info"`
	PreparationTime *int                    `json:"preparation_time" binding:"omitempty,min=0"`
	IsAvailable     bool                    `json:"is_available"`
	IsVegetarian    bool                    `json:"is_vegetarian"`
	IsVegan         bool                    `json:"is_vegan"`
	IsGlutenFree    bool                    `json:"is_gluten_free"`
	IsSpicy         bool                    `json:"is_spicy"`
	SpiceLevel      int                     `json:"spice_level" binding:"min=0,max=5"`
	Tags            []string                `json:"tags"`
	Options         []MenuItemOptionRequest `json:"options"`
}

type UpdateMenuItemRequest struct {
	CategoryID      *uuid.UUID              `json:"category_id"`
	Name            *string                 `json:"name"`
	Description     *string                 `json:"description"`
	Price           *float64                `json:"price" binding:"omitempty,min=0"`
	Cost            *float64                `json:"cost" binding:"omitempty,min=0"`
	Images          []string                `json:"images"`
	Ingredients     []string                `json:"ingredients"`
	Allergens       []string                `json:"allergens"`
	NutritionalInfo *NutritionalInfoRequest `json:"nutritional_info"`
	PreparationTime *int                    `json:"preparation_time" binding:"omitempty,min=0"`
	IsAvailable     *bool                   `json:"is_available"`
	IsVegetarian    *bool                   `json:"is_vegetarian"`
	IsVegan         *bool                   `json:"is_vegan"`
	IsGlutenFree    *bool                   `json:"is_gluten_free"`
	IsSpicy         *bool                   `json:"is_spicy"`
	SpiceLevel      *int                    `json:"spice_level" binding:"omitempty,min=0,max=5"`
	Tags            []string                `json:"tags"`
	Options         []MenuItemOptionRequest `json:"options"`
}

type MenuItemFilters struct {
	// Pagination
	Page  int `form:"page" binding:"omitempty,min=1"`
	Limit int `form:"limit" binding:"omitempty,min=1,max=100"`

	// Sorting
	SortBy    string `form:"sort_by"`
	SortOrder string `form:"sort_order" binding:"omitempty,oneof=asc desc"`

	// Search
	Search string `form:"search"`

	// Date filters
	DateFrom *time.Time `form:"date_from" time_format:"2006-01-02"`
	DateTo   *time.Time `form:"date_to" time_format:"2006-01-02"`

	// Menu item specific filters
	CategoryID   *uuid.UUID `form:"category_id"`
	IsAvailable  *bool      `form:"is_available"`
	IsVegetarian *bool      `form:"is_vegetarian"`
	IsVegan      *bool      `form:"is_vegan"`
	IsGlutenFree *bool      `form:"is_gluten_free"`
	IsSpicy      *bool      `form:"is_spicy"`
	MinPrice     *float64   `form:"min_price" binding:"omitempty,min=0"`
	MaxPrice     *float64   `form:"max_price" binding:"omitempty,min=0"`
	Tags         []string   `form:"tags"`
}

// MenuItemSortFields defines valid sort fields for menu items
var MenuItemSortFields = []string{
	"name", "price", "created_at", "updated_at", "category", "order_count",
}

// ApplyDefaults applies default values to MenuItemFilters
func (f *MenuItemFilters) ApplyDefaults() {
	if f.Page <= 0 {
		f.Page = PaginationDefaults.Page
	}
	if f.Limit <= 0 {
		f.Limit = PaginationDefaults.Limit
	}
	if f.SortBy == "" {
		f.SortBy = SortingDefaults.SortBy
	}
	if f.SortOrder == "" {
		f.SortOrder = SortingDefaults.SortOrder
	}
}

type ToggleAvailabilityRequest struct {
	IsAvailable bool `json:"is_available"`
}

// Supporting types
type NutritionalInfoRequest struct {
	Calories      *int     `json:"calories"`
	Protein       *float64 `json:"protein"`
	Carbohydrates *float64 `json:"carbohydrates"`
	Fat           *float64 `json:"fat"`
	Fiber         *float64 `json:"fiber"`
	Sugar         *float64 `json:"sugar"`
	Sodium        *float64 `json:"sodium"`
	Cholesterol   *float64 `json:"cholesterol"`
	VitaminA      *float64 `json:"vitamin_a"`
	VitaminC      *float64 `json:"vitamin_c"`
	Calcium       *float64 `json:"calcium"`
	Iron          *float64 `json:"iron"`
}

type MenuItemOptionRequest struct {
	Name       string                  `json:"name" binding:"required"`
	Type       string                  `json:"type" binding:"required,oneof=single multiple"`
	IsRequired bool                    `json:"is_required"`
	MinChoices int                     `json:"min_choices" binding:"min=0"`
	MaxChoices int                     `json:"max_choices" binding:"min=0"`
	Choices    []MenuItemChoiceRequest `json:"choices" binding:"required,min=1"`
}

type MenuItemChoiceRequest struct {
	Name        string  `json:"name" binding:"required"`
	PriceAdjust float64 `json:"price_adjust"`
	IsDefault   bool    `json:"is_default"`
}

// Response types
type CategoryResponse struct {
	ID          uuid.UUID `json:"id"`
	BranchID    uuid.UUID `json:"branch_id"`
	Name        string    `json:"name"`
	Slug        string    `json:"slug"`
	Description string    `json:"description"`
	ImageURL    string    `json:"image_url"`
	SortOrder   int       `json:"sort_order"`
	IsActive    bool      `json:"is_active"`
	ItemCount   int       `json:"item_count"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type CategoriesResponse struct {
	Data       []CategoryResponse `json:"data"`
	Total      int64              `json:"total"`
	Page       int                `json:"page"`
	Limit      int                `json:"limit"`
	TotalPages int                `json:"total_pages"`
}

type MenuItemResponse struct {
	ID              uuid.UUID                `json:"id"`
	BranchID        uuid.UUID                `json:"branch_id"`
	CategoryID      *uuid.UUID               `json:"category_id"`
	Category        *CategoryResponse        `json:"category,omitempty"`
	Name            string                   `json:"name"`
	Slug            string                   `json:"slug"`
	Description     string                   `json:"description"`
	Price           float64                  `json:"price"`
	Cost            *float64                 `json:"cost"`
	Images          []string                 `json:"images"`
	PrimaryImage    string                   `json:"primary_image"`
	Ingredients     []string                 `json:"ingredients"`
	Allergens       []string                 `json:"allergens"`
	NutritionalInfo *NutritionalInfoResponse `json:"nutritional_info"`
	PreparationTime *int                     `json:"preparation_time"`
	IsAvailable     bool                     `json:"is_available"`
	IsVegetarian    bool                     `json:"is_vegetarian"`
	IsVegan         bool                     `json:"is_vegan"`
	IsGlutenFree    bool                     `json:"is_gluten_free"`
	IsSpicy         bool                     `json:"is_spicy"`
	SpiceLevel      int                      `json:"spice_level"`
	SpiceLevelText  string                   `json:"spice_level_text"`
	Tags            []string                 `json:"tags"`
	Options         []MenuItemOptionResponse `json:"options"`
	DietaryInfo     map[string]bool          `json:"dietary_info"`
	CreatedAt       time.Time                `json:"created_at"`
	UpdatedAt       time.Time                `json:"updated_at"`
}

type MenuItemsResponse struct {
	Data       []MenuItemResponse `json:"data"`
	Total      int64              `json:"total"`
	Page       int                `json:"page"`
	Limit      int                `json:"limit"`
	TotalPages int                `json:"total_pages"`
}

type NutritionalInfoResponse struct {
	Calories      *int     `json:"calories"`
	Protein       *float64 `json:"protein"`
	Carbohydrates *float64 `json:"carbohydrates"`
	Fat           *float64 `json:"fat"`
	Fiber         *float64 `json:"fiber"`
	Sugar         *float64 `json:"sugar"`
	Sodium        *float64 `json:"sodium"`
	Cholesterol   *float64 `json:"cholesterol"`
	VitaminA      *float64 `json:"vitamin_a"`
	VitaminC      *float64 `json:"vitamin_c"`
	Calcium       *float64 `json:"calcium"`
	Iron          *float64 `json:"iron"`
}

type MenuItemOptionResponse struct {
	Name       string                   `json:"name"`
	Type       string                   `json:"type"`
	IsRequired bool                     `json:"is_required"`
	MinChoices int                      `json:"min_choices"`
	MaxChoices int                      `json:"max_choices"`
	Choices    []MenuItemChoiceResponse `json:"choices"`
}

type MenuItemChoiceResponse struct {
	Name        string  `json:"name"`
	PriceAdjust float64 `json:"price_adjust"`
	IsDefault   bool    `json:"is_default"`
}

// Popular items response
type PopularItemsResponse struct {
	Data  []MenuItemResponse `json:"data"`
	Total int                `json:"total"`
}
