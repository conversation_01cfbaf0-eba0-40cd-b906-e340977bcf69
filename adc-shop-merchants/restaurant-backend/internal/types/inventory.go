package types

import (
	"time"

	"github.com/google/uuid"
)

// InventoryItemFilters represents filters for inventory items
type InventoryItemFilters struct {
	StandardPagination
	StandardSorting
	StandardSearch
	StandardStatusFilter
	StandardDateFilters

	// Inventory-specific filters
	Category     string     `form:"category" json:"category"`
	SupplierID   *uuid.UUID `form:"supplier_id" json:"supplier_id"`
	StockLevel   string     `form:"stock_level" binding:"oneof=low out_of_stock normal" json:"stock_level"`
	ExpiryStatus string     `form:"expiry_status" binding:"oneof=expired expiring_soon fresh" json:"expiry_status"`
	MinStock     *float64   `form:"min_stock" binding:"omitempty,min=0" json:"min_stock"`
	MaxStock     *float64   `form:"max_stock" binding:"omitempty,min=0" json:"max_stock"`
	MinValue     *float64   `form:"min_value" binding:"omitempty,min=0" json:"min_value"`
	MaxValue     *float64   `form:"max_value" binding:"omitempty,min=0" json:"max_value"`
}

// IngredientFilters represents filters for ingredients
type IngredientFilters struct {
	StandardPagination
	StandardSorting
	StandardSearch
	StandardStatusFilter

	Category   string     `form:"category" json:"category"`
	SupplierID *uuid.UUID `form:"supplier_id" json:"supplier_id"`
	Unit       string     `form:"unit" json:"unit"`
}

// SupplierFilters represents filters for suppliers
type SupplierFilters struct {
	StandardPagination
	StandardSorting
	StandardSearch
	StandardStatusFilter

	Country string `form:"country" json:"country"`
	City    string `form:"city" json:"city"`
	Rating  *int   `form:"rating" binding:"omitempty,min=1,max=5" json:"rating"`
}

// StockMovementFilters represents filters for stock movements
type StockMovementFilters struct {
	StandardPagination
	StandardSorting
	StandardDateFilters

	IngredientID *uuid.UUID `form:"ingredient_id" json:"ingredient_id"`
	MovementType string     `form:"movement_type" binding:"oneof=in out adjustment waste" json:"movement_type"`
	UserID       *uuid.UUID `form:"user_id" json:"user_id"`
	MinQuantity  *float64   `form:"min_quantity" binding:"omitempty,min=0" json:"min_quantity"`
	MaxQuantity  *float64   `form:"max_quantity" binding:"omitempty,min=0" json:"max_quantity"`
}

// WasteRecordFilters represents filters for waste records
type WasteRecordFilters struct {
	StandardPagination
	StandardSorting
	StandardDateFilters

	IngredientID *uuid.UUID `form:"ingredient_id" json:"ingredient_id"`
	Reason       string     `form:"reason" json:"reason"`
	UserID       *uuid.UUID `form:"user_id" json:"user_id"`
	MinQuantity  *float64   `form:"min_quantity" binding:"omitempty,min=0" json:"min_quantity"`
	MaxQuantity  *float64   `form:"max_quantity" binding:"omitempty,min=0" json:"max_quantity"`
	MinValue     *float64   `form:"min_value" binding:"omitempty,min=0" json:"min_value"`
	MaxValue     *float64   `form:"max_value" binding:"omitempty,min=0" json:"max_value"`
}

// PurchaseOrderFilters represents filters for purchase orders (updating existing)
type PurchaseOrderFiltersV2 struct {
	StandardPagination
	StandardSorting
	StandardSearch
	StandardDateFilters

	SupplierID     *uuid.UUID `form:"supplier_id" json:"supplier_id"`
	Status         string     `form:"status" json:"status"`
	CreatedBy      *uuid.UUID `form:"created_by" json:"created_by"`
	ExpectedFrom   *time.Time `form:"expected_from" time_format:"2006-01-02" json:"expected_from"`
	ExpectedTo     *time.Time `form:"expected_to" time_format:"2006-01-02" json:"expected_to"`
	MinAmount      *float64   `form:"min_amount" binding:"omitempty,min=0" json:"min_amount"`
	MaxAmount      *float64   `form:"max_amount" binding:"omitempty,min=0" json:"max_amount"`
	Priority       string     `form:"priority" binding:"oneof=low medium high urgent" json:"priority"`
	DeliveryStatus string     `form:"delivery_status" json:"delivery_status"`
}

// Response types for inventory endpoints
type InventoryItemsResponse struct {
	StandardResponse
	Data []InventoryItemResponse `json:"data"`
}

type IngredientsResponse struct {
	StandardResponse
	Data []IngredientResponse `json:"data"`
}

type SuppliersResponse struct {
	StandardResponse
	Data []SupplierResponse `json:"data"`
}

type StockMovementsResponse struct {
	StandardResponse
	Data []StockMovementResponse `json:"data"`
}

type WasteRecordsResponse struct {
	StandardResponse
	Data []WasteRecordResponse `json:"data"`
}

// Individual response types (to be defined based on your models)
type InventoryItemResponse struct {
	ID             uuid.UUID           `json:"id"`
	BranchID       uuid.UUID           `json:"branch_id"`
	IngredientID   uuid.UUID           `json:"ingredient_id"`
	Ingredient     *IngredientResponse `json:"ingredient,omitempty"`
	CurrentStock   float64             `json:"current_stock"`
	AvailableStock float64             `json:"available_stock"`
	ReservedStock  float64             `json:"reserved_stock"`
	MinStockLevel  float64             `json:"min_stock_level"`
	MaxStockLevel  float64             `json:"max_stock_level"`
	UnitCost       float64             `json:"unit_cost"`
	TotalValue     float64             `json:"total_value"`
	ExpiryDate     *time.Time          `json:"expiry_date"`
	BatchNumber    string              `json:"batch_number"`
	Location       string              `json:"location"`
	Status         string              `json:"status"`
	LastUpdated    time.Time           `json:"last_updated"`
	CreatedAt      time.Time           `json:"created_at"`
	UpdatedAt      time.Time           `json:"updated_at"`
}

type IngredientResponse struct {
	ID            uuid.UUID         `json:"id"`
	Name          string            `json:"name"`
	Description   string            `json:"description"`
	Category      string            `json:"category"`
	Unit          string            `json:"unit"`
	MinStockLevel float64           `json:"min_stock_level"`
	MaxStockLevel float64           `json:"max_stock_level"`
	UnitCost      float64           `json:"unit_cost"`
	SupplierID    *uuid.UUID        `json:"supplier_id"`
	Supplier      *SupplierResponse `json:"supplier,omitempty"`
	IsActive      bool              `json:"is_active"`
	CreatedAt     time.Time         `json:"created_at"`
	UpdatedAt     time.Time         `json:"updated_at"`
}

type SupplierResponse struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name"`
	ContactName string    `json:"contact_name"`
	Email       string    `json:"email"`
	Phone       string    `json:"phone"`
	Address     string    `json:"address"`
	City        string    `json:"city"`
	Country     string    `json:"country"`
	Rating      int       `json:"rating"`
	IsActive    bool      `json:"is_active"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type StockMovementResponse struct {
	ID           uuid.UUID           `json:"id"`
	BranchID     uuid.UUID           `json:"branch_id"`
	IngredientID uuid.UUID           `json:"ingredient_id"`
	Ingredient   *IngredientResponse `json:"ingredient,omitempty"`
	MovementType string              `json:"movement_type"`
	Quantity     float64             `json:"quantity"`
	UnitCost     float64             `json:"unit_cost"`
	TotalCost    float64             `json:"total_cost"`
	Reason       string              `json:"reason"`
	Reference    string              `json:"reference"`
	UserID       *uuid.UUID          `json:"user_id"`
	User         *UserResponse       `json:"user,omitempty"`
	CreatedAt    time.Time           `json:"created_at"`
}

type WasteRecordResponse struct {
	ID           uuid.UUID           `json:"id"`
	BranchID     uuid.UUID           `json:"branch_id"`
	IngredientID uuid.UUID           `json:"ingredient_id"`
	Ingredient   *IngredientResponse `json:"ingredient,omitempty"`
	Quantity     float64             `json:"quantity"`
	UnitCost     float64             `json:"unit_cost"`
	TotalValue   float64             `json:"total_value"`
	Reason       string              `json:"reason"`
	Notes        string              `json:"notes"`
	UserID       *uuid.UUID          `json:"user_id"`
	User         *UserResponse       `json:"user,omitempty"`
	WasteDate    time.Time           `json:"waste_date"`
	CreatedAt    time.Time           `json:"created_at"`
}

type PurchaseOrderResponse struct {
	ID               uuid.UUID         `json:"id"`
	BranchID         uuid.UUID         `json:"branch_id"`
	SupplierID       uuid.UUID         `json:"supplier_id"`
	Supplier         *SupplierResponse `json:"supplier,omitempty"`
	OrderNumber      string            `json:"order_number"`
	Status           string            `json:"status"`
	TotalAmount      float64           `json:"total_amount"`
	ExpectedDelivery *time.Time        `json:"expected_delivery"`
	ActualDelivery   *time.Time        `json:"actual_delivery"`
	CreatedBy        uuid.UUID         `json:"created_by"`
	CreatedByUser    *UserResponse     `json:"created_by_user,omitempty"`
	Notes            string            `json:"notes"`
	CreatedAt        time.Time         `json:"created_at"`
	UpdatedAt        time.Time         `json:"updated_at"`
}

// Allowed sort fields for each entity
var InventoryItemSortFields = []string{
	"name", "category", "current_stock", "available_stock", "unit_cost",
	"total_value", "expiry_date", "status", "created_at", "updated_at",
}

var IngredientSortFields = []string{
	"name", "category", "unit", "unit_cost", "min_stock_level",
	"max_stock_level", "is_active", "created_at", "updated_at",
}

var SupplierSortFields = []string{
	"name", "contact_name", "city", "country", "rating",
	"is_active", "created_at", "updated_at",
}

var StockMovementSortFields = []string{
	"movement_type", "quantity", "unit_cost", "total_cost", "created_at",
}

var WasteRecordSortFields = []string{
	"quantity", "unit_cost", "total_value", "reason", "waste_date", "created_at",
}

var PurchaseOrderSortFields = []string{
	"order_number", "status", "total_amount", "expected_delivery",
	"actual_delivery", "created_at", "updated_at",
}
