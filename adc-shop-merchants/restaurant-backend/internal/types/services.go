package types

import (
	"time"

	"github.com/google/uuid"
)

// Service request/response types
type CreateServiceRequest struct {
	Name            string     `json:"name" binding:"required"`
	Description     string     `json:"description"`
	Category        string     `json:"category" binding:"required"`
	Price           float64    `json:"price" binding:"required,min=0"`
	Duration        int        `json:"duration" binding:"required,min=1"` // in minutes
	MaxCapacity     int        `json:"max_capacity" binding:"min=1"`
	RequiresStaff   bool       `json:"requires_staff"`
	PreparationTime int        `json:"preparation_time" binding:"min=0"`
	CleanupTime     int        `json:"cleanup_time" binding:"min=0"`
	ImageURL        string     `json:"image_url"`
	BranchID        *uuid.UUID `json:"branch_id"`
}

type UpdateServiceRequest struct {
	Name            *string  `json:"name"`
	Description     *string  `json:"description"`
	Category        *string  `json:"category"`
	Price           *float64 `json:"price" binding:"omitempty,min=0"`
	Duration        *int     `json:"duration" binding:"omitempty,min=1"`
	MaxCapacity     *int     `json:"max_capacity" binding:"omitempty,min=1"`
	RequiresStaff   *bool    `json:"requires_staff"`
	PreparationTime *int     `json:"preparation_time" binding:"omitempty,min=0"`
	CleanupTime     *int     `json:"cleanup_time" binding:"omitempty,min=0"`
	ImageURL        *string  `json:"image_url"`
	IsActive        *bool    `json:"is_active"`
}

type ServiceFilters struct {
	StandardPagination
	StandardSorting
	StandardSearch
	StandardDateFilters

	// Service specific filters
	Category      string   `form:"category"`
	RequiresStaff *bool    `form:"requires_staff"`
	IsActive      *bool    `form:"is_active"`
	MinPrice      *float64 `form:"min_price"`
	MaxPrice      *float64 `form:"max_price"`
	MinDuration   *int     `form:"min_duration"`
	MaxDuration   *int     `form:"max_duration"`
}

// ServiceSortFields defines valid sort fields for services
var ServiceSortFields = []string{
	"name", "price", "duration", "created_at", "updated_at", "category",
}

// Appointment request/response types
type CreateAppointmentRequest struct {
	ServiceID       uuid.UUID  `json:"service_id" binding:"required"`
	StaffID         *uuid.UUID `json:"staff_id"`
	CustomerName    string     `json:"customer_name" binding:"required"`
	CustomerEmail   string     `json:"customer_email" binding:"omitempty,email"`
	CustomerPhone   string     `json:"customer_phone" binding:"required"`
	AppointmentDate time.Time  `json:"appointment_date" binding:"required"`
	StartTime       time.Time  `json:"start_time" binding:"required"`
	PartySize       int        `json:"party_size" binding:"min=1"`
	Notes           string     `json:"notes"`
	SpecialRequests string     `json:"special_requests"`
	Source          string     `json:"source"`
}

type UpdateAppointmentRequest struct {
	StaffID         *uuid.UUID `json:"staff_id"`
	CustomerName    *string    `json:"customer_name"`
	CustomerEmail   *string    `json:"customer_email" binding:"omitempty,email"`
	CustomerPhone   *string    `json:"customer_phone"`
	AppointmentDate *time.Time `json:"appointment_date"`
	StartTime       *time.Time `json:"start_time"`
	PartySize       *int       `json:"party_size" binding:"omitempty,min=1"`
	Status          *string    `json:"status"`
	Notes           *string    `json:"notes"`
	SpecialRequests *string    `json:"special_requests"`
}

type AppointmentFilters struct {
	StandardPagination
	StandardSorting
	StandardSearch
	StandardDateFilters

	// Appointment specific filters
	ServiceID *uuid.UUID `form:"service_id"`
	StaffID   *uuid.UUID `form:"staff_id"`
	Status    string     `form:"status"`
	Date      string     `form:"date"` // YYYY-MM-DD format
}

// AppointmentSortFields defines valid sort fields for appointments
var AppointmentSortFields = []string{
	"appointment_date", "start_time", "customer_name", "status", "created_at", "updated_at",
}

type CancelAppointmentRequest struct {
	Reason string `json:"reason" binding:"required"`
}

// Staff request/response types
type CreateStaffRequest struct {
	FirstName      string     `json:"first_name" binding:"required"`
	LastName       string     `json:"last_name" binding:"required"`
	Email          string     `json:"email" binding:"omitempty,email"`
	Phone          string     `json:"phone"`
	Position       string     `json:"position"`
	Department     string     `json:"department"`
	EmployeeID     string     `json:"employee_id"`
	HireDate       time.Time  `json:"hire_date"`
	Salary         *float64   `json:"salary" binding:"omitempty,min=0"`
	HourlyRate     *float64   `json:"hourly_rate" binding:"omitempty,min=0"`
	AvatarURL      string     `json:"avatar_url"`
	Bio            string     `json:"bio"`
	Specialties    []string   `json:"specialties"`
	Languages      []string   `json:"languages"`
	Certifications []string   `json:"certifications"`
	BranchID       *uuid.UUID `json:"branch_id"`
	UserID         *uuid.UUID `json:"user_id"`
}

type UpdateStaffRequest struct {
	FirstName      *string    `json:"first_name"`
	LastName       *string    `json:"last_name"`
	Email          *string    `json:"email" binding:"omitempty,email"`
	Phone          *string    `json:"phone"`
	Position       *string    `json:"position"`
	Department     *string    `json:"department"`
	EmployeeID     *string    `json:"employee_id"`
	HireDate       *time.Time `json:"hire_date"`
	Salary         *float64   `json:"salary" binding:"omitempty,min=0"`
	HourlyRate     *float64   `json:"hourly_rate" binding:"omitempty,min=0"`
	Status         *string    `json:"status"`
	AvatarURL      *string    `json:"avatar_url"`
	Bio            *string    `json:"bio"`
	Specialties    *[]string  `json:"specialties"`
	Languages      *[]string  `json:"languages"`
	Certifications *[]string  `json:"certifications"`
	IsActive       *bool      `json:"is_active"`
}

type StaffFilters struct {
	StandardPagination
	StandardSorting
	StandardSearch
	StandardDateFilters

	// Staff specific filters
	Position   string `form:"position"`
	Department string `form:"department"`
	Status     string `form:"status"`
	IsActive   *bool  `form:"is_active"`
}

// StaffSortFields defines valid sort fields for staff
var StaffSortFields = []string{
	"first_name", "last_name", "position", "department", "status", "hire_date", "created_at", "updated_at",
}

// Availability types
type AvailabilityRequest struct {
	ServiceID uuid.UUID `form:"service_id" binding:"required"`
	StaffID   uuid.UUID `form:"staff_id"`
	Date      string    `form:"date" binding:"required"` // YYYY-MM-DD format
}

type TimeSlotRequest struct {
	ServiceID uuid.UUID `form:"service_id" binding:"required"`
	StaffID   uuid.UUID `form:"staff_id"`
	Date      string    `form:"date" binding:"required"` // YYYY-MM-DD format
}

// TimeSlotFilters for service time slot filtering with standardized pagination
type TimeSlotFilters struct {
	StandardPagination
	StandardSorting
	StandardDateFilters

	// Time slot specific filters
	ServiceID     uuid.UUID  `form:"service_id" binding:"required"`
	StaffID       *uuid.UUID `form:"staff_id"`
	Date          string     `form:"date" binding:"required"` // YYYY-MM-DD
	TimeFrom      string     `form:"time_from"`               // HH:MM
	TimeTo        string     `form:"time_to"`                 // HH:MM
	Duration      *int       `form:"duration"`                // minutes
	AvailableOnly *bool      `form:"available_only"`
}

// TimeSlotSortFields defines valid sort fields for time slots
var TimeSlotSortFields = []string{
	"time", "duration", "availability", "staff_name",
}

type ServiceAvailabilityResponse struct {
	Date      string              `json:"date"`
	Available bool                `json:"available"`
	TimeSlots []ServiceTimeSlot   `json:"time_slots"`
	Staff     []StaffAvailability `json:"staff,omitempty"`
}

type ServiceTimeSlot struct {
	StartTime       string `json:"start_time"` // HH:MM format
	EndTime         string `json:"end_time"`   // HH:MM format
	Available       bool   `json:"available"`
	MaxBookings     int    `json:"max_bookings"`
	CurrentBookings int    `json:"current_bookings"`
}

// TimeSlotResponse for individual time slot details
type TimeSlotResponse struct {
	Time        string     `json:"time"`     // HH:MM format
	EndTime     string     `json:"end_time"` // HH:MM format
	Duration    int        `json:"duration"` // minutes
	IsAvailable bool       `json:"is_available"`
	ServiceID   uuid.UUID  `json:"service_id"`
	ServiceName string     `json:"service_name"`
	StaffID     *uuid.UUID `json:"staff_id,omitempty"`
	StaffName   *string    `json:"staff_name,omitempty"`
	Price       float64    `json:"price"`
	Notes       string     `json:"notes,omitempty"`
}

// TimeSlotsResponse for paginated time slots
type TimeSlotsResponse struct {
	StandardResponse
	Data []TimeSlotResponse `json:"data"`
}

type StaffAvailability struct {
	StaffID   uuid.UUID         `json:"staff_id"`
	Name      string            `json:"name"`
	Available bool              `json:"available"`
	TimeSlots []ServiceTimeSlot `json:"time_slots"`
}

// Response types
type ServicesResponse struct {
	Data       []ServiceResponse `json:"data"`
	Total      int64             `json:"total"`
	Page       int               `json:"page"`
	Limit      int               `json:"limit"`
	TotalPages int               `json:"total_pages"`
}

type ServiceResponse struct {
	ID              uuid.UUID  `json:"id"`
	MerchantID      uuid.UUID  `json:"merchant_id"`
	BranchID        *uuid.UUID `json:"branch_id"`
	Name            string     `json:"name"`
	Description     string     `json:"description"`
	Category        string     `json:"category"`
	Price           float64    `json:"price"`
	Duration        int        `json:"duration"`
	MaxCapacity     int        `json:"max_capacity"`
	RequiresStaff   bool       `json:"requires_staff"`
	PreparationTime int        `json:"preparation_time"`
	CleanupTime     int        `json:"cleanup_time"`
	ImageURL        string     `json:"image_url"`
	IsActive        bool       `json:"is_active"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
}

type AppointmentsResponse struct {
	Data       []AppointmentResponse `json:"data"`
	Total      int64                 `json:"total"`
	Page       int                   `json:"page"`
	Limit      int                   `json:"limit"`
	TotalPages int                   `json:"total_pages"`
}

type AppointmentResponse struct {
	ID              uuid.UUID        `json:"id"`
	ServiceID       uuid.UUID        `json:"service_id"`
	StaffID         *uuid.UUID       `json:"staff_id"`
	CustomerName    string           `json:"customer_name"`
	CustomerEmail   string           `json:"customer_email"`
	CustomerPhone   string           `json:"customer_phone"`
	AppointmentDate time.Time        `json:"appointment_date"`
	StartTime       time.Time        `json:"start_time"`
	EndTime         time.Time        `json:"end_time"`
	Duration        int              `json:"duration"`
	PartySize       int              `json:"party_size"`
	Status          string           `json:"status"`
	Notes           string           `json:"notes"`
	SpecialRequests string           `json:"special_requests"`
	Source          string           `json:"source"`
	TotalPrice      float64          `json:"total_price"`
	DepositPaid     float64          `json:"deposit_paid"`
	CreatedAt       time.Time        `json:"created_at"`
	UpdatedAt       time.Time        `json:"updated_at"`
	Service         *ServiceResponse `json:"service,omitempty"`
	Staff           *StaffResponse   `json:"staff,omitempty"`
}

type StaffResponse struct {
	ID             uuid.UUID  `json:"id"`
	MerchantID     uuid.UUID  `json:"merchant_id"`
	BranchID       *uuid.UUID `json:"branch_id"`
	FirstName      string     `json:"first_name"`
	LastName       string     `json:"last_name"`
	Slug           string     `json:"slug"`
	Email          string     `json:"email"`
	Phone          string     `json:"phone"`
	Position       string     `json:"position"`
	Department     string     `json:"department"`
	EmployeeID     string     `json:"employee_id"`
	HireDate       time.Time  `json:"hire_date"`
	Status         string     `json:"status"`
	AvatarURL      string     `json:"avatar_url"`
	Bio            string     `json:"bio"`
	Specialties    []string   `json:"specialties"`
	Languages      []string   `json:"languages"`
	Certifications []string   `json:"certifications"`
	IsActive       bool       `json:"is_active"`
	CreatedAt      time.Time  `json:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at"`
}

type StaffListResponse struct {
	Data       []StaffResponse `json:"data"`
	Total      int64           `json:"total"`
	Page       int             `json:"page"`
	Limit      int             `json:"limit"`
	TotalPages int             `json:"total_pages"`
}

// IngredientUsage represents ingredient usage statistics
type IngredientUsage struct {
	IngredientID   uuid.UUID `json:"ingredient_id"`
	IngredientName string    `json:"ingredient_name"`
	TotalUsed      float64   `json:"total_used"`
	Unit           string    `json:"unit"`
	Cost           float64   `json:"cost"`
	UsageCount     int       `json:"usage_count"`
}
