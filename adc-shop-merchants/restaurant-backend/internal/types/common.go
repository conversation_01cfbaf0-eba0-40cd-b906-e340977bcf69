package types

import (
	"time"

	"github.com/google/uuid"
)

// StandardPagination represents common pagination parameters
type StandardPagination struct {
	Page  int `form:"page" binding:"min=1" json:"page"`
	Limit int `form:"limit" binding:"min=1,max=100" json:"limit"`
}

// StandardSorting represents common sorting parameters
type StandardSorting struct {
	SortBy    string `form:"sort_by" json:"sort_by"`
	SortOrder string `form:"sort_order" binding:"oneof=asc desc" json:"sort_order"`
}

// StandardSearch represents common search parameters
type StandardSearch struct {
	Search string `form:"search" json:"search"`
}

// StandardResponse represents common response structure for paginated lists
type StandardResponse struct {
	Total      int64 `json:"total"`
	Page       int   `json:"page"`
	Limit      int   `json:"limit"`
	TotalPages int   `json:"total_pages"`
}

// PaginationDefaults contains default values for pagination
var PaginationDefaults = struct {
	Page  int
	Limit int
}{
	Page:  1,
	Limit: 20,
}

// SortingDefaults contains default values for sorting
var SortingDefaults = struct {
	SortBy    string
	SortOrder string
}{
	SortBy:    "created_at",
	SortOrder: "desc",
}

// ApplyPaginationDefaults applies default values to pagination if not set
func (p *StandardPagination) ApplyDefaults() {
	if p.Page <= 0 {
		p.Page = PaginationDefaults.Page
	}
	if p.Limit <= 0 {
		p.Limit = PaginationDefaults.Limit
	}
}

// ApplySortingDefaults applies default values to sorting if not set
func (s *StandardSorting) ApplyDefaults() {
	if s.SortBy == "" {
		s.SortBy = SortingDefaults.SortBy
	}
	if s.SortOrder == "" {
		s.SortOrder = SortingDefaults.SortOrder
	}
}

// CalculateTotalPages calculates total pages based on total records and limit
func CalculateTotalPages(total int64, limit int) int {
	if limit <= 0 {
		return 0
	}
	return int((total + int64(limit) - 1) / int64(limit))
}

// CalculateOffset calculates the offset for database queries
func CalculateOffset(page, limit int) int {
	if page <= 0 {
		page = 1
	}
	return (page - 1) * limit
}

// ValidateSortField checks if a sort field is in the allowed list
func ValidateSortField(field string, allowedFields []string) bool {
	for _, allowed := range allowedFields {
		if field == allowed {
			return true
		}
	}
	return false
}

// StandardDateFilters represents common date filtering parameters
type StandardDateFilters struct {
	DateFrom *time.Time `form:"date_from" time_format:"2006-01-02" json:"date_from"`
	DateTo   *time.Time `form:"date_to" time_format:"2006-01-02" json:"date_to"`
}

// StandardStatusFilter represents common status filtering
type StandardStatusFilter struct {
	Status   string `form:"status" json:"status"`
	IsActive *bool  `form:"is_active" json:"is_active"`
}

// StandardIDFilters represents common ID-based filtering
type StandardIDFilters struct {
	BranchID *uuid.UUID `form:"branch_id" json:"branch_id"`
	ShopID   *uuid.UUID `form:"shop_id" json:"shop_id"`
	UserID   *uuid.UUID `form:"user_id" json:"user_id"`
}
