package types

import (
	"restaurant-backend/internal/models"

	"github.com/google/uuid"
)

// AI Generation Request types
type CreateAIGenerationJobRequest struct {
	Type      string                       `json:"type" binding:"required,oneof=menu_image text food_images"`
	InputData AIGenerationInputDataRequest `json:"input_data" binding:"required"`
}

type AIGenerationInputDataRequest struct {
	// For menu image type
	MenuImageURL string `json:"menu_image_url,omitempty"`

	// For text type
	MenuText string `json:"menu_text,omitempty"`

	// For food images type
	FoodImageURLs []string `json:"food_image_urls,omitempty"`

	// Additional context
	CuisineType    string `json:"cuisine_type,omitempty"`
	PriceRange     string `json:"price_range,omitempty"`
	RestaurantName string `json:"restaurant_name,omitempty"`

	// AI Generation Options
	GenerateImages bool   `json:"generate_images,omitempty"`
	AIProvider     string `json:"ai_provider,omitempty"`
	ImageStyle     string `json:"image_style,omitempty"`
	ImageTheme     string `json:"image_theme,omitempty"`
	ImageQuality   string `json:"image_quality,omitempty"`
	ImageSize      string `json:"image_size,omitempty"`
}

// AI Generation Response types
type AIGenerationJobResponse struct {
	ID          uuid.UUID                     `json:"id"`
	BranchID    uuid.UUID                     `json:"branch_id"`
	UserID      uuid.UUID                     `json:"user_id"`
	Type        string                        `json:"type"`
	Status      string                        `json:"status"`
	Progress    int                           `json:"progress"`
	InputData   models.AIGenerationInputData  `json:"input_data"`
	OutputData  models.AIGenerationOutputData `json:"output_data"`
	ErrorMsg    string                        `json:"error_msg,omitempty"`
	StartedAt   *string                       `json:"started_at,omitempty"`
	CompletedAt *string                       `json:"completed_at,omitempty"`
	CreatedAt   string                        `json:"created_at"`
	UpdatedAt   string                        `json:"updated_at"`
}

type AIGenerationJobsResponse struct {
	Jobs       []AIGenerationJobResponse `json:"jobs"`
	Total      int64                     `json:"total"`
	Page       int                       `json:"page"`
	Limit      int                       `json:"limit"`
	TotalPages int                       `json:"total_pages"`
}

// AI Generation Filters
type AIGenerationJobFilters struct {
	StandardPagination
	StandardSorting

	// Job specific filters
	Type   string     `form:"type"`
	Status string     `form:"status"`
	UserID *uuid.UUID `form:"user_id"`
}

// AI Generation Sort Fields
var AIGenerationJobSortFields = []string{
	"created_at", "updated_at", "started_at", "completed_at", "status", "progress",
}

// Publish Menu Request
type PublishAIGeneratedMenuRequest struct {
	JobID     uuid.UUID                `json:"job_id" binding:"required"`
	MenuItems []PublishMenuItemRequest `json:"menu_items" binding:"required"`
	MenuInfo  *PublishMenuInfoRequest  `json:"menu_info,omitempty"`
}

type PublishMenuItemRequest struct {
	Name            string   `json:"name" binding:"required"`
	Description     string   `json:"description"`
	Price           float64  `json:"price" binding:"required,min=0"`
	CategoryName    string   `json:"category_name"`
	ImageURL        string   `json:"image_url,omitempty"`
	Ingredients     []string `json:"ingredients,omitempty"`
	Allergens       []string `json:"allergens,omitempty"`
	IsVegetarian    bool     `json:"is_vegetarian"`
	IsVegan         bool     `json:"is_vegan"`
	IsGlutenFree    bool     `json:"is_gluten_free"`
	IsSpicy         bool     `json:"is_spicy"`
	SpiceLevel      int      `json:"spice_level"`
	PreparationTime int      `json:"preparation_time,omitempty"`
	Tags            []string `json:"tags,omitempty"`
}

type PublishMenuInfoRequest struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	ImageURL    string `json:"image_url,omitempty"`
}

// Upload File Request for AI Generation
type UploadAIFileRequest struct {
	Type string `form:"type" binding:"required,oneof=menu_image food_image"`
}

// Upload File Response
type UploadAIFileResponse struct {
	FileURL string `json:"file_url"`
	Message string `json:"message"`
}

// WebSocket Message types for AI Generation
type AIGenerationStatusUpdate struct {
	JobID    uuid.UUID `json:"job_id"`
	Status   string    `json:"status"`
	Progress int       `json:"progress"`
	Message  string    `json:"message,omitempty"`
	Error    string    `json:"error,omitempty"`
}
