package types

import (
	"time"

	"github.com/google/uuid"
)

// Tag request/response types
type CreateTagRequest struct {
	Name        string `json:"name" binding:"required,min=1,max=100"`
	Description string `json:"description"`
	Category    string `json:"category" binding:"required"`
	Color       string `json:"color"`
	Icon        string `json:"icon"`
}

type UpdateTagRequest struct {
	Name        *string `json:"name" binding:"omitempty,min=1,max=100"`
	Description *string `json:"description"`
	Category    *string `json:"category"`
	Color       *string `json:"color"`
	Icon        *string `json:"icon"`
	IsActive    *bool   `json:"is_active"`
}

type TagResponse struct {
	ID          uuid.UUID `json:"id"`
	BranchID    uuid.UUID `json:"branch_id"`
	Name        string    `json:"name"`
	Slug        string    `json:"slug"`
	Description string    `json:"description"`
	Category    string    `json:"category"`
	Color       string    `json:"color"`
	Icon        string    `json:"icon"`
	UsageCount  int       `json:"usage_count"`
	IsSystem    bool      `json:"is_system"`
	IsActive    bool      `json:"is_active"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type TagsResponse struct {
	Tags       []TagResponse `json:"tags"`
	Total      int64         `json:"total"`
	Page       int           `json:"page"`
	Limit      int           `json:"limit"`
	TotalPages int           `json:"total_pages"`
}

// Tag Category request/response types
type CreateTagCategoryRequest struct {
	Name        string `json:"name" binding:"required,min=1,max=100"`
	Description string `json:"description"`
	Color       string `json:"color"`
	Icon        string `json:"icon"`
	SortOrder   int    `json:"sort_order"`
}

type UpdateTagCategoryRequest struct {
	Name        *string `json:"name" binding:"omitempty,min=1,max=100"`
	Description *string `json:"description"`
	Color       *string `json:"color"`
	Icon        *string `json:"icon"`
	SortOrder   *int    `json:"sort_order"`
	IsActive    *bool   `json:"is_active"`
}

type TagCategoryResponse struct {
	ID          uuid.UUID     `json:"id"`
	BranchID    uuid.UUID     `json:"branch_id"`
	Name        string        `json:"name"`
	Slug        string        `json:"slug"`
	Description string        `json:"description"`
	Color       string        `json:"color"`
	Icon        string        `json:"icon"`
	SortOrder   int           `json:"sort_order"`
	IsSystem    bool          `json:"is_system"`
	IsActive    bool          `json:"is_active"`
	Tags        []TagResponse `json:"tags,omitempty"`
	CreatedAt   time.Time     `json:"created_at"`
	UpdatedAt   time.Time     `json:"updated_at"`
}

type TagCategoriesResponse struct {
	Categories []TagCategoryResponse `json:"categories"`
	Total      int64                 `json:"total"`
	Page       int                   `json:"page"`
	Limit      int                   `json:"limit"`
	TotalPages int                   `json:"total_pages"`
}

// Entity Tag request/response types
type AssignTagsRequest struct {
	TagIDs []uuid.UUID `json:"tag_ids" binding:"required"`
}

type EntityTagResponse struct {
	ID         uuid.UUID   `json:"id"`
	TagID      uuid.UUID   `json:"tag_id"`
	EntityType string      `json:"entity_type"`
	EntityID   uuid.UUID   `json:"entity_id"`
	Tag        TagResponse `json:"tag"`
	CreatedAt  time.Time   `json:"created_at"`
}

// Filter types
type TagFilters struct {
	StandardPagination
	StandardSorting
	Category string `form:"category"`
	IsActive *bool  `form:"is_active"`
	IsSystem *bool  `form:"is_system"`
	Search   string `form:"search"`
}

type TagCategoryFilters struct {
	StandardPagination
	StandardSorting
	IsActive *bool  `form:"is_active"`
	IsSystem *bool  `form:"is_system"`
	Search   string `form:"search"`
}

type EntityTagFilters struct {
	EntityType string      `form:"entity_type"`
	EntityID   *uuid.UUID  `form:"entity_id"`
	TagIDs     []uuid.UUID `form:"tag_ids"`
	Category   string      `form:"category"`
}

// Tag suggestion types
type TagSuggestionRequest struct {
	Query      string `json:"query" binding:"required,min=1"`
	Category   string `json:"category"`
	EntityType string `json:"entity_type"`
	Limit      int    `json:"limit"`
}

type TagSuggestionResponse struct {
	Tags []TagResponse `json:"tags"`
}

// Popular tags response
type PopularTagsResponse struct {
	Tags []TagResponse `json:"tags"`
}

// Tag analytics types
type TagAnalyticsResponse struct {
	TotalTags          int                    `json:"total_tags"`
	TotalCategories    int                    `json:"total_categories"`
	MostUsedTags       []TagResponse          `json:"most_used_tags"`
	TagsByCategory     map[string]int         `json:"tags_by_category"`
	RecentlyCreated    []TagResponse          `json:"recently_created"`
	UnusedTags         []TagResponse          `json:"unused_tags"`
	TagUsageOverTime   []TagUsageDataPoint    `json:"tag_usage_over_time"`
	CategoryDistribution []CategoryDataPoint  `json:"category_distribution"`
}

type TagUsageDataPoint struct {
	Date  string `json:"date"`
	Count int    `json:"count"`
}

type CategoryDataPoint struct {
	Category string `json:"category"`
	Count    int    `json:"count"`
	Color    string `json:"color"`
}

// Sort fields
var TagSortFields = []string{
	"name", "category", "usage_count", "created_at", "updated_at",
}

var TagCategorySortFields = []string{
	"name", "sort_order", "created_at", "updated_at",
}
