package types

import (
	"time"

	"github.com/google/uuid"
)

// Campaign request/response types
type CreateCampaignRequest struct {
	Name           string     `json:"name" binding:"required"`
	Description    string     `json:"description"`
	Type           string     `json:"type" binding:"required,oneof=email sms push"`
	TemplateID     *uuid.UUID `json:"template_id"`
	SegmentID      *uuid.UUID `json:"segment_id"`
	Subject        string     `json:"subject"`
	Content        string     `json:"content" binding:"required"`
	ScheduledAt    *time.Time `json:"scheduled_at"`
	TargetAudience string     `json:"target_audience" binding:"required,oneof=all segment custom"`
	Recipients     []string   `json:"recipients"`
	Settings       CampaignSettingsRequest `json:"settings"`
}

type UpdateCampaignRequest struct {
	Name           *string     `json:"name"`
	Description    *string     `json:"description"`
	Type           *string     `json:"type" binding:"omitempty,oneof=email sms push"`
	TemplateID     *uuid.UUID  `json:"template_id"`
	SegmentID      *uuid.UUID  `json:"segment_id"`
	Subject        *string     `json:"subject"`
	Content        *string     `json:"content"`
	ScheduledAt    *time.Time  `json:"scheduled_at"`
	Status         *string     `json:"status" binding:"omitempty,oneof=draft scheduled running completed cancelled"`
	TargetAudience *string     `json:"target_audience" binding:"omitempty,oneof=all segment custom"`
	Recipients     *[]string   `json:"recipients"`
	Settings       *CampaignSettingsRequest `json:"settings"`
}

type CampaignFilters struct {
	Type           string `form:"type"`
	Status         string `form:"status"`
	TargetAudience string `form:"target_audience"`
	TemplateID     uuid.UUID `form:"template_id"`
	SegmentID      uuid.UUID `form:"segment_id"`
	Search         string `form:"search"`
	Page           int    `form:"page" binding:"min=1"`
	Limit          int    `form:"limit" binding:"min=1,max=100"`
}

type ExecuteCampaignRequest struct {
	SendNow bool `json:"send_now"`
}

// Template request/response types
type CreateTemplateRequest struct {
	Name        string   `json:"name" binding:"required"`
	Description string   `json:"description"`
	Type        string   `json:"type" binding:"required,oneof=email sms push"`
	Category    string   `json:"category" binding:"required"`
	Subject     string   `json:"subject"`
	Content     string   `json:"content" binding:"required"`
	Variables   []string `json:"variables"`
	IsDefault   bool     `json:"is_default"`
}

type UpdateTemplateRequest struct {
	Name        *string   `json:"name"`
	Description *string   `json:"description"`
	Type        *string   `json:"type" binding:"omitempty,oneof=email sms push"`
	Category    *string   `json:"category"`
	Subject     *string   `json:"subject"`
	Content     *string   `json:"content"`
	Variables   *[]string `json:"variables"`
	IsDefault   *bool     `json:"is_default"`
	IsActive    *bool     `json:"is_active"`
}

type TemplateFilters struct {
	Type      string `form:"type"`
	Category  string `form:"category"`
	IsDefault *bool  `form:"is_default"`
	IsActive  *bool  `form:"is_active"`
	Search    string `form:"search"`
	Page      int    `form:"page" binding:"min=1"`
	Limit     int    `form:"limit" binding:"min=1,max=100"`
}

// Segment request/response types
type CreateSegmentRequest struct {
	Name        string          `json:"name" binding:"required"`
	Description string          `json:"description"`
	Type        string          `json:"type" binding:"required,oneof=demographic behavioral geographic"`
	Criteria    SegmentCriteriaRequest `json:"criteria" binding:"required"`
}

type UpdateSegmentRequest struct {
	Name        *string          `json:"name"`
	Description *string          `json:"description"`
	Type        *string          `json:"type" binding:"omitempty,oneof=demographic behavioral geographic"`
	Criteria    *SegmentCriteriaRequest `json:"criteria"`
	IsActive    *bool            `json:"is_active"`
}

type SegmentFilters struct {
	Type     string `form:"type"`
	IsActive *bool  `form:"is_active"`
	Search   string `form:"search"`
	Page     int    `form:"page" binding:"min=1"`
	Limit    int    `form:"limit" binding:"min=1,max=100"`
}

// Supporting request types
type CampaignSettingsRequest struct {
	SendTime         string            `json:"send_time"`
	Timezone         string            `json:"timezone"`
	TrackOpens       bool              `json:"track_opens"`
	TrackClicks      bool              `json:"track_clicks"`
	AllowUnsubscribe bool              `json:"allow_unsubscribe"`
	ReplyToEmail     string            `json:"reply_to_email"`
	FromName         string            `json:"from_name"`
	CustomFields     map[string]string `json:"custom_fields"`
}

type SegmentCriteriaRequest struct {
	// Demographic criteria
	AgeRange      *AgeRangeRequest `json:"age_range,omitempty"`
	Gender        string           `json:"gender,omitempty"`
	Location      string           `json:"location,omitempty"`
	
	// Behavioral criteria
	LastVisitDays     *int     `json:"last_visit_days,omitempty"`
	TotalOrders       *int     `json:"total_orders,omitempty"`
	TotalSpent        *float64 `json:"total_spent,omitempty"`
	AverageOrderValue *float64 `json:"average_order_value,omitempty"`
	FavoriteCategory  string   `json:"favorite_category,omitempty"`
	
	// Engagement criteria
	EmailEngagement   string   `json:"email_engagement,omitempty"`
	SMSEngagement     string   `json:"sms_engagement,omitempty"`
	ReviewsLeft       *int     `json:"reviews_left,omitempty"`
	ReferralsMade     *int     `json:"referrals_made,omitempty"`
	
	// Custom criteria
	CustomFilters     map[string]interface{} `json:"custom_filters,omitempty"`
}

type AgeRangeRequest struct {
	Min int `json:"min" binding:"min=0,max=120"`
	Max int `json:"max" binding:"min=0,max=120"`
}

// Response types
type CampaignsResponse struct {
	Data       []CampaignResponse `json:"data"`
	Total      int64              `json:"total"`
	Page       int                `json:"page"`
	Limit      int                `json:"limit"`
	TotalPages int                `json:"total_pages"`
}

type CampaignResponse struct {
	ID               uuid.UUID  `json:"id"`
	MerchantID       uuid.UUID  `json:"merchant_id"`
	Name             string     `json:"name"`
	Description      string     `json:"description"`
	Type             string     `json:"type"`
	Status           string     `json:"status"`
	TemplateID       *uuid.UUID `json:"template_id"`
	SegmentID        *uuid.UUID `json:"segment_id"`
	Subject          string     `json:"subject"`
	Content          string     `json:"content"`
	ScheduledAt      *time.Time `json:"scheduled_at"`
	StartedAt        *time.Time `json:"started_at"`
	CompletedAt      *time.Time `json:"completed_at"`
	TargetAudience   string     `json:"target_audience"`
	Recipients       []string   `json:"recipients"`
	TotalRecipients  int        `json:"total_recipients"`
	SentCount        int        `json:"sent_count"`
	DeliveredCount   int        `json:"delivered_count"`
	OpenedCount      int        `json:"opened_count"`
	ClickedCount     int        `json:"clicked_count"`
	UnsubscribeCount int        `json:"unsubscribe_count"`
	BounceCount      int        `json:"bounce_count"`
	Settings         CampaignSettingsResponse `json:"settings"`
	CreatedAt        time.Time  `json:"created_at"`
	UpdatedAt        time.Time  `json:"updated_at"`
	Template         *TemplateResponse `json:"template,omitempty"`
	Segment          *SegmentResponse  `json:"segment,omitempty"`
}

type TemplatesResponse struct {
	Data       []TemplateResponse `json:"data"`
	Total      int64              `json:"total"`
	Page       int                `json:"page"`
	Limit      int                `json:"limit"`
	TotalPages int                `json:"total_pages"`
}

type TemplateResponse struct {
	ID          uuid.UUID `json:"id"`
	MerchantID  uuid.UUID `json:"merchant_id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Type        string    `json:"type"`
	Category    string    `json:"category"`
	Subject     string    `json:"subject"`
	Content     string    `json:"content"`
	Variables   []string  `json:"variables"`
	IsDefault   bool      `json:"is_default"`
	IsActive    bool      `json:"is_active"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type SegmentsResponse struct {
	Data       []SegmentResponse `json:"data"`
	Total      int64             `json:"total"`
	Page       int               `json:"page"`
	Limit      int               `json:"limit"`
	TotalPages int               `json:"total_pages"`
}

type SegmentResponse struct {
	ID            uuid.UUID `json:"id"`
	MerchantID    uuid.UUID `json:"merchant_id"`
	Name          string    `json:"name"`
	Description   string    `json:"description"`
	Type          string    `json:"type"`
	Criteria      SegmentCriteriaResponse `json:"criteria"`
	CustomerCount int       `json:"customer_count"`
	LastUpdated   time.Time `json:"last_updated"`
	IsActive      bool      `json:"is_active"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

type CampaignSettingsResponse struct {
	SendTime         string            `json:"send_time"`
	Timezone         string            `json:"timezone"`
	TrackOpens       bool              `json:"track_opens"`
	TrackClicks      bool              `json:"track_clicks"`
	AllowUnsubscribe bool              `json:"allow_unsubscribe"`
	ReplyToEmail     string            `json:"reply_to_email"`
	FromName         string            `json:"from_name"`
	CustomFields     map[string]string `json:"custom_fields"`
}

type SegmentCriteriaResponse struct {
	// Demographic criteria
	AgeRange      *AgeRangeResponse `json:"age_range,omitempty"`
	Gender        string            `json:"gender,omitempty"`
	Location      string            `json:"location,omitempty"`
	
	// Behavioral criteria
	LastVisitDays     *int     `json:"last_visit_days,omitempty"`
	TotalOrders       *int     `json:"total_orders,omitempty"`
	TotalSpent        *float64 `json:"total_spent,omitempty"`
	AverageOrderValue *float64 `json:"average_order_value,omitempty"`
	FavoriteCategory  string   `json:"favorite_category,omitempty"`
	
	// Engagement criteria
	EmailEngagement   string   `json:"email_engagement,omitempty"`
	SMSEngagement     string   `json:"sms_engagement,omitempty"`
	ReviewsLeft       *int     `json:"reviews_left,omitempty"`
	ReferralsMade     *int     `json:"referrals_made,omitempty"`
	
	// Custom criteria
	CustomFilters     map[string]interface{} `json:"custom_filters,omitempty"`
}

type AgeRangeResponse struct {
	Min int `json:"min"`
	Max int `json:"max"`
}

// Analytics response types
type CommunicationAnalyticsOverview struct {
	TotalCampaigns   int     `json:"total_campaigns"`
	ActiveCampaigns  int     `json:"active_campaigns"`
	TotalSent        int     `json:"total_sent"`
	TotalDelivered   int     `json:"total_delivered"`
	TotalOpened      int     `json:"total_opened"`
	TotalClicked     int     `json:"total_clicked"`
	AverageOpenRate  float64 `json:"average_open_rate"`
	AverageClickRate float64 `json:"average_click_rate"`
	TopPerformingCampaign string `json:"top_performing_campaign"`
}

type CampaignAnalyticsResponse struct {
	CampaignID       uuid.UUID `json:"campaign_id"`
	CampaignName     string    `json:"campaign_name"`
	TotalRecipients  int       `json:"total_recipients"`
	SentCount        int       `json:"sent_count"`
	DeliveredCount   int       `json:"delivered_count"`
	OpenedCount      int       `json:"opened_count"`
	ClickedCount     int       `json:"clicked_count"`
	UnsubscribeCount int       `json:"unsubscribe_count"`
	BounceCount      int       `json:"bounce_count"`
	OpenRate         float64   `json:"open_rate"`
	ClickRate        float64   `json:"click_rate"`
	UnsubscribeRate  float64   `json:"unsubscribe_rate"`
	BounceRate       float64   `json:"bounce_rate"`
	CreatedAt        time.Time `json:"created_at"`
	CompletedAt      *time.Time `json:"completed_at"`
}
