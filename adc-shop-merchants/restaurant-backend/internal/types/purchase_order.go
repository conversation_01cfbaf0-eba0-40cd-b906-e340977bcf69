package types

import (
	"time"

	"github.com/google/uuid"
)

// PurchaseOrderStatus represents the status of a purchase order
type PurchaseOrderStatus string

const (
	PurchaseOrderStatusPending    PurchaseOrderStatus = "pending"
	PurchaseOrderStatusApproved   PurchaseOrderStatus = "approved"
	PurchaseOrderStatusOrdered    PurchaseOrderStatus = "ordered"
	PurchaseOrderStatusReceived   PurchaseOrderStatus = "received"
	PurchaseOrderStatusPartial    PurchaseOrderStatus = "partial"
	PurchaseOrderStatusCancelled  PurchaseOrderStatus = "cancelled"
	PurchaseOrderStatusCompleted  PurchaseOrderStatus = "completed"
)

// PurchaseOrder represents a purchase order
type PurchaseOrder struct {
	ID                uuid.UUID           `json:"id" db:"id"`
	OrderNumber       string              `json:"order_number" db:"order_number"`
	ShopID            uuid.UUID           `json:"shop_id" db:"shop_id"`
	BranchID          uuid.UUID           `json:"branch_id" db:"branch_id"`
	SupplierID        uuid.UUID           `json:"supplier_id" db:"supplier_id"`
	SupplierName      string              `json:"supplier_name" db:"supplier_name"`
	Status            PurchaseOrderStatus `json:"status" db:"status"`
	TotalAmount       float64             `json:"total_amount" db:"total_amount"`
	Currency          string              `json:"currency" db:"currency"`
	ExpectedDelivery  *time.Time          `json:"expected_delivery" db:"expected_delivery"`
	ActualDelivery    *time.Time          `json:"actual_delivery" db:"actual_delivery"`
	Notes             string              `json:"notes" db:"notes"`
	CreatedBy         uuid.UUID           `json:"created_by" db:"created_by"`
	CreatedAt         time.Time           `json:"created_at" db:"created_at"`
	UpdatedAt         time.Time           `json:"updated_at" db:"updated_at"`
	Items             []PurchaseOrderItem `json:"items,omitempty"`
}

// PurchaseOrderItem represents an item in a purchase order
type PurchaseOrderItem struct {
	ID               uuid.UUID `json:"id" db:"id"`
	PurchaseOrderID  uuid.UUID `json:"purchase_order_id" db:"purchase_order_id"`
	IngredientID     uuid.UUID `json:"ingredient_id" db:"ingredient_id"`
	IngredientName   string    `json:"ingredient_name" db:"ingredient_name"`
	Quantity         float64   `json:"quantity" db:"quantity"`
	Unit             string    `json:"unit" db:"unit"`
	UnitPrice        float64   `json:"unit_price" db:"unit_price"`
	TotalPrice       float64   `json:"total_price" db:"total_price"`
	ReceivedQuantity float64   `json:"received_quantity" db:"received_quantity"`
	CreatedAt        time.Time `json:"created_at" db:"created_at"`
	UpdatedAt        time.Time `json:"updated_at" db:"updated_at"`
}

// PurchaseOrderFilters represents filters for purchase orders
type PurchaseOrderFilters struct {
	// Filtering
	SupplierID       *uuid.UUID           `form:"supplier_id"`
	Status           *PurchaseOrderStatus `form:"status"`
	CreatedBy        *uuid.UUID           `form:"created_by"`
	DateFrom         *time.Time           `form:"date_from" time_format:"2006-01-02"`
	DateTo           *time.Time           `form:"date_to" time_format:"2006-01-02"`
	ExpectedFrom     *time.Time           `form:"expected_from" time_format:"2006-01-02"`
	ExpectedTo       *time.Time           `form:"expected_to" time_format:"2006-01-02"`
	MinAmount        *float64             `form:"min_amount" binding:"omitempty,min=0"`
	MaxAmount        *float64             `form:"max_amount" binding:"omitempty,min=0"`
	Search           string               `form:"search"`
	
	// Sorting
	SortBy    string `form:"sort_by" binding:"omitempty,oneof=order_number supplier_name status total_amount expected_delivery created_at updated_at"`
	SortOrder string `form:"sort_order" binding:"omitempty,oneof=asc desc"`
	
	// Pagination
	Page  int `form:"page" binding:"min=1"`
	Limit int `form:"limit" binding:"min=1,max=100"`
}

// PurchaseOrdersResponse represents the response for purchase orders list
type PurchaseOrdersResponse struct {
	Data       []PurchaseOrder `json:"data"`
	Total      int64           `json:"total"`
	Page       int             `json:"page"`
	Limit      int             `json:"limit"`
	TotalPages int             `json:"total_pages"`
}

// CreatePurchaseOrderRequest represents the request to create a purchase order
type CreatePurchaseOrderRequest struct {
	SupplierID       uuid.UUID                     `json:"supplier_id" binding:"required"`
	ExpectedDelivery *time.Time                    `json:"expected_delivery"`
	Notes            string                        `json:"notes"`
	Items            []CreatePurchaseOrderItemRequest `json:"items" binding:"required,min=1"`
}

// CreatePurchaseOrderItemRequest represents the request to create a purchase order item
type CreatePurchaseOrderItemRequest struct {
	IngredientID uuid.UUID `json:"ingredient_id" binding:"required"`
	Quantity     float64   `json:"quantity" binding:"required,min=0"`
	Unit         string    `json:"unit" binding:"required"`
	UnitPrice    float64   `json:"unit_price" binding:"required,min=0"`
}

// UpdatePurchaseOrderRequest represents the request to update a purchase order
type UpdatePurchaseOrderRequest struct {
	Status           *PurchaseOrderStatus `json:"status"`
	ExpectedDelivery *time.Time           `json:"expected_delivery"`
	ActualDelivery   *time.Time           `json:"actual_delivery"`
	Notes            *string              `json:"notes"`
}

// UpdatePurchaseOrderItemRequest represents the request to update a purchase order item
type UpdatePurchaseOrderItemRequest struct {
	ReceivedQuantity *float64 `json:"received_quantity" binding:"omitempty,min=0"`
}
