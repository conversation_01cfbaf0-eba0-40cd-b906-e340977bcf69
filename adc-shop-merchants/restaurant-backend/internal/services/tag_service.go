package services

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/types"
	"restaurant-backend/internal/utils/pagination"
)

type TagService interface {
	// Tag operations
	CreateTag(ctx context.Context, branchID uuid.UUID, req types.CreateTagRequest) (*types.TagResponse, error)
	GetTag(ctx context.Context, id uuid.UUID) (*types.TagResponse, error)
	GetTagBySlug(ctx context.Context, branchID uuid.UUID, slug string) (*types.TagResponse, error)
	UpdateTag(ctx context.Context, id uuid.UUID, req types.UpdateTagRequest) (*types.TagResponse, error)
	DeleteTag(ctx context.Context, id uuid.UUID) error
	GetTags(ctx context.Context, branchID uuid.UUID, filters types.TagFilters) (*types.TagsResponse, error)

	// Tag Category operations
	CreateTagCategory(ctx context.Context, branchID uuid.UUID, req types.CreateTagCategoryRequest) (*types.TagCategoryResponse, error)
	GetTagCategory(ctx context.Context, id uuid.UUID) (*types.TagCategoryResponse, error)
	GetTagCategoryBySlug(ctx context.Context, branchID uuid.UUID, slug string) (*types.TagCategoryResponse, error)
	UpdateTagCategory(ctx context.Context, id uuid.UUID, req types.UpdateTagCategoryRequest) (*types.TagCategoryResponse, error)
	DeleteTagCategory(ctx context.Context, id uuid.UUID) error
	GetTagCategories(ctx context.Context, branchID uuid.UUID, filters types.TagCategoryFilters) (*types.TagCategoriesResponse, error)

	// Entity Tag operations
	AssignTagsToEntity(ctx context.Context, entityType string, entityID uuid.UUID, req types.AssignTagsRequest) error
	RemoveTagsFromEntity(ctx context.Context, entityType string, entityID uuid.UUID, tagIDs []uuid.UUID) error
	GetEntityTags(ctx context.Context, entityType string, entityID uuid.UUID) ([]types.EntityTagResponse, error)

	// Utility operations
	GetPopularTags(ctx context.Context, branchID uuid.UUID, limit int) (*types.PopularTagsResponse, error)
	GetTagsByCategory(ctx context.Context, branchID uuid.UUID, category string) (*types.TagsResponse, error)
	SearchTags(ctx context.Context, branchID uuid.UUID, req types.TagSuggestionRequest) (*types.TagSuggestionResponse, error)
	GetTagAnalytics(ctx context.Context, branchID uuid.UUID) (*types.TagAnalyticsResponse, error)
}

type tagService struct {
	tagRepo repositories.TagRepository
	logger  *logrus.Logger
}

func NewTagService(tagRepo repositories.TagRepository, logger *logrus.Logger) TagService {
	return &tagService{
		tagRepo: tagRepo,
		logger:  logger,
	}
}

// Tag operations
func (s *tagService) CreateTag(ctx context.Context, branchID uuid.UUID, req types.CreateTagRequest) (*types.TagResponse, error) {
	tag := &models.Tag{
		BranchID:    branchID,
		Name:        req.Name,
		Description: req.Description,
		Category:    req.Category,
		Color:       req.Color,
		Icon:        req.Icon,
	}

	// Set default color if not provided
	if tag.Color == "" {
		tag.Color = "#8a745c"
	}

	if err := s.tagRepo.CreateTag(ctx, tag); err != nil {
		s.logger.WithError(err).Error("Failed to create tag")
		return nil, fmt.Errorf("failed to create tag: %w", err)
	}

	return s.modelToResponse(tag), nil
}

func (s *tagService) GetTag(ctx context.Context, id uuid.UUID) (*types.TagResponse, error) {
	tag, err := s.tagRepo.GetTagByID(ctx, id)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get tag")
		return nil, fmt.Errorf("failed to get tag: %w", err)
	}

	return s.modelToResponse(tag), nil
}

func (s *tagService) GetTagBySlug(ctx context.Context, branchID uuid.UUID, slug string) (*types.TagResponse, error) {
	tag, err := s.tagRepo.GetTagBySlug(ctx, branchID, slug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get tag by slug")
		return nil, fmt.Errorf("failed to get tag: %w", err)
	}

	return s.modelToResponse(tag), nil
}

func (s *tagService) UpdateTag(ctx context.Context, id uuid.UUID, req types.UpdateTagRequest) (*types.TagResponse, error) {
	tag, err := s.tagRepo.GetTagByID(ctx, id)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get tag for update")
		return nil, fmt.Errorf("failed to get tag: %w", err)
	}

	// Update fields if provided
	if req.Name != nil {
		tag.Name = *req.Name
		tag.Slug = models.Slugify(*req.Name)
	}
	if req.Description != nil {
		tag.Description = *req.Description
	}
	if req.Category != nil {
		tag.Category = *req.Category
	}
	if req.Color != nil {
		tag.Color = *req.Color
	}
	if req.Icon != nil {
		tag.Icon = *req.Icon
	}
	if req.IsActive != nil {
		tag.IsActive = *req.IsActive
	}

	if err := s.tagRepo.UpdateTag(ctx, tag); err != nil {
		s.logger.WithError(err).Error("Failed to update tag")
		return nil, fmt.Errorf("failed to update tag: %w", err)
	}

	return s.modelToResponse(tag), nil
}

func (s *tagService) DeleteTag(ctx context.Context, id uuid.UUID) error {
	if err := s.tagRepo.DeleteTag(ctx, id); err != nil {
		s.logger.WithError(err).Error("Failed to delete tag")
		return fmt.Errorf("failed to delete tag: %w", err)
	}

	return nil
}

func (s *tagService) GetTags(ctx context.Context, branchID uuid.UUID, filters types.TagFilters) (*types.TagsResponse, error) {
	// Apply pagination and sorting defaults
	pagination.ApplyStandardDefaults(&filters.StandardPagination, &filters.StandardSorting)
	pagination.ValidateAndApplySorting(&filters.StandardSorting, types.TagSortFields, "name")

	tags, total, err := s.tagRepo.GetTagsByBranchID(ctx, branchID, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get tags")
		return nil, fmt.Errorf("failed to get tags: %w", err)
	}

	response := &types.TagsResponse{
		Tags:       make([]types.TagResponse, len(tags)),
		Total:      total,
		Page:       filters.Page,
		Limit:      filters.Limit,
		TotalPages: int((total + int64(filters.Limit) - 1) / int64(filters.Limit)),
	}

	for i, tag := range tags {
		response.Tags[i] = *s.modelToResponse(&tag)
	}

	return response, nil
}

// Helper function to convert model to response
func (s *tagService) modelToResponse(tag *models.Tag) *types.TagResponse {
	return &types.TagResponse{
		ID:          tag.ID,
		BranchID:    tag.BranchID,
		Name:        tag.Name,
		Slug:        tag.Slug,
		Description: tag.Description,
		Category:    tag.Category,
		Color:       tag.Color,
		Icon:        tag.Icon,
		UsageCount:  tag.UsageCount,
		IsSystem:    tag.IsSystem,
		IsActive:    tag.IsActive,
		CreatedAt:   tag.CreatedAt,
		UpdatedAt:   tag.UpdatedAt,
	}
}

// Helper function to convert category model to response
func (s *tagService) categoryModelToResponse(category *models.TagCategory) *types.TagCategoryResponse {
	response := &types.TagCategoryResponse{
		ID:          category.ID,
		BranchID:    category.BranchID,
		Name:        category.Name,
		Slug:        category.Slug,
		Description: category.Description,
		Color:       category.Color,
		Icon:        category.Icon,
		SortOrder:   category.SortOrder,
		IsSystem:    category.IsSystem,
		IsActive:    category.IsActive,
		CreatedAt:   category.CreatedAt,
		UpdatedAt:   category.UpdatedAt,
	}

	// Tags will be loaded separately if needed

	return response
}

// Tag Category operations
func (s *tagService) CreateTagCategory(ctx context.Context, branchID uuid.UUID, req types.CreateTagCategoryRequest) (*types.TagCategoryResponse, error) {
	category := &models.TagCategory{
		BranchID:    branchID,
		Name:        req.Name,
		Description: req.Description,
		Color:       req.Color,
		Icon:        req.Icon,
		SortOrder:   req.SortOrder,
	}

	// Set default color if not provided
	if category.Color == "" {
		category.Color = "#8a745c"
	}

	if err := s.tagRepo.CreateTagCategory(ctx, category); err != nil {
		s.logger.WithError(err).Error("Failed to create tag category")
		return nil, fmt.Errorf("failed to create tag category: %w", err)
	}

	return s.categoryModelToResponse(category), nil
}

func (s *tagService) GetTagCategory(ctx context.Context, id uuid.UUID) (*types.TagCategoryResponse, error) {
	category, err := s.tagRepo.GetTagCategoryByID(ctx, id)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get tag category")
		return nil, fmt.Errorf("failed to get tag category: %w", err)
	}

	return s.categoryModelToResponse(category), nil
}

func (s *tagService) GetTagCategoryBySlug(ctx context.Context, branchID uuid.UUID, slug string) (*types.TagCategoryResponse, error) {
	category, err := s.tagRepo.GetTagCategoryBySlug(ctx, branchID, slug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get tag category by slug")
		return nil, fmt.Errorf("failed to get tag category: %w", err)
	}

	return s.categoryModelToResponse(category), nil
}

func (s *tagService) UpdateTagCategory(ctx context.Context, id uuid.UUID, req types.UpdateTagCategoryRequest) (*types.TagCategoryResponse, error) {
	category, err := s.tagRepo.GetTagCategoryByID(ctx, id)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get tag category for update")
		return nil, fmt.Errorf("failed to get tag category: %w", err)
	}

	// Update fields if provided
	if req.Name != nil {
		category.Name = *req.Name
		category.Slug = models.Slugify(*req.Name)
	}
	if req.Description != nil {
		category.Description = *req.Description
	}
	if req.Color != nil {
		category.Color = *req.Color
	}
	if req.Icon != nil {
		category.Icon = *req.Icon
	}
	if req.SortOrder != nil {
		category.SortOrder = *req.SortOrder
	}
	if req.IsActive != nil {
		category.IsActive = *req.IsActive
	}

	if err := s.tagRepo.UpdateTagCategory(ctx, category); err != nil {
		s.logger.WithError(err).Error("Failed to update tag category")
		return nil, fmt.Errorf("failed to update tag category: %w", err)
	}

	return s.categoryModelToResponse(category), nil
}

func (s *tagService) DeleteTagCategory(ctx context.Context, id uuid.UUID) error {
	if err := s.tagRepo.DeleteTagCategory(ctx, id); err != nil {
		s.logger.WithError(err).Error("Failed to delete tag category")
		return fmt.Errorf("failed to delete tag category: %w", err)
	}

	return nil
}

func (s *tagService) GetTagCategories(ctx context.Context, branchID uuid.UUID, filters types.TagCategoryFilters) (*types.TagCategoriesResponse, error) {
	// Apply pagination and sorting defaults
	pagination.ApplyStandardDefaults(&filters.StandardPagination, &filters.StandardSorting)
	pagination.ValidateAndApplySorting(&filters.StandardSorting, types.TagCategorySortFields, "sort_order")

	categories, total, err := s.tagRepo.GetTagCategoriesByBranchID(ctx, branchID, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get tag categories")
		return nil, fmt.Errorf("failed to get tag categories: %w", err)
	}

	response := &types.TagCategoriesResponse{
		Categories: make([]types.TagCategoryResponse, len(categories)),
		Total:      total,
		Page:       filters.Page,
		Limit:      filters.Limit,
		TotalPages: int((total + int64(filters.Limit) - 1) / int64(filters.Limit)),
	}

	for i, category := range categories {
		response.Categories[i] = *s.categoryModelToResponse(&category)
	}

	return response, nil
}

// Entity Tag operations
func (s *tagService) AssignTagsToEntity(ctx context.Context, entityType string, entityID uuid.UUID, req types.AssignTagsRequest) error {
	if err := s.tagRepo.AssignTagsToEntity(ctx, entityType, entityID, req.TagIDs); err != nil {
		s.logger.WithError(err).Error("Failed to assign tags to entity")
		return fmt.Errorf("failed to assign tags to entity: %w", err)
	}

	return nil
}

func (s *tagService) RemoveTagsFromEntity(ctx context.Context, entityType string, entityID uuid.UUID, tagIDs []uuid.UUID) error {
	if err := s.tagRepo.RemoveTagsFromEntity(ctx, entityType, entityID, tagIDs); err != nil {
		s.logger.WithError(err).Error("Failed to remove tags from entity")
		return fmt.Errorf("failed to remove tags from entity: %w", err)
	}

	return nil
}

func (s *tagService) GetEntityTags(ctx context.Context, entityType string, entityID uuid.UUID) ([]types.EntityTagResponse, error) {
	entityTags, err := s.tagRepo.GetEntityTags(ctx, entityType, entityID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get entity tags")
		return nil, fmt.Errorf("failed to get entity tags: %w", err)
	}

	response := make([]types.EntityTagResponse, len(entityTags))
	for i, entityTag := range entityTags {
		response[i] = types.EntityTagResponse{
			ID:         entityTag.ID,
			TagID:      entityTag.TagID,
			EntityType: entityTag.EntityType,
			EntityID:   entityTag.EntityID,
			Tag:        *s.modelToResponse(&entityTag.Tag),
			CreatedAt:  entityTag.CreatedAt,
		}
	}

	return response, nil
}

// Utility operations
func (s *tagService) GetPopularTags(ctx context.Context, branchID uuid.UUID, limit int) (*types.PopularTagsResponse, error) {
	if limit <= 0 {
		limit = 10
	}

	tags, err := s.tagRepo.GetPopularTags(ctx, branchID, limit)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get popular tags")
		return nil, fmt.Errorf("failed to get popular tags: %w", err)
	}

	response := &types.PopularTagsResponse{
		Tags: make([]types.TagResponse, len(tags)),
	}

	for i, tag := range tags {
		response.Tags[i] = *s.modelToResponse(&tag)
	}

	return response, nil
}

func (s *tagService) GetTagsByCategory(ctx context.Context, branchID uuid.UUID, category string) (*types.TagsResponse, error) {
	tags, err := s.tagRepo.GetTagsByCategory(ctx, branchID, category)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get tags by category")
		return nil, fmt.Errorf("failed to get tags by category: %w", err)
	}

	response := &types.TagsResponse{
		Tags:       make([]types.TagResponse, len(tags)),
		Total:      int64(len(tags)),
		Page:       1,
		Limit:      len(tags),
		TotalPages: 1,
	}

	for i, tag := range tags {
		response.Tags[i] = *s.modelToResponse(&tag)
	}

	return response, nil
}

func (s *tagService) SearchTags(ctx context.Context, branchID uuid.UUID, req types.TagSuggestionRequest) (*types.TagSuggestionResponse, error) {
	limit := req.Limit
	if limit <= 0 {
		limit = 10
	}

	tags, err := s.tagRepo.SearchTags(ctx, branchID, req.Query, limit)
	if err != nil {
		s.logger.WithError(err).Error("Failed to search tags")
		return nil, fmt.Errorf("failed to search tags: %w", err)
	}

	response := &types.TagSuggestionResponse{
		Tags: make([]types.TagResponse, len(tags)),
	}

	for i, tag := range tags {
		response.Tags[i] = *s.modelToResponse(&tag)
	}

	return response, nil
}

func (s *tagService) GetTagAnalytics(ctx context.Context, branchID uuid.UUID) (*types.TagAnalyticsResponse, error) {
	analytics, err := s.tagRepo.GetTagAnalytics(ctx, branchID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get tag analytics")
		return nil, fmt.Errorf("failed to get tag analytics: %w", err)
	}

	return analytics, nil
}
