package services

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/types"
)

// ReviewService defines the interface for review business logic
type ReviewService interface {
	GetReviews(ctx context.Context, branchID uuid.UUID, filters types.ReviewFilters) (*types.ReviewsListResponse, error)
	GetReview(ctx context.Context, reviewID uuid.UUID) (*models.Review, error)
	GetReviewStats(ctx context.Context, branchID uuid.UUID, period string) (*types.ReviewStats, error)
	GetRecentReviews(ctx context.Context, branchID uuid.UUID, limit int) ([]models.Review, error)
	GetPendingReviews(ctx context.Context, branchID uuid.UUID) ([]models.Review, error)
	GetReviewInsights(ctx context.Context, branchID uuid.UUID, period string) (*types.ReviewInsights, error)
	CreateReview(ctx context.Context, branchID uuid.UUID, req types.CreateReviewRequest) (*models.Review, error)
	RespondToReview(ctx context.Context, reviewID uuid.UUID, req types.RespondToReviewRequest) error
	UpdateReviewResponse(ctx context.Context, reviewID uuid.UUID, req types.UpdateReviewResponseRequest) error
	DeleteReviewResponse(ctx context.Context, reviewID uuid.UUID) error
	UpdateReviewStatus(ctx context.Context, reviewID uuid.UUID, status string) error
	FlagReview(ctx context.Context, reviewID uuid.UUID) error
	HideReview(ctx context.Context, reviewID uuid.UUID) error
	ShowReview(ctx context.Context, reviewID uuid.UUID) error
}

type reviewService struct {
	reviewRepo       repositories.ReviewRepository
	entityTagService EntityTagService
	logger           *logrus.Logger
}

func NewReviewService(reviewRepo repositories.ReviewRepository, entityTagService EntityTagService, logger *logrus.Logger) ReviewService {
	return &reviewService{
		reviewRepo:       reviewRepo,
		entityTagService: entityTagService,
		logger:           logger,
	}
}

func (s *reviewService) GetReviews(ctx context.Context, branchID uuid.UUID, filters types.ReviewFilters) (*types.ReviewsListResponse, error) {
	// Set default pagination if not provided
	if filters.Page <= 0 {
		filters.Page = 1
	}
	if filters.Limit <= 0 {
		filters.Limit = 20
	}

	reviews, total, err := s.reviewRepo.GetByBranchID(ctx, branchID, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get reviews")
		return nil, fmt.Errorf("failed to get reviews: %w", err)
	}

	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))

	return &types.ReviewsListResponse{
		Data: reviews,
		Pagination: types.PaginationResponse{
			Page:       filters.Page,
			Limit:      filters.Limit,
			Total:      int(total),
			TotalPages: totalPages,
		},
	}, nil
}

func (s *reviewService) GetReview(ctx context.Context, reviewID uuid.UUID) (*models.Review, error) {
	review, err := s.reviewRepo.GetByID(ctx, reviewID)
	if err != nil {
		s.logger.WithError(err).WithField("review_id", reviewID).Error("Failed to get review")
		return nil, fmt.Errorf("failed to get review: %w", err)
	}

	if review == nil {
		return nil, fmt.Errorf("review not found")
	}

	return review, nil
}

func (s *reviewService) GetReviewStats(ctx context.Context, branchID uuid.UUID, period string) (*types.ReviewStats, error) {
	stats, err := s.reviewRepo.GetReviewStats(ctx, branchID, period)
	if err != nil {
		s.logger.WithError(err).WithField("branch_id", branchID).Error("Failed to get review stats")
		return nil, fmt.Errorf("failed to get review stats: %w", err)
	}

	return stats, nil
}

func (s *reviewService) GetRecentReviews(ctx context.Context, branchID uuid.UUID, limit int) ([]models.Review, error) {
	if limit <= 0 {
		limit = 10
	}

	reviews, err := s.reviewRepo.GetRecentReviews(ctx, branchID, limit)
	if err != nil {
		s.logger.WithError(err).WithField("branch_id", branchID).Error("Failed to get recent reviews")
		return nil, fmt.Errorf("failed to get recent reviews: %w", err)
	}

	return reviews, nil
}

func (s *reviewService) GetPendingReviews(ctx context.Context, branchID uuid.UUID) ([]models.Review, error) {
	reviews, err := s.reviewRepo.GetPendingReviews(ctx, branchID)
	if err != nil {
		s.logger.WithError(err).WithField("branch_id", branchID).Error("Failed to get pending reviews")
		return nil, fmt.Errorf("failed to get pending reviews: %w", err)
	}

	return reviews, nil
}

func (s *reviewService) GetReviewInsights(ctx context.Context, branchID uuid.UUID, period string) (*types.ReviewInsights, error) {
	insights, err := s.reviewRepo.GetReviewInsights(ctx, branchID, period)
	if err != nil {
		s.logger.WithError(err).WithField("branch_id", branchID).Error("Failed to get review insights")
		return nil, fmt.Errorf("failed to get review insights: %w", err)
	}

	return insights, nil
}

func (s *reviewService) CreateReview(ctx context.Context, branchID uuid.UUID, req types.CreateReviewRequest) (*models.Review, error) {
	// Validate rating
	if req.Rating < 1 || req.Rating > 5 {
		return nil, fmt.Errorf("rating must be between 1 and 5")
	}

	// Validate required fields
	if req.CustomerName == "" {
		return nil, fmt.Errorf("customer name is required")
	}
	if req.Comment == "" {
		return nil, fmt.Errorf("comment is required")
	}

	review := &models.Review{
		BranchID:       branchID,
		CustomerName:   req.CustomerName,
		CustomerEmail:  req.CustomerEmail,
		CustomerAvatar: req.CustomerAvatar,
		Rating:         req.Rating,
		Title:          req.Title,
		Comment:        req.Comment,
		Photos:         models.PhotosData(req.Photos),
		Source:         req.Source,
		Status:         models.ReviewStatusPending,
		IsVerified:     false,
		IsPublic:       true,
		Tags:           models.TagsData(req.Tags),
	}

	// Set default source if not provided
	if review.Source == "" {
		review.Source = "internal"
	}

	err := s.reviewRepo.Create(ctx, review)
	if err != nil {
		s.logger.WithError(err).Error("Failed to create review")
		return nil, fmt.Errorf("failed to create review: %w", err)
	}

	// Sync tags with centralized tag system
	if len(req.Tags) > 0 && s.entityTagService != nil {
		if err := s.entityTagService.SyncReviewTags(ctx, review.ID, req.Tags); err != nil {
			s.logger.WithError(err).Error("Failed to sync review tags")
			// Don't fail the entire operation, just log the error
		}
	}

	return review, nil
}

func (s *reviewService) RespondToReview(ctx context.Context, reviewID uuid.UUID, req types.RespondToReviewRequest) error {
	// Get the review first to validate it exists
	review, err := s.reviewRepo.GetByID(ctx, reviewID)
	if err != nil {
		return fmt.Errorf("failed to get review: %w", err)
	}
	if review == nil {
		return fmt.Errorf("review not found")
	}

	// Validate message
	if req.Message == "" {
		return fmt.Errorf("response message is required")
	}

	response := &models.Response{
		Message:     req.Message,
		RespondedBy: req.RespondedBy,
		RespondedAt: time.Now(),
	}

	err = s.reviewRepo.RespondToReview(ctx, reviewID, response)
	if err != nil {
		s.logger.WithError(err).WithField("review_id", reviewID).Error("Failed to respond to review")
		return fmt.Errorf("failed to respond to review: %w", err)
	}

	return nil
}

func (s *reviewService) UpdateReviewResponse(ctx context.Context, reviewID uuid.UUID, req types.UpdateReviewResponseRequest) error {
	// Get the review first to validate it exists and has a response
	review, err := s.reviewRepo.GetByID(ctx, reviewID)
	if err != nil {
		return fmt.Errorf("failed to get review: %w", err)
	}
	if review == nil {
		return fmt.Errorf("review not found")
	}
	if review.Response == nil {
		return fmt.Errorf("review has no response to update")
	}

	// Validate message
	if req.Message == "" {
		return fmt.Errorf("response message is required")
	}

	// Update the response
	response := &models.Response{
		Message:     req.Message,
		RespondedBy: review.Response.RespondedBy, // Keep original responder
		RespondedAt: review.Response.RespondedAt, // Keep original response time
	}

	err = s.reviewRepo.RespondToReview(ctx, reviewID, response)
	if err != nil {
		s.logger.WithError(err).WithField("review_id", reviewID).Error("Failed to update review response")
		return fmt.Errorf("failed to update review response: %w", err)
	}

	return nil
}

func (s *reviewService) DeleteReviewResponse(ctx context.Context, reviewID uuid.UUID) error {
	// Get the review first to validate it exists
	review, err := s.reviewRepo.GetByID(ctx, reviewID)
	if err != nil {
		return fmt.Errorf("failed to get review: %w", err)
	}
	if review == nil {
		return fmt.Errorf("review not found")
	}

	err = s.reviewRepo.RespondToReview(ctx, reviewID, nil)
	if err != nil {
		s.logger.WithError(err).WithField("review_id", reviewID).Error("Failed to delete review response")
		return fmt.Errorf("failed to delete review response: %w", err)
	}

	return nil
}

func (s *reviewService) UpdateReviewStatus(ctx context.Context, reviewID uuid.UUID, status string) error {
	// Validate status
	validStatuses := []string{
		models.ReviewStatusPending,
		models.ReviewStatusApproved,
		models.ReviewStatusRejected,
		models.ReviewStatusFlagged,
	}

	isValid := false
	for _, validStatus := range validStatuses {
		if status == validStatus {
			isValid = true
			break
		}
	}

	if !isValid {
		return fmt.Errorf("invalid status: %s", status)
	}

	err := s.reviewRepo.UpdateReviewStatus(ctx, reviewID, status)
	if err != nil {
		s.logger.WithError(err).WithField("review_id", reviewID).Error("Failed to update review status")
		return fmt.Errorf("failed to update review status: %w", err)
	}

	return nil
}

func (s *reviewService) FlagReview(ctx context.Context, reviewID uuid.UUID) error {
	return s.UpdateReviewStatus(ctx, reviewID, models.ReviewStatusFlagged)
}

func (s *reviewService) HideReview(ctx context.Context, reviewID uuid.UUID) error {
	// Get the review first
	review, err := s.reviewRepo.GetByID(ctx, reviewID)
	if err != nil {
		return fmt.Errorf("failed to get review: %w", err)
	}
	if review == nil {
		return fmt.Errorf("review not found")
	}

	// Update the review to hide it
	review.IsPublic = false
	err = s.reviewRepo.Update(ctx, review)
	if err != nil {
		s.logger.WithError(err).WithField("review_id", reviewID).Error("Failed to hide review")
		return fmt.Errorf("failed to hide review: %w", err)
	}

	return nil
}

func (s *reviewService) ShowReview(ctx context.Context, reviewID uuid.UUID) error {
	// Get the review first
	review, err := s.reviewRepo.GetByID(ctx, reviewID)
	if err != nil {
		return fmt.Errorf("failed to get review: %w", err)
	}
	if review == nil {
		return fmt.Errorf("review not found")
	}

	// Update the review to show it
	review.IsPublic = true
	err = s.reviewRepo.Update(ctx, review)
	if err != nil {
		s.logger.WithError(err).WithField("review_id", reviewID).Error("Failed to show review")
		return fmt.Errorf("failed to show review: %w", err)
	}

	return nil
}
