package services

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"time"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/types"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// IntegrationTokenService handles business logic for integration tokens
type IntegrationTokenService struct {
	tokenRepo *repositories.IntegrationTokenRepository
	userRepo  repositories.UserRepository
	shopRepo  *repositories.ShopRepository
	logger    *logrus.Logger
	db        *gorm.DB
}

// NewIntegrationTokenService creates a new integration token service
func NewIntegrationTokenService(
	tokenRepo *repositories.IntegrationTokenRepository,
	userRepo repositories.UserRepository,
	shopRepo *repositories.ShopRepository,
	logger *logrus.Logger,
	db *gorm.DB,
) *IntegrationTokenService {
	return &IntegrationTokenService{
		tokenRepo: tokenRepo,
		userRepo:  userRepo,
		shopRepo:  shopRepo,
		logger:    logger,
		db:        db,
	}
}

// CreateToken creates a new integration token
func (s *IntegrationTokenService) CreateToken(ctx context.Context, req types.CreateIntegrationTokenRequest) (*types.CreateIntegrationTokenResponse, error) {
	// Validate user exists and has access to the shop
	user, err := s.userRepo.GetByID(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	if user.ShopID != req.ShopID {
		return nil, fmt.Errorf("user does not have access to this shop")
	}

	// Validate branch if specified
	if req.BranchID != nil {
		if user.BranchID == nil || *user.BranchID != *req.BranchID {
			return nil, fmt.Errorf("user does not have access to this branch")
		}
	}

	// Create token model
	token := &models.IntegrationToken{
		BaseModel:       models.BaseModel{ID: uuid.New()},
		Name:            req.Name,
		Description:     req.Description,
		Type:            models.IntegrationTokenType(req.Type),
		Status:          models.TokenStatusActive,
		ShopID:          req.ShopID,
		BranchID:        req.BranchID,
		UserID:          req.UserID,
		Scopes:          convertToModelScopes(req.Scopes),
		RateLimit:       req.RateLimit,
		RateLimitWindow: req.RateLimitWindow,
		AllowedIPs:      req.AllowedIPs,
		AllowedOrigins:  req.AllowedOrigins,
		ExpiresAt:       req.ExpiresAt,
	}

	// Set default rate limit if not specified
	if token.RateLimit == 0 {
		token.RateLimit = 1000 // Default: 1000 requests per hour
	}
	if token.RateLimitWindow == 0 {
		token.RateLimitWindow = 3600 // Default: 1 hour
	}

	// Generate token string
	tokenString, err := token.GenerateToken()
	if err != nil {
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}

	// Hash the token for storage
	hash := sha256.Sum256([]byte(tokenString))
	token.TokenHash = hex.EncodeToString(hash[:])

	// Save to database
	if err := s.tokenRepo.Create(ctx, token); err != nil {
		return nil, fmt.Errorf("failed to create token: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"token_id": token.ID,
		"shop_id":  token.ShopID,
		"user_id":  token.UserID,
		"type":     token.Type,
	}).Info("Integration token created")

	return &types.CreateIntegrationTokenResponse{
		Token:       tokenString, // Return the actual token only once
		TokenID:     token.ID,
		TokenPrefix: token.TokenPrefix,
		Name:        token.Name,
		Type:        string(token.Type),
		Status:      string(token.Status),
		Scopes:      convertFromModelScopes(token.Scopes),
		RateLimit:   token.RateLimit,
		ExpiresAt:   token.ExpiresAt,
		CreatedAt:   token.CreatedAt,
	}, nil
}

// GetTokens retrieves integration tokens for a shop
func (s *IntegrationTokenService) GetTokens(ctx context.Context, req types.GetIntegrationTokensRequest) (*types.GetIntegrationTokensResponse, error) {
	// Validate user has access to shop
	user, err := s.userRepo.GetByID(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	if user.ShopID != req.ShopID {
		return nil, fmt.Errorf("user does not have access to this shop")
	}

	// Set default pagination
	limit := req.Limit
	if limit <= 0 || limit > 100 {
		limit = 50
	}
	offset := req.Offset
	if offset < 0 {
		offset = 0
	}

	// Get tokens
	tokens, total, err := s.tokenRepo.GetByShopID(ctx, req.ShopID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to get tokens: %w", err)
	}

	// Convert to response format
	tokenResponses := make([]types.IntegrationTokenResponse, len(tokens))
	for i, token := range tokens {
		tokenResponses[i] = types.IntegrationTokenResponse{
			ID:           token.ID,
			Name:         token.Name,
			Description:  token.Description,
			Type:         string(token.Type),
			Status:       string(token.Status),
			TokenPrefix:  token.TokenPrefix,
			Scopes:       convertFromModelScopes(token.Scopes),
			RateLimit:    token.RateLimit,
			CurrentUsage: token.CurrentUsage,
			UsageCount:   token.UsageCount,
			LastUsedAt:   token.LastUsedAt,
			ExpiresAt:    token.ExpiresAt,
			CreatedAt:    token.CreatedAt,
			UpdatedAt:    token.UpdatedAt,
		}
	}

	return &types.GetIntegrationTokensResponse{
		Tokens: tokenResponses,
		Total:  total,
		Limit:  limit,
		Offset: offset,
	}, nil
}

// GetToken retrieves a specific integration token
func (s *IntegrationTokenService) GetToken(ctx context.Context, req types.GetIntegrationTokenRequest) (*types.IntegrationTokenResponse, error) {
	// Get token
	token, err := s.tokenRepo.GetByID(ctx, req.TokenID)
	if err != nil {
		return nil, fmt.Errorf("token not found: %w", err)
	}

	// Validate user has access
	user, err := s.userRepo.GetByID(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	if user.ShopID != token.ShopID {
		return nil, fmt.Errorf("user does not have access to this token")
	}

	return &types.IntegrationTokenResponse{
		ID:           token.ID,
		Name:         token.Name,
		Description:  token.Description,
		Type:         string(token.Type),
		Status:       string(token.Status),
		TokenPrefix:  token.TokenPrefix,
		Scopes:       convertFromModelScopes(token.Scopes),
		RateLimit:    token.RateLimit,
		CurrentUsage: token.CurrentUsage,
		UsageCount:   token.UsageCount,
		LastUsedAt:   token.LastUsedAt,
		ExpiresAt:    token.ExpiresAt,
		CreatedAt:    token.CreatedAt,
		UpdatedAt:    token.UpdatedAt,
	}, nil
}

// UpdateToken updates an integration token
func (s *IntegrationTokenService) UpdateToken(ctx context.Context, req types.UpdateIntegrationTokenRequest) (*types.IntegrationTokenResponse, error) {
	// Get existing token
	token, err := s.tokenRepo.GetByID(ctx, req.TokenID)
	if err != nil {
		return nil, fmt.Errorf("token not found: %w", err)
	}

	// Validate user has access
	user, err := s.userRepo.GetByID(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	if user.ShopID != token.ShopID {
		return nil, fmt.Errorf("user does not have access to this token")
	}

	// Update fields
	if req.Name != nil {
		token.Name = *req.Name
	}
	if req.Description != nil {
		token.Description = *req.Description
	}
	if req.Status != nil {
		token.Status = models.IntegrationTokenStatus(*req.Status)
	}
	if req.Scopes != nil {
		token.Scopes = convertToModelScopes(*req.Scopes)
	}
	if req.RateLimit != nil {
		token.RateLimit = *req.RateLimit
	}
	if req.AllowedIPs != nil {
		token.AllowedIPs = *req.AllowedIPs
	}
	if req.AllowedOrigins != nil {
		token.AllowedOrigins = *req.AllowedOrigins
	}
	if req.ExpiresAt != nil {
		token.ExpiresAt = req.ExpiresAt
	}

	// Save changes
	if err := s.tokenRepo.Update(ctx, token); err != nil {
		return nil, fmt.Errorf("failed to update token: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"token_id": token.ID,
		"shop_id":  token.ShopID,
		"user_id":  req.UserID,
	}).Info("Integration token updated")

	return &types.IntegrationTokenResponse{
		ID:           token.ID,
		Name:         token.Name,
		Description:  token.Description,
		Type:         string(token.Type),
		Status:       string(token.Status),
		TokenPrefix:  token.TokenPrefix,
		Scopes:       convertFromModelScopes(token.Scopes),
		RateLimit:    token.RateLimit,
		CurrentUsage: token.CurrentUsage,
		UsageCount:   token.UsageCount,
		LastUsedAt:   token.LastUsedAt,
		ExpiresAt:    token.ExpiresAt,
		CreatedAt:    token.CreatedAt,
		UpdatedAt:    token.UpdatedAt,
	}, nil
}

// RevokeToken revokes an integration token
func (s *IntegrationTokenService) RevokeToken(ctx context.Context, req types.RevokeIntegrationTokenRequest) error {
	// Get token
	token, err := s.tokenRepo.GetByID(ctx, req.TokenID)
	if err != nil {
		return fmt.Errorf("token not found: %w", err)
	}

	// Validate user has access
	user, err := s.userRepo.GetByID(ctx, req.UserID)
	if err != nil {
		return fmt.Errorf("user not found: %w", err)
	}

	if user.ShopID != token.ShopID {
		return fmt.Errorf("user does not have access to this token")
	}

	// Revoke token
	if err := s.tokenRepo.RevokeToken(ctx, req.TokenID); err != nil {
		return fmt.Errorf("failed to revoke token: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"token_id": req.TokenID,
		"shop_id":  token.ShopID,
		"user_id":  req.UserID,
	}).Info("Integration token revoked")

	return nil
}

// ValidateToken validates an integration token and returns token info
func (s *IntegrationTokenService) ValidateToken(ctx context.Context, tokenString, ipAddress, userAgent string) (*models.IntegrationToken, error) {
	// Validate and get token
	token, err := s.tokenRepo.ValidateAndGetToken(ctx, tokenString)
	if err != nil {
		return nil, err
	}

	// Update usage statistics
	if err := token.UpdateUsage(s.db, ipAddress, userAgent); err != nil {
		s.logger.WithError(err).Error("Failed to update token usage")
		// Don't fail the request if usage update fails
	}

	return token, nil
}

// GetTokenUsage retrieves token usage statistics
func (s *IntegrationTokenService) GetTokenUsage(ctx context.Context, req types.GetTokenUsageRequest) (*types.GetTokenUsageResponse, error) {
	// Get token
	token, err := s.tokenRepo.GetByID(ctx, req.TokenID)
	if err != nil {
		return nil, fmt.Errorf("token not found: %w", err)
	}

	// Validate user has access
	user, err := s.userRepo.GetByID(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	if user.ShopID != token.ShopID {
		return nil, fmt.Errorf("user does not have access to this token")
	}

	// Set default time range if not provided
	to := req.To
	if to.IsZero() {
		to = time.Now()
	}
	from := req.From
	if from.IsZero() {
		from = to.AddDate(0, 0, -30) // Default: last 30 days
	}

	// Get usage records
	usage, err := s.tokenRepo.GetTokenUsage(ctx, req.TokenID, from, to)
	if err != nil {
		return nil, fmt.Errorf("failed to get token usage: %w", err)
	}

	// Get aggregated stats
	stats, err := s.tokenRepo.GetTokenUsageStats(ctx, req.TokenID, from, to)
	if err != nil {
		return nil, fmt.Errorf("failed to get token usage stats: %w", err)
	}

	return &types.GetTokenUsageResponse{
		Usage: usage,
		Stats: stats,
		From:  from,
		To:    to,
	}, nil
}

// Helper functions to convert between types and models
func convertToModelScopes(scopes []types.IntegrationTokenScope) []models.IntegrationTokenScope {
	modelScopes := make([]models.IntegrationTokenScope, len(scopes))
	for i, scope := range scopes {
		modelScopes[i] = models.IntegrationTokenScope{
			Resource:     scope.Resource,
			Actions:      scope.Actions,
			BranchIDs:    scope.BranchIDs,
			Restrictions: scope.Restrictions,
		}
	}
	return modelScopes
}

func convertFromModelScopes(scopes []models.IntegrationTokenScope) []types.IntegrationTokenScope {
	typeScopes := make([]types.IntegrationTokenScope, len(scopes))
	for i, scope := range scopes {
		typeScopes[i] = types.IntegrationTokenScope{
			Resource:     scope.Resource,
			Actions:      scope.Actions,
			BranchIDs:    scope.BranchIDs,
			Restrictions: scope.Restrictions,
		}
	}
	return typeScopes
}
