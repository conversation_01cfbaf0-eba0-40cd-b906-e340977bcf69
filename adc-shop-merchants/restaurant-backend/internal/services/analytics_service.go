package services

import (
	"context"
	"fmt"
	"time"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/types"
	"restaurant-backend/pkg/pagination"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// AnalyticsService handles analytics business logic
type AnalyticsService struct {
	analyticsRepo repositories.AnalyticsRepository
	orderRepo     repositories.OrderRepository
	logger        *logrus.Logger
}

// NewAnalyticsService creates a new analytics service
func NewAnalyticsService(
	analyticsRepo repositories.AnalyticsRepository,
	orderRepo repositories.OrderRepository,
	logger *logrus.Logger,
) *AnalyticsService {
	return &AnalyticsService{
		analyticsRepo: analyticsRepo,
		orderRepo:     orderRepo,
		logger:        logger,
	}
}

// DashboardData represents the main dashboard analytics
type DashboardData struct {
	TodayStats   *DailyStats   `json:"today_stats"`
	WeeklyTrends *WeeklyTrends `json:"weekly_trends"`
	PopularItems []PopularItem `json:"popular_items"`
	RecentOrders []RecentOrder `json:"recent_orders"`
	Alerts       []Alert       `json:"alerts"`
}

// DailyStats represents daily statistics
type DailyStats struct {
	Revenue       float64 `json:"revenue"`
	OrderCount    int     `json:"order_count"`
	CustomerCount int     `json:"customer_count"`
	AvgOrderValue float64 `json:"avg_order_value"`
	GrowthRate    float64 `json:"growth_rate"`
}

// WeeklyTrends represents weekly trend data
type WeeklyTrends struct {
	Revenue   []TrendPoint `json:"revenue"`
	Orders    []TrendPoint `json:"orders"`
	Customers []TrendPoint `json:"customers"`
}

// TrendPoint represents a data point in trends
type TrendPoint struct {
	Date  string  `json:"date"`
	Value float64 `json:"value"`
}

// PopularItem represents popular menu items
type PopularItem struct {
	ItemID     uuid.UUID `json:"item_id"`
	Name       string    `json:"name"`
	OrderCount int       `json:"order_count"`
	Revenue    float64   `json:"revenue"`
	GrowthRate float64   `json:"growth_rate"`
}

// RecentOrder represents recent order data
type RecentOrder struct {
	OrderID      uuid.UUID `json:"order_id"`
	OrderNumber  string    `json:"order_number"`
	CustomerName string    `json:"customer_name"`
	Total        float64   `json:"total"`
	Status       string    `json:"status"`
	CreatedAt    time.Time `json:"created_at"`
}

// Alert represents system alerts
type Alert struct {
	Type      string    `json:"type"`
	Message   string    `json:"message"`
	Severity  string    `json:"severity"`
	CreatedAt time.Time `json:"created_at"`
}

// SalesReport represents sales analytics report
type SalesReport struct {
	Period         string              `json:"period"`
	StartDate      time.Time           `json:"start_date"`
	EndDate        time.Time           `json:"end_date"`
	TotalRevenue   float64             `json:"total_revenue"`
	TotalOrders    int                 `json:"total_orders"`
	AvgOrderValue  float64             `json:"avg_order_value"`
	DailyBreakdown []DailyBreakdown    `json:"daily_breakdown"`
	HourlyTrends   []HourlyTrend       `json:"hourly_trends"`
	PaymentMethods []PaymentMethodStat `json:"payment_methods"`
	OrderTypes     []OrderTypeStat     `json:"order_types"`
}

// DailyBreakdown represents daily sales breakdown
type DailyBreakdown struct {
	Date          string  `json:"date"`
	Revenue       float64 `json:"revenue"`
	OrderCount    int     `json:"order_count"`
	CustomerCount int     `json:"customer_count"`
}

// HourlyTrend represents hourly sales trends
type HourlyTrend struct {
	Hour       int     `json:"hour"`
	Revenue    float64 `json:"revenue"`
	OrderCount int     `json:"order_count"`
}

// PaymentMethodStat represents payment method statistics
type PaymentMethodStat struct {
	Method     string  `json:"method"`
	Count      int     `json:"count"`
	Revenue    float64 `json:"revenue"`
	Percentage float64 `json:"percentage"`
}

// OrderTypeStat represents order type statistics
type OrderTypeStat struct {
	Type       string  `json:"type"`
	Count      int     `json:"count"`
	Revenue    float64 `json:"revenue"`
	Percentage float64 `json:"percentage"`
}

// GetDashboardData retrieves dashboard analytics data
func (s *AnalyticsService) GetDashboardData(ctx context.Context, branchID uuid.UUID) (*DashboardData, error) {
	s.logger.WithField("branch_id", branchID).Info("Getting dashboard data")

	// Get today's stats
	todayStats, err := s.getTodayStats(ctx, branchID)
	if err != nil {
		return nil, fmt.Errorf("failed to get today stats: %w", err)
	}

	// Get weekly trends
	weeklyTrends, err := s.getWeeklyTrends(ctx, branchID)
	if err != nil {
		return nil, fmt.Errorf("failed to get weekly trends: %w", err)
	}

	// Get popular items
	popularItems, err := s.getPopularItems(ctx, branchID, 10)
	if err != nil {
		return nil, fmt.Errorf("failed to get popular items: %w", err)
	}

	// Get recent orders
	recentOrders, err := s.getRecentOrders(ctx, branchID, 10)
	if err != nil {
		return nil, fmt.Errorf("failed to get recent orders: %w", err)
	}

	// Get alerts
	alerts, err := s.getAlerts(ctx, branchID)
	if err != nil {
		return nil, fmt.Errorf("failed to get alerts: %w", err)
	}

	return &DashboardData{
		TodayStats:   todayStats,
		WeeklyTrends: weeklyTrends,
		PopularItems: popularItems,
		RecentOrders: recentOrders,
		Alerts:       alerts,
	}, nil
}

// GetSalesReport generates a comprehensive sales report
func (s *AnalyticsService) GetSalesReport(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) (*SalesReport, error) {
	s.logger.WithFields(logrus.Fields{
		"branch_id":  branchID,
		"start_date": startDate,
		"end_date":   endDate,
	}).Info("Generating sales report")

	// Get sales metrics
	salesMetrics, err := s.analyticsRepo.GetSalesMetrics(ctx, branchID, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get sales metrics: %w", err)
	}

	// Calculate totals
	var totalRevenue float64
	var totalOrders int
	for _, metric := range salesMetrics {
		if metric.MetricType == models.SalesMetricRevenue {
			totalRevenue += metric.Value
		} else if metric.MetricType == models.SalesMetricOrderCount {
			totalOrders += int(metric.Value)
		}
	}

	avgOrderValue := float64(0)
	if totalOrders > 0 {
		avgOrderValue = totalRevenue / float64(totalOrders)
	}

	// Get daily breakdown
	dailyBreakdown, err := s.getDailyBreakdown(ctx, branchID, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get daily breakdown: %w", err)
	}

	// Get hourly trends
	hourlyTrends, err := s.getHourlyTrends(ctx, branchID, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get hourly trends: %w", err)
	}

	// Get payment method stats
	paymentMethods, err := s.getPaymentMethodStats(ctx, branchID, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get payment method stats: %w", err)
	}

	// Get order type stats
	orderTypes, err := s.getOrderTypeStats(ctx, branchID, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get order type stats: %w", err)
	}

	period := fmt.Sprintf("%s to %s", startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))

	return &SalesReport{
		Period:         period,
		StartDate:      startDate,
		EndDate:        endDate,
		TotalRevenue:   totalRevenue,
		TotalOrders:    totalOrders,
		AvgOrderValue:  avgOrderValue,
		DailyBreakdown: dailyBreakdown,
		HourlyTrends:   hourlyTrends,
		PaymentMethods: paymentMethods,
		OrderTypes:     orderTypes,
	}, nil
}

// RecordOrderMetrics records metrics when an order is completed
func (s *AnalyticsService) RecordOrderMetrics(ctx context.Context, order *models.Order) error {
	s.logger.WithField("order_id", order.ID).Info("Recording order metrics")

	now := time.Now()
	date := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	hour := now.Hour()

	// Record sales metrics
	salesMetrics := []*models.SalesMetric{
		{
			BranchID:   order.BranchID,
			Date:       date,
			Hour:       hour,
			MetricType: models.SalesMetricRevenue,
			Value:      order.Total,
			Count:      1,
		},
		{
			BranchID:   order.BranchID,
			Date:       date,
			Hour:       hour,
			MetricType: models.SalesMetricOrderCount,
			Value:      1,
			Count:      1,
		},
	}

	for _, metric := range salesMetrics {
		if err := s.analyticsRepo.UpsertSalesMetric(ctx, metric); err != nil {
			return fmt.Errorf("failed to record sales metric: %w", err)
		}
	}

	// Record menu item metrics
	for _, item := range order.Items {
		if item.MenuItemID != nil {
			menuMetric := &models.MenuItemMetric{
				BranchID:     order.BranchID,
				MenuItemID:   *item.MenuItemID,
				Date:         date,
				OrderCount:   1,
				QuantitySold: item.Quantity,
				Revenue:      item.Total,
			}

			if err := s.analyticsRepo.UpsertMenuItemMetric(ctx, menuMetric); err != nil {
				return fmt.Errorf("failed to record menu item metric: %w", err)
			}
		}
	}

	// Record customer metrics if customer info is available
	if order.CustomerPhone != "" || order.CustomerEmail != "" {
		customerMetric := &models.CustomerMetric{
			BranchID:      order.BranchID,
			Date:          date,
			VisitCount:    1,
			OrderCount:    1,
			TotalSpent:    order.Total,
			AvgOrderValue: order.Total,
			LastVisit:     now,
			CustomerType:  s.determineCustomerType(ctx, order),
		}

		if err := s.analyticsRepo.UpsertCustomerMetric(ctx, customerMetric); err != nil {
			return fmt.Errorf("failed to record customer metric: %w", err)
		}
	}

	return nil
}

// Helper methods

func (s *AnalyticsService) getTodayStats(ctx context.Context, branchID uuid.UUID) (*DailyStats, error) {
	today := time.Now()
	startOfDay := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, today.Location())
	endOfDay := startOfDay.Add(24 * time.Hour)

	// Get today's metrics
	metrics, err := s.analyticsRepo.GetSalesMetrics(ctx, branchID, startOfDay, endOfDay)
	if err != nil {
		return nil, err
	}

	stats := &DailyStats{}
	for _, metric := range metrics {
		switch metric.MetricType {
		case models.SalesMetricRevenue:
			stats.Revenue += metric.Value
		case models.SalesMetricOrderCount:
			stats.OrderCount += int(metric.Value)
		case models.SalesMetricCustomerCount:
			stats.CustomerCount += int(metric.Value)
		}
	}

	if stats.OrderCount > 0 {
		stats.AvgOrderValue = stats.Revenue / float64(stats.OrderCount)
	}

	// Calculate growth rate (compare with yesterday)
	yesterday := startOfDay.Add(-24 * time.Hour)
	yesterdayMetrics, err := s.analyticsRepo.GetSalesMetrics(ctx, branchID, yesterday, startOfDay)
	if err == nil {
		var yesterdayRevenue float64
		for _, metric := range yesterdayMetrics {
			if metric.MetricType == models.SalesMetricRevenue {
				yesterdayRevenue += metric.Value
			}
		}
		if yesterdayRevenue > 0 {
			stats.GrowthRate = ((stats.Revenue - yesterdayRevenue) / yesterdayRevenue) * 100
		}
	}

	return stats, nil
}

func (s *AnalyticsService) getWeeklyTrends(ctx context.Context, branchID uuid.UUID) (*WeeklyTrends, error) {
	endDate := time.Now()
	startDate := endDate.Add(-7 * 24 * time.Hour)

	metrics, err := s.analyticsRepo.GetSalesMetrics(ctx, branchID, startDate, endDate)
	if err != nil {
		return nil, err
	}

	// Group metrics by date
	dailyMetrics := make(map[string]map[string]float64)
	for _, metric := range metrics {
		dateStr := metric.Date.Format("2006-01-02")
		if dailyMetrics[dateStr] == nil {
			dailyMetrics[dateStr] = make(map[string]float64)
		}
		dailyMetrics[dateStr][metric.MetricType] += metric.Value
	}

	trends := &WeeklyTrends{
		Revenue:   make([]TrendPoint, 0),
		Orders:    make([]TrendPoint, 0),
		Customers: make([]TrendPoint, 0),
	}

	// Create trend points for each day
	for i := 0; i < 7; i++ {
		date := startDate.Add(time.Duration(i) * 24 * time.Hour)
		dateStr := date.Format("2006-01-02")

		if dayMetrics, exists := dailyMetrics[dateStr]; exists {
			trends.Revenue = append(trends.Revenue, TrendPoint{
				Date:  dateStr,
				Value: dayMetrics[models.SalesMetricRevenue],
			})
			trends.Orders = append(trends.Orders, TrendPoint{
				Date:  dateStr,
				Value: dayMetrics[models.SalesMetricOrderCount],
			})
			trends.Customers = append(trends.Customers, TrendPoint{
				Date:  dateStr,
				Value: dayMetrics[models.SalesMetricCustomerCount],
			})
		} else {
			trends.Revenue = append(trends.Revenue, TrendPoint{Date: dateStr, Value: 0})
			trends.Orders = append(trends.Orders, TrendPoint{Date: dateStr, Value: 0})
			trends.Customers = append(trends.Customers, TrendPoint{Date: dateStr, Value: 0})
		}
	}

	return trends, nil
}

func (s *AnalyticsService) getPopularItems(ctx context.Context, branchID uuid.UUID, limit int) ([]PopularItem, error) {
	endDate := time.Now()
	startDate := endDate.Add(-30 * 24 * time.Hour) // Last 30 days

	metrics, err := s.analyticsRepo.GetMenuItemMetrics(ctx, branchID, startDate, endDate)
	if err != nil {
		return nil, err
	}

	// Aggregate metrics by menu item
	itemMetrics := make(map[uuid.UUID]*PopularItem)
	for _, metric := range metrics {
		if item, exists := itemMetrics[metric.MenuItemID]; exists {
			item.OrderCount += metric.OrderCount
			item.Revenue += metric.Revenue
		} else {
			itemMetrics[metric.MenuItemID] = &PopularItem{
				ItemID:     metric.MenuItemID,
				Name:       metric.MenuItem.Name,
				OrderCount: metric.OrderCount,
				Revenue:    metric.Revenue,
			}
		}
	}

	// Convert to slice and sort by order count
	items := make([]PopularItem, 0, len(itemMetrics))
	for _, item := range itemMetrics {
		items = append(items, *item)
	}

	// Sort by order count (descending)
	for i := 0; i < len(items)-1; i++ {
		for j := i + 1; j < len(items); j++ {
			if items[i].OrderCount < items[j].OrderCount {
				items[i], items[j] = items[j], items[i]
			}
		}
	}

	// Return top items
	if len(items) > limit {
		items = items[:limit]
	}

	return items, nil
}

func (s *AnalyticsService) getRecentOrders(ctx context.Context, branchID uuid.UUID, limit int) ([]RecentOrder, error) {
	filters := types.OrderFilters{
		Page:  1,
		Limit: limit,
	}

	orders, _, err := s.orderRepo.GetByBranchID(ctx, branchID, filters)
	if err != nil {
		return nil, err
	}

	recentOrders := make([]RecentOrder, len(orders))
	for i, order := range orders {
		recentOrders[i] = RecentOrder{
			OrderID:      order.ID,
			OrderNumber:  order.OrderNumber,
			CustomerName: order.CustomerName,
			Total:        order.Total,
			Status:       order.Status,
			CreatedAt:    order.CreatedAt,
		}
	}

	return recentOrders, nil
}

func (s *AnalyticsService) getAlerts(ctx context.Context, branchID uuid.UUID) ([]Alert, error) {
	// This would typically check for various conditions and generate alerts
	// For now, return empty slice
	return []Alert{}, nil
}

func (s *AnalyticsService) getDailyBreakdown(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]DailyBreakdown, error) {
	metrics, err := s.analyticsRepo.GetSalesMetrics(ctx, branchID, startDate, endDate)
	if err != nil {
		return nil, err
	}

	// Group metrics by date
	dailyMetrics := make(map[string]map[string]float64)
	for _, metric := range metrics {
		dateStr := metric.Date.Format("2006-01-02")
		if dailyMetrics[dateStr] == nil {
			dailyMetrics[dateStr] = make(map[string]float64)
		}
		dailyMetrics[dateStr][metric.MetricType] += metric.Value
	}

	// Create daily breakdown
	var breakdown []DailyBreakdown
	current := startDate
	for current.Before(endDate) || current.Equal(endDate) {
		dateStr := current.Format("2006-01-02")
		dayMetrics := dailyMetrics[dateStr]

		breakdown = append(breakdown, DailyBreakdown{
			Date:          current.Format("2006-01-02"),
			Revenue:       dayMetrics[models.SalesMetricRevenue],
			OrderCount:    int(dayMetrics[models.SalesMetricOrderCount]),
			CustomerCount: int(dayMetrics[models.SalesMetricCustomerCount]),
		})

		current = current.AddDate(0, 0, 1)
	}

	return breakdown, nil
}

func (s *AnalyticsService) getHourlyTrends(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]HourlyTrend, error) {
	metrics, err := s.analyticsRepo.GetSalesMetrics(ctx, branchID, startDate, endDate)
	if err != nil {
		return nil, err
	}

	// Group metrics by hour
	hourlyMetrics := make(map[int]map[string]float64)
	for _, metric := range metrics {
		if hourlyMetrics[metric.Hour] == nil {
			hourlyMetrics[metric.Hour] = make(map[string]float64)
		}
		hourlyMetrics[metric.Hour][metric.MetricType] += metric.Value
	}

	// Create hourly trends for all 24 hours
	var trends []HourlyTrend
	for hour := 0; hour < 24; hour++ {
		hourMetrics := hourlyMetrics[hour]
		trends = append(trends, HourlyTrend{
			Hour:       hour,
			Revenue:    hourMetrics[models.SalesMetricRevenue],
			OrderCount: int(hourMetrics[models.SalesMetricOrderCount]),
		})
	}

	return trends, nil
}

func (s *AnalyticsService) getPaymentMethodStats(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]PaymentMethodStat, error) {
	// Get order stats which includes payment breakdown
	orderStats, err := s.orderRepo.GetOrderStats(ctx, branchID, startDate, endDate)
	if err != nil {
		return nil, err
	}

	// For now, return mock payment method data since the order stats doesn't include payment breakdown
	// This would need to be implemented in the order repository to get actual payment method data
	totalRevenue := orderStats.TotalRevenue

	// Mock payment method distribution
	stats := []PaymentMethodStat{
		{
			Method:     "cash",
			Count:      int(orderStats.TotalOrders * 40 / 100), // 40% cash
			Revenue:    totalRevenue * 0.4,
			Percentage: 40.0,
		},
		{
			Method:     "card",
			Count:      int(orderStats.TotalOrders * 45 / 100), // 45% card
			Revenue:    totalRevenue * 0.45,
			Percentage: 45.0,
		},
		{
			Method:     "digital",
			Count:      int(orderStats.TotalOrders * 15 / 100), // 15% digital
			Revenue:    totalRevenue * 0.15,
			Percentage: 15.0,
		},
	}

	return stats, nil
}

func (s *AnalyticsService) getOrderTypeStats(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]OrderTypeStat, error) {
	// Get order stats which includes order type breakdown
	orderStats, err := s.orderRepo.GetOrderStats(ctx, branchID, startDate, endDate)
	if err != nil {
		return nil, err
	}

	// Calculate total orders for percentage calculation
	totalOrders := orderStats.TotalOrders
	totalRevenue := orderStats.TotalRevenue

	// Convert order type breakdown to OrderTypeStat format
	var stats []OrderTypeStat
	for orderType, count := range orderStats.OrdersByType {
		// Estimate revenue distribution based on order count
		// This is a simplified approach - in reality, different order types might have different average values
		revenue := (float64(count) / float64(totalOrders)) * totalRevenue
		percentage := 0.0
		if totalOrders > 0 {
			percentage = (float64(count) / float64(totalOrders)) * 100
		}

		stats = append(stats, OrderTypeStat{
			Type:       orderType,
			Count:      int(count),
			Revenue:    revenue,
			Percentage: percentage,
		})
	}

	return stats, nil
}

func (s *AnalyticsService) determineCustomerType(ctx context.Context, order *models.Order) string {
	// Logic to determine if customer is new, returning, or VIP
	// This would typically check order history
	return models.CustomerTypeNew
}

// GetPopularItemsAnalytics retrieves popular items analytics with standardized pagination, filtering, and sorting
func (s *AnalyticsService) GetPopularItemsAnalytics(ctx context.Context, branchID uuid.UUID, filters types.PopularItemsFilters) (*types.PopularItemsAnalyticsResponse, error) {
	s.logger.WithField("branch_id", branchID).Info("Getting popular items analytics with filters")

	// Apply pagination and sorting defaults
	pagination.ApplyStandardDefaults(&filters.StandardPagination, &filters.StandardSorting)
	pagination.ValidateAndApplySorting(&filters.StandardSorting, types.PopularItemsAnalyticsSortFields, "order_count")

	// TODO: Use date filters when getPopularItems method supports date range
	// For now, getPopularItems uses a fixed 30-day range

	// Get popular items from analytics service
	popularItems, err := s.getPopularItems(ctx, branchID, 100) // Get more items for filtering
	if err != nil {
		return nil, err
	}

	// Convert to analytics format
	var analyticsData []types.PopularItemAnalytics
	for i, item := range popularItems {
		analyticsData = append(analyticsData, types.PopularItemAnalytics{
			ItemID:       item.ItemID,
			ItemName:     item.Name,
			Category:     "Main Course", // TODO: Get actual category from menu item
			OrderCount:   item.OrderCount,
			TotalRevenue: item.Revenue,
			AvgRating:    4.5, // TODO: Get actual rating from reviews
			Rank:         i + 1,
			Period:       filters.Period,
		})
	}

	// Apply pagination
	total := int64(len(analyticsData))
	offset := (filters.Page - 1) * filters.Limit
	end := offset + filters.Limit
	if end > len(analyticsData) {
		end = len(analyticsData)
	}
	if offset < len(analyticsData) {
		analyticsData = analyticsData[offset:end]
	} else {
		analyticsData = []types.PopularItemAnalytics{}
	}

	return &types.PopularItemsAnalyticsResponse{
		StandardResponse: pagination.CreateStandardResponse(total, filters.Page, filters.Limit),
		Data:             analyticsData,
	}, nil
}

// GetSalesReportAnalytics retrieves sales report analytics with standardized pagination, filtering, and sorting
func (s *AnalyticsService) GetSalesReportAnalytics(ctx context.Context, branchID uuid.UUID, filters types.SalesReportFilters) (*types.SalesReportAnalyticsResponse, error) {
	s.logger.WithField("branch_id", branchID).Info("Getting sales report analytics with filters")

	// Apply pagination and sorting defaults
	pagination.ApplyStandardDefaults(&filters.StandardPagination, &filters.StandardSorting)
	pagination.ValidateAndApplySorting(&filters.StandardSorting, types.SalesReportAnalyticsSortFields, "date")

	// Set default date range if not provided
	endDate := time.Now()
	startDate := endDate.AddDate(0, -1, 0) // Default to last month

	if filters.DateFrom != nil {
		startDate = *filters.DateFrom
	}
	if filters.DateTo != nil {
		endDate = *filters.DateTo
	}

	// Get sales report data
	salesReport, err := s.GetSalesReport(ctx, branchID, startDate, endDate)
	if err != nil {
		return nil, err
	}

	// Convert to analytics format
	var analyticsData []types.SalesReportAnalytics
	for _, daily := range salesReport.DailyBreakdown {
		date, _ := time.Parse("2006-01-02", daily.Date)

		// Convert hourly trends to hourly breakdown
		var hourlyBreakdown []types.HourlySales
		for _, hourly := range salesReport.HourlyTrends {
			hourlyBreakdown = append(hourlyBreakdown, types.HourlySales{
				Hour:    hourly.Hour,
				Orders:  hourly.OrderCount,
				Revenue: hourly.Revenue,
			})
		}

		// Convert payment methods to breakdown map
		paymentBreakdown := make(map[string]float64)
		for _, payment := range salesReport.PaymentMethods {
			paymentBreakdown[payment.Method] = payment.Revenue
		}

		// Convert order types to breakdown map
		orderTypeBreakdown := make(map[string]int)
		for _, orderType := range salesReport.OrderTypes {
			orderTypeBreakdown[orderType.Type] = orderType.Count
		}

		analyticsData = append(analyticsData, types.SalesReportAnalytics{
			Date:         date,
			Period:       salesReport.Period,
			TotalOrders:  daily.OrderCount,
			TotalRevenue: daily.Revenue,
			AvgOrderValue: func() float64 {
				if daily.OrderCount > 0 {
					return daily.Revenue / float64(daily.OrderCount)
				}
				return 0
			}(),
			TotalRefunds:       0, // TODO: Implement refunds tracking
			NetRevenue:         daily.Revenue,
			PaymentBreakdown:   paymentBreakdown,
			OrderTypeBreakdown: orderTypeBreakdown,
			HourlyBreakdown:    hourlyBreakdown,
		})
	}

	return &types.SalesReportAnalyticsResponse{
		StandardResponse: pagination.CreateStandardResponse(int64(len(analyticsData)), filters.Page, filters.Limit),
		Data:             analyticsData,
	}, nil
}

// GetCustomerAnalytics retrieves customer analytics with standardized pagination, filtering, and sorting
func (s *AnalyticsService) GetCustomerAnalytics(ctx context.Context, branchID uuid.UUID, filters types.CustomerAnalyticsFilters) (*types.CustomerAnalyticsResponse, error) {
	s.logger.WithField("branch_id", branchID).Info("Getting customer analytics with filters")

	// Apply pagination and sorting defaults
	pagination.ApplyStandardDefaults(&filters.StandardPagination, &filters.StandardSorting)
	pagination.ValidateAndApplySorting(&filters.StandardSorting, types.CustomerAnalyticsSortFields, "total_spent")

	// Get analytics from repository (this would need to be implemented in the repository layer)
	// For now, return empty response
	return &types.CustomerAnalyticsResponse{
		StandardResponse: pagination.CreateStandardResponse(0, filters.Page, filters.Limit),
		Data:             []types.CustomerAnalytics{},
	}, nil
}

// GetStaffPerformanceAnalytics retrieves staff performance analytics with standardized pagination, filtering, and sorting
func (s *AnalyticsService) GetStaffPerformanceAnalytics(ctx context.Context, branchID uuid.UUID, filters types.StaffPerformanceFilters) (*types.StaffPerformanceAnalyticsResponse, error) {
	s.logger.WithField("branch_id", branchID).Info("Getting staff performance analytics with filters")

	// Apply pagination and sorting defaults
	pagination.ApplyStandardDefaults(&filters.StandardPagination, &filters.StandardSorting)
	pagination.ValidateAndApplySorting(&filters.StandardSorting, types.StaffPerformanceAnalyticsSortFields, "efficiency_score")

	// Get analytics from repository (this would need to be implemented in the repository layer)
	// For now, return empty response
	return &types.StaffPerformanceAnalyticsResponse{
		StandardResponse: pagination.CreateStandardResponse(0, filters.Page, filters.Limit),
		Data:             []types.StaffPerformanceAnalytics{},
	}, nil
}

// GetTableUtilizationAnalytics retrieves table utilization analytics with standardized pagination, filtering, and sorting
func (s *AnalyticsService) GetTableUtilizationAnalytics(ctx context.Context, branchID uuid.UUID, filters types.TableUtilizationFilters) (*types.TableUtilizationAnalyticsResponse, error) {
	s.logger.WithField("branch_id", branchID).Info("Getting table utilization analytics with filters")

	// Apply pagination and sorting defaults
	pagination.ApplyStandardDefaults(&filters.StandardPagination, &filters.StandardSorting)
	pagination.ValidateAndApplySorting(&filters.StandardSorting, types.TableUtilizationAnalyticsSortFields, "utilization_rate")

	// Get analytics from repository (this would need to be implemented in the repository layer)
	// For now, return empty response
	return &types.TableUtilizationAnalyticsResponse{
		StandardResponse: pagination.CreateStandardResponse(0, filters.Page, filters.Limit),
		Data:             []types.TableUtilizationAnalytics{},
	}, nil
}

// GetCommunicationAnalytics retrieves communication analytics with standardized pagination, filtering, and sorting
func (s *AnalyticsService) GetCommunicationAnalytics(ctx context.Context, branchID uuid.UUID, filters types.CommunicationAnalyticsFilters) (*types.CommunicationAnalyticsResponse, error) {
	s.logger.WithField("branch_id", branchID).Info("Getting communication analytics with filters")

	// Apply pagination and sorting defaults
	pagination.ApplyStandardDefaults(&filters.StandardPagination, &filters.StandardSorting)
	pagination.ValidateAndApplySorting(&filters.StandardSorting, types.CommunicationAnalyticsSortFields, "sent_date")

	// Get analytics from repository (this would need to be implemented in the repository layer)
	// For now, return empty response
	return &types.CommunicationAnalyticsResponse{
		StandardResponse: pagination.CreateStandardResponse(0, filters.Page, filters.Limit),
		Data:             []types.CommunicationAnalytics{},
	}, nil
}
