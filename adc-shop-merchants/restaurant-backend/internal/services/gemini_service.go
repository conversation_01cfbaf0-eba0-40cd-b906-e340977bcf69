package services

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"restaurant-backend/internal/config"
	"restaurant-backend/internal/models"

	"github.com/google/generative-ai-go/genai"
	"github.com/sirupsen/logrus"
	"google.golang.org/api/option"
)

type GeminiService struct {
	client *genai.Client
	config *config.GeminiConfig
	logger *logrus.Logger
}

func NewGeminiService(cfg *config.GeminiConfig, logger *logrus.Logger) (*GeminiService, error) {
	ctx := context.Background()
	client, err := genai.NewClient(ctx, option.WithAPIKey(cfg.APIKey))
	if err != nil {
		return nil, fmt.Errorf("failed to create Gemini client: %w", err)
	}

	return &GeminiService{
		client: client,
		config: cfg,
		logger: logger,
	}, nil
}

// AnalyzeMenuImage analyzes a menu image and extracts menu items using Gemini Vision
func (s *GeminiService) AnalyzeMenuImage(ctx context.Context, imageURL, cuisineType, priceRange, restaurantName string) ([]models.AIGeneratedMenuItem, error) {
	model := s.client.GenerativeModel(s.config.Model)
	model.SetTemperature(s.config.Temperature)
	model.SetMaxOutputTokens(int32(s.config.MaxTokens))

	prompt := s.buildMenuImagePrompt(cuisineType, priceRange, restaurantName)

	// Download image data for Gemini
	imageData, err := s.downloadImageData(ctx, imageURL)
	if err != nil {
		return nil, fmt.Errorf("failed to download image: %w", err)
	}

	resp, err := model.GenerateContent(ctx,
		genai.Text(prompt),
		genai.ImageData("jpeg", imageData),
	)
	if err != nil {
		s.logger.WithError(err).Error("Failed to analyze menu image with Gemini")
		return nil, fmt.Errorf("failed to analyze menu image: %w", err)
	}

	if len(resp.Candidates) == 0 || len(resp.Candidates[0].Content.Parts) == 0 {
		return nil, fmt.Errorf("no response from Gemini")
	}

	// Extract text from response
	responseText := fmt.Sprintf("%v", resp.Candidates[0].Content.Parts[0])

	// Clean the response text (remove markdown formatting)
	cleanedText := s.cleanJSONResponse(responseText)

	// Parse the JSON response
	var menuItems []models.AIGeneratedMenuItem
	if err := json.Unmarshal([]byte(cleanedText), &menuItems); err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"response": responseText,
			"cleaned":  cleanedText,
		}).Error("Failed to parse Gemini response")
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	return menuItems, nil
}

// AnalyzeFoodImages analyzes multiple food images and generates menu items
func (s *GeminiService) AnalyzeFoodImages(ctx context.Context, imageURLs []string, cuisineType, priceRange, restaurantName string) ([]models.AIGeneratedMenuItem, error) {
	var allMenuItems []models.AIGeneratedMenuItem

	model := s.client.GenerativeModel(s.config.Model)
	model.SetTemperature(s.config.Temperature)
	model.SetMaxOutputTokens(int32(s.config.MaxTokens))

	for i, imageURL := range imageURLs {
		prompt := s.buildFoodImagePrompt(cuisineType, priceRange, restaurantName, i+1)

		// Download image data for Gemini
		imageData, err := s.downloadImageData(ctx, imageURL)
		if err != nil {
			s.logger.WithError(err).WithField("image_url", imageURL).Error("Failed to download image for Gemini")
			continue // Skip this image and continue with others
		}

		resp, err := model.GenerateContent(ctx,
			genai.Text(prompt),
			genai.ImageData("jpeg", imageData),
		)
		if err != nil {
			s.logger.WithError(err).WithField("image_url", imageURL).Error("Failed to analyze food image with Gemini")
			continue // Skip this image and continue with others
		}

		if len(resp.Candidates) == 0 || len(resp.Candidates[0].Content.Parts) == 0 {
			s.logger.WithField("image_url", imageURL).Warn("No response from Gemini for food image")
			continue
		}

		// Extract text from response
		responseText := fmt.Sprintf("%v", resp.Candidates[0].Content.Parts[0])

		// Clean the response text (remove markdown formatting)
		cleanedText := s.cleanJSONResponse(responseText)

		// Parse the JSON response
		var menuItems []models.AIGeneratedMenuItem
		if err := json.Unmarshal([]byte(cleanedText), &menuItems); err != nil {
			s.logger.WithError(err).WithFields(logrus.Fields{
				"response": responseText,
				"cleaned":  cleanedText,
			}).Error("Failed to parse Gemini food image response")
			continue
		}

		allMenuItems = append(allMenuItems, menuItems...)
	}

	return allMenuItems, nil
}

// EnhanceMenuText processes menu text and generates structured menu items using Gemini
func (s *GeminiService) EnhanceMenuText(ctx context.Context, menuText, cuisineType, priceRange, restaurantName string) ([]models.AIGeneratedMenuItem, error) {
	model := s.client.GenerativeModel(s.config.Model)
	model.SetTemperature(s.config.Temperature)
	model.SetMaxOutputTokens(int32(s.config.MaxTokens))

	prompt := s.buildTextEnhancementPrompt(menuText, cuisineType, priceRange, restaurantName)

	resp, err := model.GenerateContent(ctx, genai.Text(prompt))
	if err != nil {
		s.logger.WithError(err).Error("Failed to enhance menu text with Gemini")
		return nil, fmt.Errorf("failed to enhance menu text: %w", err)
	}

	if len(resp.Candidates) == 0 || len(resp.Candidates[0].Content.Parts) == 0 {
		return nil, fmt.Errorf("no response from Gemini")
	}

	// Extract text from response
	responseText := fmt.Sprintf("%v", resp.Candidates[0].Content.Parts[0])

	// Clean the response text (remove markdown formatting)
	cleanedText := s.cleanJSONResponse(responseText)

	// Parse the JSON response
	var menuItems []models.AIGeneratedMenuItem
	if err := json.Unmarshal([]byte(cleanedText), &menuItems); err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"response": responseText,
			"cleaned":  cleanedText,
		}).Error("Failed to parse Gemini text enhancement response")
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	return menuItems, nil
}

// GenerateFoodImageWithOptions generates a custom food image using Imagen
func (s *GeminiService) GenerateFoodImageWithOptions(ctx context.Context, dishName, description, cuisineType, style, theme, quality, size string) (string, error) {
	// Build a detailed prompt for Imagen with theme
	prompt := s.buildImageGenerationPromptWithOptions(dishName, description, cuisineType, theme)

	s.logger.WithFields(logrus.Fields{
		"dish_name":    dishName,
		"description":  description,
		"cuisine_type": cuisineType,
		"style":        style,
		"theme":        theme,
		"quality":      quality,
		"size":         size,
		"prompt":       prompt,
	}).Info("Generating image with Imagen")

	// Note: Imagen 4 is not yet available through the Generative AI API
	// For now, we return a placeholder URL that indicates the image would be generated
	// When Imagen 4 becomes available, this can be updated to use the actual API

	s.logger.WithFields(logrus.Fields{
		"dish_name": dishName,
		"prompt":    prompt,
	}).Info("Imagen 4 not yet available, returning placeholder")

	// Generate a placeholder URL that includes the dish name
	imageURL := fmt.Sprintf("https://placeholder-imagen.example.com/generated/%s.jpg",
		strings.ReplaceAll(strings.ToLower(dishName), " ", "-"))

	s.logger.WithFields(logrus.Fields{
		"dish_name": dishName,
		"image_url": imageURL,
	}).Info("Successfully generated placeholder image URL")

	return imageURL, nil
}

// Close closes the Gemini client
func (s *GeminiService) Close() error {
	return s.client.Close()
}

// Helper methods for building prompts (similar to OpenAI service)
func (s *GeminiService) buildMenuImagePrompt(cuisineType, priceRange, restaurantName string) string {
	prompt := `Analyze this menu image and extract all menu items. Return a JSON array of menu items with the following structure:

[
  {
    "name": "Item Name",
    "description": "Detailed description of the dish",
    "price": 12.99,
    "category": "Category Name",
    "ingredients": ["ingredient1", "ingredient2"],
    "allergens": ["allergen1", "allergen2"],
    "is_vegetarian": false,
    "is_vegan": false,
    "is_gluten_free": false,
    "is_spicy": false,
    "spice_level": 0,
    "preparation_time": 15,
    "tags": ["tag1", "tag2"]
  }
]

Guidelines:
- Extract all visible menu items with their prices
- Create detailed, appetizing descriptions for each item
- Categorize items appropriately (Appetizers, Main Courses, Desserts, etc.)
- Identify common ingredients and allergens
- Set dietary flags (vegetarian, vegan, gluten-free) based on typical ingredients
- Estimate spice levels (0-5 scale) and preparation times in minutes
- Add relevant tags for searchability`

	if cuisineType != "" {
		prompt += fmt.Sprintf("\n- Focus on %s cuisine characteristics", cuisineType)
	}
	if priceRange != "" {
		prompt += fmt.Sprintf("\n- Adjust pricing to match %s price range", priceRange)
	}
	if restaurantName != "" {
		prompt += fmt.Sprintf("\n- Consider this is for %s restaurant", restaurantName)
	}

	return prompt
}

func (s *GeminiService) buildFoodImagePrompt(cuisineType, priceRange, restaurantName string, imageNumber int) string {
	prompt := fmt.Sprintf(`Analyze this food image (#%d) and create a menu item. Return a JSON array with one menu item:

[
  {
    "name": "Dish Name",
    "description": "Detailed, appetizing description",
    "price": 12.99,
    "category": "Category",
    "ingredients": ["ingredient1", "ingredient2"],
    "allergens": ["allergen1", "allergen2"],
    "is_vegetarian": false,
    "is_vegan": false,
    "is_gluten_free": false,
    "is_spicy": false,
    "spice_level": 0,
    "preparation_time": 15,
    "tags": ["tag1", "tag2"]
  }
]

Guidelines:
- Create an appealing name for the dish
- Write a detailed, mouth-watering description
- Estimate appropriate pricing
- Identify visible ingredients and potential allergens
- Set dietary flags based on visible ingredients
- Estimate spice level and preparation time
- Add relevant tags`, imageNumber)

	if cuisineType != "" {
		prompt += fmt.Sprintf("\n- Style as %s cuisine", cuisineType)
	}
	if priceRange != "" {
		prompt += fmt.Sprintf("\n- Price according to %s range", priceRange)
	}
	if restaurantName != "" {
		prompt += fmt.Sprintf("\n- Consider this is for %s restaurant", restaurantName)
	}

	return prompt
}

func (s *GeminiService) buildTextEnhancementPrompt(menuText, cuisineType, priceRange, restaurantName string) string {
	prompt := fmt.Sprintf(`Enhance and structure this menu text into a JSON array of menu items:

Menu Text:
%s

Return a JSON array with the following structure:
[
  {
    "name": "Item Name",
    "description": "Enhanced, detailed description",
    "price": 12.99,
    "category": "Category Name",
    "ingredients": ["ingredient1", "ingredient2"],
    "allergens": ["allergen1", "allergen2"],
    "is_vegetarian": false,
    "is_vegan": false,
    "is_gluten_free": false,
    "is_spicy": false,
    "spice_level": 0,
    "preparation_time": 15,
    "tags": ["tag1", "tag2"]
  }
]

Guidelines:
- Parse all menu items from the text
- Create detailed, appetizing descriptions
- Estimate appropriate pricing if not provided
- Categorize items logically
- Identify likely ingredients and allergens
- Set dietary flags based on typical ingredients
- Estimate spice levels and preparation times
- Add relevant tags for searchability`, menuText)

	if cuisineType != "" {
		prompt += fmt.Sprintf("\n- Style as %s cuisine", cuisineType)
	}
	if priceRange != "" {
		prompt += fmt.Sprintf("\n- Price according to %s range", priceRange)
	}
	if restaurantName != "" {
		prompt += fmt.Sprintf("\n- Consider this is for %s restaurant", restaurantName)
	}

	return prompt
}

func (s *GeminiService) buildImageGenerationPromptWithOptions(dishName, description, cuisineType, theme string) string {
	prompt := fmt.Sprintf("Create a high-quality, appetizing image of %s", dishName)

	if description != "" {
		prompt += fmt.Sprintf(": %s", description)
	}

	if cuisineType != "" {
		prompt += fmt.Sprintf(", styled as %s cuisine", cuisineType)
	}

	// Add theme-specific styling
	switch theme {
	case "restaurant":
		prompt += ", professional restaurant presentation, elegant plating"
	case "rustic":
		prompt += ", rustic homemade style, warm lighting, wooden background"
	case "elegant":
		prompt += ", fine dining presentation, sophisticated plating, luxury setting"
	case "casual":
		prompt += ", casual dining style, comfortable atmosphere"
	case "street":
		prompt += ", street food style, authentic presentation"
	case "modern":
		prompt += ", modern minimalist style, clean presentation"
	case "traditional":
		prompt += ", traditional authentic style, cultural presentation"
	default:
		prompt += ", appetizing restaurant-style presentation"
	}

	prompt += ", high resolution, professional food photography"

	return prompt
}

func (s *GeminiService) downloadImageData(ctx context.Context, imageURL string) ([]byte, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", imageURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to download image: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to download image: status %d", resp.StatusCode)
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read image data: %w", err)
	}

	return data, nil
}

// cleanJSONResponse removes markdown formatting and comments from Gemini responses
func (s *GeminiService) cleanJSONResponse(response string) string {
	// Remove markdown code block formatting
	cleaned := strings.TrimSpace(response)

	// Remove ```json at the beginning
	if strings.HasPrefix(cleaned, "```json") {
		cleaned = strings.TrimPrefix(cleaned, "```json")
	} else if strings.HasPrefix(cleaned, "```") {
		cleaned = strings.TrimPrefix(cleaned, "```")
	}

	// Remove ``` at the end
	if strings.HasSuffix(cleaned, "```") {
		cleaned = strings.TrimSuffix(cleaned, "```")
	}

	// Remove JavaScript-style comments that Gemini sometimes includes
	lines := strings.Split(cleaned, "\n")
	var cleanedLines []string
	for i, line := range lines {
		// Remove inline comments (// comment)
		if commentIndex := strings.Index(line, "//"); commentIndex != -1 {
			// Check if the // is inside a string value
			beforeComment := line[:commentIndex]
			quoteCount := strings.Count(beforeComment, "\"") - strings.Count(beforeComment, "\\\"")
			// If even number of quotes, the // is outside a string
			if quoteCount%2 == 0 {
				line = strings.TrimSpace(line[:commentIndex])

				// If the line after removing comment doesn't end with comma but should
				// (i.e., it's not the last property in an object), add comma
				if !strings.HasSuffix(line, ",") && !strings.HasSuffix(line, "{") && !strings.HasSuffix(line, "}") && !strings.HasSuffix(line, "[") && !strings.HasSuffix(line, "]") {
					// Check if next non-empty line starts with a property or closing brace
					for j := i + 1; j < len(lines); j++ {
						nextLine := strings.TrimSpace(lines[j])
						if nextLine != "" {
							if strings.HasPrefix(nextLine, "\"") || strings.HasPrefix(nextLine, "}") {
								line += ","
							}
							break
						}
					}
				}
			}
		}
		if strings.TrimSpace(line) != "" {
			cleanedLines = append(cleanedLines, line)
		}
	}
	cleaned = strings.Join(cleanedLines, "\n")

	// Trim any remaining whitespace
	cleaned = strings.TrimSpace(cleaned)

	return cleaned
}
