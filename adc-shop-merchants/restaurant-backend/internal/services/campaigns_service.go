package services

import (
	"context"
	"fmt"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/types"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type CampaignService struct {
	campaignRepo *repositories.CampaignRepository
	logger       *logrus.Logger
}

func NewCampaignService(campaignRepo *repositories.CampaignRepository, logger *logrus.Logger) *CampaignService {
	return &CampaignService{
		campaignRepo: campaignRepo,
		logger:       logger,
	}
}

// Campaign methods
func (s *CampaignService) GetCampaigns(ctx context.Context, merchantID uuid.UUID, filters types.CampaignFilters) (*types.CampaignsResponse, error) {
	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	campaigns, total, err := s.campaignRepo.GetCampaigns(ctx, merchantID, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get campaigns")
		return nil, fmt.Errorf("failed to get campaigns: %w", err)
	}

	// Convert to response format
	campaignResponses := make([]types.CampaignResponse, len(campaigns))
	for i, campaign := range campaigns {
		campaignResponses[i] = s.convertCampaignToResponse(campaign)
	}

	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))

	return &types.CampaignsResponse{
		Data:       campaignResponses,
		Total:      total,
		Page:       filters.Page,
		Limit:      filters.Limit,
		TotalPages: totalPages,
	}, nil
}

func (s *CampaignService) GetCampaignByID(ctx context.Context, campaignID uuid.UUID) (*types.CampaignResponse, error) {
	campaign, err := s.campaignRepo.GetCampaignByID(ctx, campaignID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get campaign by ID")
		return nil, fmt.Errorf("failed to get campaign: %w", err)
	}

	response := s.convertCampaignToResponse(*campaign)
	return &response, nil
}

func (s *CampaignService) CreateCampaign(ctx context.Context, merchantID uuid.UUID, req types.CreateCampaignRequest) (*types.CampaignResponse, error) {
	campaign := &models.CommunicationCampaign{
		MerchantID:     merchantID,
		Name:           req.Name,
		Description:    req.Description,
		Type:           req.Type,
		TemplateID:     req.TemplateID,
		SegmentID:      req.SegmentID,
		Subject:        req.Subject,
		Content:        req.Content,
		ScheduledAt:    req.ScheduledAt,
		TargetAudience: req.TargetAudience,
		Recipients:     req.Recipients,
		Status:         "draft",
		Settings:       s.convertCampaignSettingsRequestToModel(req.Settings),
	}

	if err := s.campaignRepo.CreateCampaign(ctx, campaign); err != nil {
		s.logger.WithError(err).Error("Failed to create campaign")
		return nil, fmt.Errorf("failed to create campaign: %w", err)
	}

	// Reload with relationships
	campaign, err := s.campaignRepo.GetCampaignByID(ctx, campaign.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to reload campaign: %w", err)
	}

	response := s.convertCampaignToResponse(*campaign)
	return &response, nil
}

func (s *CampaignService) UpdateCampaign(ctx context.Context, campaignID uuid.UUID, req types.UpdateCampaignRequest) (*types.CampaignResponse, error) {
	updates := make(map[string]interface{})

	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.Type != nil {
		updates["type"] = *req.Type
	}
	if req.TemplateID != nil {
		updates["template_id"] = *req.TemplateID
	}
	if req.SegmentID != nil {
		updates["segment_id"] = *req.SegmentID
	}
	if req.Subject != nil {
		updates["subject"] = *req.Subject
	}
	if req.Content != nil {
		updates["content"] = *req.Content
	}
	if req.ScheduledAt != nil {
		updates["scheduled_at"] = *req.ScheduledAt
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}
	if req.TargetAudience != nil {
		updates["target_audience"] = *req.TargetAudience
	}
	if req.Recipients != nil {
		updates["recipients"] = *req.Recipients
	}
	if req.Settings != nil {
		updates["settings"] = s.convertCampaignSettingsRequestToModel(*req.Settings)
	}

	campaign, err := s.campaignRepo.UpdateCampaign(ctx, campaignID, updates)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update campaign")
		return nil, fmt.Errorf("failed to update campaign: %w", err)
	}

	response := s.convertCampaignToResponse(*campaign)
	return &response, nil
}

func (s *CampaignService) DeleteCampaign(ctx context.Context, campaignID uuid.UUID) error {
	if err := s.campaignRepo.DeleteCampaign(ctx, campaignID); err != nil {
		s.logger.WithError(err).Error("Failed to delete campaign")
		return fmt.Errorf("failed to delete campaign: %w", err)
	}

	return nil
}

func (s *CampaignService) ExecuteCampaign(ctx context.Context, campaignID uuid.UUID, req types.ExecuteCampaignRequest) (*types.CampaignResponse, error) {
	campaign, err := s.campaignRepo.ExecuteCampaign(ctx, campaignID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to execute campaign")
		return nil, fmt.Errorf("failed to execute campaign: %w", err)
	}

	// Here you would implement the actual campaign execution logic
	// For now, we'll just update the status
	s.logger.WithField("campaign_id", campaignID).Info("Campaign execution started")

	response := s.convertCampaignToResponse(*campaign)
	return &response, nil
}

// Template methods
func (s *CampaignService) GetTemplates(ctx context.Context, merchantID uuid.UUID, filters types.TemplateFilters) (*types.TemplatesResponse, error) {
	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	templates, total, err := s.campaignRepo.GetTemplates(ctx, merchantID, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get templates")
		return nil, fmt.Errorf("failed to get templates: %w", err)
	}

	// Convert to response format
	templateResponses := make([]types.TemplateResponse, len(templates))
	for i, template := range templates {
		templateResponses[i] = s.convertTemplateToResponse(template)
	}

	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))

	return &types.TemplatesResponse{
		Data:       templateResponses,
		Total:      total,
		Page:       filters.Page,
		Limit:      filters.Limit,
		TotalPages: totalPages,
	}, nil
}

func (s *CampaignService) GetTemplateByID(ctx context.Context, templateID uuid.UUID) (*types.TemplateResponse, error) {
	template, err := s.campaignRepo.GetTemplateByID(ctx, templateID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get template by ID")
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	response := s.convertTemplateToResponse(*template)
	return &response, nil
}

func (s *CampaignService) CreateTemplate(ctx context.Context, merchantID uuid.UUID, req types.CreateTemplateRequest) (*types.TemplateResponse, error) {
	template := &models.CommunicationTemplate{
		MerchantID:  merchantID,
		Name:        req.Name,
		Description: req.Description,
		Type:        req.Type,
		Category:    req.Category,
		Subject:     req.Subject,
		Content:     req.Content,
		Variables:   req.Variables,
		IsDefault:   req.IsDefault,
		IsActive:    true,
	}

	if err := s.campaignRepo.CreateTemplate(ctx, template); err != nil {
		s.logger.WithError(err).Error("Failed to create template")
		return nil, fmt.Errorf("failed to create template: %w", err)
	}

	response := s.convertTemplateToResponse(*template)
	return &response, nil
}

func (s *CampaignService) UpdateTemplate(ctx context.Context, templateID uuid.UUID, req types.UpdateTemplateRequest) (*types.TemplateResponse, error) {
	updates := make(map[string]interface{})

	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.Type != nil {
		updates["type"] = *req.Type
	}
	if req.Category != nil {
		updates["category"] = *req.Category
	}
	if req.Subject != nil {
		updates["subject"] = *req.Subject
	}
	if req.Content != nil {
		updates["content"] = *req.Content
	}
	if req.Variables != nil {
		updates["variables"] = *req.Variables
	}
	if req.IsDefault != nil {
		updates["is_default"] = *req.IsDefault
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	template, err := s.campaignRepo.UpdateTemplate(ctx, templateID, updates)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update template")
		return nil, fmt.Errorf("failed to update template: %w", err)
	}

	response := s.convertTemplateToResponse(*template)
	return &response, nil
}

func (s *CampaignService) DeleteTemplate(ctx context.Context, templateID uuid.UUID) error {
	if err := s.campaignRepo.DeleteTemplate(ctx, templateID); err != nil {
		s.logger.WithError(err).Error("Failed to delete template")
		return fmt.Errorf("failed to delete template: %w", err)
	}

	return nil
}

// Helper methods
func (s *CampaignService) convertCampaignToResponse(campaign models.CommunicationCampaign) types.CampaignResponse {
	response := types.CampaignResponse{
		ID:               campaign.ID,
		MerchantID:       campaign.MerchantID,
		Name:             campaign.Name,
		Description:      campaign.Description,
		Type:             campaign.Type,
		Status:           campaign.Status,
		TemplateID:       campaign.TemplateID,
		SegmentID:        campaign.SegmentID,
		Subject:          campaign.Subject,
		Content:          campaign.Content,
		ScheduledAt:      campaign.ScheduledAt,
		StartedAt:        campaign.StartedAt,
		CompletedAt:      campaign.CompletedAt,
		TargetAudience:   campaign.TargetAudience,
		Recipients:       campaign.Recipients,
		TotalRecipients:  campaign.TotalRecipients,
		SentCount:        campaign.SentCount,
		DeliveredCount:   campaign.DeliveredCount,
		OpenedCount:      campaign.OpenedCount,
		ClickedCount:     campaign.ClickedCount,
		UnsubscribeCount: campaign.UnsubscribeCount,
		BounceCount:      campaign.BounceCount,
		Settings:         s.convertCampaignSettingsToResponse(campaign.Settings),
		CreatedAt:        campaign.CreatedAt,
		UpdatedAt:        campaign.UpdatedAt,
	}

	// Add related data if available
	if campaign.Template != nil && campaign.Template.ID != uuid.Nil {
		templateResponse := s.convertTemplateToResponse(*campaign.Template)
		response.Template = &templateResponse
	}

	if campaign.Segment != nil && campaign.Segment.ID != uuid.Nil {
		segmentResponse := s.convertSegmentToResponse(*campaign.Segment)
		response.Segment = &segmentResponse
	}

	return response
}

func (s *CampaignService) convertTemplateToResponse(template models.CommunicationTemplate) types.TemplateResponse {
	return types.TemplateResponse{
		ID:          template.ID,
		MerchantID:  template.MerchantID,
		Name:        template.Name,
		Description: template.Description,
		Type:        template.Type,
		Category:    template.Category,
		Subject:     template.Subject,
		Content:     template.Content,
		Variables:   template.Variables,
		IsDefault:   template.IsDefault,
		IsActive:    template.IsActive,
		CreatedAt:   template.CreatedAt,
		UpdatedAt:   template.UpdatedAt,
	}
}

func (s *CampaignService) convertSegmentToResponse(segment models.CampaignSegment) types.SegmentResponse {
	return types.SegmentResponse{
		ID:            segment.ID,
		MerchantID:    segment.MerchantID,
		Name:          segment.Name,
		Description:   segment.Description,
		Type:          segment.Type,
		Criteria:      s.convertSegmentCriteriaToResponse(segment.Criteria),
		CustomerCount: segment.CustomerCount,
		LastUpdated:   segment.LastUpdated,
		IsActive:      segment.IsActive,
		CreatedAt:     segment.CreatedAt,
		UpdatedAt:     segment.UpdatedAt,
	}
}

func (s *CampaignService) convertCampaignSettingsToResponse(settings models.CampaignSettings) types.CampaignSettingsResponse {
	return types.CampaignSettingsResponse{
		SendTime:         settings.SendTime,
		Timezone:         settings.Timezone,
		TrackOpens:       settings.TrackOpens,
		TrackClicks:      settings.TrackClicks,
		AllowUnsubscribe: settings.AllowUnsubscribe,
		ReplyToEmail:     settings.ReplyToEmail,
		FromName:         settings.FromName,
		CustomFields:     settings.CustomFields,
	}
}

func (s *CampaignService) convertSegmentCriteriaToResponse(criteria models.SegmentCriteria) types.SegmentCriteriaResponse {
	response := types.SegmentCriteriaResponse{
		Gender:            criteria.Gender,
		Location:          criteria.Location,
		LastVisitDays:     criteria.LastVisitDays,
		TotalOrders:       criteria.TotalOrders,
		TotalSpent:        criteria.TotalSpent,
		AverageOrderValue: criteria.AverageOrderValue,
		FavoriteCategory:  criteria.FavoriteCategory,
		EmailEngagement:   criteria.EmailEngagement,
		SMSEngagement:     criteria.SMSEngagement,
		ReviewsLeft:       criteria.ReviewsLeft,
		ReferralsMade:     criteria.ReferralsMade,
		CustomFilters:     criteria.CustomFilters,
	}

	if criteria.AgeRange != nil {
		response.AgeRange = &types.AgeRangeResponse{
			Min: criteria.AgeRange.Min,
			Max: criteria.AgeRange.Max,
		}
	}

	return response
}

func (s *CampaignService) convertCampaignSettingsRequestToModel(req types.CampaignSettingsRequest) models.CampaignSettings {
	return models.CampaignSettings{
		SendTime:         req.SendTime,
		Timezone:         req.Timezone,
		TrackOpens:       req.TrackOpens,
		TrackClicks:      req.TrackClicks,
		AllowUnsubscribe: req.AllowUnsubscribe,
		ReplyToEmail:     req.ReplyToEmail,
		FromName:         req.FromName,
		CustomFields:     req.CustomFields,
	}
}

// Segment methods
func (s *CampaignService) GetSegments(ctx context.Context, merchantID uuid.UUID, filters types.SegmentFilters) (*types.SegmentsResponse, error) {
	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	segments, total, err := s.campaignRepo.GetSegments(ctx, merchantID, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get segments")
		return nil, fmt.Errorf("failed to get segments: %w", err)
	}

	// Convert to response format
	segmentResponses := make([]types.SegmentResponse, len(segments))
	for i, segment := range segments {
		segmentResponses[i] = s.convertSegmentToResponse(segment)
	}

	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))

	return &types.SegmentsResponse{
		Data:       segmentResponses,
		Total:      total,
		Page:       filters.Page,
		Limit:      filters.Limit,
		TotalPages: totalPages,
	}, nil
}

func (s *CampaignService) GetSegmentByID(ctx context.Context, segmentID uuid.UUID) (*types.SegmentResponse, error) {
	segment, err := s.campaignRepo.GetSegmentByID(ctx, segmentID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get segment by ID")
		return nil, fmt.Errorf("failed to get segment: %w", err)
	}

	response := s.convertSegmentToResponse(*segment)
	return &response, nil
}

func (s *CampaignService) CreateSegment(ctx context.Context, merchantID uuid.UUID, req types.CreateSegmentRequest) (*types.SegmentResponse, error) {
	segment := &models.CampaignSegment{
		MerchantID:  merchantID,
		Name:        req.Name,
		Description: req.Description,
		Type:        req.Type,
		Criteria:    s.convertSegmentCriteriaRequestToModel(req.Criteria),
		IsActive:    true,
	}

	if err := s.campaignRepo.CreateSegment(ctx, segment); err != nil {
		s.logger.WithError(err).Error("Failed to create segment")
		return nil, fmt.Errorf("failed to create segment: %w", err)
	}

	response := s.convertSegmentToResponse(*segment)
	return &response, nil
}

func (s *CampaignService) UpdateSegment(ctx context.Context, segmentID uuid.UUID, req types.UpdateSegmentRequest) (*types.SegmentResponse, error) {
	updates := make(map[string]interface{})

	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.Type != nil {
		updates["type"] = *req.Type
	}
	if req.Criteria != nil {
		updates["criteria"] = s.convertSegmentCriteriaRequestToModel(*req.Criteria)
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	segment, err := s.campaignRepo.UpdateSegment(ctx, segmentID, updates)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update segment")
		return nil, fmt.Errorf("failed to update segment: %w", err)
	}

	response := s.convertSegmentToResponse(*segment)
	return &response, nil
}

func (s *CampaignService) DeleteSegment(ctx context.Context, segmentID uuid.UUID) error {
	if err := s.campaignRepo.DeleteSegment(ctx, segmentID); err != nil {
		s.logger.WithError(err).Error("Failed to delete segment")
		return fmt.Errorf("failed to delete segment: %w", err)
	}

	return nil
}

func (s *CampaignService) GetSegmentCustomers(ctx context.Context, segmentID uuid.UUID) ([]map[string]interface{}, error) {
	customers, err := s.campaignRepo.GetSegmentCustomers(ctx, segmentID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get segment customers")
		return nil, fmt.Errorf("failed to get segment customers: %w", err)
	}

	return customers, nil
}

func (s *CampaignService) GetSegmentEmails(ctx context.Context, segmentID uuid.UUID) ([]string, error) {
	emails, err := s.campaignRepo.GetSegmentEmails(ctx, segmentID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get segment emails")
		return nil, fmt.Errorf("failed to get segment emails: %w", err)
	}

	return emails, nil
}

func (s *CampaignService) GetSegmentPhones(ctx context.Context, segmentID uuid.UUID) ([]string, error) {
	phones, err := s.campaignRepo.GetSegmentPhones(ctx, segmentID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get segment phones")
		return nil, fmt.Errorf("failed to get segment phones: %w", err)
	}

	return phones, nil
}

// Analytics methods
func (s *CampaignService) GetCommunicationAnalyticsOverview(ctx context.Context, merchantID uuid.UUID) (*types.CommunicationAnalyticsOverview, error) {
	overview, err := s.campaignRepo.GetCommunicationAnalyticsOverview(ctx, merchantID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get communication analytics overview")
		return nil, fmt.Errorf("failed to get analytics overview: %w", err)
	}

	return overview, nil
}

func (s *CampaignService) GetCampaignAnalytics(ctx context.Context, campaignID uuid.UUID) (*types.CampaignAnalyticsResponse, error) {
	analytics, err := s.campaignRepo.GetCampaignAnalytics(ctx, campaignID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get campaign analytics")
		return nil, fmt.Errorf("failed to get campaign analytics: %w", err)
	}

	return analytics, nil
}

func (s *CampaignService) convertSegmentCriteriaRequestToModel(req types.SegmentCriteriaRequest) models.SegmentCriteria {
	criteria := models.SegmentCriteria{
		Gender:            req.Gender,
		Location:          req.Location,
		LastVisitDays:     req.LastVisitDays,
		TotalOrders:       req.TotalOrders,
		TotalSpent:        req.TotalSpent,
		AverageOrderValue: req.AverageOrderValue,
		FavoriteCategory:  req.FavoriteCategory,
		EmailEngagement:   req.EmailEngagement,
		SMSEngagement:     req.SMSEngagement,
		ReviewsLeft:       req.ReviewsLeft,
		ReferralsMade:     req.ReferralsMade,
		CustomFilters:     req.CustomFilters,
	}

	if req.AgeRange != nil {
		criteria.AgeRange = &models.AgeRange{
			Min: req.AgeRange.Min,
			Max: req.AgeRange.Max,
		}
	}

	return criteria
}
