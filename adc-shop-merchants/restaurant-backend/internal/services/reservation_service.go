package services

import (
	"context"
	"fmt"
	"time"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/types"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// ReservationService handles reservation business logic
type ReservationService struct {
	reservationRepo repositories.ReservationRepository
	logger          *logrus.Logger
}

func NewReservationService(reservationRepo repositories.ReservationRepository, logger *logrus.Logger) *ReservationService {
	return &ReservationService{reservationRepo: reservationRepo, logger: logger}
}

// GetReservations retrieves reservations for a branch with filters
func (s *ReservationService) GetReservations(ctx context.Context, branchID uuid.UUID, filters types.ReservationFilters) (*types.ReservationsResponse, error) {
	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	reservations, total, err := s.reservationRepo.GetByBranchID(ctx, branchID, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get reservations")
		return nil, fmt.Errorf("failed to get reservations: %w", err)
	}

	// Convert to response format
	reservationResponses := make([]types.ReservationResponse, len(reservations))
	for i, reservation := range reservations {
		reservationResponses[i] = s.convertReservationToResponse(reservation)
	}

	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))

	return &types.ReservationsResponse{
		Data:       reservationResponses,
		Total:      total,
		Page:       filters.Page,
		Limit:      filters.Limit,
		TotalPages: totalPages,
	}, nil
}

// GetReservation retrieves a single reservation by ID
func (s *ReservationService) GetReservation(ctx context.Context, reservationID uuid.UUID) (*types.ReservationResponse, error) {
	reservation, err := s.reservationRepo.GetByID(ctx, reservationID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get reservation")
		return nil, fmt.Errorf("failed to get reservation: %w", err)
	}

	if reservation == nil {
		return nil, fmt.Errorf("reservation not found")
	}

	response := s.convertReservationToResponse(*reservation)
	return &response, nil
}

// CreateReservation creates a new reservation
func (s *ReservationService) CreateReservation(ctx context.Context, branchID uuid.UUID, req types.CreateReservationRequest) (*types.ReservationResponse, error) {
	// Parse reservation date
	reservationDate, err := time.Parse("2006-01-02", req.ReservationDate)
	if err != nil {
		return nil, fmt.Errorf("invalid reservation date format: %w", err)
	}

	// Parse reservation time and combine with date to create proper timestamp
	timeOnly, err := time.Parse("15:04", req.ReservationTime)
	if err != nil {
		return nil, fmt.Errorf("invalid reservation time format: %w", err)
	}

	// Combine date and time into a single timestamp
	// Use the date from reservationDate and time from timeOnly
	reservationDateTime := time.Date(
		reservationDate.Year(),
		reservationDate.Month(),
		reservationDate.Day(),
		timeOnly.Hour(),
		timeOnly.Minute(),
		0,        // seconds
		0,        // nanoseconds
		time.UTC, // timezone - you might want to use the branch's timezone
	)

	// Set default duration if not provided
	duration := req.Duration
	if duration == 0 {
		duration = 120 // 2 hours default
	}

	// Set default source if not provided
	source := req.Source
	if source == "" {
		source = types.ReservationSourceWebsite
	}

	reservation := &models.Reservation{
		BranchID:        branchID,
		CustomerName:    req.CustomerName,
		CustomerPhone:   req.CustomerPhone,
		CustomerEmail:   req.CustomerEmail,
		PartySize:       req.PartySize,
		ReservationDate: reservationDate,
		ReservationTime: reservationDateTime,
		Duration:        duration,
		TableID:         req.TableID,
		Status:          types.ReservationStatusPending,
		SpecialRequests: req.SpecialRequests,
		Notes:           req.Notes,
		Source:          source,
	}

	if err := s.reservationRepo.Create(ctx, reservation); err != nil {
		s.logger.WithError(err).Error("Failed to create reservation")
		return nil, fmt.Errorf("failed to create reservation: %w", err)
	}

	response := s.convertReservationToResponse(*reservation)
	return &response, nil
}

// UpdateReservation updates an existing reservation
func (s *ReservationService) UpdateReservation(ctx context.Context, reservationID uuid.UUID, req types.UpdateReservationRequest) (*types.ReservationResponse, error) {
	reservation, err := s.reservationRepo.GetByID(ctx, reservationID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get reservation for update")
		return nil, fmt.Errorf("failed to get reservation: %w", err)
	}

	if reservation == nil {
		return nil, fmt.Errorf("reservation not found")
	}

	// Update fields if provided
	if req.CustomerName != "" {
		reservation.CustomerName = req.CustomerName
	}
	if req.CustomerPhone != "" {
		reservation.CustomerPhone = req.CustomerPhone
	}
	if req.CustomerEmail != "" {
		reservation.CustomerEmail = req.CustomerEmail
	}
	if req.PartySize > 0 {
		reservation.PartySize = req.PartySize
	}
	if req.ReservationDate != "" {
		if date, err := time.Parse("2006-01-02", req.ReservationDate); err == nil {
			reservation.ReservationDate = date
		}
	}
	if req.ReservationTime != "" {
		if timeVal, err := time.Parse("15:04", req.ReservationTime); err == nil {
			reservation.ReservationTime = timeVal
		}
	}
	if req.Duration > 0 {
		reservation.Duration = req.Duration
	}
	if req.TableID != nil {
		reservation.TableID = req.TableID
	}
	if req.Status != "" {
		reservation.Status = req.Status
	}
	if req.SpecialRequests != "" {
		reservation.SpecialRequests = req.SpecialRequests
	}
	if req.Notes != "" {
		reservation.Notes = req.Notes
	}
	if req.CancellationReason != "" {
		reservation.CancellationReason = req.CancellationReason
	}

	if err := s.reservationRepo.Update(ctx, reservation); err != nil {
		s.logger.WithError(err).Error("Failed to update reservation")
		return nil, fmt.Errorf("failed to update reservation: %w", err)
	}

	response := s.convertReservationToResponse(*reservation)
	return &response, nil
}

// GetTodayReservations retrieves today's reservations for a branch
func (s *ReservationService) GetTodayReservations(ctx context.Context, branchID uuid.UUID) ([]types.ReservationResponse, error) {
	reservations, err := s.reservationRepo.GetTodayReservations(ctx, branchID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get today's reservations")
		return nil, fmt.Errorf("failed to get today's reservations: %w", err)
	}

	// Convert to response format
	reservationResponses := make([]types.ReservationResponse, len(reservations))
	for i, reservation := range reservations {
		reservationResponses[i] = s.convertReservationToResponse(reservation)
	}

	return reservationResponses, nil
}

// GetAvailability retrieves available time slots for a date
func (s *ReservationService) GetAvailability(ctx context.Context, branchID uuid.UUID, date time.Time) (*types.AvailabilityResponse, error) {
	timeSlots, err := s.reservationRepo.GetAvailability(ctx, branchID, date)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get availability")
		return nil, fmt.Errorf("failed to get availability: %w", err)
	}

	return &types.AvailabilityResponse{
		Date:      date.Format("2006-01-02"),
		TimeSlots: timeSlots,
	}, nil
}

// GetReservationStats retrieves reservation statistics for a branch
func (s *ReservationService) GetReservationStats(ctx context.Context, branchID uuid.UUID, period string) (*types.ReservationStats, error) {
	stats, err := s.reservationRepo.GetReservationStats(ctx, branchID, period)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get reservation stats")
		return nil, fmt.Errorf("failed to get reservation stats: %w", err)
	}

	return stats, nil
}

// Slug-based methods

// GetReservationsBySlug retrieves reservations for a branch using shop and branch slugs
func (s *ReservationService) GetReservationsBySlug(ctx context.Context, shopSlug, branchSlug string, filters types.ReservationFilters) (*types.ReservationsResponse, error) {
	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	reservations, total, err := s.reservationRepo.GetByBranchSlug(ctx, shopSlug, branchSlug, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get reservations by slug")
		return nil, fmt.Errorf("failed to get reservations: %w", err)
	}

	// Convert to response format
	reservationResponses := make([]types.ReservationResponse, len(reservations))
	for i, reservation := range reservations {
		reservationResponses[i] = s.convertReservationToResponse(reservation)
	}

	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))

	return &types.ReservationsResponse{
		Data:       reservationResponses,
		Total:      total,
		Page:       filters.Page,
		Limit:      filters.Limit,
		TotalPages: totalPages,
	}, nil
}

// GetReservationBySlug retrieves a single reservation by slug
func (s *ReservationService) GetReservationBySlug(ctx context.Context, slug string) (*types.ReservationResponse, error) {
	reservation, err := s.reservationRepo.GetBySlug(ctx, slug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get reservation by slug")
		return nil, fmt.Errorf("failed to get reservation: %w", err)
	}

	if reservation == nil {
		return nil, fmt.Errorf("reservation not found")
	}

	response := s.convertReservationToResponse(*reservation)
	return &response, nil
}

// UpdateReservationBySlug updates an existing reservation by slug
func (s *ReservationService) UpdateReservationBySlug(ctx context.Context, slug string, req types.UpdateReservationRequest) (*types.ReservationResponse, error) {
	reservation, err := s.reservationRepo.GetBySlug(ctx, slug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get reservation for update by slug")
		return nil, fmt.Errorf("failed to get reservation: %w", err)
	}

	if reservation == nil {
		return nil, fmt.Errorf("reservation not found")
	}

	// Update fields if provided
	if req.CustomerName != "" {
		reservation.CustomerName = req.CustomerName
	}
	if req.CustomerPhone != "" {
		reservation.CustomerPhone = req.CustomerPhone
	}
	if req.CustomerEmail != "" {
		reservation.CustomerEmail = req.CustomerEmail
	}
	if req.PartySize > 0 {
		reservation.PartySize = req.PartySize
	}
	if req.ReservationDate != "" {
		if date, err := time.Parse("2006-01-02", req.ReservationDate); err == nil {
			reservation.ReservationDate = date
		}
	}
	if req.ReservationTime != "" {
		if timeVal, err := time.Parse("15:04", req.ReservationTime); err == nil {
			reservation.ReservationTime = timeVal
		}
	}
	if req.Duration > 0 {
		reservation.Duration = req.Duration
	}
	if req.TableID != nil {
		reservation.TableID = req.TableID
	}
	if req.Status != "" {
		reservation.Status = req.Status
	}
	if req.SpecialRequests != "" {
		reservation.SpecialRequests = req.SpecialRequests
	}
	if req.Notes != "" {
		reservation.Notes = req.Notes
	}
	if req.CancellationReason != "" {
		reservation.CancellationReason = req.CancellationReason
	}

	if err := s.reservationRepo.Update(ctx, reservation); err != nil {
		s.logger.WithError(err).Error("Failed to update reservation by slug")
		return nil, fmt.Errorf("failed to update reservation: %w", err)
	}

	response := s.convertReservationToResponse(*reservation)
	return &response, nil
}

// GetTodayReservationsBySlug retrieves today's reservations using shop and branch slugs
func (s *ReservationService) GetTodayReservationsBySlug(ctx context.Context, shopSlug, branchSlug string) ([]types.ReservationResponse, error) {
	reservations, err := s.reservationRepo.GetTodayReservationsBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get today's reservations by slug")
		return nil, fmt.Errorf("failed to get today's reservations: %w", err)
	}

	// Convert to response format
	reservationResponses := make([]types.ReservationResponse, len(reservations))
	for i, reservation := range reservations {
		reservationResponses[i] = s.convertReservationToResponse(reservation)
	}

	return reservationResponses, nil
}

// GetAvailabilityBySlug retrieves available time slots using shop and branch slugs
func (s *ReservationService) GetAvailabilityBySlug(ctx context.Context, shopSlug, branchSlug string, date time.Time) (*types.AvailabilityResponse, error) {
	timeSlots, err := s.reservationRepo.GetAvailabilityBySlug(ctx, shopSlug, branchSlug, date)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get availability by slug")
		return nil, fmt.Errorf("failed to get availability: %w", err)
	}

	return &types.AvailabilityResponse{
		Date:      date.Format("2006-01-02"),
		TimeSlots: timeSlots,
	}, nil
}

// GetReservationStatsBySlug retrieves reservation statistics using shop and branch slugs
func (s *ReservationService) GetReservationStatsBySlug(ctx context.Context, shopSlug, branchSlug string, period string) (*types.ReservationStats, error) {
	stats, err := s.reservationRepo.GetReservationStatsBySlug(ctx, shopSlug, branchSlug, period)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get reservation stats by slug")
		return nil, fmt.Errorf("failed to get reservation stats: %w", err)
	}

	return stats, nil
}

// CancelReservationBySlug cancels a reservation by slug
func (s *ReservationService) CancelReservationBySlug(ctx context.Context, reservationSlug string, reason string) (*types.ReservationResponse, error) {
	// Get the reservation first to check if it exists and can be cancelled
	reservation, err := s.reservationRepo.GetBySlug(ctx, reservationSlug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get reservation for cancellation by slug")
		return nil, fmt.Errorf("failed to get reservation: %w", err)
	}

	if reservation == nil {
		return nil, fmt.Errorf("reservation not found")
	}

	// Check if reservation can be cancelled
	if !reservation.CanBeCancelled() {
		return nil, fmt.Errorf("reservation cannot be cancelled (current status: %s)", reservation.Status)
	}

	// Update reservation status to cancelled
	now := time.Now()
	reservation.Status = types.ReservationStatusCancelled
	reservation.CancelledAt = &now
	if reason != "" {
		reservation.CancellationReason = reason
	}

	// Save the updated reservation
	if err := s.reservationRepo.Update(ctx, reservation); err != nil {
		s.logger.WithError(err).Error("Failed to cancel reservation by slug")
		return nil, fmt.Errorf("failed to cancel reservation: %w", err)
	}

	response := s.convertReservationToResponse(*reservation)
	return &response, nil
}

// Helper method to convert model to response
func (s *ReservationService) convertReservationToResponse(reservation models.Reservation) types.ReservationResponse {
	response := types.ReservationResponse{
		ID:                 reservation.ID,
		Slug:               reservation.Slug,
		BranchID:           reservation.BranchID,
		CustomerName:       reservation.CustomerName,
		CustomerPhone:      reservation.CustomerPhone,
		CustomerEmail:      reservation.CustomerEmail,
		PartySize:          reservation.PartySize,
		ReservationDate:    reservation.ReservationDate,
		ReservationTime:    reservation.ReservationTime,
		Duration:           reservation.Duration,
		TableID:            reservation.TableID,
		Status:             reservation.Status,
		SpecialRequests:    reservation.SpecialRequests,
		Notes:              reservation.Notes,
		Source:             reservation.Source,
		CheckedInAt:        reservation.CheckedInAt,
		CompletedAt:        reservation.CompletedAt,
		CancelledAt:        reservation.CancelledAt,
		CancellationReason: reservation.CancellationReason,
		CreatedAt:          reservation.CreatedAt,
		UpdatedAt:          reservation.UpdatedAt,
	}

	// Add table name if table is loaded
	if reservation.Table != nil {
		response.TableName = reservation.Table.Name
	}

	return response
}
