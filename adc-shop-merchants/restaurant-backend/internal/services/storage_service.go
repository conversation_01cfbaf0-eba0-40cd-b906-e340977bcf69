package services

import (
	"context"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"path/filepath"
	"strings"
	"time"

	"restaurant-backend/internal/config"

	"cloud.google.com/go/storage"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"google.golang.org/api/option"
)

// StorageService handles file uploads to Google Cloud Storage
type StorageService struct {
	client    *storage.Client
	bucket    string
	baseURL   string
	logger    *logrus.Logger
	projectID string
}

// NewStorageService creates a new storage service
func NewStorageService(cfg *config.Config, logger *logrus.Logger) (*StorageService, error) {
	ctx := context.Background()

	var client *storage.Client
	var err error

	// Initialize GCS client with credentials
	if cfg.GCS.CredentialsKey != "" {
		client, err = storage.NewClient(ctx, option.WithCredentialsFile(cfg.GCS.CredentialsKey))
	} else {
		// Use default credentials (for production with service account)
		client, err = storage.NewClient(ctx)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to create storage client: %w", err)
	}

	baseURL := cfg.GCS.BaseURL
	if baseURL == "" {
		baseURL = fmt.Sprintf("https://storage.googleapis.com/%s", cfg.GCS.BucketName)
	}

	return &StorageService{
		client:    client,
		bucket:    cfg.GCS.BucketName,
		baseURL:   baseURL,
		logger:    logger,
		projectID: cfg.GCS.ProjectID,
	}, nil
}

// UploadFile uploads a file to Google Cloud Storage
func (s *StorageService) UploadFile(ctx context.Context, file multipart.File, header *multipart.FileHeader, folder string) (string, error) {
	// Generate unique filename
	ext := filepath.Ext(header.Filename)
	filename := fmt.Sprintf("%s_%d%s", uuid.New().String(), time.Now().Unix(), ext)
	objectName := fmt.Sprintf("%s/%s", folder, filename)

	// Get bucket handle
	bucket := s.client.Bucket(s.bucket)

	// Create object handle
	obj := bucket.Object(objectName)

	// Create writer
	writer := obj.NewWriter(ctx)
	defer writer.Close()

	// Set content type
	writer.ContentType = header.Header.Get("Content-Type")
	if writer.ContentType == "" {
		writer.ContentType = "application/octet-stream"
	}

	// Set cache control for images
	if strings.HasPrefix(writer.ContentType, "image/") {
		writer.CacheControl = "public, max-age=86400" // 24 hours
	}

	// Copy file content to GCS
	if _, err := io.Copy(writer, file); err != nil {
		s.logger.WithError(err).Error("Failed to upload file to GCS")
		return "", fmt.Errorf("failed to upload file: %w", err)
	}

	// Close writer to finalize upload
	if err := writer.Close(); err != nil {
		s.logger.WithError(err).Error("Failed to close GCS writer")
		return "", fmt.Errorf("failed to finalize upload: %w", err)
	}

	// Make object publicly readable
	if err := obj.ACL().Set(ctx, storage.AllUsers, storage.RoleReader); err != nil {
		s.logger.WithError(err).Warn("Failed to make object public")
		// Don't return error as upload was successful
	}

	// Return public URL
	publicURL := fmt.Sprintf("%s/%s", s.baseURL, objectName)
	s.logger.WithFields(logrus.Fields{
		"filename":   header.Filename,
		"object":     objectName,
		"public_url": publicURL,
	}).Info("File uploaded successfully to GCS")

	return publicURL, nil
}

// DeleteFile deletes a file from Google Cloud Storage
func (s *StorageService) DeleteFile(ctx context.Context, fileURL string) error {
	// Extract object name from URL
	objectName := s.extractObjectNameFromURL(fileURL)
	if objectName == "" {
		return fmt.Errorf("invalid file URL: %s", fileURL)
	}

	// Get bucket handle
	bucket := s.client.Bucket(s.bucket)

	// Delete object
	obj := bucket.Object(objectName)
	if err := obj.Delete(ctx); err != nil {
		s.logger.WithError(err).WithField("object", objectName).Error("Failed to delete file from GCS")
		return fmt.Errorf("failed to delete file: %w", err)
	}

	s.logger.WithField("object", objectName).Info("File deleted successfully from GCS")
	return nil
}

// ValidateFile validates uploaded file
func (s *StorageService) ValidateFile(header *multipart.FileHeader, allowedTypes []string, maxSize int64) error {
	// Check file size
	if header.Size > maxSize {
		return fmt.Errorf("file size %d exceeds maximum allowed size %d", header.Size, maxSize)
	}

	// Check file type
	contentType := header.Header.Get("Content-Type")
	if contentType == "" {
		return fmt.Errorf("content type not specified")
	}

	// Validate against allowed types
	for _, allowedType := range allowedTypes {
		if strings.HasPrefix(contentType, allowedType) {
			return nil
		}
	}

	return fmt.Errorf("file type %s not allowed", contentType)
}

// extractObjectNameFromURL extracts the object name from a GCS URL
func (s *StorageService) extractObjectNameFromURL(fileURL string) string {
	// Handle different URL formats
	baseURLs := []string{
		fmt.Sprintf("https://storage.googleapis.com/%s/", s.bucket),
		fmt.Sprintf("https://storage.cloud.google.com/%s/", s.bucket),
		fmt.Sprintf("%s/", s.baseURL),
	}

	for _, baseURL := range baseURLs {
		if strings.HasPrefix(fileURL, baseURL) {
			return strings.TrimPrefix(fileURL, baseURL)
		}
	}

	return ""
}

// Close closes the storage client
func (s *StorageService) Close() error {
	return s.client.Close()
}

// UploadMenuItemImage uploads a menu item image
func (s *StorageService) UploadMenuItemImage(ctx context.Context, file multipart.File, header *multipart.FileHeader, shopID, branchID, itemID string) (string, error) {
	// Validate file
	allowedTypes := []string{"image/jpeg", "image/png", "image/webp"}
	maxSize := int64(5 * 1024 * 1024) // 5MB

	if err := s.ValidateFile(header, allowedTypes, maxSize); err != nil {
		return "", err
	}

	// Create folder structure: menu-items/shopID/branchID/itemID
	folder := fmt.Sprintf("menu-items/%s/%s/%s", shopID, branchID, itemID)

	return s.UploadFile(ctx, file, header, folder)
}

// UploadTableImage uploads a table image
func (s *StorageService) UploadTableImage(ctx context.Context, file multipart.File, header *multipart.FileHeader, shopID, branchID, tableID string) (string, error) {
	// Validate file
	allowedTypes := []string{"image/jpeg", "image/png", "image/webp"}
	maxSize := int64(5 * 1024 * 1024) // 5MB

	if err := s.ValidateFile(header, allowedTypes, maxSize); err != nil {
		return "", err
	}

	// Create folder structure: tables/shopID/branchID/tableID
	folder := fmt.Sprintf("tables/%s/%s/%s", shopID, branchID, tableID)

	return s.UploadFile(ctx, file, header, folder)
}

// UploadStaffAvatar uploads a staff avatar image
func (s *StorageService) UploadStaffAvatar(ctx context.Context, file multipart.File, header *multipart.FileHeader, shopID, branchID, staffID string) (string, error) {
	// Validate file
	allowedTypes := []string{"image/jpeg", "image/png", "image/webp"}
	maxSize := int64(2 * 1024 * 1024) // 2MB for avatars

	if err := s.ValidateFile(header, allowedTypes, maxSize); err != nil {
		return "", err
	}

	// Create folder structure: staff/shopID/branchID/staffID
	folder := fmt.Sprintf("staff/%s/%s/%s", shopID, branchID, staffID)

	return s.UploadFile(ctx, file, header, folder)
}

// UploadImageFromURL downloads an image from a URL and uploads it to GCS
func (s *StorageService) UploadImageFromURL(ctx context.Context, imageURL, folder string) (string, error) {
	// Download image from URL
	resp, err := http.Get(imageURL)
	if err != nil {
		s.logger.WithError(err).WithField("url", imageURL).Error("Failed to download image from URL")
		return "", fmt.Errorf("failed to download image: %w", err)
	}
	defer resp.Body.Close()

	// Check if response is successful
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("failed to download image: HTTP %d", resp.StatusCode)
	}

	// Determine file extension from content type
	contentType := resp.Header.Get("Content-Type")
	var ext string
	switch contentType {
	case "image/jpeg":
		ext = ".jpg"
	case "image/png":
		ext = ".png"
	case "image/webp":
		ext = ".webp"
	default:
		// Try to get extension from URL
		ext = filepath.Ext(imageURL)
		if ext == "" {
			ext = ".jpg" // Default to jpg
		}
	}

	// Generate unique filename
	filename := fmt.Sprintf("%s_%d%s", uuid.New().String(), time.Now().Unix(), ext)
	objectName := fmt.Sprintf("%s/%s", folder, filename)

	// Get bucket handle
	bucket := s.client.Bucket(s.bucket)

	// Create object handle
	obj := bucket.Object(objectName)

	// Create writer
	writer := obj.NewWriter(ctx)
	defer writer.Close()

	// Set content type
	writer.ContentType = contentType
	if writer.ContentType == "" {
		writer.ContentType = "image/jpeg" // Default
	}

	// Set cache control for images
	writer.CacheControl = "public, max-age=86400" // 24 hours

	// Copy image content to GCS
	if _, err := io.Copy(writer, resp.Body); err != nil {
		s.logger.WithError(err).Error("Failed to upload image to GCS")
		return "", fmt.Errorf("failed to upload image: %w", err)
	}

	// Close writer to finalize upload
	if err := writer.Close(); err != nil {
		s.logger.WithError(err).Error("Failed to close GCS writer")
		return "", fmt.Errorf("failed to finalize upload: %w", err)
	}

	// Make object publicly readable
	if err := obj.ACL().Set(ctx, storage.AllUsers, storage.RoleReader); err != nil {
		s.logger.WithError(err).Warn("Failed to make object public")
		// Don't return error as upload was successful
	}

	// Return public URL
	publicURL := fmt.Sprintf("%s/%s", s.baseURL, objectName)
	s.logger.WithFields(logrus.Fields{
		"source_url": imageURL,
		"object":     objectName,
		"public_url": publicURL,
	}).Info("Image downloaded and uploaded successfully to GCS")

	return publicURL, nil
}

// UploadAIGeneratedImage uploads an AI-generated image from URL to GCS
func (s *StorageService) UploadAIGeneratedImage(ctx context.Context, imageURL, shopID, branchID, itemName string) (string, error) {
	// Create folder structure: ai-generated/shopID/branchID
	folder := fmt.Sprintf("ai-generated/%s/%s", shopID, branchID)

	return s.UploadImageFromURL(ctx, imageURL, folder)
}
