package services

import (
	"context"
	"fmt"
	"strings"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
)

type EntityTagService interface {
	// Menu Item tag operations
	SyncMenuItemTags(ctx context.Context, menuItemID uuid.UUID, tagNames []string) error
	GetMenuItemTags(ctx context.Context, menuItemID uuid.UUID) ([]models.Tag, error)

	// Review tag operations
	SyncReviewTags(ctx context.Context, reviewID uuid.UUID, tagNames []string) error
	GetReviewTags(ctx context.Context, reviewID uuid.UUID) ([]models.Tag, error)

	// Service tag operations
	SyncServiceTags(ctx context.Context, serviceID uuid.UUID, tagNames []string) error
	GetServiceTags(ctx context.Context, serviceID uuid.UUID) ([]models.Tag, error)

	// Staff tag operations
	SyncStaffTags(ctx context.Context, staffID uuid.UUID, tagNames []string) error
	GetStaffTags(ctx context.Context, staffID uuid.UUID) ([]models.Tag, error)

	// Campaign tag operations
	SyncCampaignTags(ctx context.Context, campaignID uuid.UUID, tagNames []string) error
	GetCampaignTags(ctx context.Context, campaignID uuid.UUID) ([]models.Tag, error)

	// Order tag operations
	SyncOrderTags(ctx context.Context, orderID uuid.UUID, tagNames []string) error
	GetOrderTags(ctx context.Context, orderID uuid.UUID) ([]models.Tag, error)

	// Reservation tag operations
	SyncReservationTags(ctx context.Context, reservationID uuid.UUID, tagNames []string) error
	GetReservationTags(ctx context.Context, reservationID uuid.UUID) ([]models.Tag, error)

	// Table tag operations
	SyncTableTags(ctx context.Context, tableID uuid.UUID, tagNames []string) error
	GetTableTags(ctx context.Context, tableID uuid.UUID) ([]models.Tag, error)

	// Notification tag operations
	SyncNotificationTags(ctx context.Context, notificationID uuid.UUID, tagNames []string) error
	GetNotificationTags(ctx context.Context, notificationID uuid.UUID) ([]models.Tag, error)

	// Generic entity tag operations
	SyncEntityTags(ctx context.Context, entityType string, entityID uuid.UUID, branchID uuid.UUID, tagNames []string) error
	GetEntityTags(ctx context.Context, entityType string, entityID uuid.UUID) ([]models.Tag, error)

	// Tag creation and management
	FindOrCreateTags(ctx context.Context, branchID uuid.UUID, tagNames []string) ([]models.Tag, error)

	// Bulk operations
	GetEntitiesByTags(ctx context.Context, entityType string, branchID uuid.UUID, tagNames []string) ([]uuid.UUID, error)
	GetTagUsageByEntity(ctx context.Context, branchID uuid.UUID) (map[string]int, error)
}

type entityTagService struct {
	tagRepo         repositories.TagRepository
	menuRepo        repositories.MenuItemRepository
	reviewRepo      repositories.ReviewRepository
	serviceRepo     repositories.ServiceRepository
	shopRepo        repositories.ShopRepository
	campaignRepo    repositories.CampaignRepository
	orderRepo       repositories.OrderRepository
	reservationRepo repositories.ReservationRepository
	tableRepo       repositories.TableRepository
	logger          *logrus.Logger
}

func NewEntityTagService(
	tagRepo repositories.TagRepository,
	menuRepo repositories.MenuItemRepository,
	reviewRepo repositories.ReviewRepository,
	serviceRepo repositories.ServiceRepository,
	shopRepo repositories.ShopRepository,
	campaignRepo repositories.CampaignRepository,
	orderRepo repositories.OrderRepository,
	reservationRepo repositories.ReservationRepository,
	tableRepo repositories.TableRepository,
	logger *logrus.Logger,
) EntityTagService {
	return &entityTagService{
		tagRepo:         tagRepo,
		menuRepo:        menuRepo,
		reviewRepo:      reviewRepo,
		serviceRepo:     serviceRepo,
		shopRepo:        shopRepo,
		campaignRepo:    campaignRepo,
		orderRepo:       orderRepo,
		reservationRepo: reservationRepo,
		tableRepo:       tableRepo,
		logger:          logger,
	}
}

// Menu Item tag operations
func (s *entityTagService) SyncMenuItemTags(ctx context.Context, menuItemID uuid.UUID, tagNames []string) error {
	// Get menu item to get branch ID
	menuItem, err := s.menuRepo.GetByID(ctx, menuItemID)
	if err != nil {
		return fmt.Errorf("failed to get menu item: %w", err)
	}

	return s.SyncEntityTags(ctx, "menu_item", menuItemID, menuItem.BranchID, tagNames)
}

func (s *entityTagService) GetMenuItemTags(ctx context.Context, menuItemID uuid.UUID) ([]models.Tag, error) {
	return s.GetEntityTags(ctx, "menu_item", menuItemID)
}

// Review tag operations
func (s *entityTagService) SyncReviewTags(ctx context.Context, reviewID uuid.UUID, tagNames []string) error {
	// Get review to get branch ID
	review, err := s.reviewRepo.GetByID(ctx, reviewID)
	if err != nil {
		return fmt.Errorf("failed to get review: %w", err)
	}

	return s.SyncEntityTags(ctx, "review", reviewID, review.BranchID, tagNames)
}

func (s *entityTagService) GetReviewTags(ctx context.Context, reviewID uuid.UUID) ([]models.Tag, error) {
	return s.GetEntityTags(ctx, "review", reviewID)
}

// Service tag operations
func (s *entityTagService) SyncServiceTags(ctx context.Context, serviceID uuid.UUID, tagNames []string) error {
	// For now, we'll require the branch ID to be passed through context or use a different approach
	// This is a placeholder implementation that would need to be updated based on actual repository methods
	return fmt.Errorf("service tag sync requires branch ID - use SyncEntityTags directly")
}

func (s *entityTagService) GetServiceTags(ctx context.Context, serviceID uuid.UUID) ([]models.Tag, error) {
	return s.GetEntityTags(ctx, "service", serviceID)
}

// Staff tag operations
func (s *entityTagService) SyncStaffTags(ctx context.Context, staffID uuid.UUID, tagNames []string) error {
	// For now, we'll require the branch ID to be passed through context or use a different approach
	// This is a placeholder implementation that would need to be updated based on actual repository methods
	return fmt.Errorf("staff tag sync requires branch ID - use SyncEntityTags directly")
}

func (s *entityTagService) GetStaffTags(ctx context.Context, staffID uuid.UUID) ([]models.Tag, error) {
	return s.GetEntityTags(ctx, "staff", staffID)
}

// Campaign tag operations
func (s *entityTagService) SyncCampaignTags(ctx context.Context, campaignID uuid.UUID, tagNames []string) error {
	// For now, we'll require the branch ID to be passed through context or use a different approach
	// This is a placeholder implementation that would need to be updated based on actual repository methods
	return fmt.Errorf("campaign tag sync requires branch ID - use SyncEntityTags directly")
}

func (s *entityTagService) GetCampaignTags(ctx context.Context, campaignID uuid.UUID) ([]models.Tag, error) {
	return s.GetEntityTags(ctx, "campaign", campaignID)
}

// Order tag operations
func (s *entityTagService) SyncOrderTags(ctx context.Context, orderID uuid.UUID, tagNames []string) error {
	// For now, we'll require the branch ID to be passed through context or use a different approach
	// This is a placeholder implementation that would need to be updated based on actual repository methods
	return fmt.Errorf("order tag sync requires branch ID - use SyncEntityTags directly")
}

func (s *entityTagService) GetOrderTags(ctx context.Context, orderID uuid.UUID) ([]models.Tag, error) {
	return s.GetEntityTags(ctx, "order", orderID)
}

// Reservation tag operations
func (s *entityTagService) SyncReservationTags(ctx context.Context, reservationID uuid.UUID, tagNames []string) error {
	// For now, we'll require the branch ID to be passed through context or use a different approach
	// This is a placeholder implementation that would need to be updated based on actual repository methods
	return fmt.Errorf("reservation tag sync requires branch ID - use SyncEntityTags directly")
}

func (s *entityTagService) GetReservationTags(ctx context.Context, reservationID uuid.UUID) ([]models.Tag, error) {
	return s.GetEntityTags(ctx, "reservation", reservationID)
}

// Table tag operations
func (s *entityTagService) SyncTableTags(ctx context.Context, tableID uuid.UUID, tagNames []string) error {
	// For now, we'll require the branch ID to be passed through context or use a different approach
	// This is a placeholder implementation that would need to be updated based on actual repository methods
	return fmt.Errorf("table tag sync requires branch ID - use SyncEntityTags directly")
}

func (s *entityTagService) GetTableTags(ctx context.Context, tableID uuid.UUID) ([]models.Tag, error) {
	return s.GetEntityTags(ctx, "table", tableID)
}

// Notification tag operations
func (s *entityTagService) SyncNotificationTags(ctx context.Context, notificationID uuid.UUID, tagNames []string) error {
	// For notifications, we'll use a generic approach since they might not have a direct branch relationship
	// We'll need to pass the branch ID explicitly or get it from context
	return fmt.Errorf("notification tag sync requires branch ID - use SyncEntityTags directly")
}

func (s *entityTagService) GetNotificationTags(ctx context.Context, notificationID uuid.UUID) ([]models.Tag, error) {
	return s.GetEntityTags(ctx, "notification", notificationID)
}

// Generic entity tag operations
func (s *entityTagService) SyncEntityTags(ctx context.Context, entityType string, entityID uuid.UUID, branchID uuid.UUID, tagNames []string) error {
	s.logger.WithFields(logrus.Fields{
		"entity_type": entityType,
		"entity_id":   entityID,
		"branch_id":   branchID,
		"tag_count":   len(tagNames),
	}).Info("Syncing entity tags")

	// Find or create tags
	tags, err := s.FindOrCreateTags(ctx, branchID, tagNames)
	if err != nil {
		return fmt.Errorf("failed to find/create tags: %w", err)
	}

	// Extract tag IDs
	tagIDs := make([]uuid.UUID, len(tags))
	for i, tag := range tags {
		tagIDs[i] = tag.ID
	}

	// Assign tags to entity (this will replace existing tags)
	if err := s.tagRepo.AssignTagsToEntity(ctx, entityType, entityID, tagIDs); err != nil {
		return fmt.Errorf("failed to assign tags to entity: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"entity_type": entityType,
		"entity_id":   entityID,
		"tag_count":   len(tagIDs),
	}).Info("Successfully synced entity tags")

	return nil
}

func (s *entityTagService) GetEntityTags(ctx context.Context, entityType string, entityID uuid.UUID) ([]models.Tag, error) {
	entityTags, err := s.tagRepo.GetEntityTags(ctx, entityType, entityID)
	if err != nil {
		return nil, fmt.Errorf("failed to get entity tags: %w", err)
	}

	tags := make([]models.Tag, len(entityTags))
	for i, entityTag := range entityTags {
		tags[i] = entityTag.Tag
	}

	return tags, nil
}

// Tag creation and management
func (s *entityTagService) FindOrCreateTags(ctx context.Context, branchID uuid.UUID, tagNames []string) ([]models.Tag, error) {
	var tags []models.Tag

	for _, tagName := range tagNames {
		if tagName == "" {
			continue
		}

		// Generate slug
		slug := models.Slugify(tagName)

		// Try to find existing tag
		tag, err := s.tagRepo.GetTagBySlug(ctx, branchID, slug)
		if err == nil {
			tags = append(tags, *tag)
			continue
		}

		// Create new tag if not found
		newTag := &models.Tag{
			BranchID:   branchID,
			Name:       tagName,
			Slug:       slug,
			Category:   s.categorizeTag(tagName),
			Color:      s.getDefaultColorForCategory(s.categorizeTag(tagName)),
			Icon:       s.getDefaultIconForCategory(s.categorizeTag(tagName)),
			UsageCount: 0,
			IsSystem:   false,
			IsActive:   true,
		}

		if err := s.tagRepo.CreateTag(ctx, newTag); err != nil {
			s.logger.WithError(err).Errorf("Failed to create tag: %s", tagName)
			continue
		}

		tags = append(tags, *newTag)
		s.logger.WithField("tag_name", tagName).Info("Created new tag")
	}

	return tags, nil
}

// Helper methods for tag categorization
func (s *entityTagService) categorizeTag(tagName string) string {
	tagLower := strings.ToLower(tagName)

	// Dietary tags
	dietaryTags := []string{"vegetarian", "vegan", "gluten-free", "dairy-free", "nut-free", "keto", "low-carb", "high-protein"}
	for _, dietary := range dietaryTags {
		if strings.Contains(tagLower, dietary) {
			return "dietary"
		}
	}

	// Style tags
	styleTags := []string{"spicy", "mild", "grilled", "fried", "steamed", "raw", "baked", "roasted"}
	for _, style := range styleTags {
		if strings.Contains(tagLower, style) {
			return "style"
		}
	}

	// Popularity tags
	popularityTags := []string{"popular", "new", "chef", "bestseller", "trending", "signature", "special"}
	for _, popularity := range popularityTags {
		if strings.Contains(tagLower, popularity) {
			return "popularity"
		}
	}

	// Occasion tags
	occasionTags := []string{"date", "family", "business", "celebration", "comfort", "quick"}
	for _, occasion := range occasionTags {
		if strings.Contains(tagLower, occasion) {
			return "occasion"
		}
	}

	// Default to general category
	return "general"
}

func (s *entityTagService) getDefaultColorForCategory(category string) string {
	switch category {
	case "dietary":
		return "#22c55e"
	case "style":
		return "#f59e0b"
	case "popularity":
		return "#ef4444"
	case "occasion":
		return "#8b5cf6"
	default:
		return "#8a745c"
	}
}

func (s *entityTagService) getDefaultIconForCategory(category string) string {
	switch category {
	case "dietary":
		return "🥗"
	case "style":
		return "🍳"
	case "popularity":
		return "⭐"
	case "occasion":
		return "🎉"
	default:
		return "🏷️"
	}
}

// Bulk operations
func (s *entityTagService) GetEntitiesByTags(ctx context.Context, entityType string, branchID uuid.UUID, tagNames []string) ([]uuid.UUID, error) {
	// Find tags by names
	tags, err := s.FindOrCreateTags(ctx, branchID, tagNames)
	if err != nil {
		return nil, fmt.Errorf("failed to find tags: %w", err)
	}

	// Extract tag IDs
	tagIDs := make([]uuid.UUID, len(tags))
	for i, tag := range tags {
		tagIDs[i] = tag.ID
	}

	// Get entities by tag IDs
	return s.tagRepo.GetEntitiesByTags(ctx, entityType, tagIDs)
}

func (s *entityTagService) GetTagUsageByEntity(ctx context.Context, branchID uuid.UUID) (map[string]int, error) {
	// This would require a more complex query to get tag usage by entity type
	// For now, return a simple implementation
	usage := make(map[string]int)

	// Get all entity tags for the branch
	// This is a simplified implementation - in production you'd want a more efficient query
	entityTypes := []string{"menu_item", "review", "service", "staff", "campaign", "order", "reservation", "table", "notification"}

	for _, entityType := range entityTypes {
		// Count entities with tags for this type
		// This would need a custom repository method for efficiency
		usage[entityType] = 0 // Placeholder
	}

	return usage, nil
}
