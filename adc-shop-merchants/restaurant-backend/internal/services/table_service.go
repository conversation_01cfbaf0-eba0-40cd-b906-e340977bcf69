package services

import (
	"context"
	"fmt"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/types"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// TableService handles table business logic
type TableService struct {
	tableRepo repositories.TableRepository
	areaRepo  repositories.AreaRepository
	logger    *logrus.Logger
}

func NewTableService(
	tableRepo repositories.TableRepository,
	areaRepo repositories.AreaRepository,
	logger *logrus.Logger,
) *TableService {
	return &TableService{
		tableRepo: tableRepo,
		areaRepo:  areaRepo,
		logger:    logger,
	}
}

// Table methods
func (s *TableService) GetTables(ctx context.Context, branchID uuid.UUID, filters types.TableFilters) (*types.TablesResponse, error) {
	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	tables, total, err := s.tableRepo.GetByBranchID(ctx, branchID, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get tables")
		return nil, fmt.Errorf("failed to get tables: %w", err)
	}

	// Convert to response format
	tableResponses := make([]types.TableResponse, len(tables))
	for i, table := range tables {
		tableResponses[i] = s.convertTableToResponse(table)
	}

	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))

	return &types.TablesResponse{
		Data:       tableResponses,
		Total:      total,
		Page:       filters.Page,
		Limit:      filters.Limit,
		TotalPages: totalPages,
	}, nil
}

func (s *TableService) GetTableByID(ctx context.Context, branchID, tableID uuid.UUID) (*types.TableResponse, error) {
	table, err := s.tableRepo.GetByID(ctx, tableID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get table")
		return nil, fmt.Errorf("failed to get table: %w", err)
	}

	// Verify table belongs to branch
	if table.BranchID != branchID {
		return nil, fmt.Errorf("table not found")
	}

	response := s.convertTableToResponse(*table)
	return &response, nil
}

func (s *TableService) CreateTable(ctx context.Context, branchID uuid.UUID, req types.CreateTableRequest) (*types.TableResponse, error) {
	// Check if table number already exists
	existingTable, _ := s.tableRepo.GetByNumber(ctx, branchID, req.Number)
	if existingTable != nil {
		return nil, fmt.Errorf("table with number %s already exists", req.Number)
	}

	// Verify area exists if provided
	if req.AreaID != nil {
		area, err := s.areaRepo.GetByID(ctx, *req.AreaID)
		if err != nil || area.BranchID != branchID {
			return nil, fmt.Errorf("invalid area")
		}
	}

	table := &models.Table{
		BranchID:  branchID,
		AreaID:    req.AreaID,
		Name:      req.Name,
		Number:    convertStringToInt(req.Number),
		Capacity:  req.Capacity,
		Shape:     req.Shape,
		Position:  convertPositionRequestToModel(req.Position),
		Status:    types.TableStatusAvailable,
		IsActive:  req.IsActive,
		QRCodeURL: req.QRCode,
	}

	if err := s.tableRepo.Create(ctx, table); err != nil {
		s.logger.WithError(err).Error("Failed to create table")
		return nil, fmt.Errorf("failed to create table: %w", err)
	}

	// Reload table with relationships
	createdTable, err := s.tableRepo.GetByID(ctx, table.ID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get created table")
		return nil, fmt.Errorf("failed to get created table")
	}

	response := s.convertTableToResponse(*createdTable)
	return &response, nil
}

func (s *TableService) UpdateTable(ctx context.Context, branchID, tableID uuid.UUID, req types.UpdateTableRequest) (*types.TableResponse, error) {
	table, err := s.tableRepo.GetByID(ctx, tableID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get table")
		return nil, fmt.Errorf("failed to get table: %w", err)
	}

	// Verify table belongs to branch
	if table.BranchID != branchID {
		return nil, fmt.Errorf("table not found")
	}

	// Check if new number conflicts with existing table
	if req.Number != nil && *req.Number != convertIntToString(table.Number) {
		newNumber := convertStringToInt(*req.Number)
		existingTable, _ := s.tableRepo.GetByNumber(ctx, branchID, *req.Number)
		if existingTable != nil {
			return nil, fmt.Errorf("table with number %s already exists", *req.Number)
		}
		table.Number = newNumber
	}

	// Verify area exists if provided
	if req.AreaID != nil {
		area, err := s.areaRepo.GetByID(ctx, *req.AreaID)
		if err != nil || area.BranchID != branchID {
			return nil, fmt.Errorf("invalid area")
		}
		table.AreaID = req.AreaID
	}

	// Update fields
	if req.Name != nil {
		table.Name = *req.Name
	}
	if req.Capacity != nil {
		table.Capacity = *req.Capacity
	}
	// MinCapacity and MaxCapacity are not in the table model, skipping
	if req.Shape != nil {
		table.Shape = *req.Shape
	}
	if req.Position != nil {
		table.Position = convertPositionRequestToModel(*req.Position)
	}
	if req.IsActive != nil {
		table.IsActive = *req.IsActive
	}
	if req.QRCode != nil {
		table.QRCodeURL = *req.QRCode
	}

	if err := s.tableRepo.Update(ctx, table); err != nil {
		s.logger.WithError(err).Error("Failed to update table")
		return nil, fmt.Errorf("failed to update table: %w", err)
	}

	response := s.convertTableToResponse(*table)
	return &response, nil
}

func (s *TableService) DeleteTable(ctx context.Context, branchID, tableID uuid.UUID) error {
	table, err := s.tableRepo.GetByID(ctx, tableID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get table")
		return fmt.Errorf("failed to get table: %w", err)
	}

	// Verify table belongs to branch
	if table.BranchID != branchID {
		return fmt.Errorf("table not found")
	}

	if err := s.tableRepo.Delete(ctx, tableID); err != nil {
		s.logger.WithError(err).Error("Failed to delete table")
		return fmt.Errorf("failed to delete table: %w", err)
	}

	return nil
}

func (s *TableService) UpdateTableStatus(ctx context.Context, branchID, tableID uuid.UUID, req types.UpdateTableStatusRequest) (*types.TableResponse, error) {
	table, err := s.tableRepo.GetByID(ctx, tableID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get table")
		return nil, fmt.Errorf("failed to get table: %w", err)
	}

	// Verify table belongs to branch
	if table.BranchID != branchID {
		return nil, fmt.Errorf("table not found")
	}

	if err := s.tableRepo.UpdateStatus(ctx, tableID, req.Status); err != nil {
		s.logger.WithError(err).Error("Failed to update table status")
		return nil, fmt.Errorf("failed to update table status: %w", err)
	}

	// Reload table with updated status
	updatedTable, err := s.tableRepo.GetByID(ctx, tableID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get updated table")
		return nil, fmt.Errorf("failed to get updated table")
	}

	response := s.convertTableToResponse(*updatedTable)
	return &response, nil
}

func (s *TableService) GenerateQRCode(ctx context.Context, branchID, tableID uuid.UUID, req types.GenerateQRCodeRequest) (*types.QRCodeResponse, error) {
	table, err := s.tableRepo.GetByID(ctx, tableID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get table")
		return nil, fmt.Errorf("failed to get table: %w", err)
	}

	// Verify table belongs to branch
	if table.BranchID != branchID {
		return nil, fmt.Errorf("table not found")
	}

	// Generate QR code (simplified implementation)
	qrCode := fmt.Sprintf("table-%s-%s", branchID.String(), tableID.String())
	qrCodeURL := fmt.Sprintf("/table/%s", tableID.String())

	// Update table with QR code
	table.QRCodeURL = qrCode
	if err := s.tableRepo.Update(ctx, table); err != nil {
		s.logger.WithError(err).Error("Failed to update table with QR code")
		return nil, fmt.Errorf("failed to update table with QR code: %w", err)
	}

	return &types.QRCodeResponse{
		TableID:   tableID,
		QRCode:    qrCode,
		QRCodeURL: qrCodeURL,
		Format:    req.Format,
		Size:      req.Size,
		// ImageData would be generated by a QR code library in a real implementation
	}, nil
}

// Area methods
func (s *TableService) GetAreas(ctx context.Context, branchID uuid.UUID, filters types.AreaFilters) (*types.AreasResponse, error) {
	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	areas, total, err := s.areaRepo.GetByBranchID(ctx, branchID, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get areas")
		return nil, fmt.Errorf("failed to get areas: %w", err)
	}

	// Convert to response format
	areaResponses := make([]types.AreaResponse, len(areas))
	for i, area := range areas {
		areaResponses[i] = s.convertAreaToResponse(area)
	}

	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))

	return &types.AreasResponse{
		Data:       areaResponses,
		Total:      total,
		Page:       filters.Page,
		Limit:      filters.Limit,
		TotalPages: totalPages,
	}, nil
}

func (s *TableService) CreateArea(ctx context.Context, branchID uuid.UUID, req types.CreateAreaRequest) (*types.AreaResponse, error) {
	area := &models.TableArea{
		BranchID:    branchID,
		Name:        req.Name,
		Description: req.Description,
		Color:       req.Color,
		IsActive:    req.IsActive,
	}

	if err := s.areaRepo.Create(ctx, area); err != nil {
		s.logger.WithError(err).Error("Failed to create area")
		return nil, fmt.Errorf("failed to create area: %w", err)
	}

	// Reload area with relationships
	createdArea, err := s.areaRepo.GetByID(ctx, area.ID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get created area")
		return nil, fmt.Errorf("failed to get created area")
	}

	response := s.convertAreaToResponse(*createdArea)
	return &response, nil
}

func (s *TableService) UpdateArea(ctx context.Context, branchID, areaID uuid.UUID, req types.UpdateAreaRequest) (*types.AreaResponse, error) {
	area, err := s.areaRepo.GetByID(ctx, areaID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get area")
		return nil, fmt.Errorf("failed to get area: %w", err)
	}

	// Verify area belongs to branch
	if area.BranchID != branchID {
		return nil, fmt.Errorf("area not found")
	}

	// Update fields
	if req.Name != nil {
		area.Name = *req.Name
	}
	if req.Description != nil {
		area.Description = *req.Description
	}
	if req.Color != nil {
		area.Color = *req.Color
	}
	// Position is not supported in TableArea model, skipping
	if req.IsActive != nil {
		area.IsActive = *req.IsActive
	}

	if err := s.areaRepo.Update(ctx, area); err != nil {
		s.logger.WithError(err).Error("Failed to update area")
		return nil, fmt.Errorf("failed to update area: %w", err)
	}

	response := s.convertAreaToResponse(*area)
	return &response, nil
}

func (s *TableService) DeleteArea(ctx context.Context, branchID, areaID uuid.UUID) error {
	area, err := s.areaRepo.GetByID(ctx, areaID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get area")
		return fmt.Errorf("failed to get area: %w", err)
	}

	// Verify area belongs to branch
	if area.BranchID != branchID {
		return fmt.Errorf("area not found")
	}

	if err := s.areaRepo.Delete(ctx, areaID); err != nil {
		s.logger.WithError(err).Error("Failed to delete area")
		return fmt.Errorf("failed to delete area: %w", err)
	}

	return nil
}

// Helper functions
func convertStringToInt(s string) int {
	// Simple conversion - in a real implementation you might want better error handling
	if s == "" {
		return 0
	}
	// For now, just return a hash of the string or parse if it's numeric
	// This is a simplified implementation
	return len(s) // placeholder
}

func convertPositionRequestToModel(req types.PositionRequest) models.PositionData {
	return models.PositionData{
		X: req.X,
		Y: req.Y,
	}
}

func (s *TableService) convertTableToResponse(table models.Table) types.TableResponse {
	var areaResponse *types.AreaResponse
	if table.Area != nil {
		area := s.convertAreaToResponse(*table.Area)
		areaResponse = &area
	}

	return types.TableResponse{
		ID:          table.ID,
		BranchID:    table.BranchID,
		AreaID:      table.AreaID,
		Area:        areaResponse,
		Number:      convertIntToString(table.Number),
		Name:        table.Name,
		Capacity:    table.Capacity,
		MinCapacity: table.Capacity, // Model doesn't have min/max, use capacity
		MaxCapacity: table.Capacity,
		Shape:       table.Shape,
		Position:    convertPositionModelToResponse(table.Position),
		Status:      table.Status,
		IsActive:    table.IsActive,
		QRCode:      table.QRCodeURL,
		QRCodeURL:   table.QRCodeURL,
		CreatedAt:   table.CreatedAt,
		UpdatedAt:   table.UpdatedAt,
	}
}

func (s *TableService) convertAreaToResponse(area models.TableArea) types.AreaResponse {
	return types.AreaResponse{
		ID:          area.ID,
		BranchID:    area.BranchID,
		FloorID:     area.FloorID,
		Name:        area.Name,
		Description: area.Description,
		Color:       area.Color,
		Position:    types.PositionResponse{}, // TableArea model doesn't have position
		IsActive:    area.IsActive,
		TableCount:  len(area.Tables),
		CreatedAt:   area.CreatedAt,
		UpdatedAt:   area.UpdatedAt,
	}
}

func convertIntToString(i int) string {
	return fmt.Sprintf("%d", i)
}

func convertPositionModelToResponse(pos models.PositionData) types.PositionResponse {
	return types.PositionResponse{
		X:      pos.X,
		Y:      pos.Y,
		Width:  0, // Model doesn't have width/height/angle
		Height: 0,
		Angle:  0,
	}
}
