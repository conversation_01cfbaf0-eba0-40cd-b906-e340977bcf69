package database

import (
	"math/rand"
	"time"

	"restaurant-backend/internal/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// SeedAnalyticsData creates sample analytics data for development
func SeedAnalyticsData(db *gorm.DB) error {
	// Check if analytics data already exists
	var count int64
	db.Model(&models.SalesMetric{}).Count(&count)
	if count > 0 {
		return nil // Analytics data already exists
	}

	// Get existing shop and branch
	var shop models.Shop
	if err := db.Where("slug = ?", "thai-delight").First(&shop).Error; err != nil {
		return err
	}

	var branch models.ShopBranch
	if err := db.Where("shop_id = ? AND slug = ?", shop.ID, "downtown").First(&branch).Error; err != nil {
		return err
	}

	// Get existing menu items
	var menuItems []models.MenuItem
	if err := db.Where("branch_id = ?", branch.ID).Find(&menuItems).Error; err != nil {
		return err
	}

	if len(menuItems) == 0 {
		return nil // No menu items to create analytics for
	}

	// Seed sales metrics for the last 30 days
	if err := seedSalesMetrics(db, branch.ID); err != nil {
		return err
	}

	// Seed menu item metrics
	if err := seedMenuItemMetrics(db, branch.ID, menuItems); err != nil {
		return err
	}

	// Seed customer metrics
	if err := seedCustomerMetrics(db, branch.ID); err != nil {
		return err
	}

	// Get existing staff members for staff metrics
	var staffMembers []models.User
	if err := db.Where("branch_id = ?", branch.ID).Find(&staffMembers).Error; err != nil {
		return err
	}

	// Seed staff metrics if staff members exist
	if len(staffMembers) > 0 {
		if err := seedStaffMetrics(db, branch.ID, staffMembers); err != nil {
			return err
		}
	}

	// Get existing tables for table metrics
	var tables []models.Table
	if err := db.Where("branch_id = ?", branch.ID).Find(&tables).Error; err != nil {
		return err
	}

	// Seed table metrics if tables exist
	if len(tables) > 0 {
		if err := seedTableMetrics(db, branch.ID, tables); err != nil {
			return err
		}
	}

	// Seed analytics reports
	if err := seedAnalyticsReports(db, branch.ID); err != nil {
		return err
	}

	return nil
}

func seedSalesMetrics(db *gorm.DB, branchID uuid.UUID) error {
	now := time.Now()

	// Create sales metrics for the last 30 days
	for i := 0; i < 30; i++ {
		date := now.AddDate(0, 0, -i)
		dayStart := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())

		// Generate different patterns for different days
		baseOrders := 20 + rand.Intn(40)                               // 20-60 orders per day
		baseRevenue := float64(baseOrders) * (50 + rand.Float64()*100) // $50-150 per order average

		// Weekend boost
		if date.Weekday() == time.Saturday || date.Weekday() == time.Sunday {
			baseOrders = int(float64(baseOrders) * 1.3)
			baseRevenue = baseRevenue * 1.3
		}

		// Create hourly metrics for business hours (10 AM to 10 PM)
		for hour := 10; hour <= 22; hour++ {
			// Peak hours: 12-2 PM and 6-8 PM
			multiplier := 1.0
			if (hour >= 12 && hour <= 14) || (hour >= 18 && hour <= 20) {
				multiplier = 2.0
			} else if hour < 11 || hour > 21 {
				multiplier = 0.3
			}

			hourlyOrders := int(float64(baseOrders) * multiplier / 12) // Distribute across 12 hours
			hourlyRevenue := baseRevenue * multiplier / 12

			// Revenue metric
			revenueMetric := &models.SalesMetric{
				BranchID:   branchID,
				Date:       dayStart,
				Hour:       hour,
				MetricType: models.SalesMetricRevenue,
				Value:      hourlyRevenue,
				Count:      1,
			}

			if err := db.Create(revenueMetric).Error; err != nil {
				return err
			}

			// Order count metric
			orderMetric := &models.SalesMetric{
				BranchID:   branchID,
				Date:       dayStart,
				Hour:       hour,
				MetricType: models.SalesMetricOrderCount,
				Value:      float64(hourlyOrders),
				Count:      1,
			}

			if err := db.Create(orderMetric).Error; err != nil {
				return err
			}

			// Customer count metric (slightly less than orders due to multiple items per order)
			customerCount := int(float64(hourlyOrders) * 0.8)
			customerMetric := &models.SalesMetric{
				BranchID:   branchID,
				Date:       dayStart,
				Hour:       hour,
				MetricType: models.SalesMetricCustomerCount,
				Value:      float64(customerCount),
				Count:      1,
			}

			if err := db.Create(customerMetric).Error; err != nil {
				return err
			}
		}
	}

	return nil
}

func seedMenuItemMetrics(db *gorm.DB, branchID uuid.UUID, menuItems []models.MenuItem) error {
	now := time.Now()

	// Create menu item metrics for the last 30 days
	for i := 0; i < 30; i++ {
		date := now.AddDate(0, 0, -i)
		dayStart := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())

		// Create metrics for each menu item with varying popularity
		for j, item := range menuItems {
			// Make some items more popular than others
			popularityFactor := 1.0
			if j < len(menuItems)/3 { // Top third are popular
				popularityFactor = 2.0
			} else if j > len(menuItems)*2/3 { // Bottom third are less popular
				popularityFactor = 0.5
			}

			baseOrders := int(float64(2+rand.Intn(8)) * popularityFactor) // 2-10 orders per item per day
			quantitySold := baseOrders + rand.Intn(baseOrders)            // Sometimes multiple quantities per order
			revenue := float64(quantitySold) * item.Price

			metric := &models.MenuItemMetric{
				BranchID:     branchID,
				MenuItemID:   item.ID,
				Date:         dayStart,
				OrderCount:   baseOrders,
				QuantitySold: quantitySold,
				Revenue:      revenue,
			}

			if err := db.Create(metric).Error; err != nil {
				return err
			}
		}
	}

	return nil
}

func seedCustomerMetrics(db *gorm.DB, branchID uuid.UUID) error {
	now := time.Now()

	// Create customer metrics for the last 30 days
	for i := 0; i < 30; i++ {
		date := now.AddDate(0, 0, -i)
		dayStart := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())

		// Daily customer metrics
		newCustomers := 5 + rand.Intn(15)        // 5-20 new customers per day
		returningCustomers := 10 + rand.Intn(25) // 10-35 returning customers per day

		avgSpend := 75.0 + rand.Float64()*50 // $75-125 average spend

		// New customers metric
		newCustomerMetric := &models.CustomerMetric{
			BranchID:      branchID,
			Date:          dayStart,
			VisitCount:    newCustomers,
			OrderCount:    newCustomers,
			TotalSpent:    float64(newCustomers) * avgSpend,
			AvgOrderValue: avgSpend,
			LastVisit:     dayStart.Add(12 * time.Hour), // Noon
			CustomerType:  models.CustomerTypeNew,
		}

		if err := db.Create(newCustomerMetric).Error; err != nil {
			return err
		}

		// Returning customers metric
		returningCustomerMetric := &models.CustomerMetric{
			BranchID:      branchID,
			Date:          dayStart,
			VisitCount:    returningCustomers,
			OrderCount:    returningCustomers + rand.Intn(returningCustomers/2), // Some have multiple orders
			TotalSpent:    float64(returningCustomers) * avgSpend * 1.2,         // Returning customers spend more
			AvgOrderValue: avgSpend * 1.2,
			LastVisit:     dayStart.Add(12 * time.Hour),
			CustomerType:  models.CustomerTypeReturning,
		}

		if err := db.Create(returningCustomerMetric).Error; err != nil {
			return err
		}
	}

	return nil
}

func seedStaffMetrics(db *gorm.DB, branchID uuid.UUID, staffMembers []models.User) error {
	now := time.Now()

	// Create staff metrics for the last 30 days
	for i := 0; i < 30; i++ {
		date := now.AddDate(0, 0, -i)
		dayStart := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())

		// Create metrics for each staff member
		for _, staff := range staffMembers {
			// Skip if staff member doesn't belong to this branch
			if staff.BranchID == nil || *staff.BranchID != branchID {
				continue
			}

			// Generate realistic staff performance data
			ordersProcessed := 5 + rand.Intn(20)                           // 5-25 orders per day
			revenue := float64(ordersProcessed) * (30 + rand.Float64()*70) // $30-100 per order average
			hoursWorked := 6.0 + rand.Float64()*4.0                        // 6-10 hours per day
			avgOrderTime := 10.0 + rand.Float64()*20.0                     // 10-30 minutes per order
			customerRating := 3.5 + rand.Float64()*1.5                     // 3.5-5.0 rating
			efficiency := float64(ordersProcessed) / hoursWorked           // orders per hour

			// Weekend patterns
			if date.Weekday() == time.Saturday || date.Weekday() == time.Sunday {
				ordersProcessed = int(float64(ordersProcessed) * 1.4)
				revenue = revenue * 1.4
				hoursWorked = hoursWorked * 1.2
			}

			metric := &models.StaffMetric{
				BranchID:        branchID,
				UserID:          staff.ID,
				Date:            dayStart,
				OrdersProcessed: ordersProcessed,
				Revenue:         revenue,
				HoursWorked:     hoursWorked,
				AvgOrderTime:    avgOrderTime,
				CustomerRating:  customerRating,
				Efficiency:      efficiency,
			}

			if err := db.Create(metric).Error; err != nil {
				return err
			}
		}
	}

	return nil
}

func seedTableMetrics(db *gorm.DB, branchID uuid.UUID, tables []models.Table) error {
	now := time.Now()

	// Create table metrics for the last 30 days
	for i := 0; i < 30; i++ {
		date := now.AddDate(0, 0, -i)
		dayStart := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())

		// Create hourly metrics for business hours (10 AM to 10 PM)
		for hour := 10; hour <= 22; hour++ {
			// Create metrics for each table
			for _, table := range tables {
				// Skip if table doesn't belong to this branch
				if table.BranchID != branchID {
					continue
				}

				// Generate realistic table utilization data
				// Peak hours: 12-2 PM and 6-8 PM
				multiplier := 1.0
				if (hour >= 12 && hour <= 14) || (hour >= 18 && hour <= 20) {
					multiplier = 2.0
				} else if hour < 11 || hour > 21 {
					multiplier = 0.3
				}

				// Base utilization depends on table capacity
				baseUtilization := 0.3 + rand.Float64()*0.4 // 30-70% base utilization
				utilization := baseUtilization * multiplier
				if utilization > 1.0 {
					utilization = 1.0
				}

				occupiedMinutes := int(60 * utilization)                       // Minutes occupied in this hour
				turnoverCount := rand.Intn(3)                                  // 0-2 turnovers per hour
				revenue := float64(turnoverCount) * (20 + rand.Float64()*80)   // $20-100 per turnover
				utilizationRate := utilization * 100                           // Percentage
				avgPartySize := 1.0 + rand.Float64()*float64(table.Capacity-1) // 1 to table capacity
				avgDiningTime := 45.0 + rand.Float64()*75.0                    // 45-120 minutes

				// Weekend boost
				if date.Weekday() == time.Saturday || date.Weekday() == time.Sunday {
					occupiedMinutes = int(float64(occupiedMinutes) * 1.3)
					turnoverCount = int(float64(turnoverCount) * 1.3)
					revenue = revenue * 1.3
					utilizationRate = utilizationRate * 1.3
					if utilizationRate > 100 {
						utilizationRate = 100
					}
				}

				metric := &models.TableMetric{
					BranchID:        branchID,
					TableID:         table.ID,
					Date:            dayStart,
					Hour:            hour,
					OccupiedMinutes: occupiedMinutes,
					TurnoverCount:   turnoverCount,
					Revenue:         revenue,
					UtilizationRate: utilizationRate,
					AvgPartySize:    avgPartySize,
					AvgDiningTime:   avgDiningTime,
				}

				if err := db.Create(metric).Error; err != nil {
					return err
				}
			}
		}
	}

	return nil
}

func seedAnalyticsReports(db *gorm.DB, branchID uuid.UUID) error {
	// Get an admin user to be the report generator
	var adminUser models.User
	err := db.Where("branch_id = ? AND position LIKE ?", branchID, "%Manager%").First(&adminUser).Error
	if err != nil {
		// If no manager found, get any user from this branch
		err = db.Where("branch_id = ?", branchID).First(&adminUser).Error
		if err != nil {
			// If no users found, skip report generation
			return nil
		}
	}

	now := time.Now()

	// Create sample analytics reports
	reports := []*models.AnalyticsReport{
		{
			BranchID:    branchID,
			ReportType:  "sales",
			Title:       "Weekly Sales Report",
			Description: "Comprehensive sales analysis for the past week",
			StartDate:   now.AddDate(0, 0, -7),
			EndDate:     now,
			Data: models.ReportData{
				"totalRevenue":    15420.50,
				"totalOrders":     287,
				"avgOrderValue":   53.73,
				"topSellingItem":  "Pad Thai",
				"peakHour":        "19:00",
				"customerCount":   245,
				"returnCustomers": 89,
			},
			Status:      "completed",
			GeneratedBy: adminUser.ID,
		},
		{
			BranchID:    branchID,
			ReportType:  "menu",
			Title:       "Menu Performance Analysis",
			Description: "Analysis of menu item performance and popularity",
			StartDate:   now.AddDate(0, -1, 0),
			EndDate:     now,
			Data: models.ReportData{
				"totalItems":     25,
				"topPerformers":  []string{"Pad Thai", "Green Curry", "Tom Yum Soup"},
				"lowPerformers":  []string{"Mango Sticky Rice", "Thai Iced Coffee"},
				"avgRating":      4.3,
				"profitMargin":   68.5,
				"inventoryTurns": 12.3,
			},
			Status:      "completed",
			GeneratedBy: adminUser.ID,
		},
		{
			BranchID:    branchID,
			ReportType:  "staff",
			Title:       "Staff Performance Report",
			Description: "Monthly staff performance and productivity analysis",
			StartDate:   now.AddDate(0, -1, 0),
			EndDate:     now,
			Data: models.ReportData{
				"totalStaff":        8,
				"avgEfficiency":     12.5,
				"topPerformer":      "Sarah Johnson",
				"avgCustomerRating": 4.2,
				"totalHours":        1280,
				"avgOrderTime":      18.5,
			},
			Status:      "completed",
			GeneratedBy: adminUser.ID,
		},
		{
			BranchID:    branchID,
			ReportType:  "customer",
			Title:       "Customer Analytics Report",
			Description: "Customer behavior and satisfaction analysis",
			StartDate:   now.AddDate(0, -1, 0),
			EndDate:     now,
			Data: models.ReportData{
				"totalCustomers":       1250,
				"newCustomers":         320,
				"returningCustomers":   930,
				"avgVisitFrequency":    2.3,
				"customerSatisfaction": 4.4,
				"churnRate":            12.5,
			},
			Status:      "completed",
			GeneratedBy: adminUser.ID,
		},
		{
			BranchID:    branchID,
			ReportType:  "financial",
			Title:       "Monthly Financial Summary",
			Description: "Complete financial overview for the month",
			StartDate:   now.AddDate(0, -1, 0),
			EndDate:     now,
			Data: models.ReportData{
				"grossRevenue":  45230.75,
				"netRevenue":    38946.14,
				"totalCosts":    6284.61,
				"profitMargin":  86.1,
				"taxAmount":     3622.46,
				"avgDailySales": 1507.69,
			},
			Status:      "completed",
			GeneratedBy: adminUser.ID,
		},
	}

	for _, report := range reports {
		if err := db.Create(report).Error; err != nil {
			return err
		}
	}

	return nil
}
