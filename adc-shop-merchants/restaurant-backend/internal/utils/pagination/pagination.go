package pagination

import (
	"fmt"
	"strings"

	"restaurant-backend/internal/types"
)

// ApplyStandardDefaults applies default values to pagination, sorting, and search
func ApplyStandardDefaults(pagination *types.StandardPagination, sorting *types.StandardSorting) {
	if pagination != nil {
		pagination.ApplyDefaults()
	}
	if sorting != nil {
		sorting.ApplyDefaults()
	}
}

// ValidateAndApplySorting validates sort fields and applies defaults
func ValidateAndApplySorting(sorting *types.StandardSorting, allowedFields []string, defaultField string) {
	if sorting == nil {
		return
	}

	// Apply defaults first
	sorting.ApplyDefaults()

	// Validate sort field
	if sorting.SortBy != "" {
		valid := false
		for _, field := range allowedFields {
			if field == sorting.SortBy {
				valid = true
				break
			}
		}
		if !valid {
			sorting.SortBy = defaultField
		}
	}

	// Validate sort order
	if sorting.SortOrder != "asc" && sorting.SortOrder != "desc" {
		sorting.SortOrder = "asc"
	}
}

// BuildOrderClause builds SQL ORDER BY clause from sorting parameters
func BuildOrderClause(sorting *types.StandardSorting, tableAlias string) string {
	if sorting == nil || sorting.SortBy == "" {
		return ""
	}

	field := sorting.SortBy
	if tableAlias != "" {
		field = fmt.Sprintf("%s.%s", tableAlias, field)
	}

	order := "ASC"
	if sorting.SortOrder == "desc" {
		order = "DESC"
	}

	return fmt.Sprintf("%s %s", field, order)
}

// BuildSearchCondition builds SQL search condition for multiple fields
func BuildSearchCondition(search string, fields []string, tableAlias string) (string, []interface{}) {
	if search == "" || len(fields) == 0 {
		return "", nil
	}

	searchTerm := "%" + strings.ToLower(search) + "%"
	var conditions []string
	var args []interface{}

	for _, field := range fields {
		fieldName := field
		if tableAlias != "" {
			fieldName = fmt.Sprintf("%s.%s", tableAlias, field)
		}
		conditions = append(conditions, fmt.Sprintf("LOWER(%s) LIKE ?", fieldName))
		args = append(args, searchTerm)
	}

	return "(" + strings.Join(conditions, " OR ") + ")", args
}

// CreateStandardResponse creates a standardized response with pagination info
func CreateStandardResponse(total int64, page, limit int) types.StandardResponse {
	return types.StandardResponse{
		Total:      total,
		Page:       page,
		Limit:      limit,
		TotalPages: types.CalculateTotalPages(total, limit),
	}
}

// PaginationInfo holds pagination calculation results
type PaginationInfo struct {
	Offset     int
	Limit      int
	Page       int
	TotalPages int
}

// CalculatePaginationInfo calculates all pagination-related values
func CalculatePaginationInfo(total int64, page, limit int) PaginationInfo {
	if page <= 0 {
		page = 1
	}
	if limit <= 0 {
		limit = types.PaginationDefaults.Limit
	}

	offset := types.CalculateOffset(page, limit)
	totalPages := types.CalculateTotalPages(total, limit)

	return PaginationInfo{
		Offset:     offset,
		Limit:      limit,
		Page:       page,
		TotalPages: totalPages,
	}
}

// ApplyFilters applies common filters to a query builder
type FilterBuilder struct {
	conditions []string
	args       []interface{}
}

// NewFilterBuilder creates a new filter builder
func NewFilterBuilder() *FilterBuilder {
	return &FilterBuilder{
		conditions: make([]string, 0),
		args:       make([]interface{}, 0),
	}
}

// AddCondition adds a condition to the filter
func (fb *FilterBuilder) AddCondition(condition string, args ...interface{}) *FilterBuilder {
	if condition != "" {
		fb.conditions = append(fb.conditions, condition)
		fb.args = append(fb.args, args...)
	}
	return fb
}

// AddSearch adds search conditions for multiple fields
func (fb *FilterBuilder) AddSearch(search string, fields []string, tableAlias string) *FilterBuilder {
	condition, args := BuildSearchCondition(search, fields, tableAlias)
	if condition != "" {
		fb.AddCondition(condition, args...)
	}
	return fb
}

// AddDateRange adds date range filter
func (fb *FilterBuilder) AddDateRange(field, startDate, endDate, tableAlias string) *FilterBuilder {
	fieldName := field
	if tableAlias != "" {
		fieldName = fmt.Sprintf("%s.%s", tableAlias, field)
	}

	if startDate != "" {
		fb.AddCondition(fmt.Sprintf("%s >= ?", fieldName), startDate)
	}
	if endDate != "" {
		fb.AddCondition(fmt.Sprintf("%s <= ?", fieldName), endDate)
	}
	return fb
}

// AddBoolFilter adds boolean filter
func (fb *FilterBuilder) AddBoolFilter(field string, value *bool, tableAlias string) *FilterBuilder {
	if value != nil {
		fieldName := field
		if tableAlias != "" {
			fieldName = fmt.Sprintf("%s.%s", tableAlias, field)
		}
		fb.AddCondition(fmt.Sprintf("%s = ?", fieldName), *value)
	}
	return fb
}

// AddStringFilter adds string equality filter
func (fb *FilterBuilder) AddStringFilter(field, value, tableAlias string) *FilterBuilder {
	if value != "" {
		fieldName := field
		if tableAlias != "" {
			fieldName = fmt.Sprintf("%s.%s", tableAlias, field)
		}
		fb.AddCondition(fmt.Sprintf("%s = ?", fieldName), value)
	}
	return fb
}

// AddInFilter adds IN filter for array values
func (fb *FilterBuilder) AddInFilter(field string, values []interface{}, tableAlias string) *FilterBuilder {
	if len(values) > 0 {
		fieldName := field
		if tableAlias != "" {
			fieldName = fmt.Sprintf("%s.%s", tableAlias, field)
		}
		placeholders := strings.Repeat("?,", len(values))
		placeholders = placeholders[:len(placeholders)-1] // Remove trailing comma
		fb.AddCondition(fmt.Sprintf("%s IN (%s)", fieldName, placeholders), values...)
	}
	return fb
}

// Build returns the final WHERE clause and arguments
func (fb *FilterBuilder) Build() (string, []interface{}) {
	if len(fb.conditions) == 0 {
		return "", nil
	}
	return strings.Join(fb.conditions, " AND "), fb.args
}

// BuildWithPrefix returns the WHERE clause with "WHERE" prefix if conditions exist
func (fb *FilterBuilder) BuildWithPrefix() (string, []interface{}) {
	whereClause, args := fb.Build()
	if whereClause != "" {
		whereClause = "WHERE " + whereClause
	}
	return whereClause, args
}
