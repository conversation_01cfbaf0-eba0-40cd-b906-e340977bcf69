package utils

import (
	"regexp"
	"strings"
)

// Slugify converts text to a URL-friendly slug
func Slugify(text string) string {
	// Convert to lowercase
	text = strings.ToLower(text)

	// Replace spaces with hyphens
	text = strings.ReplaceAll(text, " ", "-")

	// Replace multiple spaces/whitespace with single hyphen
	reg := regexp.MustCompile(`\s+`)
	text = reg.ReplaceAllString(text, "-")

	// Remove special punctuation but keep Unicode letters, marks, and numbers
	reg = regexp.MustCompile(`[^\p{L}\p{M}\p{N}\-]`)
	text = reg.ReplaceAllString(text, "")

	// Remove multiple consecutive hyphens
	reg = regexp.MustCompile(`-+`)
	text = reg.ReplaceAllString(text, "-")

	// Trim hyphens from start and end
	text = strings.Trim(text, "-")

	return text
}
