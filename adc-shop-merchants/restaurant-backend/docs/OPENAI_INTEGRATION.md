# OpenAI Integration for AI Menu Generation

This document explains how the OpenAI Vision API is integrated into the restaurant management system for AI-powered menu generation.

## Overview

The AI Generation API now uses **OpenAI's GPT-4 Vision** model to analyze menu images, food photos, and text to automatically generate structured menu items with detailed descriptions, pricing, and metadata.

## Features

### 🖼️ Menu Image Analysis
- Upload menu photos and extract all menu items
- Automatic categorization and pricing
- Detailed descriptions and ingredient lists
- Dietary information (vegetarian, vegan, gluten-free, etc.)

### 🍕 Food Image Recognition
- Analyze individual food photos
- Generate creative dish names and descriptions
- Estimate appropriate pricing based on visual cues
- Identify ingredients and allergens

### 📝 Menu Text Enhancement
- Parse existing menu text
- Enhance descriptions with AI-generated content
- Structure unorganized menu data
- Add missing metadata and categorization
- **NEW**: Generate custom food images using DALL-E 3

### 🎨 AI Image Generation (DALL-E 3)
- Create professional food photography for each menu item
- Generate images based on dish names and descriptions
- Cuisine-specific styling and presentation
- High-quality, restaurant-grade food photos
- Automatic fallback to placeholders if generation fails

## Configuration

### Environment Variables

Add these to your `.env` file:

```bash
# OpenAI Configuration
OPENAI_API_KEY=sk-your-actual-openai-api-key
OPENAI_MODEL=gpt-4-vision-preview
OPENAI_MAX_TOKENS=4096
OPENAI_TEMPERATURE=0.7
```

### Getting an OpenAI API Key

1. Visit [OpenAI Platform](https://platform.openai.com/)
2. Sign up or log in to your account
3. Navigate to API Keys section
4. Create a new API key
5. Copy the key and add it to your environment variables

**Important**: Keep your API key secure and never commit it to version control.

## API Usage

### 1. Menu Image Analysis

```bash
# Upload menu image
POST /api/v1/shops/slug/{shopSlug}/branches/slug/{branchSlug}/ai-generation/upload
Content-Type: multipart/form-data

file: [menu_image.jpg]
type: menu_image

# Create AI generation job
POST /api/v1/shops/slug/{shopSlug}/branches/slug/{branchSlug}/ai-generation/jobs
{
  "type": "menu_image",
  "input_data": {
    "menu_image_url": "https://storage.googleapis.com/...",
    "cuisine_type": "Italian",
    "price_range": "$$",
    "restaurant_name": "Mario's Pizzeria"
  }
}
```

### 2. Food Image Analysis

```bash
# Upload multiple food images
POST /api/v1/shops/slug/{shopSlug}/branches/slug/{branchSlug}/ai-generation/upload
Content-Type: multipart/form-data

file: [food_image1.jpg]
type: food_image

# Create AI generation job
POST /api/v1/shops/slug/{shopSlug}/branches/slug/{branchSlug}/ai-generation/jobs
{
  "type": "food_images",
  "input_data": {
    "food_image_urls": [
      "https://storage.googleapis.com/food1.jpg",
      "https://storage.googleapis.com/food2.jpg"
    ],
    "cuisine_type": "Thai",
    "price_range": "$$$",
    "restaurant_name": "Bangkok Garden"
  }
}
```

### 3. Text Enhancement

```bash
POST /api/v1/shops/slug/{shopSlug}/branches/slug/{branchSlug}/ai-generation/jobs
{
  "type": "text",
  "input_data": {
    "menu_text": "Spaghetti Carbonara - $14.99\nGrilled Salmon - $18.99\nCaesar Salad - $8.99",
    "cuisine_type": "Italian",
    "price_range": "$$",
    "restaurant_name": "Bella Vista"
  }
}
```

## Response Format

The AI generates structured menu items with the following format:

```json
{
  "menu_items": [
    {
      "name": "Spaghetti Carbonara",
      "description": "Classic Roman pasta dish with eggs, pecorino cheese, pancetta, and freshly cracked black pepper",
      "price": 14.99,
      "category": "Main Course",
      "image_url": "https://storage.googleapis.com/...",
      "ingredients": ["spaghetti", "eggs", "pecorino cheese", "pancetta", "black pepper"],
      "allergens": ["eggs", "dairy", "gluten"],
      "is_vegetarian": false,
      "is_vegan": false,
      "is_gluten_free": false,
      "is_spicy": false,
      "spice_level": 0,
      "preparation_time": 15,
      "tags": ["classic", "italian", "comfort food"]
    }
  ],
  "menu_info": {
    "name": "AI Generated Menu",
    "description": "Menu generated using OpenAI Vision",
    "image_url": "https://storage.googleapis.com/menu.jpg"
  }
}
```

## Real-time Updates

The system provides real-time progress updates via WebSocket:

```javascript
// WebSocket message format
{
  "type": "ai_generation_update",
  "job_id": "uuid",
  "status": "processing",
  "progress": 45,
  "message": "Analyzing menu image with AI..."
}
```

## Testing

Run the test script to verify your OpenAI integration:

```bash
cd restaurant-backend
go run test_openai.go
```

This will test the text enhancement feature and verify your API key is working correctly.

## Cost Considerations

- **GPT-4 Vision**: ~$0.01-0.03 per image depending on size
- **GPT-4 Text**: ~$0.03 per 1K tokens
- **DALL-E 3 Standard**: ~$0.04 per generated image
- **DALL-E 3 HD**: ~$0.08 per generated image (if enabled)
- Monitor usage in OpenAI dashboard
- Consider implementing rate limiting for production
- Text enhancement with image generation: ~$0.07 per menu item total

## Error Handling

The system includes comprehensive error handling:

- **Invalid API Key**: Job fails with authentication error
- **Rate Limiting**: Automatic retry with exponential backoff
- **Image Processing Errors**: Graceful fallback to mock data
- **Network Issues**: Timeout handling and retry logic

## Security

- API keys are stored as environment variables
- No API keys are logged or exposed in responses
- All OpenAI requests are made server-side
- Image URLs are validated before processing

## Monitoring

Monitor OpenAI integration through:

- Application logs for API call success/failure
- OpenAI dashboard for usage and costs
- WebSocket messages for real-time status
- Database job status tracking

## Troubleshooting

### Common Issues

1. **"Invalid API Key"**
   - Verify OPENAI_API_KEY is set correctly
   - Check API key has sufficient credits
   - Ensure key has access to GPT-4 Vision

2. **"Rate Limit Exceeded"**
   - Reduce concurrent requests
   - Implement request queuing
   - Upgrade OpenAI plan if needed

3. **"Image Processing Failed"**
   - Verify image URLs are accessible
   - Check image format (JPG, PNG supported)
   - Ensure images are not too large (>20MB)

4. **"No Response from OpenAI"**
   - Check network connectivity
   - Verify OpenAI service status
   - Review timeout settings

For additional support, check the OpenAI documentation or contact support.
