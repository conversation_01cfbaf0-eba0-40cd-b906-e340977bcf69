# Performance Optimization Guide

## Overview

This guide covers comprehensive performance optimization strategies for the Restaurant Management API, including database optimization, caching strategies, code optimization, and monitoring.

## Database Optimization

### 1. Index Optimization

#### Current Indexes
```sql
-- Core business indexes
CREATE INDEX CONCURRENTLY idx_orders_branch_status ON orders(branch_id, status);
CREATE INDEX CONCURRENTLY idx_orders_created_at ON orders(created_at DESC);
CREATE INDEX CONCURRENTLY idx_orders_customer_phone ON orders(customer_phone);
CREATE INDEX CONCURRENTLY idx_menu_items_branch_available ON menu_items(branch_id) WHERE is_available = true;
CREATE INDEX CONCURRENTLY idx_reservations_branch_date ON reservations(branch_id, reservation_date);
CREATE INDEX CONCURRENTLY idx_reviews_branch_rating ON reviews(branch_id, rating);

-- Composite indexes for common queries
CREATE INDEX CONCURRENTLY idx_orders_branch_type_status ON orders(branch_id, type, status);
CREATE INDEX CONCURRENTLY idx_menu_items_category_available ON menu_items(category_id, is_available);
CREATE INDEX CONCURRENTLY idx_users_branch_status ON users(branch_id, status);
```

#### Performance Monitoring Queries
```sql
-- Find missing indexes
SELECT schemaname, tablename, attname, n_distinct, correlation
FROM pg_stats
WHERE schemaname = 'public'
  AND n_distinct > 100
  AND correlation < 0.1;

-- Analyze slow queries
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
WHERE mean_time > 100
ORDER BY mean_time DESC
LIMIT 10;
```

### 2. Query Optimization

#### Optimized Order Queries
```go
// Optimized order retrieval with proper joins and pagination
func (r *orderRepository) GetByBranchOptimized(ctx context.Context, branchID uuid.UUID, filters types.OrderFilters) ([]*models.Order, error) {
    query := r.db.WithContext(ctx).
        Select("orders.*, tables.name as table_name").
        Joins("LEFT JOIN tables ON orders.table_id = tables.id").
        Where("orders.branch_id = ?", branchID)

    // Use indexes effectively
    if filters.Status != "" {
        if filters.Status == "active" {
            query = query.Where("orders.status IN ?", []string{
                models.OrderStatusPending,
                models.OrderStatusConfirmed,
                models.OrderStatusPreparing,
                models.OrderStatusReady,
                models.OrderStatusServed,
            })
        } else {
            query = query.Where("orders.status = ?", filters.Status)
        }
    }

    // Efficient pagination
    if filters.Page > 0 && filters.Limit > 0 {
        offset := (filters.Page - 1) * filters.Limit
        query = query.Offset(offset).Limit(filters.Limit)
    }

    // Use covering index
    query = query.Order("orders.created_at DESC")

    var orders []*models.Order
    err := query.Find(&orders).Error
    return orders, err
}
```

#### Batch Operations
```go
// Batch insert for order items
func (r *orderRepository) CreateOrderWithItems(ctx context.Context, order *models.Order) error {
    return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
        // Create order
        if err := tx.Create(order).Error; err != nil {
            return err
        }

        // Batch insert items
        if len(order.Items) > 0 {
            if err := tx.CreateInBatches(order.Items, 100).Error; err != nil {
                return err
            }
        }

        return nil
    })
}
```

### 3. Connection Pool Optimization

```go
// Optimized database configuration
func optimizeDatabase(db *gorm.DB) error {
    sqlDB, err := db.DB()
    if err != nil {
        return err
    }

    // Connection pool settings
    sqlDB.SetMaxIdleConns(25)
    sqlDB.SetMaxOpenConns(100)
    sqlDB.SetConnMaxLifetime(5 * time.Minute)
    sqlDB.SetConnMaxIdleTime(1 * time.Minute)

    // PostgreSQL specific optimizations
    db.Exec("SET statement_timeout = '30s'")
    db.Exec("SET lock_timeout = '10s'")
    db.Exec("SET idle_in_transaction_session_timeout = '60s'")

    return nil
}
```

## Caching Strategies

### 1. Redis Caching Layer

```go
// Cache service implementation
type CacheService struct {
    redis  *redis.Client
    logger *logrus.Logger
}

func NewCacheService(redisClient *redis.Client, logger *logrus.Logger) *CacheService {
    return &CacheService{
        redis:  redisClient,
        logger: logger,
    }
}

// Cache menu items with TTL
func (c *CacheService) CacheMenuItems(branchID uuid.UUID, items []*models.MenuItem) error {
    key := fmt.Sprintf("menu:branch:%s", branchID.String())
    
    data, err := json.Marshal(items)
    if err != nil {
        return err
    }

    return c.redis.Set(context.Background(), key, data, 15*time.Minute).Err()
}

// Get cached menu items
func (c *CacheService) GetCachedMenuItems(branchID uuid.UUID) ([]*models.MenuItem, error) {
    key := fmt.Sprintf("menu:branch:%s", branchID.String())
    
    data, err := c.redis.Get(context.Background(), key).Result()
    if err != nil {
        if err == redis.Nil {
            return nil, nil // Cache miss
        }
        return nil, err
    }

    var items []*models.MenuItem
    err = json.Unmarshal([]byte(data), &items)
    return items, err
}

// Cache with pipeline for better performance
func (c *CacheService) CacheMultiple(data map[string]interface{}, ttl time.Duration) error {
    pipe := c.redis.Pipeline()
    
    for key, value := range data {
        jsonData, err := json.Marshal(value)
        if err != nil {
            continue
        }
        pipe.Set(context.Background(), key, jsonData, ttl)
    }
    
    _, err := pipe.Exec(context.Background())
    return err
}
```

### 2. Application-Level Caching

```go
// In-memory cache for frequently accessed data
type MemoryCache struct {
    cache map[string]CacheItem
    mutex sync.RWMutex
    ttl   time.Duration
}

type CacheItem struct {
    Data      interface{}
    ExpiresAt time.Time
}

func NewMemoryCache(ttl time.Duration) *MemoryCache {
    cache := &MemoryCache{
        cache: make(map[string]CacheItem),
        ttl:   ttl,
    }
    
    // Start cleanup goroutine
    go cache.cleanup()
    
    return cache
}

func (m *MemoryCache) Set(key string, value interface{}) {
    m.mutex.Lock()
    defer m.mutex.Unlock()
    
    m.cache[key] = CacheItem{
        Data:      value,
        ExpiresAt: time.Now().Add(m.ttl),
    }
}

func (m *MemoryCache) Get(key string) (interface{}, bool) {
    m.mutex.RLock()
    defer m.mutex.RUnlock()
    
    item, exists := m.cache[key]
    if !exists || time.Now().After(item.ExpiresAt) {
        return nil, false
    }
    
    return item.Data, true
}

func (m *MemoryCache) cleanup() {
    ticker := time.NewTicker(5 * time.Minute)
    defer ticker.Stop()
    
    for range ticker.C {
        m.mutex.Lock()
        now := time.Now()
        for key, item := range m.cache {
            if now.After(item.ExpiresAt) {
                delete(m.cache, key)
            }
        }
        m.mutex.Unlock()
    }
}
```

### 3. HTTP Response Caching

```go
// Cache middleware for GET requests
func CacheMiddleware(cache *CacheService, ttl time.Duration) gin.HandlerFunc {
    return func(c *gin.Context) {
        // Only cache GET requests
        if c.Request.Method != "GET" {
            c.Next()
            return
        }

        // Generate cache key
        cacheKey := fmt.Sprintf("http:%s:%s", c.Request.URL.Path, c.Request.URL.RawQuery)
        
        // Try to get from cache
        if cachedData, err := cache.redis.Get(c.Request.Context(), cacheKey).Result(); err == nil {
            c.Header("X-Cache", "HIT")
            c.Header("Content-Type", "application/json")
            c.String(http.StatusOK, cachedData)
            c.Abort()
            return
        }

        // Capture response
        writer := &responseWriter{
            ResponseWriter: c.Writer,
            body:          &bytes.Buffer{},
        }
        c.Writer = writer

        c.Next()

        // Cache successful responses
        if c.Writer.Status() == http.StatusOK {
            cache.redis.Set(c.Request.Context(), cacheKey, writer.body.String(), ttl)
            c.Header("X-Cache", "MISS")
        }
    }
}

type responseWriter struct {
    gin.ResponseWriter
    body *bytes.Buffer
}

func (w *responseWriter) Write(data []byte) (int, error) {
    w.body.Write(data)
    return w.ResponseWriter.Write(data)
}
```

## Code Optimization

### 1. Goroutine Pool for Concurrent Processing

```go
// Worker pool for handling concurrent tasks
type WorkerPool struct {
    workers    int
    jobQueue   chan Job
    workerPool chan chan Job
    quit       chan bool
}

type Job struct {
    ID       string
    Function func() error
    Result   chan error
}

func NewWorkerPool(workers int, queueSize int) *WorkerPool {
    return &WorkerPool{
        workers:    workers,
        jobQueue:   make(chan Job, queueSize),
        workerPool: make(chan chan Job, workers),
        quit:       make(chan bool),
    }
}

func (wp *WorkerPool) Start() {
    for i := 0; i < wp.workers; i++ {
        worker := NewWorker(wp.workerPool, wp.quit)
        worker.Start()
    }

    go wp.dispatch()
}

func (wp *WorkerPool) Submit(job Job) {
    wp.jobQueue <- job
}

func (wp *WorkerPool) dispatch() {
    for {
        select {
        case job := <-wp.jobQueue:
            go func(job Job) {
                jobChannel := <-wp.workerPool
                jobChannel <- job
            }(job)
        case <-wp.quit:
            return
        }
    }
}
```

### 2. Optimized JSON Serialization

```go
// Use json-iterator for faster JSON processing
import jsoniter "github.com/json-iterator/go"

var json = jsoniter.ConfigCompatibleWithStandardLibrary

// Custom JSON marshaling for models
func (o *Order) MarshalJSON() ([]byte, error) {
    type Alias Order
    return json.Marshal(&struct {
        *Alias
        FormattedTotal string `json:"formatted_total"`
    }{
        Alias:          (*Alias)(o),
        FormattedTotal: fmt.Sprintf("$%.2f", o.Total),
    })
}
```

### 3. Memory Pool for Frequent Allocations

```go
// Object pool for reducing GC pressure
var orderPool = sync.Pool{
    New: func() interface{} {
        return &models.Order{
            Items: make([]models.OrderItem, 0, 10),
        }
    },
}

func GetOrder() *models.Order {
    return orderPool.Get().(*models.Order)
}

func PutOrder(order *models.Order) {
    // Reset order for reuse
    order.Reset()
    orderPool.Put(order)
}
```

## Monitoring and Profiling

### 1. Performance Metrics

```go
// Custom metrics for monitoring
var (
    httpRequestDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "http_request_duration_seconds",
            Help:    "Duration of HTTP requests",
            Buckets: []float64{.005, .01, .025, .05, .1, .25, .5, 1, 2.5, 5, 10},
        },
        []string{"method", "endpoint", "status"},
    )

    databaseQueryDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "database_query_duration_seconds",
            Help:    "Duration of database queries",
            Buckets: prometheus.DefBuckets,
        },
        []string{"operation", "table"},
    )

    cacheHitRatio = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "cache_requests_total",
            Help: "Total cache requests",
        },
        []string{"type", "result"},
    )
)
```

### 2. Database Query Monitoring

```go
// GORM callback for query monitoring
func (db *gorm.DB) AddQueryMonitoring() {
    db.Callback().Query().Before("gorm:query").Register("monitor:before", func(db *gorm.DB) {
        db.Set("start_time", time.Now())
    })

    db.Callback().Query().After("gorm:query").Register("monitor:after", func(db *gorm.DB) {
        if startTime, ok := db.Get("start_time"); ok {
            duration := time.Since(startTime.(time.Time))
            
            // Extract table name from SQL
            tableName := extractTableName(db.Statement.SQL.String())
            
            databaseQueryDuration.WithLabelValues("select", tableName).Observe(duration.Seconds())
            
            // Log slow queries
            if duration > 100*time.Millisecond {
                logrus.WithFields(logrus.Fields{
                    "duration": duration,
                    "sql":      db.Statement.SQL.String(),
                    "vars":     db.Statement.Vars,
                }).Warn("Slow query detected")
            }
        }
    })
}
```

### 3. Application Profiling

```go
// Enable pprof in development
func setupProfiling(router *gin.Engine) {
    if gin.Mode() == gin.DebugMode {
        pprof.Register(router)
    }
}

// Memory usage monitoring
func monitorMemoryUsage() {
    ticker := time.NewTicker(30 * time.Second)
    defer ticker.Stop()

    for range ticker.C {
        var m runtime.MemStats
        runtime.ReadMemStats(&m)

        logrus.WithFields(logrus.Fields{
            "alloc_mb":      bToMb(m.Alloc),
            "total_alloc_mb": bToMb(m.TotalAlloc),
            "sys_mb":        bToMb(m.Sys),
            "num_gc":        m.NumGC,
        }).Info("Memory stats")
    }
}

func bToMb(b uint64) uint64 {
    return b / 1024 / 1024
}
```

## Load Testing

### 1. Artillery.js Configuration

```yaml
# load-test.yml
config:
  target: 'http://localhost:8080'
  phases:
    - duration: 60
      arrivalRate: 10
    - duration: 120
      arrivalRate: 50
    - duration: 60
      arrivalRate: 100
  defaults:
    headers:
      Authorization: 'Bearer {{ $randomString() }}'

scenarios:
  - name: "Order Management"
    weight: 70
    flow:
      - get:
          url: "/api/v1/merchants/{{ merchantId }}/branches/{{ branchId }}/orders"
      - post:
          url: "/api/v1/merchants/{{ merchantId }}/branches/{{ branchId }}/orders"
          json:
            customer_name: "Test Customer"
            type: "dine-in"
            items:
              - menu_item_id: "{{ menuItemId }}"
                name: "Test Item"
                price: 10.99
                quantity: 1

  - name: "Menu Browsing"
    weight: 30
    flow:
      - get:
          url: "/api/v1/merchants/{{ merchantId }}/branches/{{ branchId }}/menu/items"
      - get:
          url: "/api/v1/merchants/{{ merchantId }}/branches/{{ branchId }}/menu/categories"
```

### 2. Performance Benchmarks

```bash
#!/bin/bash
# benchmark.sh

echo "Running performance benchmarks..."

# API endpoint benchmarks
echo "Testing order creation endpoint..."
ab -n 1000 -c 10 -H "Authorization: Bearer $TOKEN" \
   -p order.json -T application/json \
   http://localhost:8080/api/v1/merchants/$MERCHANT_ID/branches/$BRANCH_ID/orders

echo "Testing menu items endpoint..."
ab -n 2000 -c 20 -H "Authorization: Bearer $TOKEN" \
   http://localhost:8080/api/v1/merchants/$MERCHANT_ID/branches/$BRANCH_ID/menu/items

# Database benchmarks
echo "Running database benchmarks..."
pgbench -h localhost -p 5432 -U restaurant_user -d restaurant_db -c 10 -j 2 -T 60
```

This performance optimization guide provides comprehensive strategies for optimizing the Restaurant Management API across all layers, from database queries to application code and monitoring.
