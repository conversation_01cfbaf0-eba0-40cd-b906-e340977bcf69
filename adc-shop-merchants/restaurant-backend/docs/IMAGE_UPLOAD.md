# Image Upload Implementation

This document describes the implementation of image upload functionality using Google Cloud Storage (GCS) for the restaurant management system.

## Overview

The system supports uploading images for:
- Menu items
- Tables
- Staff avatars (future implementation)

All images are stored in Google Cloud Storage and organized in a structured folder hierarchy.

## Architecture

### Backend Components

1. **StorageService** (`internal/services/storage_service.go`)
   - Handles file uploads to Google Cloud Storage
   - Validates file types and sizes
   - Manages folder structure
   - Provides specialized upload methods for different entity types

2. **Configuration** (`internal/config/config.go`)
   - GCS configuration structure
   - Environment variable bindings

3. **Handlers**
   - Menu item image upload: `MenuHandler.UploadMenuItemImage`
   - Table image upload: `TableHandler.UploadTableImage`

### Frontend Components

1. **API Mutations**
   - `useUploadMenuItemImageMutation` - Upload menu item images
   - `useUploadTableImageMutation` - Upload table images

2. **Components**
   - Image upload forms in add/edit pages
   - Preview functionality
   - Error handling

## Configuration

### Environment Variables

Add the following environment variables to your `.env` file:

```bash
# Google Cloud Storage Configuration
GOOGLE_CLOUD_PROJECT_ID=your-gcp-project-id
GOOGLE_CLOUD_BUCKET_NAME=your-gcs-bucket-name
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account-key.json
GCS_BASE_URL=https://storage.googleapis.com/your-bucket-name
```

### Google Cloud Setup

1. **Create a GCS Bucket**
   ```bash
   gsutil mb gs://your-bucket-name
   ```

2. **Set Bucket Permissions**
   ```bash
   gsutil iam ch allUsers:objectViewer gs://your-bucket-name
   ```

3. **Create Service Account**
   - Go to Google Cloud Console
   - Navigate to IAM & Admin > Service Accounts
   - Create a new service account
   - Download the JSON key file
   - Grant "Storage Object Admin" role

## Folder Structure

Images are organized in GCS with the following structure:

```
bucket-name/
├── menu-items/
│   └── {shopId}/
│       └── {branchId}/
│           └── {itemId}/
│               ├── image1.jpg
│               └── image2.png
├── tables/
│   └── {shopId}/
│       └── {branchId}/
│           └── {tableId}/
│               └── table-image.jpg
└── staff/
    └── {shopId}/
        └── {branchId}/
            └── {staffId}/
                └── avatar.jpg
```

## API Endpoints

### Upload Menu Item Image

```http
POST /api/v1/merchants/{merchantId}/branches/{branchId}/menu/items/{itemId}/image
Content-Type: multipart/form-data

Form Data:
- image: File (required)
```

**Response:**
```json
{
  "imageUrl": "https://storage.googleapis.com/bucket/menu-items/shop/branch/item/uuid_timestamp.jpg",
  "message": "Image uploaded successfully"
}
```

### Upload Table Image

```http
POST /api/v1/merchants/{merchantId}/branches/{branchId}/tables/{tableId}/image
Content-Type: multipart/form-data

Form Data:
- image: File (required)
```

**Response:**
```json
{
  "imageUrl": "https://storage.googleapis.com/bucket/tables/shop/branch/table/uuid_timestamp.jpg",
  "message": "Image uploaded successfully"
}
```

## File Validation

### Supported File Types
- JPEG (.jpg, .jpeg) - Best for photos with many colors
- PNG (.png) - Best for images with transparency or few colors
- WebP (.webp) - Modern format with superior compression and quality

### File Size Limits
- Menu item images: 5MB maximum
- Table images: 5MB maximum
- Staff avatars: 2MB maximum

### Validation Process
1. Check file type via Content-Type header
2. Validate file size
3. Generate unique filename with UUID and timestamp
4. Upload to appropriate folder in GCS
5. Set public read permissions
6. Return public URL

## WebP Support & Benefits

### Why WebP?
- **Superior Compression**: 25-35% smaller file sizes compared to JPEG/PNG
- **Better Quality**: Maintains higher image quality at smaller file sizes
- **Modern Standard**: Supported by all modern browsers (Chrome, Firefox, Safari, Edge)
- **Faster Loading**: Reduced bandwidth usage and faster page load times
- **SEO Benefits**: Google favors faster-loading pages

### Automatic Optimization
- Next.js automatically serves WebP when supported by the browser
- Fallback to original format for older browsers
- Images are optimized on-demand for different device sizes

### Browser Support
- Chrome: Full support since version 23
- Firefox: Full support since version 65
- Safari: Full support since version 14
- Edge: Full support since version 18
- Mobile browsers: Widely supported

## Frontend Usage

### Menu Item Image Upload

```typescript
import { useUploadMenuItemImageMutation } from '@/lib/redux/api/endpoints/restaurant/menuApi';

const [uploadMenuItemImage, { isLoading }] = useUploadMenuItemImageMutation();

const handleUpload = async (file: File) => {
  try {
    const result = await uploadMenuItemImage({
      merchantId: shopId,
      branchId: branchId,
      itemId: itemId,
      file: file
    }).unwrap();

    console.log('Upload successful:', result.imageUrl);
  } catch (error) {
    console.error('Upload failed:', error);
  }
};
```

### Table Image Upload

```typescript
import { useUploadTableImageMutation } from '@/lib/redux/api/endpoints/restaurant/tablesApi';

const [uploadTableImage, { isLoading }] = useUploadTableImageMutation();

const handleUpload = async (file: File) => {
  try {
    const result = await uploadTableImage({
      shopId: shopId,
      branchId: branchId,
      tableId: tableId,
      file: file
    }).unwrap();

    console.log('Upload successful:', result.imageUrl);
  } catch (error) {
    console.error('Upload failed:', error);
  }
};
```

## Error Handling

### Backend Errors
- `400 Bad Request`: Invalid file type or size
- `500 Internal Server Error`: GCS upload failure

### Frontend Error Handling
- Display user-friendly error messages
- Graceful degradation if upload fails
- Retry mechanisms for network failures

## Security Considerations

1. **File Type Validation**: Only allow image files
2. **File Size Limits**: Prevent large file uploads
3. **Unique Filenames**: Prevent file conflicts and enumeration
4. **Public Access**: Images are publicly readable but not writable
5. **Authentication**: Upload endpoints require valid authentication

## Performance Optimizations

1. **Parallel Uploads**: Upload images after entity creation
2. **Caching**: Set appropriate cache headers for images
3. **CDN**: Consider using Cloud CDN for better performance
4. **Image Optimization**: Consider implementing image resizing/compression

## Monitoring and Logging

- Upload success/failure rates
- File size distribution
- Storage usage metrics
- Error tracking and alerting

## Future Enhancements

1. **Image Resizing**: Automatic thumbnail generation
2. **Multiple Formats**: Support for different image sizes
3. **Bulk Upload**: Upload multiple images at once
4. **Image Editing**: Basic image editing capabilities
5. **Compression**: Automatic image compression
6. **Backup**: Cross-region replication for disaster recovery
