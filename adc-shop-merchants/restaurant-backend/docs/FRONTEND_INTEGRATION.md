# Frontend Integration Guide

## Overview

This guide provides comprehensive instructions for integrating the Restaurant Management API with frontend applications, including React, Next.js, and other modern frameworks.

## API Base Configuration

### Environment Variables
```javascript
// Frontend .env file
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080/api/v1
NEXT_PUBLIC_API_TIMEOUT=10000
NEXT_PUBLIC_WS_URL=ws://localhost:8080/ws
```

### API Client Setup

#### 1. Axios Configuration
```javascript
// lib/api/client.js
import axios from 'axios';

const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL,
  timeout: parseInt(process.env.NEXT_PUBLIC_API_TIMEOUT) || 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Handle token refresh or redirect to login
      const refreshToken = localStorage.getItem('refresh_token');
      if (refreshToken) {
        try {
          const response = await axios.post('/auth/refresh', {
            refresh_token: refreshToken
          });
          const { token } = response.data;
          localStorage.setItem('auth_token', token);
          // Retry original request
          error.config.headers.Authorization = `Bearer ${token}`;
          return apiClient.request(error.config);
        } catch (refreshError) {
          // Redirect to login
          window.location.href = '/login';
        }
      } else {
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

export default apiClient;
```

#### 2. RTK Query Setup (Recommended)
```javascript
// lib/redux/api/apiSlice.js
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

const baseQuery = fetchBaseQuery({
  baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL,
  prepareHeaders: (headers, { getState }) => {
    const token = getState().auth.token || localStorage.getItem('auth_token');
    if (token) {
      headers.set('authorization', `Bearer ${token}`);
    }
    return headers;
  },
});

const baseQueryWithReauth = async (args, api, extraOptions) => {
  let result = await baseQuery(args, api, extraOptions);
  
  if (result.error && result.error.status === 401) {
    // Try to refresh token
    const refreshResult = await baseQuery(
      {
        url: '/auth/refresh',
        method: 'POST',
        body: {
          refresh_token: localStorage.getItem('refresh_token')
        }
      },
      api,
      extraOptions
    );
    
    if (refreshResult.data) {
      const { token } = refreshResult.data;
      localStorage.setItem('auth_token', token);
      // Retry original query
      result = await baseQuery(args, api, extraOptions);
    } else {
      // Redirect to login
      window.location.href = '/login';
    }
  }
  
  return result;
};

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['Order', 'MenuItem', 'User', 'Table', 'Reservation', 'Review'],
  endpoints: (builder) => ({}),
});
```

### API Endpoints Implementation

#### Authentication API
```javascript
// lib/redux/api/endpoints/authApi.js
import { apiSlice } from '../apiSlice';

export const authApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    login: builder.mutation({
      query: (credentials) => ({
        url: '/auth/login',
        method: 'POST',
        body: credentials,
      }),
    }),
    register: builder.mutation({
      query: (userData) => ({
        url: '/auth/register',
        method: 'POST',
        body: userData,
      }),
    }),
    getCurrentUser: builder.query({
      query: () => '/auth/me',
      providesTags: ['User'],
    }),
    logout: builder.mutation({
      query: () => ({
        url: '/auth/logout',
        method: 'POST',
      }),
    }),
  }),
});

export const {
  useLoginMutation,
  useRegisterMutation,
  useGetCurrentUserQuery,
  useLogoutMutation,
} = authApi;
```

#### Orders API
```javascript
// lib/redux/api/endpoints/ordersApi.js
import { apiSlice } from '../apiSlice';

export const ordersApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getOrders: builder.query({
      query: ({ merchantId, branchId, ...params }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/orders`,
        params,
      }),
      providesTags: ['Order'],
    }),
    getOrder: builder.query({
      query: ({ merchantId, branchId, orderId }) => 
        `/merchants/${merchantId}/branches/${branchId}/orders/${orderId}`,
      providesTags: (result, error, { orderId }) => [{ type: 'Order', id: orderId }],
    }),
    createOrder: builder.mutation({
      query: ({ merchantId, branchId, ...orderData }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/orders`,
        method: 'POST',
        body: orderData,
      }),
      invalidatesTags: ['Order'],
    }),
    updateOrderStatus: builder.mutation({
      query: ({ merchantId, branchId, orderId, status }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/orders/${orderId}/status`,
        method: 'PATCH',
        body: { status },
      }),
      invalidatesTags: (result, error, { orderId }) => [
        { type: 'Order', id: orderId },
        'Order',
      ],
    }),
    getActiveOrders: builder.query({
      query: ({ merchantId, branchId }) => 
        `/merchants/${merchantId}/branches/${branchId}/orders/active`,
      providesTags: ['Order'],
    }),
  }),
});

export const {
  useGetOrdersQuery,
  useGetOrderQuery,
  useCreateOrderMutation,
  useUpdateOrderStatusMutation,
  useGetActiveOrdersQuery,
} = ordersApi;
```

#### Menu API
```javascript
// lib/redux/api/endpoints/menuApi.js
import { apiSlice } from '../apiSlice';

export const menuApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getMenuCategories: builder.query({
      query: ({ merchantId, branchId }) => 
        `/merchants/${merchantId}/branches/${branchId}/menu/categories`,
      providesTags: ['MenuItem'],
    }),
    getMenuItems: builder.query({
      query: ({ merchantId, branchId, ...params }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/menu/items`,
        params,
      }),
      providesTags: ['MenuItem'],
    }),
    getMenuItem: builder.query({
      query: ({ merchantId, branchId, itemId }) => 
        `/merchants/${merchantId}/branches/${branchId}/menu/items/${itemId}`,
      providesTags: (result, error, { itemId }) => [{ type: 'MenuItem', id: itemId }],
    }),
    createMenuItem: builder.mutation({
      query: ({ merchantId, branchId, ...itemData }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/menu/items`,
        method: 'POST',
        body: itemData,
      }),
      invalidatesTags: ['MenuItem'],
    }),
    updateMenuItem: builder.mutation({
      query: ({ merchantId, branchId, itemId, ...itemData }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/menu/items/${itemId}`,
        method: 'PUT',
        body: itemData,
      }),
      invalidatesTags: (result, error, { itemId }) => [
        { type: 'MenuItem', id: itemId },
        'MenuItem',
      ],
    }),
  }),
});

export const {
  useGetMenuCategoriesQuery,
  useGetMenuItemsQuery,
  useGetMenuItemQuery,
  useCreateMenuItemMutation,
  useUpdateMenuItemMutation,
} = menuApi;
```

### React Hooks for Common Operations

#### Authentication Hook
```javascript
// hooks/useAuth.js
import { useSelector, useDispatch } from 'react-redux';
import { useLoginMutation, useLogoutMutation } from '@/lib/redux/api/endpoints/authApi';
import { setCredentials, logout } from '@/lib/redux/slices/authSlice';

export const useAuth = () => {
  const dispatch = useDispatch();
  const { user, token, isAuthenticated } = useSelector((state) => state.auth);
  const [loginMutation, { isLoading: isLoggingIn }] = useLoginMutation();
  const [logoutMutation] = useLogoutMutation();

  const login = async (credentials) => {
    try {
      const result = await loginMutation(credentials).unwrap();
      dispatch(setCredentials(result));
      localStorage.setItem('auth_token', result.token);
      localStorage.setItem('refresh_token', result.refresh_token);
      return result;
    } catch (error) {
      throw error;
    }
  };

  const logoutUser = async () => {
    try {
      await logoutMutation().unwrap();
    } catch (error) {
      // Continue with logout even if API call fails
    } finally {
      dispatch(logout());
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');
    }
  };

  return {
    user,
    token,
    isAuthenticated,
    isLoggingIn,
    login,
    logout: logoutUser,
  };
};
```

#### Orders Hook
```javascript
// hooks/useOrders.js
import { useState } from 'react';
import {
  useGetOrdersQuery,
  useCreateOrderMutation,
  useUpdateOrderStatusMutation,
} from '@/lib/redux/api/endpoints/ordersApi';

export const useOrders = (merchantId, branchId) => {
  const [filters, setFilters] = useState({});
  
  const {
    data: orders,
    isLoading,
    error,
    refetch,
  } = useGetOrdersQuery({ merchantId, branchId, ...filters });

  const [createOrder, { isLoading: isCreating }] = useCreateOrderMutation();
  const [updateStatus, { isLoading: isUpdating }] = useUpdateOrderStatusMutation();

  const handleCreateOrder = async (orderData) => {
    try {
      const result = await createOrder({
        merchantId,
        branchId,
        ...orderData,
      }).unwrap();
      return result;
    } catch (error) {
      throw error;
    }
  };

  const handleUpdateStatus = async (orderId, status) => {
    try {
      const result = await updateStatus({
        merchantId,
        branchId,
        orderId,
        status,
      }).unwrap();
      return result;
    } catch (error) {
      throw error;
    }
  };

  return {
    orders,
    isLoading,
    error,
    isCreating,
    isUpdating,
    filters,
    setFilters,
    createOrder: handleCreateOrder,
    updateStatus: handleUpdateStatus,
    refetch,
  };
};
```

### Error Handling

#### Error Boundary Component
```javascript
// components/ErrorBoundary.jsx
import React from 'react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
    // Log to error reporting service
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Something went wrong
            </h2>
            <p className="text-gray-600 mb-4">
              We're sorry, but something unexpected happened.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Reload Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
```

#### API Error Handler
```javascript
// utils/errorHandler.js
import { toast } from 'react-hot-toast';

export const handleApiError = (error) => {
  if (error.status === 401) {
    toast.error('Please log in to continue');
    window.location.href = '/login';
  } else if (error.status === 403) {
    toast.error('You do not have permission to perform this action');
  } else if (error.status === 404) {
    toast.error('The requested resource was not found');
  } else if (error.status >= 500) {
    toast.error('Server error. Please try again later.');
  } else if (error.data?.message) {
    toast.error(error.data.message);
  } else {
    toast.error('An unexpected error occurred');
  }
};
```

### Real-time Updates (WebSocket)

```javascript
// hooks/useWebSocket.js
import { useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';

export const useWebSocket = (url, onMessage) => {
  const ws = useRef(null);
  const dispatch = useDispatch();

  useEffect(() => {
    const token = localStorage.getItem('auth_token');
    const wsUrl = `${url}?token=${token}`;
    
    ws.current = new WebSocket(wsUrl);

    ws.current.onopen = () => {
      console.log('WebSocket connected');
    };

    ws.current.onmessage = (event) => {
      const data = JSON.parse(event.data);
      onMessage(data);
    };

    ws.current.onclose = () => {
      console.log('WebSocket disconnected');
      // Implement reconnection logic
    };

    ws.current.onerror = (error) => {
      console.error('WebSocket error:', error);
    };

    return () => {
      if (ws.current) {
        ws.current.close();
      }
    };
  }, [url, onMessage]);

  const sendMessage = (message) => {
    if (ws.current && ws.current.readyState === WebSocket.OPEN) {
      ws.current.send(JSON.stringify(message));
    }
  };

  return { sendMessage };
};
```

### TypeScript Definitions

```typescript
// types/api.ts
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  refresh_token: string;
  user: User;
  expires_at: string;
}

export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: Role;
  merchant_id: string;
  branch_id?: string;
}

export interface Order {
  id: string;
  order_number: string;
  customer_name: string;
  customer_phone?: string;
  status: OrderStatus;
  type: OrderType;
  total: number;
  items: OrderItem[];
  created_at: string;
  updated_at: string;
}

export interface OrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  total: number;
  modifications?: OrderModification[];
}

export type OrderStatus = 
  | 'pending' 
  | 'confirmed' 
  | 'preparing' 
  | 'ready' 
  | 'served' 
  | 'completed' 
  | 'cancelled';

export type OrderType = 'dine-in' | 'takeaway' | 'delivery';
```

### Testing Integration

```javascript
// __tests__/api/orders.test.js
import { renderHook, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { store } from '@/lib/redux/store';
import { useOrders } from '@/hooks/useOrders';

const wrapper = ({ children }) => (
  <Provider store={store}>{children}</Provider>
);

describe('useOrders hook', () => {
  it('should fetch orders successfully', async () => {
    const { result } = renderHook(
      () => useOrders('merchant-id', 'branch-id'),
      { wrapper }
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.orders).toBeDefined();
  });
});
```

This frontend integration guide provides everything needed to connect your React/Next.js application with the Restaurant Management API, including proper error handling, real-time updates, and TypeScript support.
