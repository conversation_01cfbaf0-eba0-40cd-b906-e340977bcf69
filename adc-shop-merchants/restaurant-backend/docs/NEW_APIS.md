# New API Endpoints Documentation

This document describes the newly implemented API endpoints for Services, Shops, and Campaigns functionality.

## 🎯 Overview

The following new API endpoints have been implemented to support the frontend requirements:

- **Services & Appointments API**: Service management and appointment booking
- **Shops API**: Shop and branch management
- **Campaigns & Communication API**: Marketing campaigns and customer communication

## 🔗 API Endpoints

### 1. Shops API

Base URL: `/api/v1/shops`

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/shops` | Get all shops with filters |
| POST | `/shops` | Create a new shop |
| GET | `/shops/{id}` | Get shop by ID |
| PUT | `/shops/{id}` | Update shop |
| DELETE | `/shops/{id}` | Delete shop |
| GET | `/shops/slug/{slug}` | Get shop by slug |
| GET | `/shops/owner/{ownerId}` | Get shops by owner |
| GET | `/shops/type/{type}` | Get shops by type |

#### Shop Branches

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/shops/{shopId}/branches` | Get shop branches |
| POST | `/shops/{shopId}/branches` | Create branch |
| GET | `/shops/{shopId}/branches/{branchId}` | Get branch by ID |
| PUT | `/shops/{shopId}/branches/{branchId}` | Update branch |
| DELETE | `/shops/{shopId}/branches/{branchId}` | Delete branch |
| GET | `/shops/{shopId}/branches/{branchId}/settings` | Get branch settings |
| PUT | `/shops/{shopId}/branches/{branchId}/settings` | Update branch settings |

### 2. Services & Appointments API

Base URL: `/api/v1/merchants/{merchantId}`

#### Services

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/services` | Get merchant services |
| POST | `/services` | Create service |
| GET | `/services/{serviceId}` | Get service by ID |
| PUT | `/services/{serviceId}` | Update service |
| DELETE | `/services/{serviceId}` | Delete service |
| GET | `/services/{serviceId}/availability` | Get service availability |
| GET | `/services/{serviceId}/time-slots` | Get available time slots |

#### Appointments

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/appointments` | Get merchant appointments |
| POST | `/appointments` | Create appointment |
| GET | `/appointments/{appointmentId}` | Get appointment by ID |
| PUT | `/appointments/{appointmentId}` | Update appointment |
| POST | `/appointments/{appointmentId}/cancel` | Cancel appointment |

#### Staff

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/staff` | Get merchant staff |
| POST | `/staff` | Create staff member |
| GET | `/staff/{staffId}` | Get staff member by ID |
| PUT | `/staff/{staffId}` | Update staff member |
| DELETE | `/staff/{staffId}` | Delete staff member |

### 3. Campaigns & Communication API

Base URL: `/api/v1/merchants/{merchantId}`

#### Campaigns

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/campaigns` | Get merchant campaigns |
| POST | `/campaigns` | Create campaign |
| GET | `/campaigns/{campaignId}` | Get campaign by ID |
| PUT | `/campaigns/{campaignId}` | Update campaign |
| DELETE | `/campaigns/{campaignId}` | Delete campaign |
| POST | `/campaigns/{campaignId}/execute` | Execute campaign |

#### Communication Templates

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/communication-templates` | Get templates |
| POST | `/communication-templates` | Create template |
| GET | `/communication-templates/{templateId}` | Get template by ID |
| PUT | `/communication-templates/{templateId}` | Update template |
| DELETE | `/communication-templates/{templateId}` | Delete template |

#### Campaign Segments

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/campaign-segments` | Get segments |
| POST | `/campaign-segments` | Create segment |
| GET | `/campaign-segments/{segmentId}` | Get segment by ID |
| PUT | `/campaign-segments/{segmentId}` | Update segment |
| DELETE | `/campaign-segments/{segmentId}` | Delete segment |
| GET | `/campaign-segments/{segmentId}/customers` | Get segment customers |
| GET | `/campaign-segments/{segmentId}/emails` | Get segment emails |
| GET | `/campaign-segments/{segmentId}/phones` | Get segment phones |

#### Communication Analytics

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/communication-analytics/overview` | Get analytics overview |
| GET | `/communication-analytics/campaigns/{campaignId}` | Get campaign analytics |

## 🔐 Authentication

All endpoints require authentication via Bearer token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## 📊 Query Parameters

### Pagination
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 100)

### Filtering
Each endpoint supports specific filters. Common filters include:
- `search`: Text search
- `status`: Status filter
- `is_active`: Active status filter
- `type`: Type filter
- `category`: Category filter

## 📝 Request/Response Examples

### Create Shop
```json
POST /api/v1/shops
{
  "name": "My Restaurant",
  "slug": "my-restaurant",
  "description": "A great restaurant",
  "shop_type": "restaurant",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "address": {
    "street": "123 Main St",
    "city": "New York",
    "state": "NY",
    "zip_code": "10001",
    "country": "USA"
  },
  "cuisine_type": "italian",
  "price_range": "$$",
  "business_hours": {
    "monday": "9:00-22:00",
    "tuesday": "9:00-22:00"
  }
}
```

### Create Service
```json
POST /api/v1/merchants/{merchantId}/services
{
  "name": "Hair Cut",
  "description": "Professional hair cutting",
  "category": "hair",
  "price": 25.00,
  "duration": 30,
  "max_capacity": 1,
  "requires_staff": true
}
```

### Create Appointment
```json
POST /api/v1/merchants/{merchantId}/appointments
{
  "service_id": "uuid",
  "staff_id": "uuid",
  "customer_name": "John Doe",
  "customer_email": "<EMAIL>",
  "customer_phone": "+1234567890",
  "appointment_date": "2024-01-15T00:00:00Z",
  "start_time": "2024-01-15T10:00:00Z",
  "party_size": 1,
  "notes": "First time customer"
}
```

## 🗄️ Database Models

### New Tables Created:
- `shops` - Shop information
- `shop_branches` - Shop branch locations
- `services` - Services offered by merchants
- `staff` - Staff members
- `appointments` - Service appointments
- `staff_services` - Staff-service relationships
- `service_availability` - Service availability slots
- `communication_templates` - Email/SMS templates
- `campaign_segments` - Customer segments
- `communication_campaigns` - Marketing campaigns
- `communication_analytics` - Campaign analytics

## 🚀 Getting Started

1. **Run Migrations**: The new tables will be created automatically when the application starts
2. **Test Endpoints**: Use the provided test suite or API documentation
3. **Frontend Integration**: All endpoints are now ready for frontend integration

## 📚 Additional Resources

- **Swagger Documentation**: Available at `/docs/` when the server is running
- **Test Suite**: Run `go test ./test/...` to verify implementation
- **Database Migrations**: Located in `/migrations/` directory

## 🔧 Configuration

No additional configuration is required. The new APIs use the existing:
- Authentication middleware
- Database connection
- Logging system
- Error handling
- CORS settings

All new endpoints follow the same patterns and conventions as existing APIs.
