#!/bin/bash

# Restaurant API Startup Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  Restaurant Management API${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Check if .env file exists
check_env() {
    if [ ! -f .env ]; then
        print_warning ".env file not found. Creating from .env.example..."
        cp .env.example .env
        print_warning "Please update .env file with your configuration before running the application."
        return 1
    fi
    return 0
}

# Check if PostgreSQL is running
check_postgres() {
    print_status "Checking PostgreSQL connection..."
    
    # Load environment variables
    if [ -f .env ]; then
        export $(cat .env | grep -v '^#' | xargs)
    fi
    
    # Try to connect to PostgreSQL
    if command -v psql >/dev/null 2>&1; then
        if PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c '\q' >/dev/null 2>&1; then
            print_status "PostgreSQL connection successful"
            return 0
        else
            print_error "Cannot connect to PostgreSQL. Please check your database configuration."
            print_error "Host: $DB_HOST:$DB_PORT, Database: $DB_NAME, User: $DB_USER"
            return 1
        fi
    else
        print_warning "psql not found. Skipping PostgreSQL connection check."
        return 0
    fi
}

# Check if Redis is running
check_redis() {
    print_status "Checking Redis connection..."
    
    # Load environment variables
    if [ -f .env ]; then
        export $(cat .env | grep -v '^#' | xargs)
    fi
    
    # Try to connect to Redis
    if command -v redis-cli >/dev/null 2>&1; then
        if redis-cli -h $REDIS_HOST -p $REDIS_PORT ping >/dev/null 2>&1; then
            print_status "Redis connection successful"
            return 0
        else
            print_warning "Cannot connect to Redis. The application will work without Redis but some features may be limited."
            return 0
        fi
    else
        print_warning "redis-cli not found. Skipping Redis connection check."
        return 0
    fi
}

# Build the application
build_app() {
    print_status "Building application..."
    if go build -o restaurant-api ./cmd/server; then
        print_status "Build successful"
        return 0
    else
        print_error "Build failed"
        return 1
    fi
}

# Start the application
start_app() {
    print_status "Starting Restaurant Management API..."
    print_status "Server will be available at: http://localhost:${PORT:-8080}"
    print_status "API Documentation: http://localhost:${PORT:-8080}/docs/"
    print_status "Health Check: http://localhost:${PORT:-8080}/health"
    echo ""
    print_status "Press Ctrl+C to stop the server"
    echo ""
    
    # Start the application
    ./restaurant-api
}

# Start services with Docker Compose
start_with_docker() {
    print_status "Starting services with Docker Compose..."
    
    if ! command -v docker-compose >/dev/null 2>&1; then
        print_error "docker-compose not found. Please install Docker Compose."
        return 1
    fi
    
    # Start PostgreSQL and Redis
    docker-compose up -d postgres redis
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 10
    
    # Check if services are running
    if docker-compose ps | grep -q "postgres.*Up" && docker-compose ps | grep -q "redis.*Up"; then
        print_status "Services are ready"
        return 0
    else
        print_error "Services failed to start"
        return 1
    fi
}

# Stop Docker services
stop_docker() {
    print_status "Stopping Docker services..."
    docker-compose down
}

# Show help
show_help() {
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  start         Start the API server (default)"
    echo "  docker        Start with Docker Compose services"
    echo "  stop-docker   Stop Docker Compose services"
    echo "  build         Build the application only"
    echo "  check         Check dependencies and configuration"
    echo "  help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0              # Start the server"
    echo "  $0 docker       # Start with Docker services"
    echo "  $0 check        # Check configuration"
}

# Main function
main() {
    print_header
    
    case "${1:-start}" in
        "start")
            if check_env; then
                if check_postgres && check_redis; then
                    if build_app; then
                        start_app
                    fi
                fi
            fi
            ;;
        "docker")
            if check_env; then
                if start_with_docker; then
                    if build_app; then
                        start_app
                    fi
                fi
            fi
            ;;
        "stop-docker")
            stop_docker
            ;;
        "build")
            build_app
            ;;
        "check")
            check_env
            check_postgres
            check_redis
            print_status "Configuration check completed"
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
