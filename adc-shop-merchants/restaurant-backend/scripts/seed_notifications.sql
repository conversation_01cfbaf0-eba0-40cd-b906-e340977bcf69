-- Seed notifications for testing
-- This script adds sample notifications to the database

-- Insert sample notifications for existing shops and branches
INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'New Order Received' as title,
    'Order #ORD-2024-001 has been placed and requires preparation. Customer: <PERSON>, Total: $45.99' as message,
    'order' as type,
    'high' as priority,
    false as is_read,
    '/orders/ORD-2024-001' as link,
    'View Order' as action_label,
    '{"order_id": "ORD-2024-001", "customer_name": "<PERSON>", "amount": 45.99, "items_count": 3}' as data,
    NOW() - INTERVAL '5 minutes' as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.status = 'active' AND sb.status = 'active'
LIMIT 1;

INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'Table Reservation Confirmed' as title,
    'New reservation for 4 people at 7:00 PM tonight. Table 5 has been reserved for the <PERSON> family.' as message,
    'reservation' as type,
    'medium' as priority,
    false as is_read,
    '/reservations/RES-2024-001' as link,
    'View Reservation' as action_label,
    '{"reservation_id": "RES-2024-001", "party_size": 4, "time": "19:00", "table": "T5", "customer": "Smith Family"}' as data,
    NOW() - INTERVAL '15 minutes' as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.status = 'active' AND sb.status = 'active'
LIMIT 1;

INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'Low Inventory Alert' as title,
    'Tomatoes are running low. Only 5 units remaining. Please restock immediately to avoid running out.' as message,
    'inventory' as type,
    'urgent' as priority,
    false as is_read,
    '/inventory/tomatoes' as link,
    'Restock Now' as action_label,
    '{"item": "tomatoes", "remaining": 5, "threshold": 10, "supplier": "Fresh Produce Co."}' as data,
    NOW() - INTERVAL '30 minutes' as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.status = 'active' AND sb.status = 'active'
LIMIT 1;

INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'New Customer Review' as title,
    'A customer left a 5-star review: "Amazing food and excellent service! The pad thai was perfect and the staff was very friendly."' as message,
    'review' as type,
    'low' as priority,
    true as is_read,
    '/reviews/REV-2024-001' as link,
    'View Review' as action_label,
    '{"review_id": "REV-2024-001", "rating": 5, "customer": "Sarah Johnson", "dish": "Pad Thai"}' as data,
    NOW() - INTERVAL '1 hour' as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.status = 'active' AND sb.status = 'active'
LIMIT 1;

INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'Payment Processing Issue' as title,
    'Payment for Order #ORD-2024-002 failed. Customer payment method was declined. Please contact customer.' as message,
    'payment' as type,
    'high' as priority,
    false as is_read,
    '/orders/ORD-2024-002' as link,
    'Contact Customer' as action_label,
    '{"order_id": "ORD-2024-002", "amount": 32.50, "payment_method": "Credit Card", "error": "Card Declined"}' as data,
    NOW() - INTERVAL '2 hours' as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.status = 'active' AND sb.status = 'active'
LIMIT 1;

INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'Staff Schedule Update' as title,
    'Maria Garcia has requested a shift change for tomorrow. Please review and approve the schedule modification.' as message,
    'staff' as type,
    'medium' as priority,
    false as is_read,
    '/staff/schedule' as link,
    'Review Schedule' as action_label,
    '{"staff_member": "Maria Garcia", "requested_date": "2024-12-22", "shift_type": "evening", "reason": "family event"}' as data,
    NOW() - INTERVAL '3 hours' as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.status = 'active' AND sb.status = 'active'
LIMIT 1;

INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'System Maintenance Scheduled' as title,
    'Scheduled maintenance will occur tonight from 2:00 AM to 4:00 AM. POS system may be temporarily unavailable.' as message,
    'system' as type,
    'medium' as priority,
    true as is_read,
    '/system/maintenance' as link,
    'View Details' as action_label,
    '{"start_time": "02:00", "end_time": "04:00", "date": "2024-12-22", "affected_systems": ["POS", "Inventory"]}' as data,
    NOW() - INTERVAL '4 hours' as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.status = 'active' AND sb.status = 'active'
LIMIT 1;

INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'Holiday Promotion Active' as title,
    'Your Christmas special promotion is now live! 20% off all desserts until December 25th.' as message,
    'promotion' as type,
    'low' as priority,
    true as is_read,
    '/promotions/christmas-2024' as link,
    'View Promotion' as action_label,
    '{"promotion_id": "christmas-2024", "discount": 20, "category": "desserts", "end_date": "2024-12-25"}' as data,
    NOW() - INTERVAL '6 hours' as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.status = 'active' AND sb.status = 'active'
LIMIT 1;

INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'Order Ready for Pickup' as title,
    'Order #ORD-2024-003 is ready for pickup. Customer has been notified and is on the way.' as message,
    'order' as type,
    'medium' as priority,
    true as is_read,
    '/orders/ORD-2024-003' as link,
    'Mark as Collected' as action_label,
    '{"order_id": "ORD-2024-003", "customer": "Mike Wilson", "pickup_time": "18:30", "items": ["Green Curry", "Jasmine Rice"]}' as data,
    NOW() - INTERVAL '8 hours' as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.status = 'active' AND sb.status = 'active'
LIMIT 1;

INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'Critical: Equipment Malfunction' as title,
    'Kitchen freezer temperature is rising. Current temperature: 5°C. Immediate attention required to prevent food spoilage.' as message,
    'system' as type,
    'urgent' as priority,
    false as is_read,
    '/equipment/freezer-01' as link,
    'Check Equipment' as action_label,
    '{"equipment": "freezer-01", "current_temp": 5, "normal_temp": -18, "alert_level": "critical"}' as data,
    NOW() - INTERVAL '10 minutes' as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.status = 'active' AND sb.status = 'active'
LIMIT 1;

-- Add more notifications for different shops/branches if they exist
INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'Daily Sales Report' as title,
    'Today''s sales performance: $1,245.50 total revenue, 45 orders completed, average order value: $27.68' as message,
    'system' as type,
    'low' as priority,
    false as is_read,
    '{"total_revenue": 1245.50, "orders_count": 45, "avg_order_value": 27.68, "date": "2024-12-21"}' as data,
    NOW() - INTERVAL '12 hours' as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.status = 'active' AND sb.status = 'active'
LIMIT 5;

-- Add some older notifications for pagination testing
INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'Weekly Inventory Report' as title,
    'Weekly inventory summary: 15 items need restocking, 3 items overstocked, total inventory value: $8,450' as message,
    'inventory' as type,
    'low' as priority,
    true as is_read,
    '{"items_to_restock": 15, "overstocked_items": 3, "total_value": 8450, "week": "2024-W51"}' as data,
    NOW() - INTERVAL '1 day' as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.status = 'active' AND sb.status = 'active'
LIMIT 3;

-- Verify the inserted data
SELECT 
    n.id,
    s.name as shop_name,
    sb.name as branch_name,
    n.title,
    n.type,
    n.priority,
    n.is_read,
    n.timestamp
FROM notifications n
JOIN shops s ON n.shop_id = s.id
JOIN shop_branches sb ON n.branch_id = sb.id
ORDER BY n.timestamp DESC
LIMIT 20;
