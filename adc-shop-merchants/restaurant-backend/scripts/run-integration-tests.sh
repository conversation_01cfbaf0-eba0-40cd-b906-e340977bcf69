#!/bin/bash

# Integration Test Runner for Gemini and AI Generation Services
# This script runs comprehensive integration tests for the AI generation system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if environment variable is set
check_env_var() {
    local var_name=$1
    local var_value=${!var_name}
    
    if [ -z "$var_value" ]; then
        print_warning "$var_name is not set"
        return 1
    else
        # Mask the API key for security
        local masked_value
        if [[ ${#var_value} -gt 8 ]]; then
            masked_value="${var_value:0:4}...${var_value: -4}"
        else
            masked_value="***"
        fi
        print_status "$var_name is set: $masked_value"
        return 0
    fi
}

# Function to run tests with timeout
run_test_with_timeout() {
    local test_name=$1
    local test_command=$2
    local timeout_duration=${3:-300} # Default 5 minutes
    
    print_status "Running $test_name..."
    
    if timeout $timeout_duration $test_command; then
        print_success "$test_name completed successfully"
        return 0
    else
        local exit_code=$?
        if [ $exit_code -eq 124 ]; then
            print_error "$test_name timed out after $timeout_duration seconds"
        else
            print_error "$test_name failed with exit code $exit_code"
        fi
        return $exit_code
    fi
}

# Main function
main() {
    print_status "Starting AI Generation Integration Tests"
    echo "========================================"
    
    # Check if we're in the correct directory
    if [ ! -f "go.mod" ]; then
        print_error "go.mod not found. Please run this script from the restaurant-backend directory."
        exit 1
    fi
    
    # Check environment variables
    print_status "Checking environment configuration..."
    
    local has_openai=false
    local has_gemini=false
    
    if check_env_var "OPENAI_API_KEY"; then
        has_openai=true
    fi
    
    if check_env_var "GEMINI_API_KEY"; then
        has_gemini=true
    fi
    
    if check_env_var "GEMINI_PROJECT_ID"; then
        print_status "GEMINI_PROJECT_ID: $GEMINI_PROJECT_ID"
    else
        print_warning "GEMINI_PROJECT_ID not set, using default: scandine-457107"
        export GEMINI_PROJECT_ID="scandine-457107"
    fi
    
    if check_env_var "GEMINI_LOCATION"; then
        print_status "GEMINI_LOCATION: $GEMINI_LOCATION"
    else
        print_warning "GEMINI_LOCATION not set, using default: us-central1"
        export GEMINI_LOCATION="us-central1"
    fi
    
    # Check if at least one AI provider is available
    if [ "$has_openai" = false ] && [ "$has_gemini" = false ]; then
        print_error "No AI provider API keys found. Please set OPENAI_API_KEY or GEMINI_API_KEY."
        print_error "Tests will run but most functionality will be skipped."
    fi
    
    echo ""
    print_status "Test Configuration Summary:"
    echo "- OpenAI Available: $has_openai"
    echo "- Gemini Available: $has_gemini"
    echo "- Project ID: ${GEMINI_PROJECT_ID:-scandine-457107}"
    echo "- Location: ${GEMINI_LOCATION:-us-central1}"
    echo ""
    
    # Set test timeout
    export TEST_TIMEOUT=300 # 5 minutes
    
    # Run tests
    local test_failed=false
    
    # 1. Run Gemini Service Tests
    if [ "$has_gemini" = true ]; then
        print_status "Running Gemini Service Integration Tests..."
        if ! run_test_with_timeout "Gemini Service Tests" \
            "go test -v -timeout=${TEST_TIMEOUT}s ./test/integration -run TestGeminiServiceTestSuite" 300; then
            test_failed=true
        fi
    else
        print_warning "Skipping Gemini Service Tests (API key not available)"
    fi
    
    echo ""
    
    # 2. Run AI Generation Service Tests
    print_status "Running AI Generation Service Integration Tests..."
    if ! run_test_with_timeout "AI Generation Service Tests" \
        "go test -v -timeout=${TEST_TIMEOUT}s ./test/integration -run TestAIGenerationServiceTestSuite" 600; then
        test_failed=true
    fi
    
    echo ""
    
    # 3. Run API Integration Tests
    print_status "Running API Integration Tests..."
    if ! run_test_with_timeout "API Integration Tests" \
        "go test -v -timeout=${TEST_TIMEOUT}s ./test/integration -run TestAIGenerationAPITestSuite" 300; then
        test_failed=true
    fi
    
    echo ""
    
    # 4. Run All Integration Tests Together
    print_status "Running Complete Integration Test Suite..."
    if ! run_test_with_timeout "Complete Integration Tests" \
        "go test -v -timeout=900s ./test/integration/..." 900; then
        test_failed=true
    fi
    
    echo ""
    echo "========================================"
    
    # Summary
    if [ "$test_failed" = true ]; then
        print_error "Some tests failed. Please check the output above for details."
        exit 1
    else
        print_success "All integration tests passed successfully!"
        
        # Print summary of what was tested
        echo ""
        print_status "Test Summary:"
        if [ "$has_gemini" = true ]; then
            echo "✅ Gemini API integration"
            echo "✅ Gemini menu image analysis"
            echo "✅ Gemini food image analysis"
            echo "✅ Gemini text enhancement"
            echo "✅ Imagen image generation framework"
        fi
        if [ "$has_openai" = true ]; then
            echo "✅ OpenAI API integration"
        fi
        echo "✅ AI Generation Service with provider selection"
        echo "✅ API endpoints for AI generation"
        echo "✅ Job creation and processing"
        echo "✅ Error handling and validation"
        
        print_success "Integration tests completed successfully!"
    fi
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "AI Generation Integration Test Runner"
        echo ""
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --quick, -q    Run quick tests only (skip long-running tests)"
        echo "  --gemini-only  Run only Gemini-related tests"
        echo "  --openai-only  Run only OpenAI-related tests"
        echo ""
        echo "Environment Variables:"
        echo "  OPENAI_API_KEY     OpenAI API key for testing"
        echo "  GEMINI_API_KEY     Gemini API key for testing"
        echo "  GEMINI_PROJECT_ID  Google Cloud project ID (default: scandine-457107)"
        echo "  GEMINI_LOCATION    Google Cloud location (default: us-central1)"
        echo ""
        exit 0
        ;;
    --quick|-q)
        print_status "Running quick tests only..."
        export TEST_TIMEOUT=60
        ;;
    --gemini-only)
        print_status "Running Gemini tests only..."
        if ! check_env_var "GEMINI_API_KEY"; then
            print_error "GEMINI_API_KEY is required for Gemini-only tests"
            exit 1
        fi
        exec go test -v -timeout=300s ./test/integration -run ".*Gemini.*"
        ;;
    --openai-only)
        print_status "Running OpenAI tests only..."
        if ! check_env_var "OPENAI_API_KEY"; then
            print_error "OPENAI_API_KEY is required for OpenAI-only tests"
            exit 1
        fi
        exec go test -v -timeout=300s ./test/integration -run ".*OpenAI.*"
        ;;
esac

# Run main function
main "$@"
