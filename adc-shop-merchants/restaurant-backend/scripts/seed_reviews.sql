-- Seed reviews for testing
-- This script adds comprehensive sample reviews to the database

-- First, let's check if we have shops and branches to work with
DO $$
DECLARE
    shop_count INTEGER;
    branch_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO shop_count FROM shops WHERE status = 'active';
    SELECT COUNT(*) INTO branch_count FROM shop_branches WHERE status = 'active';

    IF shop_count = 0 OR branch_count = 0 THEN
        RAISE NOTICE 'No active shops or branches found. Please seed shops and branches first.';
        RETURN;
    END IF;

    RAISE NOTICE 'Found % active shops and % active branches. Proceeding with review seeding.', shop_count, branch_count;
END $$;

-- Insert diverse reviews with different ratings, sources, and statuses
-- 5-star reviews (positive sentiment)
INSERT INTO reviews (branch_id, customer_name, customer_email, customer_avatar, rating, title, comment, photos, source, status, is_verified, is_public, response, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    '<PERSON>' as customer_name,
    '<EMAIL>' as customer_email,
    'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face' as customer_avatar,
    5 as rating,
    'Outstanding Experience!' as title,
    'Absolutely amazing food and service! The pad thai was perfectly balanced with just the right amount of spice. Our server was attentive and friendly throughout the meal. The atmosphere was cozy and perfect for a date night. Will definitely be coming back!' as comment,
    '["https://images.unsplash.com/photo-1559339352-11d035aa65de?w=400&h=300&fit=crop", "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop"]'::jsonb as photos,
    'google' as source,
    'approved' as status,
    true as is_verified,
    true as is_public,
    ('{"message": "Thank you so much for your wonderful review, Sarah! We''re thrilled you enjoyed the pad thai and our service. We look forward to welcoming you back soon!", "responded_by": "Restaurant Manager", "responded_at": "' || (NOW() - INTERVAL '2 hours')::text || '"}')::jsonb as response,
    '["excellent service", "great food", "romantic atmosphere"]'::jsonb as tags,
    'positive' as sentiment,
    NOW() - INTERVAL '3 days' as created_at,
    NOW() - INTERVAL '2 hours' as updated_at
FROM shop_branches sb
WHERE sb.status = 'active'
LIMIT 1;

INSERT INTO reviews (branch_id, customer_name, customer_email, customer_avatar, rating, title, comment, source, status, is_verified, is_public, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Michael Chen' as customer_name,
    '<EMAIL>' as customer_email,
    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face' as customer_avatar,
    5 as rating,
    'Best Thai Food in Town!' as title,
    'I''ve been to many Thai restaurants, but this one stands out. The green curry was authentic and flavorful, and the spring rolls were crispy and fresh. The staff was knowledgeable about the menu and made great recommendations. Highly recommend!' as comment,
    'yelp' as source,
    'approved' as status,
    true as is_verified,
    true as is_public,
    '["authentic", "flavorful", "knowledgeable staff"]'::jsonb as tags,
    'positive' as sentiment,
    NOW() - INTERVAL '5 days' as created_at,
    NOW() - INTERVAL '5 days' as updated_at
FROM shop_branches sb
WHERE sb.status = 'active'
LIMIT 1;

-- 4-star reviews (positive sentiment)
INSERT INTO reviews (branch_id, customer_name, customer_email, customer_avatar, rating, title, comment, photos, source, status, is_verified, is_public, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Emily Rodriguez' as customer_name,
    '<EMAIL>' as customer_email,
    'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face' as customer_avatar,
    4 as rating,
    'Great Food, Minor Wait' as title,
    'The food was delicious and well-presented. We ordered the massaman curry and tom yum soup, both were excellent. The only downside was the wait time - about 25 minutes for our food. But the quality made up for it. Would come back!' as comment,
    '["https://images.unsplash.com/photo-1559847844-5315695dadae?w=400&h=300&fit=crop"]'::jsonb as photos,
    'facebook' as source,
    'approved' as status,
    true as is_verified,
    true as is_public,
    '["delicious", "well-presented", "slow service"]'::jsonb as tags,
    'positive' as sentiment,
    NOW() - INTERVAL '1 week' as created_at,
    NOW() - INTERVAL '1 week' as updated_at
FROM shop_branches sb
WHERE sb.status = 'active'
LIMIT 1;

INSERT INTO reviews (branch_id, customer_name, customer_email, customer_avatar, rating, title, comment, source, status, is_verified, is_public, response, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'David Kim' as customer_name,
    '<EMAIL>' as customer_email,
    'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face' as customer_avatar,
    4 as rating,
    'Solid Choice for Thai Food' as title,
    'Good portion sizes and reasonable prices. The drunken noodles had good flavor but could use a bit more spice. Service was friendly and the restaurant was clean. A reliable choice for Thai food in the area.' as comment,
    'tripadvisor' as source,
    'approved' as status,
    false as is_verified,
    true as is_public,
    ('{"message": "Thank you for your feedback, David! We''ll let our kitchen know about the spice level. Please don''t hesitate to ask for extra spice next time!", "responded_by": "Chef", "responded_at": "' || (NOW() - INTERVAL '1 day')::text || '"}')::jsonb as response,
    '["good portions", "reasonable prices", "friendly service"]'::jsonb as tags,
    'positive' as sentiment,
    NOW() - INTERVAL '10 days' as created_at,
    NOW() - INTERVAL '1 day' as updated_at
FROM shop_branches sb
WHERE sb.status = 'active'
LIMIT 1;

-- 3-star reviews (neutral sentiment)
INSERT INTO reviews (branch_id, customer_name, customer_email, customer_avatar, rating, title, comment, source, status, is_verified, is_public, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Jessica Thompson' as customer_name,
    '<EMAIL>' as customer_email,
    'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face' as customer_avatar,
    3 as rating,
    'Average Experience' as title,
    'The food was okay, nothing special. We ordered pad see ew and it was a bit bland. The service was adequate but not particularly attentive. The restaurant was clean and the prices were fair. It''s an okay option if you''re in the area.' as comment,
    'internal' as source,
    'approved' as status,
    false as is_verified,
    true as is_public,
    '["average", "bland", "adequate service"]'::jsonb as tags,
    'neutral' as sentiment,
    NOW() - INTERVAL '2 weeks' as created_at,
    NOW() - INTERVAL '2 weeks' as updated_at
FROM shop_branches sb
WHERE sb.status = 'active'
LIMIT 1;

-- 2-star reviews (negative sentiment)
INSERT INTO reviews (branch_id, customer_name, customer_email, customer_avatar, rating, title, comment, source, status, is_verified, is_public, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Robert Wilson' as customer_name,
    '<EMAIL>' as customer_email,
    'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face' as customer_avatar,
    2 as rating,
    'Disappointing Visit' as title,
    'Unfortunately, our experience was below expectations. The food took 45 minutes to arrive and when it did, it was lukewarm. The pad thai was too sweet and the vegetables seemed old. The server seemed overwhelmed and forgot our drink order twice.' as comment,
    'google' as source,
    'approved' as status,
    false as is_verified,
    true as is_public,
    '["slow service", "cold food", "overwhelmed staff"]'::jsonb as tags,
    'negative' as sentiment,
    NOW() - INTERVAL '3 weeks' as created_at,
    NOW() - INTERVAL '3 weeks' as updated_at
FROM shop_branches sb
WHERE sb.status = 'active'
LIMIT 1;

-- 1-star review (negative sentiment)
INSERT INTO reviews (branch_id, customer_name, customer_email, customer_avatar, rating, title, comment, source, status, is_verified, is_public, response, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Karen Davis' as customer_name,
    '<EMAIL>' as customer_email,
    'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face' as customer_avatar,
    1 as rating,
    'Terrible Experience' as title,
    'Worst dining experience I''ve had in a long time. The food was inedible - completely oversalted and the meat seemed spoiled. When we complained, the staff was rude and dismissive. We left without finishing our meal and will never return.' as comment,
    'yelp' as source,
    'flagged' as status,
    false as is_verified,
    true as is_public,
    ('{"message": "We sincerely apologize for your terrible experience. This is not the standard we strive for. Please contact our manager directly so we can make this right and investigate what went wrong.", "responded_by": "General Manager", "responded_at": "' || (NOW() - INTERVAL '1 day')::text || '"}')::jsonb as response,
    '["terrible", "oversalted", "rude staff", "spoiled food"]'::jsonb as tags,
    'negative' as sentiment,
    NOW() - INTERVAL '1 month' as created_at,
    NOW() - INTERVAL '1 day' as updated_at
FROM shop_branches sb
WHERE sb.status = 'active'
LIMIT 1;

-- Pending reviews (awaiting moderation)
INSERT INTO reviews (branch_id, customer_name, customer_email, rating, title, comment, source, status, is_verified, is_public, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Alex Martinez' as customer_name,
    '<EMAIL>' as customer_email,
    4 as rating,
    'Good Food, Needs Improvement' as title,
    'The curry was flavorful but the rice was a bit dry. Service could be faster during peak hours. Overall a decent meal but room for improvement.' as comment,
    'internal' as source,
    'pending' as status,
    false as is_verified,
    true as is_public,
    '["flavorful", "dry rice", "slow service"]'::jsonb as tags,
    'positive' as sentiment,
    NOW() - INTERVAL '2 days' as created_at,
    NOW() - INTERVAL '2 days' as updated_at
FROM shop_branches sb
WHERE sb.status = 'active'
LIMIT 1;

INSERT INTO reviews (branch_id, customer_name, customer_email, rating, title, comment, source, status, is_verified, is_public, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Lisa Chang' as customer_name,
    '<EMAIL>' as customer_email,
    5 as rating,
    'Amazing Authentic Thai!' as title,
    'Best Thai food I''ve had outside of Thailand! The som tam was perfectly spicy and the mango sticky rice was divine. Definitely coming back with friends!' as comment,
    'facebook' as source,
    'pending' as status,
    false as is_verified,
    true as is_public,
    '["authentic", "spicy", "divine", "best thai"]'::jsonb as tags,
    'positive' as sentiment,
    NOW() - INTERVAL '1 day' as created_at,
    NOW() - INTERVAL '1 day' as updated_at
FROM shop_branches sb
WHERE sb.status = 'active'
LIMIT 1;

-- Add more reviews for different branches if they exist
-- Recent reviews with various sources
INSERT INTO reviews (branch_id, customer_name, customer_email, customer_avatar, rating, title, comment, photos, source, status, is_verified, is_public, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'James Anderson' as customer_name,
    '<EMAIL>' as customer_email,
    'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face' as customer_avatar,
    5 as rating,
    'Perfect for Special Occasions' as title,
    'Celebrated my anniversary here and it was perfect! The ambiance was romantic, the food was exceptional, and the staff went above and beyond to make our evening special. The mango sticky rice dessert was the perfect ending to a wonderful meal.' as comment,
    '["https://images.unsplash.com/photo-1551218808-94e220e084d2?w=400&h=300&fit=crop"]'::jsonb as photos,
    'google' as source,
    'approved' as status,
    true as is_verified,
    true as is_public,
    '["romantic", "exceptional", "special occasion", "perfect"]'::jsonb as tags,
    'positive' as sentiment,
    NOW() - INTERVAL '6 days' as created_at,
    NOW() - INTERVAL '6 days' as updated_at
FROM shop_branches sb
WHERE sb.status = 'active'
OFFSET 1
LIMIT 1;

INSERT INTO reviews (branch_id, customer_name, customer_email, customer_avatar, rating, title, comment, source, status, is_verified, is_public, response, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Maria Gonzalez' as customer_name,
    '<EMAIL>' as customer_email,
    'https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=150&h=150&fit=crop&crop=face' as customer_avatar,
    4 as rating,
    'Great Lunch Spot' as title,
    'Perfect for a quick lunch! The lunch specials are well-priced and the portions are generous. The tom kha soup was creamy and delicious. Service was quick and efficient. Will definitely be back for lunch again.' as comment,
    'yelp' as source,
    'approved' as status,
    true as is_verified,
    true as is_public,
    ('{"message": "Thank you Maria! We''re so glad you enjoyed our lunch specials. Our tom kha soup is one of our chef''s favorites too!", "responded_by": "Owner", "responded_at": "' || (NOW() - INTERVAL '3 hours')::text || '"}')::jsonb as response,
    '["quick lunch", "well-priced", "generous portions", "efficient"]'::jsonb as tags,
    'positive' as sentiment,
    NOW() - INTERVAL '8 days' as created_at,
    NOW() - INTERVAL '3 hours' as updated_at
FROM shop_branches sb
WHERE sb.status = 'active'
LIMIT 1;

INSERT INTO reviews (branch_id, customer_name, customer_email, customer_avatar, rating, title, comment, source, status, is_verified, is_public, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Thomas Brown' as customer_name,
    '<EMAIL>' as customer_email,
    'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=150&h=150&fit=crop&crop=face' as customer_avatar,
    3 as rating,
    'Mixed Experience' as title,
    'The appetizers were great - fresh spring rolls and crispy calamari. However, the main courses were inconsistent. My partner''s curry was excellent but my stir-fry was overcooked. Service was friendly but a bit slow.' as comment,
    'tripadvisor' as source,
    'approved' as status,
    false as is_verified,
    true as is_public,
    '["great appetizers", "inconsistent mains", "friendly service", "slow"]'::jsonb as tags,
    'neutral' as sentiment,
    NOW() - INTERVAL '12 days' as created_at,
    NOW() - INTERVAL '12 days' as updated_at
FROM shop_branches sb
WHERE sb.status = 'active'
LIMIT 1;

-- Add some older reviews for historical data
INSERT INTO reviews (branch_id, customer_name, customer_email, customer_avatar, rating, title, comment, source, status, is_verified, is_public, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Jennifer Lee' as customer_name,
    '<EMAIL>' as customer_email,
    'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face' as customer_avatar,
    5 as rating,
    'Consistently Excellent' as title,
    'I''ve been coming here for over a year and the quality is always consistent. The staff remembers my usual order and the food is always fresh and flavorful. This is my go-to Thai restaurant!' as comment,
    'internal' as source,
    'approved' as status,
    true as is_verified,
    true as is_public,
    '["consistent", "fresh", "flavorful", "go-to restaurant"]'::jsonb as tags,
    'positive' as sentiment,
    NOW() - INTERVAL '2 months' as created_at,
    NOW() - INTERVAL '2 months' as updated_at
FROM shop_branches sb
WHERE sb.status = 'active'
LIMIT 1;

INSERT INTO reviews (branch_id, customer_name, customer_email, customer_avatar, rating, title, comment, source, status, is_verified, is_public, response, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Kevin O''Connor' as customer_name,
    '<EMAIL>' as customer_email,
    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face' as customer_avatar,
    2 as rating,
    'Not What It Used to Be' as title,
    'I used to love this place but something has changed. The food quality has declined and the service is not as attentive as before. The prices have gone up but the portions seem smaller. Disappointed with my recent visits.' as comment,
    'google' as source,
    'approved' as status,
    false as is_verified,
    true as is_public,
    ('{"message": "We''re sorry to hear about your disappointing experience, Kevin. We''ve recently made some changes and are working to improve. Please give us another chance and contact us directly if you have specific concerns.", "responded_by": "Manager", "responded_at": "' || (NOW() - INTERVAL '5 days')::text || '"}')::jsonb as response,
    '["declined quality", "poor service", "expensive", "smaller portions"]'::jsonb as tags,
    'negative' as sentiment,
    NOW() - INTERVAL '3 months' as created_at,
    NOW() - INTERVAL '5 days' as updated_at
FROM shop_branches sb
WHERE sb.status = 'active'
LIMIT 1;

-- Add some reviews from different time periods for analytics
INSERT INTO reviews (branch_id, customer_name, customer_email, rating, title, comment, source, status, is_verified, is_public, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Amanda Foster' as customer_name,
    '<EMAIL>' as customer_email,
    4 as rating,
    'Good Value for Money' as title,
    'Decent Thai food at reasonable prices. The portions are good and the flavors are authentic. Not the fanciest place but the food speaks for itself. Good for casual dining.' as comment,
    'facebook' as source,
    'approved' as status,
    false as is_verified,
    true as is_public,
    '["good value", "reasonable prices", "authentic flavors", "casual dining"]'::jsonb as tags,
    'positive' as sentiment,
    NOW() - INTERVAL '6 months' as created_at,
    NOW() - INTERVAL '6 months' as updated_at
FROM shop_branches sb
WHERE sb.status = 'active'
LIMIT 1;

-- Add a few more recent reviews for better testing
INSERT INTO reviews (branch_id, customer_name, customer_email, customer_avatar, rating, title, comment, source, status, is_verified, is_public, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Ryan Murphy' as customer_name,
    '<EMAIL>' as customer_email,
    'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face' as customer_avatar,
    5 as rating,
    'Exceeded Expectations!' as title,
    'Came here on a friend''s recommendation and was blown away! The green papaya salad was incredibly fresh and the larb was perfectly seasoned. The staff was attentive without being intrusive. Will definitely be back!' as comment,
    'yelp' as source,
    'approved' as status,
    true as is_verified,
    true as is_public,
    '["exceeded expectations", "fresh", "perfectly seasoned", "attentive staff"]'::jsonb as tags,
    'positive' as sentiment,
    NOW() - INTERVAL '4 hours' as created_at,
    NOW() - INTERVAL '4 hours' as updated_at
FROM shop_branches sb
WHERE sb.status = 'active'
LIMIT 1;

-- Add more reviews with different statuses for comprehensive testing

-- Rejected reviews (negative content that was rejected by moderation)
INSERT INTO reviews (branch_id, customer_name, customer_email, customer_avatar, rating, title, comment, source, status, is_verified, is_public, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Angry Customer' as customer_name,
    '<EMAIL>' as customer_email,
    'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face' as customer_avatar,
    1 as rating,
    'Completely Unacceptable!' as title,
    'This place is absolutely terrible! The food was disgusting, the service was horrible, and the place was dirty. I demand a full refund and will never come back. This is the worst restaurant experience of my life!' as comment,
    'google' as source,
    'rejected' as status,
    false as is_verified,
    false as is_public,
    '["terrible", "disgusting", "horrible", "dirty", "worst"]'::jsonb as tags,
    'negative' as sentiment,
    NOW() - INTERVAL '1 week' as created_at,
    NOW() - INTERVAL '1 week' as updated_at
FROM shop_branches sb
WHERE sb.status = 'active'
LIMIT 1;

INSERT INTO reviews (branch_id, customer_name, customer_email, rating, title, comment, source, status, is_verified, is_public, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Spam Account' as customer_name,
    '<EMAIL>' as customer_email,
    1 as rating,
    'Check out my website for better deals!' as title,
    'Visit my website www.fake-deals.com for amazing restaurant discounts! Click here now for 50% off everything! This is definitely not spam at all!' as comment,
    'internal' as source,
    'rejected' as status,
    false as is_verified,
    false as is_public,
    '["spam", "fake", "promotional"]'::jsonb as tags,
    'negative' as sentiment,
    NOW() - INTERVAL '2 weeks' as created_at,
    NOW() - INTERVAL '2 weeks' as updated_at
FROM shop_branches sb
WHERE sb.status = 'active'
LIMIT 1;

-- More flagged reviews (inappropriate content flagged by users or system)
INSERT INTO reviews (branch_id, customer_name, customer_email, customer_avatar, rating, title, comment, source, status, is_verified, is_public, response, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Inappropriate User' as customer_name,
    '<EMAIL>' as customer_email,
    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face' as customer_avatar,
    2 as rating,
    'Questionable Content' as title,
    'The food was okay but I have some inappropriate comments about the staff that are not suitable for public viewing. This review contains content that violates community guidelines.' as comment,
    'yelp' as source,
    'flagged' as status,
    false as is_verified,
    false as is_public,
    ('{"message": "Thank you for your feedback. We take all concerns seriously and are investigating this matter. We strive to maintain a respectful environment for all.", "responded_by": "Community Manager", "responded_at": "' || (NOW() - INTERVAL '2 days')::text || '"}')::jsonb as response,
    '["inappropriate", "flagged", "investigation"]'::jsonb as tags,
    'negative' as sentiment,
    NOW() - INTERVAL '4 days' as created_at,
    NOW() - INTERVAL '2 days' as updated_at
FROM shop_branches sb
WHERE sb.status = 'active'
LIMIT 1;

INSERT INTO reviews (branch_id, customer_name, customer_email, customer_avatar, rating, title, comment, source, status, is_verified, is_public, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Suspicious Reviewer' as customer_name,
    '<EMAIL>' as customer_email,
    'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face' as customer_avatar,
    5 as rating,
    'Amazing! Perfect! Best Ever!' as title,
    'This is the most amazing restaurant ever! Everything is perfect! 5 stars! Best food! Best service! Amazing! Perfect! Wonderful! Outstanding! Incredible! Fantastic! Superb! Excellent!' as comment,
    'facebook' as source,
    'flagged' as status,
    false as is_verified,
    true as is_public,
    '["suspicious", "repetitive", "fake-positive"]'::jsonb as tags,
    'positive' as sentiment,
    NOW() - INTERVAL '9 days' as created_at,
    NOW() - INTERVAL '9 days' as updated_at
FROM shop_branches sb
WHERE sb.status = 'active'
LIMIT 1;

-- More pending reviews (awaiting moderation)
INSERT INTO reviews (branch_id, customer_name, customer_email, customer_avatar, rating, title, comment, source, status, is_verified, is_public, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'New Customer' as customer_name,
    '<EMAIL>' as customer_email,
    'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face' as customer_avatar,
    4 as rating,
    'First Time Visit' as title,
    'This was my first time visiting this restaurant. The food was quite good and the atmosphere was nice. The service was a bit slow but the staff was friendly. I would consider coming back again.' as comment,
    'tripadvisor' as source,
    'pending' as status,
    false as is_verified,
    true as is_public,
    '["first time", "good food", "slow service", "friendly staff"]'::jsonb as tags,
    'positive' as sentiment,
    NOW() - INTERVAL '3 hours' as created_at,
    NOW() - INTERVAL '3 hours' as updated_at
FROM shop_branches sb
WHERE sb.status = 'active'
LIMIT 1;

INSERT INTO reviews (branch_id, customer_name, customer_email, rating, title, comment, source, status, is_verified, is_public, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Regular Diner' as customer_name,
    '<EMAIL>' as customer_email,
    3 as rating,
    'Mixed Feelings' as title,
    'I come here regularly and usually have a good experience, but today was different. The food quality seemed inconsistent and the service was not up to their usual standards. Hope this was just an off day.' as comment,
    'internal' as source,
    'pending' as status,
    true as is_verified,
    true as is_public,
    '["regular customer", "inconsistent", "off day", "usually good"]'::jsonb as tags,
    'neutral' as sentiment,
    NOW() - INTERVAL '6 hours' as created_at,
    NOW() - INTERVAL '6 hours' as updated_at
FROM shop_branches sb
WHERE sb.status = 'active'
LIMIT 1;

-- Verify the inserted data with status breakdown
SELECT
    r.status,
    COUNT(*) as count,
    ROUND(AVG(r.rating), 2) as avg_rating
FROM reviews r
JOIN shop_branches sb ON r.branch_id = sb.id
GROUP BY r.status
ORDER BY count DESC;

-- Add comprehensive seed data for all statuses and star ratings
-- This ensures we have good coverage for testing

-- More 5-star approved reviews
INSERT INTO reviews (branch_id, customer_name, customer_email, customer_avatar, rating, title, comment, photos, source, status, is_verified, is_public, response, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Emma Wilson' as customer_name,
    '<EMAIL>' as customer_email,
    'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face' as customer_avatar,
    5 as rating,
    'Absolutely Perfect!' as title,
    'Everything was flawless from start to finish. The ambiance, service, and food quality exceeded all expectations. This is definitely our new favorite restaurant!' as comment,
    '["https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop"]'::jsonb as photos,
    'google' as source,
    'approved' as status,
    true as is_verified,
    true as is_public,
    ('{"message": "Thank you Emma! We''re absolutely thrilled that everything exceeded your expectations. We can''t wait to welcome you back!", "responded_by": "Owner", "responded_at": "' || (NOW() - INTERVAL '1 hour')::text || '"}')::jsonb as response,
    '["flawless", "exceeded expectations", "favorite restaurant"]'::jsonb as tags,
    'positive' as sentiment,
    NOW() - INTERVAL '2 hours' as created_at,
    NOW() - INTERVAL '1 hour' as updated_at
FROM shop_branches sb
WHERE sb.slug = 'posriya'
LIMIT 1;

-- 4-star approved reviews
INSERT INTO reviews (branch_id, customer_name, customer_email, customer_avatar, rating, title, comment, source, status, is_verified, is_public, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'John Smith' as customer_name,
    '<EMAIL>' as customer_email,
    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face' as customer_avatar,
    4 as rating,
    'Very Good Experience' as title,
    'Great food and friendly service. The pad thai was delicious and the portions were generous. Only minor issue was the wait time, but overall very satisfied.' as comment,
    'yelp' as source,
    'approved' as status,
    true as is_verified,
    true as is_public,
    '["great food", "friendly service", "generous portions", "wait time"]'::jsonb as tags,
    'positive' as sentiment,
    NOW() - INTERVAL '5 hours' as created_at,
    NOW() - INTERVAL '5 hours' as updated_at
FROM shop_branches sb
WHERE sb.slug = 'posriya'
LIMIT 1;

-- 3-star approved reviews
INSERT INTO reviews (branch_id, customer_name, customer_email, customer_avatar, rating, title, comment, source, status, is_verified, is_public, response, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Lisa Brown' as customer_name,
    '<EMAIL>' as customer_email,
    'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face' as customer_avatar,
    3 as rating,
    'Average Meal' as title,
    'The food was decent but nothing extraordinary. Service was okay, prices are reasonable. It''s a fine option but there are better Thai restaurants in the area.' as comment,
    'tripadvisor' as source,
    'approved' as status,
    false as is_verified,
    true as is_public,
    ('{"message": "Thank you for your honest feedback, Lisa. We''re always working to improve and would love another chance to impress you!", "responded_by": "Manager", "responded_at": "' || (NOW() - INTERVAL '2 days')::text || '"}')::jsonb as response,
    '["decent", "average", "reasonable prices", "room for improvement"]'::jsonb as tags,
    'neutral' as sentiment,
    NOW() - INTERVAL '3 days' as created_at,
    NOW() - INTERVAL '2 days' as updated_at
FROM shop_branches sb
WHERE sb.slug = 'posriya'
LIMIT 1;

-- 2-star approved reviews
INSERT INTO reviews (branch_id, customer_name, customer_email, customer_avatar, rating, title, comment, source, status, is_verified, is_public, response, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Mark Johnson' as customer_name,
    '<EMAIL>' as customer_email,
    'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face' as customer_avatar,
    2 as rating,
    'Disappointing Visit' as title,
    'Unfortunately, our experience was below average. The food took too long to arrive and when it did, it was cold. The curry lacked flavor and the rice was overcooked.' as comment,
    'facebook' as source,
    'approved' as status,
    false as is_verified,
    true as is_public,
    ('{"message": "We sincerely apologize for this disappointing experience, Mark. This is not the standard we strive for. Please contact us directly so we can make this right.", "responded_by": "General Manager", "responded_at": "' || (NOW() - INTERVAL '1 day')::text || '"}')::jsonb as response,
    '["disappointing", "cold food", "slow service", "overcooked"]'::jsonb as tags,
    'negative' as sentiment,
    NOW() - INTERVAL '4 days' as created_at,
    NOW() - INTERVAL '1 day' as updated_at
FROM shop_branches sb
WHERE sb.slug = 'posriya'
LIMIT 1;

-- 1-star approved reviews
INSERT INTO reviews (branch_id, customer_name, customer_email, customer_avatar, rating, title, comment, source, status, is_verified, is_public, response, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Sarah Davis' as customer_name,
    '<EMAIL>' as customer_email,
    'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face' as customer_avatar,
    1 as rating,
    'Very Poor Experience' as title,
    'This was one of the worst dining experiences I''ve had. The food was terrible, service was rude, and the restaurant was not clean. Would not recommend to anyone.' as comment,
    'google' as source,
    'approved' as status,
    false as is_verified,
    true as is_public,
    ('{"message": "We are deeply sorry for this terrible experience, Sarah. This is completely unacceptable and we are taking immediate action to address these issues. Please contact our management team directly.", "responded_by": "Owner", "responded_at": "' || (NOW() - INTERVAL '6 hours')::text || '"}')::jsonb as response,
    '["terrible", "rude service", "not clean", "worst experience"]'::jsonb as tags,
    'negative' as sentiment,
    NOW() - INTERVAL '1 week' as created_at,
    NOW() - INTERVAL '6 hours' as updated_at
FROM shop_branches sb
WHERE sb.slug = 'posriya'
LIMIT 1;

-- Pending reviews for all star ratings
INSERT INTO reviews (branch_id, customer_name, customer_email, customer_avatar, rating, title, comment, source, status, is_verified, is_public, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Mike Chen' as customer_name,
    '<EMAIL>' as customer_email,
    'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face' as customer_avatar,
    5 as rating,
    'Outstanding Service!' as title,
    'Just had an amazing dinner here! The staff was incredibly attentive and the food was absolutely delicious. Every dish was perfectly prepared and beautifully presented.' as comment,
    'internal' as source,
    'pending' as status,
    false as is_verified,
    true as is_public,
    '["amazing", "attentive staff", "delicious", "perfectly prepared"]'::jsonb as tags,
    'positive' as sentiment,
    NOW() - INTERVAL '30 minutes' as created_at,
    NOW() - INTERVAL '30 minutes' as updated_at
FROM shop_branches sb
WHERE sb.slug = 'posriya'
LIMIT 1;

INSERT INTO reviews (branch_id, customer_name, customer_email, rating, title, comment, source, status, is_verified, is_public, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Anna Taylor' as customer_name,
    '<EMAIL>' as customer_email,
    4 as rating,
    'Good Food, Nice Atmosphere' as title,
    'Enjoyed our meal here. The green curry was excellent and the atmosphere was pleasant. Service could be a bit faster but overall a good experience.' as comment,
    'yelp' as source,
    'pending' as status,
    false as is_verified,
    true as is_public,
    '["good meal", "excellent curry", "pleasant atmosphere", "slow service"]'::jsonb as tags,
    'positive' as sentiment,
    NOW() - INTERVAL '1 hour' as created_at,
    NOW() - INTERVAL '1 hour' as updated_at
FROM shop_branches sb
WHERE sb.slug = 'posriya'
LIMIT 1;

INSERT INTO reviews (branch_id, customer_name, customer_email, rating, title, comment, source, status, is_verified, is_public, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Tom Wilson' as customer_name,
    '<EMAIL>' as customer_email,
    3 as rating,
    'Okay Experience' as title,
    'The food was alright, nothing special. Prices are fair and the location is convenient. Might come back if in the area.' as comment,
    'facebook' as source,
    'pending' as status,
    false as is_verified,
    true as is_public,
    '["alright", "fair prices", "convenient location", "nothing special"]'::jsonb as tags,
    'neutral' as sentiment,
    NOW() - INTERVAL '2 hours' as created_at,
    NOW() - INTERVAL '2 hours' as updated_at
FROM shop_branches sb
WHERE sb.slug = 'posriya'
LIMIT 1;

-- Flagged reviews for different ratings
INSERT INTO reviews (branch_id, customer_name, customer_email, customer_avatar, rating, title, comment, source, status, is_verified, is_public, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Fake Reviewer' as customer_name,
    '<EMAIL>' as customer_email,
    'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face' as customer_avatar,
    5 as rating,
    'BEST RESTAURANT EVER!!!' as title,
    'OMG THIS IS THE BEST RESTAURANT IN THE WORLD!!! AMAZING AMAZING AMAZING!!! 5 STARS!!! EVERYONE MUST COME HERE!!! PERFECT PERFECT PERFECT!!!' as comment,
    'google' as source,
    'flagged' as status,
    false as is_verified,
    false as is_public,
    '["suspicious", "excessive caps", "repetitive", "fake"]'::jsonb as tags,
    'positive' as sentiment,
    NOW() - INTERVAL '3 hours' as created_at,
    NOW() - INTERVAL '3 hours' as updated_at
FROM shop_branches sb
WHERE sb.slug = 'posriya'
LIMIT 1;

INSERT INTO reviews (branch_id, customer_name, customer_email, rating, title, comment, source, status, is_verified, is_public, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Rude Customer' as customer_name,
    '<EMAIL>' as customer_email,
    2 as rating,
    'Inappropriate Comments' as title,
    'The food was mediocre but I have some very inappropriate comments about the staff that I cannot share publicly. This review contains offensive language and personal attacks.' as comment,
    'yelp' as source,
    'flagged' as status,
    false as is_verified,
    false as is_public,
    '["inappropriate", "offensive", "personal attacks", "unprofessional"]'::jsonb as tags,
    'negative' as sentiment,
    NOW() - INTERVAL '5 hours' as created_at,
    NOW() - INTERVAL '5 hours' as updated_at
FROM shop_branches sb
WHERE sb.slug = 'posriya'
LIMIT 1;

-- Rejected reviews for different ratings
INSERT INTO reviews (branch_id, customer_name, customer_email, rating, title, comment, source, status, is_verified, is_public, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Spam Bot' as customer_name,
    '<EMAIL>' as customer_email,
    5 as rating,
    'Visit My Website!' as title,
    'Great restaurant! But check out my amazing deals at www.fake-restaurant-deals.com! Get 90% off all meals! Click here now! Limited time offer!' as comment,
    'internal' as source,
    'rejected' as status,
    false as is_verified,
    false as is_public,
    '["spam", "promotional", "fake deals", "bot"]'::jsonb as tags,
    'positive' as sentiment,
    NOW() - INTERVAL '1 day' as created_at,
    NOW() - INTERVAL '1 day' as updated_at
FROM shop_branches sb
WHERE sb.slug = 'posriya'
LIMIT 1;

INSERT INTO reviews (branch_id, customer_name, customer_email, rating, title, comment, source, status, is_verified, is_public, tags, sentiment, created_at, updated_at)
SELECT
    sb.id as branch_id,
    'Abusive User' as customer_name,
    '<EMAIL>' as customer_email,
    1 as rating,
    'Completely Unacceptable Content' as title,
    'This review contains extremely offensive language, threats, and completely inappropriate content that violates all community guidelines and terms of service.' as comment,
    'facebook' as source,
    'rejected' as status,
    false as is_verified,
    false as is_public,
    '["abusive", "threats", "offensive", "policy violation"]'::jsonb as tags,
    'negative' as sentiment,
    NOW() - INTERVAL '2 days' as created_at,
    NOW() - INTERVAL '2 days' as updated_at
FROM shop_branches sb
WHERE sb.slug = 'posriya'
LIMIT 1;

-- Show final status breakdown for posriya branch
SELECT
    r.status,
    r.rating,
    COUNT(*) as count
FROM reviews r
JOIN shop_branches sb ON r.branch_id = sb.id
WHERE sb.slug = 'posriya'
GROUP BY r.status, r.rating
ORDER BY r.status, r.rating DESC;

-- Show recent reviews by status for posriya
SELECT
    r.id,
    r.customer_name,
    r.rating,
    r.title,
    r.source,
    r.status,
    r.sentiment,
    r.created_at
FROM reviews r
JOIN shop_branches sb ON r.branch_id = sb.id
WHERE sb.slug = 'posriya'
ORDER BY r.created_at DESC
LIMIT 20;