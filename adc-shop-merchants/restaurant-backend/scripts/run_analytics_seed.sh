#!/bin/bash

# Script to run analytics seeding using PostgreSQL
# This script can be used as an alternative to the Go-based seeding

set -e

# Default database connection parameters
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-restaurant_db}"
DB_USER="${DB_USER:-postgres}"
DB_PASSWORD="${DB_PASSWORD:-password}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Analytics Seeding Script${NC}"
echo "=========================="

# Check if psql is available
if ! command -v psql &> /dev/null; then
    echo -e "${RED}Error: psql command not found. Please install PostgreSQL client.${NC}"
    exit 1
fi

# Check if the SQL file exists
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SQL_FILE="$SCRIPT_DIR/seed_analytics.sql"

if [ ! -f "$SQL_FILE" ]; then
    echo -e "${RED}Error: seed_analytics.sql not found in $SCRIPT_DIR${NC}"
    exit 1
fi

echo "Database connection parameters:"
echo "  Host: $DB_HOST"
echo "  Port: $DB_PORT"
echo "  Database: $DB_NAME"
echo "  User: $DB_USER"
echo ""

# Prompt for password if not set
if [ -z "$DB_PASSWORD" ]; then
    echo -n "Enter database password: "
    read -s DB_PASSWORD
    echo ""
fi

# Test database connection
echo -e "${YELLOW}Testing database connection...${NC}"
export PGPASSWORD="$DB_PASSWORD"

if ! psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" > /dev/null 2>&1; then
    echo -e "${RED}Error: Cannot connect to database. Please check your connection parameters.${NC}"
    exit 1
fi

echo -e "${GREEN}Database connection successful!${NC}"

# Run the analytics seeding script
echo -e "${YELLOW}Running analytics seeding script...${NC}"

if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "$SQL_FILE"; then
    echo -e "${GREEN}Analytics seeding completed successfully!${NC}"
    echo ""
    echo "The following analytics data has been seeded:"
    echo "  ✓ Sales metrics (30 days of hourly data)"
    echo "  ✓ Menu item metrics (30 days of performance data)"
    echo "  ✓ Customer metrics (30 days of customer behavior)"
    echo "  ✓ Staff metrics (30 days of staff performance)"
    echo "  ✓ Table metrics (30 days of table utilization)"
    echo "  ✓ Analytics reports (5 sample reports)"
    echo ""
    echo "You can now view the analytics data in your application!"
else
    echo -e "${RED}Error: Analytics seeding failed. Please check the error messages above.${NC}"
    exit 1
fi

# Clean up
unset PGPASSWORD
