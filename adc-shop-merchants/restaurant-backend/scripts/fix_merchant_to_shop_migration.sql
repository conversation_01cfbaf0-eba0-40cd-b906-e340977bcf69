-- Fix merchant_id to shop_id migration
-- This script handles the transition safely

BEGIN;

-- Step 1: Check if shop_id columns already exist
DO $$
BEGIN
    -- Add shop_id to users table if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'users' AND column_name = 'shop_id'
    ) THEN
        ALTER TABLE users ADD COLUMN shop_id UUID;
        RAISE NOTICE 'Added shop_id column to users table';
    ELSE
        RAISE NOTICE 'shop_id column already exists in users table';
    END IF;

    -- Add shop_id to roles table if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'roles' AND column_name = 'shop_id'
    ) THEN
        ALTER TABLE roles ADD COLUMN shop_id UUID;
        RAISE NOTICE 'Added shop_id column to roles table';
    ELSE
        RAISE NOTICE 'shop_id column already exists in roles table';
    END IF;
END $$;

-- Step 2: Copy data from merchant_id to shop_id if merchant_id exists
DO $$
BEGIN
    -- Copy data in users table
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'users' AND column_name = 'merchant_id'
    ) THEN
        UPDATE users SET shop_id = merchant_id WHERE merchant_id IS NOT NULL AND shop_id IS NULL;
        RAISE NOTICE 'Copied merchant_id to shop_id in users table';
    END IF;

    -- Copy data in roles table
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'roles' AND column_name = 'merchant_id'
    ) THEN
        UPDATE roles SET shop_id = merchant_id WHERE merchant_id IS NOT NULL AND shop_id IS NULL;
        RAISE NOTICE 'Copied merchant_id to shop_id in roles table';
    END IF;
END $$;

-- Step 3: Handle any NULL or invalid shop_id values by assigning them to the first available shop
DO $$
DECLARE
    first_shop_id UUID;
    null_users_count INTEGER;
    null_roles_count INTEGER;
    orphaned_users_count INTEGER;
    orphaned_roles_count INTEGER;
BEGIN
    -- Get the first shop ID
    SELECT id INTO first_shop_id FROM shops ORDER BY created_at LIMIT 1;

    IF first_shop_id IS NOT NULL THEN
        -- Count and fix NULL shop_id in users
        SELECT COUNT(*) INTO null_users_count FROM users WHERE shop_id IS NULL;
        IF null_users_count > 0 THEN
            UPDATE users SET shop_id = first_shop_id WHERE shop_id IS NULL;
            RAISE NOTICE 'Fixed % users with NULL shop_id', null_users_count;
        END IF;

        -- Count and fix NULL shop_id in roles
        SELECT COUNT(*) INTO null_roles_count FROM roles WHERE shop_id IS NULL;
        IF null_roles_count > 0 THEN
            UPDATE roles SET shop_id = first_shop_id WHERE shop_id IS NULL;
            RAISE NOTICE 'Fixed % roles with NULL shop_id', null_roles_count;
        END IF;

        -- Count and fix orphaned shop_id in users (shop_id that don't exist in shops table)
        SELECT COUNT(*) INTO orphaned_users_count
        FROM users u
        LEFT JOIN shops s ON u.shop_id = s.id
        WHERE u.shop_id IS NOT NULL AND s.id IS NULL;

        IF orphaned_users_count > 0 THEN
            UPDATE users SET shop_id = first_shop_id
            WHERE shop_id NOT IN (SELECT id FROM shops);
            RAISE NOTICE 'Fixed % users with orphaned shop_id', orphaned_users_count;
        END IF;

        -- Count and fix orphaned shop_id in roles
        SELECT COUNT(*) INTO orphaned_roles_count
        FROM roles r
        LEFT JOIN shops s ON r.shop_id = s.id
        WHERE r.shop_id IS NOT NULL AND s.id IS NULL;

        IF orphaned_roles_count > 0 THEN
            UPDATE roles SET shop_id = first_shop_id
            WHERE shop_id NOT IN (SELECT id FROM shops);
            RAISE NOTICE 'Fixed % roles with orphaned shop_id', orphaned_roles_count;
        END IF;
    ELSE
        RAISE NOTICE 'No shops found in database - cannot fix NULL/orphaned shop_id values';
    END IF;
END $$;

-- Step 4: Make shop_id NOT NULL
ALTER TABLE users ALTER COLUMN shop_id SET NOT NULL;
ALTER TABLE roles ALTER COLUMN shop_id SET NOT NULL;

-- Step 5: Add foreign key constraints
DO $$
BEGIN
    -- Add foreign key for users table
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_users_shop_id' AND table_name = 'users'
    ) THEN
        ALTER TABLE users ADD CONSTRAINT fk_users_shop_id
            FOREIGN KEY (shop_id) REFERENCES shops(id) ON DELETE CASCADE;
        RAISE NOTICE 'Added foreign key constraint fk_users_shop_id';
    END IF;

    -- Add foreign key for roles table
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_roles_shop_id' AND table_name = 'roles'
    ) THEN
        ALTER TABLE roles ADD CONSTRAINT fk_roles_shop_id
            FOREIGN KEY (shop_id) REFERENCES shops(id) ON DELETE CASCADE;
        RAISE NOTICE 'Added foreign key constraint fk_roles_shop_id';
    END IF;
END $$;

-- Step 6: Create indexes
CREATE INDEX IF NOT EXISTS idx_users_shop_id ON users(shop_id);
CREATE INDEX IF NOT EXISTS idx_roles_shop_id ON roles(shop_id);

-- Step 7: Drop old merchant_id columns if they exist (optional - commented for safety)
-- ALTER TABLE users DROP COLUMN IF EXISTS merchant_id;
-- ALTER TABLE roles DROP COLUMN IF EXISTS merchant_id;

COMMIT;

-- Verify the migration
SELECT
    'users' as table_name,
    COUNT(*) as total_records,
    COUNT(shop_id) as records_with_shop_id,
    COUNT(*) - COUNT(shop_id) as records_without_shop_id
FROM users

UNION ALL

SELECT
    'roles' as table_name,
    COUNT(*) as total_records,
    COUNT(shop_id) as records_with_shop_id,
    COUNT(*) - COUNT(shop_id) as records_without_shop_id
FROM roles;
