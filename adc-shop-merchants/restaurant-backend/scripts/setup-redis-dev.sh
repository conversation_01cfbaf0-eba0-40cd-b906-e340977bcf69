#!/bin/bash

# Setup Redis for Local Development
# This script installs and configures Redis for local development

set -e

echo "🚀 Setting up Redis for local development..."

# Detect OS
OS="$(uname -s)"
case "${OS}" in
    Linux*)     MACHINE=Linux;;
    Darwin*)    MACHINE=Mac;;
    CYGWIN*)    MACHINE=Cygwin;;
    MINGW*)     MACHINE=MinGw;;
    *)          MACHINE="UNKNOWN:${OS}"
esac

echo "📱 Detected OS: ${MACHINE}"

# Install Redis based on OS
install_redis() {
    case "${MACHINE}" in
        Mac)
            if command -v brew &> /dev/null; then
                echo "🍺 Installing Redis via Homebrew..."
                brew install redis
            else
                echo "❌ Homebrew not found. Please install Homebrew first:"
                echo "   /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
                exit 1
            fi
            ;;
        Linux)
            if command -v apt-get &> /dev/null; then
                echo "🐧 Installing Redis via apt..."
                sudo apt-get update
                sudo apt-get install -y redis-server
            elif command -v yum &> /dev/null; then
                echo "🐧 Installing Redis via yum..."
                sudo yum install -y redis
            elif command -v dnf &> /dev/null; then
                echo "🐧 Installing Redis via dnf..."
                sudo dnf install -y redis
            else
                echo "❌ Package manager not found. Please install Redis manually."
                exit 1
            fi
            ;;
        *)
            echo "❌ Unsupported OS: ${MACHINE}"
            echo "Please install Redis manually for your system."
            exit 1
            ;;
    esac
}

# Check if Redis is already installed
if command -v redis-server &> /dev/null; then
    echo "✅ Redis is already installed"
    REDIS_VERSION=$(redis-server --version | head -n1)
    echo "   Version: ${REDIS_VERSION}"
else
    echo "📦 Redis not found. Installing..."
    install_redis
fi

# Start Redis service
start_redis() {
    case "${MACHINE}" in
        Mac)
            echo "🚀 Starting Redis service..."
            brew services start redis
            ;;
        Linux)
            echo "🚀 Starting Redis service..."
            if command -v systemctl &> /dev/null; then
                sudo systemctl start redis-server
                sudo systemctl enable redis-server
            else
                sudo service redis-server start
            fi
            ;;
    esac
}

# Check if Redis is running
if redis-cli ping &> /dev/null; then
    echo "✅ Redis is already running"
else
    echo "🔄 Starting Redis..."
    start_redis
    
    # Wait for Redis to start
    echo "⏳ Waiting for Redis to start..."
    for i in {1..10}; do
        if redis-cli ping &> /dev/null; then
            echo "✅ Redis is now running"
            break
        fi
        sleep 1
        if [ $i -eq 10 ]; then
            echo "❌ Redis failed to start"
            exit 1
        fi
    done
fi

# Test Redis connection
echo "🧪 Testing Redis connection..."
REDIS_RESPONSE=$(redis-cli ping)
if [ "$REDIS_RESPONSE" = "PONG" ]; then
    echo "✅ Redis connection test successful"
else
    echo "❌ Redis connection test failed"
    exit 1
fi

# Create development configuration
echo "⚙️  Creating development Redis configuration..."
cat > /tmp/redis-dev.conf << EOF
# Redis Development Configuration
port 6379
bind 127.0.0.1
protected-mode yes
timeout 0
tcp-keepalive 300
daemonize no
supervised no
pidfile /var/run/redis_6379.pid
loglevel notice
databases 16
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir ./
maxmemory 256mb
maxmemory-policy allkeys-lru
EOF

echo "📋 Redis development configuration created at /tmp/redis-dev.conf"

# Show Redis info
echo ""
echo "📊 Redis Information:"
echo "   Host: localhost"
echo "   Port: 6379"
echo "   Password: (none)"
echo "   Database: 0"
echo ""

# Show useful commands
echo "🛠️  Useful Redis Commands:"
echo "   Start Redis:     redis-server"
echo "   Connect to CLI:  redis-cli"
echo "   Stop Redis:      redis-cli shutdown"
echo "   Monitor:         redis-cli monitor"
echo "   Check status:    redis-cli ping"
echo ""

# Test AI queue functionality
echo "🧪 Testing AI queue functionality..."
redis-cli FLUSHDB > /dev/null
redis-cli ZADD ai_generation_jobs 1000 '{"job_id":"test-123","type":"test","priority":1}' > /dev/null
QUEUE_SIZE=$(redis-cli ZCARD ai_generation_jobs)
if [ "$QUEUE_SIZE" = "1" ]; then
    echo "✅ AI queue test successful"
    redis-cli ZREM ai_generation_jobs '{"job_id":"test-123","type":"test","priority":1}' > /dev/null
else
    echo "❌ AI queue test failed"
fi

echo ""
echo "🎉 Redis setup complete! Your AI generation queue is ready for development."
echo ""
echo "💡 Next steps:"
echo "   1. Start your Go backend server"
echo "   2. The AI generation jobs will now use Redis queue"
echo "   3. Monitor queue with: redis-cli ZCARD ai_generation_jobs"
echo ""
