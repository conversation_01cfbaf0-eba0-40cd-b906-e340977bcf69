-- Seed Orders for weerawat-poseeya/posriya branch
-- This script creates sample orders for testing

DO $$
DECLARE
    branch_uuid UUID;
    shop_uuid UUID;
    menu_item_record RECORD;
    table_record RECORD;
    order_uuid UUID;
    order_item_uuid UUID;
    i INTEGER;
    j INTEGER;
    order_number VARCHAR(50);
    customer_names TEXT[] := ARRAY['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];
    customer_phones TEXT[] := ARRAY['+66-81-234-5678', '+66-82-345-6789', '+66-83-456-7890', '+66-84-567-8901', '+66-85-678-9012', '+66-86-789-0123', '+66-87-890-1234', '+66-88-901-2345', '+66-89-012-3456', '+66-80-123-4567'];
    order_types TEXT[] := ARRAY['dine_in', 'takeaway', 'delivery'];
    order_statuses TEXT[] := ARRAY['pending', 'confirmed', 'preparing', 'ready', 'served', 'completed'];
    current_date_val DATE := CURRENT_DATE;
    order_date TIMESTAMP;
    order_type TEXT;
    order_status TEXT;
    customer_name TEXT;
    customer_phone TEXT;
    order_subtotal DECIMAL(10,2);
    order_tax_amount DECIMAL(10,2);
    order_total_amount DECIMAL(10,2);
    item_count INTEGER;
    item_quantity INTEGER;
    item_price DECIMAL(10,2);
    item_total DECIMAL(10,2);
BEGIN
    -- Get the shop and branch IDs for weerawat-poseeya/posriya
    SELECT s.id, sb.id INTO shop_uuid, branch_uuid
    FROM shops s
    JOIN shop_branches sb ON s.id = sb.shop_id
    WHERE s.slug = 'weerawat-poseeya' AND sb.slug = 'posriya'
    LIMIT 1;

    -- Check if we have the required data
    IF branch_uuid IS NULL THEN
        RAISE NOTICE 'weerawat-poseeya/posriya branch not found. Please ensure the shop and branch exist.';
        RETURN;
    END IF;

    -- Check if orders already exist
    IF EXISTS (SELECT 1 FROM orders WHERE branch_id = branch_uuid LIMIT 1) THEN
        RAISE NOTICE 'Orders already exist for this branch. Cleaning up first...';
        -- Clean up existing orders
        DELETE FROM order_items WHERE order_id IN (SELECT id FROM orders WHERE branch_id = branch_uuid);
        DELETE FROM orders WHERE branch_id = branch_uuid;
    END IF;

    RAISE NOTICE 'Seeding orders for branch: % (weerawat-poseeya/posriya)', branch_uuid;

    -- Create orders for the last 7 days
    FOR i IN 0..6 LOOP
        order_date := (current_date_val - i)::DATE + INTERVAL '10 hours' + (random() * INTERVAL '12 hours');

        -- Create 3-8 orders per day
        FOR j IN 1..(3 + (random() * 5)::INTEGER) LOOP
            order_uuid := gen_random_uuid();
            order_number := 'ORD-' || TO_CHAR(order_date, 'YYYYMMDD') || '-' || LPAD(j::TEXT, 3, '0');

            -- Random customer
            customer_name := customer_names[1 + (random() * (array_length(customer_names, 1) - 1))::INTEGER];
            customer_phone := customer_phones[1 + (random() * (array_length(customer_phones, 1) - 1))::INTEGER];

            -- Random order type and status
            order_type := order_types[1 + (random() * (array_length(order_types, 1) - 1))::INTEGER];

            -- Status based on order age
            IF i = 0 THEN -- Today's orders
                order_status := order_statuses[1 + (random() * 3)::INTEGER]; -- pending, confirmed, preparing
            ELSIF i <= 2 THEN -- Recent orders
                order_status := order_statuses[4 + (random() * 2)::INTEGER]; -- ready, served, completed
            ELSE -- Older orders
                order_status := 'completed';
            END IF;

            -- Get a random table for dine-in orders
            SELECT id INTO table_record
            FROM tables
            WHERE branch_id = branch_uuid
            ORDER BY RANDOM()
            LIMIT 1;

            -- Initialize totals
            order_subtotal := 0;

            -- Insert the order
            INSERT INTO orders (
                id, branch_id, order_number, customer_name, customer_phone, customer_email,
                table_id, status, type, subtotal, tax, service_charge, discount, tip, total,
                payment_status, payment_method, notes, estimated_time, created_at, updated_at
            ) VALUES (
                order_uuid, branch_uuid, order_number, customer_name, customer_phone,
                LOWER(REPLACE(customer_name, ' ', '.')) || '@example.com',
                CASE WHEN order_type = 'dine_in' THEN table_record.id ELSE NULL END,
                order_status, order_type, 0, 0, 0, 0, 0, 0,
                CASE WHEN order_status IN ('completed', 'served') THEN 'paid' ELSE 'pending' END,
                'cash', '',
                CASE WHEN order_status IN ('pending', 'confirmed', 'preparing') THEN 15 + (random() * 30)::INTEGER ELSE NULL END,
                order_date, order_date
            );

            -- Add 1-4 items to each order
            item_count := 1 + (random() * 3)::INTEGER;

            FOR k IN 1..item_count LOOP
                -- Get a random menu item
                SELECT id, price INTO menu_item_record
                FROM menu_items
                WHERE branch_id = branch_uuid
                ORDER BY RANDOM()
                LIMIT 1;

                IF menu_item_record.id IS NOT NULL THEN
                    order_item_uuid := gen_random_uuid();
                    item_quantity := 1 + (random() * 2)::INTEGER; -- 1-3 quantity
                    item_price := menu_item_record.price;
                    item_total := item_price * item_quantity;

                    -- Insert order item
                    INSERT INTO order_items (
                        id, order_id, menu_item_id, name, quantity, price, total,
                        notes, modifications, status, created_at, updated_at
                    ) VALUES (
                        order_item_uuid, order_uuid, menu_item_record.id,
                        (SELECT name FROM menu_items WHERE id = menu_item_record.id),
                        item_quantity, item_price, item_total, '', '[]'::jsonb, order_status, order_date, order_date
                    );

                    -- Add to subtotal
                    order_subtotal := order_subtotal + item_total;
                END IF;
            END LOOP;

            -- Calculate tax and total
            order_tax_amount := order_subtotal * 0.07; -- 7% tax
            order_total_amount := order_subtotal + order_tax_amount;

            -- Update order totals
            UPDATE orders
            SET subtotal = order_subtotal, tax = order_tax_amount, total = order_total_amount
            WHERE id = order_uuid;

        END LOOP;
    END LOOP;

    RAISE NOTICE 'Orders seeded successfully! Created orders for the last 7 days.';
END $$;
