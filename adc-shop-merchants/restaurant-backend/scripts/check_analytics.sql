-- Analytics Data Verification Script
-- This script checks if analytics data exists and provides a summary

\echo '=== Analytics Data Verification ==='
\echo ''

-- Get branch information
\echo 'Branch Information:'
SELECT 
    s.name as shop_name,
    s.slug as shop_slug,
    sb.name as branch_name,
    sb.slug as branch_slug,
    sb.id as branch_id
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.slug = 'thai-delight' AND sb.slug = 'downtown';

\echo ''

-- Check if analytics data exists
\echo 'Analytics Data Summary:'

-- Sales metrics
SELECT 
    'Sales Metrics' as metric_type,
    COUNT(*) as total_records,
    COUNT(DISTINCT date) as days_covered,
    MIN(date) as earliest_date,
    MAX(date) as latest_date
FROM sales_metrics sm
JOIN shop_branches sb ON sm.branch_id = sb.id
JOIN shops s ON sb.shop_id = s.id
WHERE s.slug = 'thai-delight' AND sb.slug = 'downtown'

UNION ALL

-- Menu item metrics
SELECT 
    'Menu Item Metrics' as metric_type,
    COUNT(*) as total_records,
    COUNT(DISTINCT date) as days_covered,
    MIN(date) as earliest_date,
    MAX(date) as latest_date
FROM menu_item_metrics mim
JOIN shop_branches sb ON mim.branch_id = sb.id
JOIN shops s ON sb.shop_id = s.id
WHERE s.slug = 'thai-delight' AND sb.slug = 'downtown'

UNION ALL

-- Customer metrics
SELECT 
    'Customer Metrics' as metric_type,
    COUNT(*) as total_records,
    COUNT(DISTINCT date) as days_covered,
    MIN(date) as earliest_date,
    MAX(date) as latest_date
FROM customer_metrics cm
JOIN shop_branches sb ON cm.branch_id = sb.id
JOIN shops s ON sb.shop_id = s.id
WHERE s.slug = 'thai-delight' AND sb.slug = 'downtown'

UNION ALL

-- Staff metrics
SELECT 
    'Staff Metrics' as metric_type,
    COUNT(*) as total_records,
    COUNT(DISTINCT date) as days_covered,
    MIN(date) as earliest_date,
    MAX(date) as latest_date
FROM staff_metrics stm
JOIN shop_branches sb ON stm.branch_id = sb.id
JOIN shops s ON sb.shop_id = s.id
WHERE s.slug = 'thai-delight' AND sb.slug = 'downtown'

UNION ALL

-- Table metrics
SELECT 
    'Table Metrics' as metric_type,
    COUNT(*) as total_records,
    COUNT(DISTINCT date) as days_covered,
    MIN(date) as earliest_date,
    MAX(date) as latest_date
FROM table_metrics tm
JOIN shop_branches sb ON tm.branch_id = sb.id
JOIN shops s ON sb.shop_id = s.id
WHERE s.slug = 'thai-delight' AND sb.slug = 'downtown';

\echo ''

-- Analytics reports
\echo 'Analytics Reports:'
SELECT 
    report_type,
    title,
    status,
    start_date,
    end_date,
    created_at
FROM analytics_reports ar
JOIN shop_branches sb ON ar.branch_id = sb.id
JOIN shops s ON sb.shop_id = s.id
WHERE s.slug = 'thai-delight' AND sb.slug = 'downtown'
ORDER BY created_at DESC;

\echo ''

-- Sales metrics breakdown by type
\echo 'Sales Metrics Breakdown:'
SELECT 
    metric_type,
    COUNT(*) as record_count,
    ROUND(AVG(value), 2) as avg_value,
    ROUND(SUM(value), 2) as total_value
FROM sales_metrics sm
JOIN shop_branches sb ON sm.branch_id = sb.id
JOIN shops s ON sb.shop_id = s.id
WHERE s.slug = 'thai-delight' AND sb.slug = 'downtown'
GROUP BY metric_type
ORDER BY metric_type;

\echo ''

-- Top performing menu items
\echo 'Top 5 Menu Items by Revenue (Last 30 Days):'
SELECT 
    mi.name,
    SUM(mim.quantity_sold) as total_quantity,
    SUM(mim.order_count) as total_orders,
    ROUND(SUM(mim.revenue), 2) as total_revenue
FROM menu_item_metrics mim
JOIN menu_items mi ON mim.menu_item_id = mi.id
JOIN shop_branches sb ON mim.branch_id = sb.id
JOIN shops s ON sb.shop_id = s.id
WHERE s.slug = 'thai-delight' AND sb.slug = 'downtown'
GROUP BY mi.id, mi.name
ORDER BY total_revenue DESC
LIMIT 5;

\echo ''

-- Staff performance summary
\echo 'Staff Performance Summary:'
SELECT 
    u.first_name || ' ' || u.last_name as staff_name,
    u.position,
    COUNT(*) as days_worked,
    ROUND(AVG(stm.orders_processed), 1) as avg_orders_per_day,
    ROUND(AVG(stm.efficiency), 2) as avg_efficiency,
    ROUND(AVG(stm.customer_rating), 2) as avg_rating
FROM staff_metrics stm
JOIN users u ON stm.user_id = u.id
JOIN shop_branches sb ON stm.branch_id = sb.id
JOIN shops s ON sb.shop_id = s.id
WHERE s.slug = 'thai-delight' AND sb.slug = 'downtown'
GROUP BY u.id, u.first_name, u.last_name, u.position
ORDER BY avg_efficiency DESC;

\echo ''

-- Table utilization summary
\echo 'Table Utilization Summary:'
SELECT 
    t.name as table_name,
    t.capacity,
    COUNT(*) as total_hours_tracked,
    ROUND(AVG(tm.utilization_rate), 1) as avg_utilization_rate,
    SUM(tm.turnover_count) as total_turnovers,
    ROUND(SUM(tm.revenue), 2) as total_revenue
FROM table_metrics tm
JOIN tables t ON tm.table_id = t.id
JOIN shop_branches sb ON tm.branch_id = sb.id
JOIN shops s ON sb.shop_id = s.id
WHERE s.slug = 'thai-delight' AND sb.slug = 'downtown'
GROUP BY t.id, t.name, t.capacity
ORDER BY total_revenue DESC;

\echo ''
\echo '=== Verification Complete ==='
