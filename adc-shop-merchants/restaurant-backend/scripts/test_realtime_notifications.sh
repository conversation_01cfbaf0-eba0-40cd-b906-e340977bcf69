#!/bin/bash

echo "🚀 Testing Real-time Notification System"
echo "========================================"

# Function to create notification via database
create_db_notification() {
    local title="$1"
    local message="$2"
    local type="$3"
    local priority="$4"
    
    echo "📝 Creating notification: $title"
    
    psql "postgresql://postgres.sqzzpwirpwdlxzuvztey:<EMAIL>:5432/postgres" -c "
    INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
    SELECT 
        s.id as shop_id,
        sb.id as branch_id,
        '$title' as title,
        '$message Created at: ' || NOW()::TEXT as message,
        '$type' as type,
        '$priority' as priority,
        false as is_read,
        '/app/restaurant/' || s.slug || '/' || sb.slug || '/dashboard' as link,
        'View Details' as action_label,
        JSON_BUILD_OBJECT(
            'test', true,
            'created_via', 'test_script',
            'timestamp', NOW()::TEXT,
            'notification_type', '$type'
        ) as data,
        NOW() as timestamp
    FROM shops s
    JOIN shop_branches sb ON s.id = sb.shop_id
    WHERE s.slug = 'weerawat-poseeya' AND sb.slug = 'the-green-terrace';
    " > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "✅ Database notification created successfully"
    else
        echo "❌ Failed to create database notification"
    fi
}

# Function to send WebSocket notification
send_websocket_notification() {
    local title="$1"
    local message="$2"
    local type="$3"
    local priority="$4"
    
    echo "📡 Sending WebSocket notification: $title"
    
    curl -s -X POST "http://localhost:8200/api/v1/websocket/broadcast" \
      -H "Content-Type: application/json" \
      -d "{
        \"type\": \"notification\",
        \"data\": {
          \"title\": \"$title\",
          \"message\": \"$message\",
          \"type\": \"$type\",
          \"priority\": \"$priority\",
          \"timestamp\": \"$(date -Iseconds)\",
          \"test\": true
        },
        \"channel\": \"notifications\"
      }" > /dev/null
    
    if [ $? -eq 0 ]; then
        echo "✅ WebSocket notification sent successfully"
    else
        echo "❌ Failed to send WebSocket notification"
    fi
}

# Test 1: Create urgent notification
echo -e "\n🔥 Test 1: Creating URGENT notification"
create_db_notification "🚨 URGENT: Kitchen Fire Alert!" "Emergency situation in the kitchen. Please evacuate immediately and call emergency services." "system" "urgent"
send_websocket_notification "🚨 URGENT: Kitchen Fire Alert!" "Emergency situation detected via WebSocket" "system" "urgent"

sleep 2

# Test 2: Create new order notification
echo -e "\n📋 Test 2: Creating NEW ORDER notification"
create_db_notification "🍜 New Order #ORD-LIVE-$(date +%s)" "Live order just placed! Customer: Real-time Tester. Items: Pad Thai, Green Curry." "order" "high"
send_websocket_notification "🍜 Live Order Alert!" "New order received via WebSocket" "order" "high"

sleep 2

# Test 3: Create reservation notification
echo -e "\n🍽️ Test 3: Creating RESERVATION notification"
create_db_notification "📅 Urgent Reservation Request" "VIP customer requesting immediate table for 8 people. Special occasion: Anniversary dinner." "reservation" "medium"
send_websocket_notification "📅 VIP Reservation Alert!" "Urgent reservation request via WebSocket" "reservation" "medium"

sleep 2

# Test 4: Create review notification
echo -e "\n⭐ Test 4: Creating REVIEW notification"
create_db_notification "🌟 Amazing 5-Star Review!" "Customer just left: 'Best Thai food ever! The service was exceptional and the atmosphere perfect!'" "review" "low"
send_websocket_notification "🌟 New 5-Star Review!" "Excellent customer feedback received" "review" "low"

# Check final notification count
echo -e "\n📊 Final notification count:"
psql "postgresql://postgres.sqzzpwirpwdlxzuvztey:<EMAIL>:5432/postgres" -c "
SELECT 
    COUNT(*) as total_notifications, 
    COUNT(CASE WHEN is_read = false THEN 1 END) as unread_notifications
FROM notifications 
WHERE shop_id IN (SELECT id FROM shops WHERE slug = 'weerawat-poseeya');
"

echo -e "\n🎉 Real-time notification test completed!"
echo "👀 Check your notification bell in the browser to see the new notifications!"
echo "🔔 The unread count should have increased by 4"
echo "📱 If WebSocket is connected, you should see real-time updates"
