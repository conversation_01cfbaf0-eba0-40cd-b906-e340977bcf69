-- Custom Analytics Seed Data Script for specific shop
-- This script seeds analytics data for the weerawat-poseeya shop

DO $$
DECLARE
    branch_uuid UUID;
    shop_uuid UUID;
    admin_user_uuid UUID;
    current_date_val DATE := CURRENT_DATE;
    i INTEGER;
    j INTEGER;
    hour_val INTEGER;
    base_orders INTEGER;
    base_revenue DECIMAL(10,2);
    hourly_orders INTEGER;
    hourly_revenue DECIMAL(10,2);
    customer_count INTEGER;
    multiplier DECIMAL(3,2);
    day_date DATE;
    popularity_factor DECIMAL(3,2);
    orders_count INTEGER;
    quantity_sold INTEGER;
    revenue_val DECIMAL(10,2);
    new_customers INTEGER;
    returning_customers INTEGER;
    avg_spend DECIMAL(5,2);
    menu_item_record RECORD;
    staff_record RECORD;
    table_record RECORD;
    orders_processed INTEGER;
    staff_revenue DECIMAL(10,2);
    hours_worked DECIMAL(4,2);
    avg_order_time DECIMAL(5,2);
    customer_rating DECIMAL(3,2);
    efficiency DECIMAL(5,2);
    occupied_minutes INTEGER;
    turnover_count INTEGER;
    table_revenue DECIMAL(8,2);
    utilization_rate DECIMAL(5,2);
    avg_party_size DECIMAL(3,2);
    avg_dining_time DECIMAL(5,2);
    base_utilization DECIMAL(3,2);
    utilization DECIMAL(3,2);
BEGIN
    -- Get the shop and branch IDs for weerawat-poseeya/posriya
    SELECT s.id, sb.id INTO shop_uuid, branch_uuid
    FROM shops s
    JOIN shop_branches sb ON s.id = sb.shop_id
    WHERE s.slug = 'weerawat-poseeya' AND sb.slug = 'posriya'
    LIMIT 1;

    -- Get an admin user for report generation
    SELECT id INTO admin_user_uuid
    FROM users
    WHERE branch_id = branch_uuid AND position LIKE '%Manager%'
    LIMIT 1;

    -- If no manager found, get any user from this branch
    IF admin_user_uuid IS NULL THEN
        SELECT id INTO admin_user_uuid
        FROM users
        WHERE branch_id = branch_uuid
        LIMIT 1;
    END IF;

    -- Check if we have the required data
    IF branch_uuid IS NULL THEN
        RAISE NOTICE 'weerawat-poseeya/posriya branch not found. Please ensure the shop and branch exist.';
        RETURN;
    END IF;

    -- Check if analytics data already exists
    IF EXISTS (SELECT 1 FROM sales_metrics WHERE branch_id = branch_uuid LIMIT 1) THEN
        RAISE NOTICE 'Analytics data already exists for this branch. Cleaning up first...';
        -- Clean up existing data
        DELETE FROM analytics_reports WHERE branch_id = branch_uuid;
        DELETE FROM table_metrics WHERE branch_id = branch_uuid;
        DELETE FROM staff_metrics WHERE branch_id = branch_uuid;
        DELETE FROM customer_metrics WHERE branch_id = branch_uuid;
        DELETE FROM menu_item_metrics WHERE branch_id = branch_uuid;
        DELETE FROM sales_metrics WHERE branch_id = branch_uuid;
    END IF;

    RAISE NOTICE 'Seeding analytics data for branch: % (weerawat-poseeya/posriya)', branch_uuid;

    -- Seed Sales Metrics for the last 30 days
    FOR i IN 0..29 LOOP
        day_date := current_date_val - i;
        
        -- Generate different patterns for different days
        base_orders := 25 + (random() * 50)::INTEGER; -- 25-75 orders per day
        base_revenue := base_orders * (60 + random() * 120); -- $60-180 per order average
        
        -- Weekend boost
        IF EXTRACT(DOW FROM day_date) IN (0, 6) THEN -- Sunday = 0, Saturday = 6
            base_orders := (base_orders * 1.4)::INTEGER;
            base_revenue := base_revenue * 1.4;
        END IF;
        
        -- Create hourly metrics for business hours (10 AM to 10 PM)
        FOR hour_val IN 10..22 LOOP
            -- Peak hours: 12-2 PM and 6-8 PM
            multiplier := 1.0;
            IF (hour_val >= 12 AND hour_val <= 14) OR (hour_val >= 18 AND hour_val <= 20) THEN
                multiplier := 2.2;
            ELSIF hour_val < 11 OR hour_val > 21 THEN
                multiplier := 0.4;
            END IF;
            
            hourly_orders := (base_orders * multiplier / 12)::INTEGER; -- Distribute across 12 hours
            hourly_revenue := base_revenue * multiplier / 12;
            customer_count := (hourly_orders * 0.85)::INTEGER; -- Slightly less than orders
            
            -- Insert sales metrics
            INSERT INTO sales_metrics (id, branch_id, date, hour, metric_type, value, count, metadata, created_at, updated_at)
            VALUES 
                (gen_random_uuid(), branch_uuid, day_date, hour_val, 'revenue', hourly_revenue, 1, '{}', NOW(), NOW()),
                (gen_random_uuid(), branch_uuid, day_date, hour_val, 'order_count', hourly_orders, 1, '{}', NOW(), NOW()),
                (gen_random_uuid(), branch_uuid, day_date, hour_val, 'customer_count', customer_count, 1, '{}', NOW(), NOW());
        END LOOP;
    END LOOP;

    RAISE NOTICE 'Sales metrics seeded successfully';

    -- Seed Menu Item Metrics
    FOR menu_item_record IN 
        SELECT id, price FROM menu_items WHERE branch_id = branch_uuid
    LOOP
        FOR i IN 0..29 LOOP
            day_date := current_date_val - i;
            
            -- Make some items more popular than others (random popularity factor)
            popularity_factor := 0.6 + random() * 1.8; -- 0.6 to 2.4
            
            orders_count := (3 + random() * 12 * popularity_factor)::INTEGER; -- 3-15 orders per item per day
            quantity_sold := orders_count + (random() * orders_count * 0.5)::INTEGER; -- Sometimes multiple quantities per order
            revenue_val := quantity_sold * menu_item_record.price;
            
            INSERT INTO menu_item_metrics (id, branch_id, menu_item_id, date, order_count, quantity_sold, revenue, metadata, created_at, updated_at)
            VALUES (gen_random_uuid(), branch_uuid, menu_item_record.id, day_date, orders_count, quantity_sold, revenue_val, '{}', NOW(), NOW());
        END LOOP;
    END LOOP;

    RAISE NOTICE 'Menu item metrics seeded successfully';

    -- Seed Customer Metrics
    FOR i IN 0..29 LOOP
        day_date := current_date_val - i;
        
        new_customers := 8 + (random() * 20)::INTEGER; -- 8-28 new customers per day
        returning_customers := 15 + (random() * 35)::INTEGER; -- 15-50 returning customers per day
        avg_spend := 85.0 + random() * 60; -- $85-145 average spend
        
        -- New customers metric
        INSERT INTO customer_metrics (id, branch_id, date, visit_count, order_count, total_spent, avg_order_value, last_visit, customer_type, metadata, created_at, updated_at)
        VALUES (gen_random_uuid(), branch_uuid, day_date, new_customers, new_customers, new_customers * avg_spend, avg_spend, day_date + INTERVAL '12 hours', 'new', '{}', NOW(), NOW());
        
        -- Returning customers metric
        INSERT INTO customer_metrics (id, branch_id, date, visit_count, order_count, total_spent, avg_order_value, last_visit, customer_type, metadata, created_at, updated_at)
        VALUES (gen_random_uuid(), branch_uuid, day_date, returning_customers, returning_customers + (random() * returning_customers / 2)::INTEGER, returning_customers * avg_spend * 1.3, avg_spend * 1.3, day_date + INTERVAL '12 hours', 'returning', '{}', NOW(), NOW());
    END LOOP;

    RAISE NOTICE 'Customer metrics seeded successfully';

    -- Seed Staff Metrics
    FOR staff_record IN 
        SELECT id FROM users WHERE branch_id = branch_uuid
    LOOP
        FOR i IN 0..29 LOOP
            day_date := current_date_val - i;
            
            orders_processed := 8 + (random() * 25)::INTEGER; -- 8-33 orders per day
            staff_revenue := orders_processed * (40 + random() * 90); -- $40-130 per order average
            hours_worked := 6.5 + random() * 4.5; -- 6.5-11 hours per day
            avg_order_time := 8.0 + random() * 22.0; -- 8-30 minutes per order
            customer_rating := 3.8 + random() * 1.2; -- 3.8-5.0 rating
            efficiency := orders_processed / hours_worked; -- orders per hour
            
            -- Weekend patterns
            IF EXTRACT(DOW FROM day_date) IN (0, 6) THEN
                orders_processed := (orders_processed * 1.5)::INTEGER;
                staff_revenue := staff_revenue * 1.5;
                hours_worked := hours_worked * 1.3;
            END IF;
            
            INSERT INTO staff_metrics (id, branch_id, user_id, date, orders_processed, revenue, hours_worked, avg_order_time, customer_rating, efficiency, metadata, created_at, updated_at)
            VALUES (gen_random_uuid(), branch_uuid, staff_record.id, day_date, orders_processed, staff_revenue, hours_worked, avg_order_time, customer_rating, efficiency, '{}', NOW(), NOW());
        END LOOP;
    END LOOP;

    RAISE NOTICE 'Staff metrics seeded successfully';

    -- Seed Table Metrics
    FOR table_record IN 
        SELECT id, capacity FROM tables WHERE branch_id = branch_uuid
    LOOP
        FOR i IN 0..29 LOOP
            day_date := current_date_val - i;
            
            -- Create hourly metrics for business hours (10 AM to 10 PM)
            FOR hour_val IN 10..22 LOOP
                -- Peak hours: 12-2 PM and 6-8 PM
                multiplier := 1.0;
                IF (hour_val >= 12 AND hour_val <= 14) OR (hour_val >= 18 AND hour_val <= 20) THEN
                    multiplier := 2.2;
                ELSIF hour_val < 11 OR hour_val > 21 THEN
                    multiplier := 0.4;
                END IF;
                
                base_utilization := 0.35 + random() * 0.45; -- 35-80% base utilization
                utilization := base_utilization * multiplier;
                IF utilization > 1.0 THEN
                    utilization := 1.0;
                END IF;
                
                occupied_minutes := (60 * utilization)::INTEGER;
                turnover_count := (random() * 4)::INTEGER; -- 0-3 turnovers per hour
                table_revenue := turnover_count * (25 + random() * 95); -- $25-120 per turnover
                utilization_rate := utilization * 100;
                avg_party_size := 1.2 + random() * (table_record.capacity - 1.2);
                avg_dining_time := 50.0 + random() * 80.0; -- 50-130 minutes
                
                -- Weekend boost
                IF EXTRACT(DOW FROM day_date) IN (0, 6) THEN
                    occupied_minutes := (occupied_minutes * 1.4)::INTEGER;
                    turnover_count := (turnover_count * 1.4)::INTEGER;
                    table_revenue := table_revenue * 1.4;
                    utilization_rate := utilization_rate * 1.4;
                    IF utilization_rate > 100 THEN
                        utilization_rate := 100;
                    END IF;
                END IF;
                
                INSERT INTO table_metrics (id, branch_id, table_id, date, hour, occupied_minutes, turnover_count, revenue, utilization_rate, avg_party_size, avg_dining_time, metadata, created_at, updated_at)
                VALUES (gen_random_uuid(), branch_uuid, table_record.id, day_date, hour_val, occupied_minutes, turnover_count, table_revenue, utilization_rate, avg_party_size, avg_dining_time, '{}', NOW(), NOW());
            END LOOP;
        END LOOP;
    END LOOP;

    RAISE NOTICE 'Table metrics seeded successfully';

    -- Seed Analytics Reports (only if we have an admin user)
    IF admin_user_uuid IS NOT NULL THEN
        INSERT INTO analytics_reports (id, branch_id, report_type, title, description, start_date, end_date, data, status, generated_by, metadata, created_at, updated_at)
        VALUES 
            (gen_random_uuid(), branch_uuid, 'sales', 'Weekly Sales Report', 'Comprehensive sales analysis for the past week', current_date_val - 7, current_date_val, '{"totalRevenue": 18750.75, "totalOrders": 342, "avgOrderValue": 54.83, "topSellingItem": "Signature Dish", "peakHour": "19:00", "customerCount": 298, "returnCustomers": 112}', 'completed', admin_user_uuid, '{}', NOW(), NOW()),
            (gen_random_uuid(), branch_uuid, 'menu', 'Menu Performance Analysis', 'Analysis of menu item performance and popularity', current_date_val - 30, current_date_val, '{"totalItems": 28, "topPerformers": ["Signature Dish", "Popular Appetizer", "Chef Special"], "lowPerformers": ["Seasonal Item", "Limited Edition"], "avgRating": 4.4, "profitMargin": 71.2, "inventoryTurns": 14.1}', 'completed', admin_user_uuid, '{}', NOW(), NOW()),
            (gen_random_uuid(), branch_uuid, 'staff', 'Staff Performance Report', 'Monthly staff performance and productivity analysis', current_date_val - 30, current_date_val, '{"totalStaff": 10, "avgEfficiency": 13.8, "topPerformer": "Best Employee", "avgCustomerRating": 4.3, "totalHours": 1520, "avgOrderTime": 16.2}', 'completed', admin_user_uuid, '{}', NOW(), NOW()),
            (gen_random_uuid(), branch_uuid, 'customer', 'Customer Analytics Report', 'Customer behavior and satisfaction analysis', current_date_val - 30, current_date_val, '{"totalCustomers": 1420, "newCustomers": 385, "returningCustomers": 1035, "avgVisitFrequency": 2.6, "customerSatisfaction": 4.5, "churnRate": 10.8}', 'completed', admin_user_uuid, '{}', NOW(), NOW()),
            (gen_random_uuid(), branch_uuid, 'financial', 'Monthly Financial Summary', 'Complete financial overview for the month', current_date_val - 30, current_date_val, '{"grossRevenue": 52840.25, "netRevenue": 45123.82, "totalCosts": 7716.43, "profitMargin": 85.4, "taxAmount": 4224.02, "avgDailySales": 1761.34}', 'completed', admin_user_uuid, '{}', NOW(), NOW());

        RAISE NOTICE 'Analytics reports seeded successfully';
    ELSE
        RAISE NOTICE 'No admin user found, skipping analytics reports';
    END IF;

    RAISE NOTICE 'Analytics seeding completed successfully for weerawat-poseeya/posriya!';
END $$;
