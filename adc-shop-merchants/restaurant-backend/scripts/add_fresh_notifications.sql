-- Add fresh notifications for existing shops and branches
-- This script creates new notifications to show on the notification bell

-- Clear existing notifications first (optional - comment out if you want to keep old ones)
-- DELETE FROM notifications;

-- Add notifications for weerawat-poseeya / the-green-terrace (the main shop being used)
INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'New Order #ORD-2024-' || LPAD((RANDOM() * 1000)::INTEGER::TEXT, 3, '0') as title,
    'A new order has been placed and is waiting for confirmation. Customer: ' || 
    (ARRAY['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'])[FLOOR(RANDOM() * 5 + 1)] || 
    '. Total: $' || (RANDOM() * 50 + 20)::DECIMAL(10,2) as message,
    'order' as type,
    'high' as priority,
    false as is_read,
    '/app/restaurant/' || s.slug || '/' || sb.slug || '/orders' as link,
    'View Order' as action_label,
    JSON_BUILD_OBJECT(
        'order_id', 'ORD-2024-' || LPAD((RANDOM() * 1000)::INTEGER::TEXT, 3, '0'),
        'customer_name', (ARRAY['John Smith', 'Sarah Johnson', '<PERSON> <PERSON>', 'Emma Davis', 'David Brown'])[FLOOR(RANDOM() * 5 + 1)],
        'amount', (RANDOM() * 50 + 20)::DECIMAL(10,2),
        'items_count', FLOOR(RANDOM() * 5 + 1)
    ) as data,
    NOW() - INTERVAL '2 minutes' as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.slug = 'weerawat-poseeya' AND sb.slug = 'the-green-terrace';

INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'Table Reservation Request' as title,
    'New reservation request for ' || (FLOOR(RANDOM() * 6 + 2))::TEXT || ' people at ' || 
    (ARRAY['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM', '8:30 PM'])[FLOOR(RANDOM() * 6 + 1)] || 
    ' tonight. Customer: ' || (ARRAY['Alice Cooper', 'Bob Martin', 'Carol White', 'Daniel Lee', 'Eva Green'])[FLOOR(RANDOM() * 5 + 1)] as message,
    'reservation' as type,
    'medium' as priority,
    false as is_read,
    '/app/restaurant/' || s.slug || '/' || sb.slug || '/reservations' as link,
    'View Reservation' as action_label,
    JSON_BUILD_OBJECT(
        'reservation_id', 'RES-2024-' || LPAD((RANDOM() * 1000)::INTEGER::TEXT, 3, '0'),
        'party_size', FLOOR(RANDOM() * 6 + 2),
        'time', (ARRAY['18:00', '18:30', '19:00', '19:30', '20:00', '20:30'])[FLOOR(RANDOM() * 6 + 1)],
        'customer', (ARRAY['Alice Cooper', 'Bob Martin', 'Carol White', 'Daniel Lee', 'Eva Green'])[FLOOR(RANDOM() * 5 + 1)]
    ) as data,
    NOW() - INTERVAL '5 minutes' as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.slug = 'weerawat-poseeya' AND sb.slug = 'the-green-terrace';

INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'Low Inventory Alert' as title,
    (ARRAY['Chicken breast', 'Fresh basil', 'Coconut milk', 'Rice noodles', 'Fish sauce'])[FLOOR(RANDOM() * 5 + 1)] || 
    ' is running low. Only ' || (FLOOR(RANDOM() * 8 + 2))::TEXT || ' units remaining. Please restock soon.' as message,
    'inventory' as type,
    'urgent' as priority,
    false as is_read,
    '/app/restaurant/' || s.slug || '/' || sb.slug || '/inventory' as link,
    'Restock Now' as action_label,
    JSON_BUILD_OBJECT(
        'item', (ARRAY['chicken-breast', 'fresh-basil', 'coconut-milk', 'rice-noodles', 'fish-sauce'])[FLOOR(RANDOM() * 5 + 1)],
        'remaining', FLOOR(RANDOM() * 8 + 2),
        'threshold', 10,
        'supplier', 'Fresh Ingredients Co.'
    ) as data,
    NOW() - INTERVAL '10 minutes' as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.slug = 'weerawat-poseeya' AND sb.slug = 'the-green-terrace';

INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'New Customer Review' as title,
    'A customer left a ' || (FLOOR(RANDOM() * 2 + 4))::TEXT || '-star review: "' || 
    (ARRAY['Amazing food and great service!', 'Delicious Thai cuisine, will come back!', 'Best Pad Thai in town!', 'Excellent atmosphere and friendly staff', 'Authentic flavors, highly recommended!'])[FLOOR(RANDOM() * 5 + 1)] || '"' as message,
    'review' as type,
    'low' as priority,
    true as is_read,
    '/app/restaurant/' || s.slug || '/' || sb.slug || '/reviews' as link,
    'View Review' as action_label,
    JSON_BUILD_OBJECT(
        'rating', FLOOR(RANDOM() * 2 + 4),
        'customer', (ARRAY['Happy Customer', 'Food Lover', 'Regular Diner', 'Thai Food Fan', 'Local Foodie'])[FLOOR(RANDOM() * 5 + 1)],
        'review_id', 'REV-' || LPAD((RANDOM() * 1000)::INTEGER::TEXT, 3, '0')
    ) as data,
    NOW() - INTERVAL '15 minutes' as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.slug = 'weerawat-poseeya' AND sb.slug = 'the-green-terrace';

INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'Staff Schedule Update' as title,
    (ARRAY['Maria Garcia', 'James Wilson', 'Lisa Chen', 'Tom Anderson', 'Anna Rodriguez'])[FLOOR(RANDOM() * 5 + 1)] || 
    ' has requested a shift change for tomorrow. Please review and approve the schedule modification.' as message,
    'staff' as type,
    'medium' as priority,
    false as is_read,
    '/app/restaurant/' || s.slug || '/' || sb.slug || '/staff' as link,
    'Review Schedule' as action_label,
    JSON_BUILD_OBJECT(
        'staff_member', (ARRAY['Maria Garcia', 'James Wilson', 'Lisa Chen', 'Tom Anderson', 'Anna Rodriguez'])[FLOOR(RANDOM() * 5 + 1)],
        'requested_date', (CURRENT_DATE + INTERVAL '1 day')::TEXT,
        'shift_type', (ARRAY['morning', 'afternoon', 'evening'])[FLOOR(RANDOM() * 3 + 1)],
        'reason', 'Personal appointment'
    ) as data,
    NOW() - INTERVAL '25 minutes' as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.slug = 'weerawat-poseeya' AND sb.slug = 'the-green-terrace';

INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'Payment Received' as title,
    'Payment of $' || (RANDOM() * 100 + 50)::DECIMAL(10,2) || ' has been successfully processed for order #ORD-2024-' || 
    LPAD((RANDOM() * 1000)::INTEGER::TEXT, 3, '0') || '. Transaction completed via ' || 
    (ARRAY['Credit Card', 'PayPal', 'Bank Transfer', 'Cash'])[FLOOR(RANDOM() * 4 + 1)] as message,
    'payment' as type,
    'low' as priority,
    true as is_read,
    '/app/restaurant/' || s.slug || '/' || sb.slug || '/payments' as link,
    'View Transaction' as action_label,
    JSON_BUILD_OBJECT(
        'amount', (RANDOM() * 100 + 50)::DECIMAL(10,2),
        'order_id', 'ORD-2024-' || LPAD((RANDOM() * 1000)::INTEGER::TEXT, 3, '0'),
        'payment_method', (ARRAY['credit_card', 'paypal', 'bank_transfer', 'cash'])[FLOOR(RANDOM() * 4 + 1)],
        'transaction_id', 'TXN-' || LPAD((RANDOM() * 10000)::INTEGER::TEXT, 4, '0')
    ) as data,
    NOW() - INTERVAL '35 minutes' as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.slug = 'weerawat-poseeya' AND sb.slug = 'the-green-terrace';

INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'Promotion Update' as title,
    'Your "' || (ARRAY['Weekend Special', 'Happy Hour', 'Lunch Deal', 'Student Discount', 'Family Package'])[FLOOR(RANDOM() * 5 + 1)] || 
    '" promotion is performing well! ' || (FLOOR(RANDOM() * 20 + 10))::TEXT || ' customers have used it today.' as message,
    'promotion' as type,
    'low' as priority,
    false as is_read,
    '/app/restaurant/' || s.slug || '/' || sb.slug || '/promotions' as link,
    'View Details' as action_label,
    JSON_BUILD_OBJECT(
        'promotion_name', (ARRAY['Weekend Special', 'Happy Hour', 'Lunch Deal', 'Student Discount', 'Family Package'])[FLOOR(RANDOM() * 5 + 1)],
        'usage_count', FLOOR(RANDOM() * 20 + 10),
        'discount_percentage', FLOOR(RANDOM() * 20 + 10),
        'end_date', (CURRENT_DATE + INTERVAL '7 days')::TEXT
    ) as data,
    NOW() - INTERVAL '45 minutes' as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.slug = 'weerawat-poseeya' AND sb.slug = 'the-green-terrace';

INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'System Update Complete' as title,
    'The POS system has been successfully updated to version 2.1.4. New features include improved order tracking and enhanced reporting capabilities.' as message,
    'system' as type,
    'medium' as priority,
    true as is_read,
    '/app/restaurant/' || s.slug || '/' || sb.slug || '/settings' as link,
    'View Changes' as action_label,
    JSON_BUILD_OBJECT(
        'version', '2.1.4',
        'features', ARRAY['improved order tracking', 'enhanced reporting', 'bug fixes'],
        'update_time', NOW()::TEXT
    ) as data,
    NOW() - INTERVAL '1 hour' as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.slug = 'weerawat-poseeya' AND sb.slug = 'the-green-terrace';

-- Add a few more recent notifications for immediate visibility
INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'URGENT: Order Ready' as title,
    'Order #ORD-2024-' || LPAD((RANDOM() * 1000)::INTEGER::TEXT, 3, '0') || ' is ready for pickup. Customer is waiting at the counter.' as message,
    'order' as type,
    'urgent' as priority,
    false as is_read,
    '/app/restaurant/' || s.slug || '/' || sb.slug || '/orders' as link,
    'Mark as Collected' as action_label,
    JSON_BUILD_OBJECT(
        'order_id', 'ORD-2024-' || LPAD((RANDOM() * 1000)::INTEGER::TEXT, 3, '0'),
        'customer', 'Walk-in Customer',
        'wait_time', '15 minutes'
    ) as data,
    NOW() - INTERVAL '30 seconds' as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.slug = 'weerawat-poseeya' AND sb.slug = 'the-green-terrace';
