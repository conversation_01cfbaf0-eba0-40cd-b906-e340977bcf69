-- Restaurant Management Database Initialization Script

-- Create database if it doesn't exist
-- Note: This script assumes the database is already created by <PERSON><PERSON>

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types for better data integrity
DO $$ BEGIN
    CREATE TYPE order_status AS ENUM (
        'pending', 'confirmed', 'preparing', 'ready', 'served', 'completed', 'cancelled'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE order_type AS ENUM (
        'dine-in', 'takeaway', 'delivery'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE payment_status AS ENUM (
        'pending', 'paid', 'failed', 'refunded'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE reservation_status AS ENUM (
        'pending', 'confirmed', 'seated', 'completed', 'cancelled', 'no-show'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE table_status AS ENUM (
        'available', 'occupied', 'reserved', 'cleaning', 'maintenance'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE table_shape AS ENUM (
        'square', 'round', 'rectangle'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE user_status AS ENUM (
        'active', 'inactive', 'suspended'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE review_status AS ENUM (
        'pending', 'approved', 'rejected', 'flagged'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE review_source AS ENUM (
        'google', 'yelp', 'facebook', 'tripadvisor', 'internal', 'other'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE review_sentiment AS ENUM (
        'positive', 'neutral', 'negative'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE subscription_plan AS ENUM (
        'basic', 'premium', 'enterprise'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create indexes for better performance (these will be created by GORM as well)
-- But we can add some custom ones here

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create a function to generate order numbers
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TEXT AS $$
DECLARE
    order_num TEXT;
    current_date_str TEXT;
    sequence_num INTEGER;
BEGIN
    -- Get current date in YYYYMMDD format
    current_date_str := TO_CHAR(CURRENT_DATE, 'YYYYMMDD');
    
    -- Get the next sequence number for today
    SELECT COALESCE(MAX(CAST(SUBSTRING(order_number FROM 13) AS INTEGER)), 0) + 1
    INTO sequence_num
    FROM orders
    WHERE order_number LIKE 'ORD-' || current_date_str || '-%';
    
    -- Format the order number
    order_num := 'ORD-' || current_date_str || '-' || LPAD(sequence_num::TEXT, 4, '0');
    
    RETURN order_num;
END;
$$ LANGUAGE plpgsql;

-- Create a function to calculate order totals
CREATE OR REPLACE FUNCTION calculate_order_total(order_id UUID)
RETURNS DECIMAL(10,2) AS $$
DECLARE
    subtotal DECIMAL(10,2);
    tax_rate DECIMAL(5,4) := 0.08; -- 8% tax rate
    service_rate DECIMAL(5,4) := 0.0; -- No service charge by default
    total DECIMAL(10,2);
BEGIN
    -- Calculate subtotal from order items
    SELECT COALESCE(SUM(total), 0)
    INTO subtotal
    FROM order_items
    WHERE order_id = calculate_order_total.order_id;
    
    -- Calculate total with tax and service charge
    total := subtotal + (subtotal * tax_rate) + (subtotal * service_rate);
    
    RETURN total;
END;
$$ LANGUAGE plpgsql;

-- Create a function to check table availability
CREATE OR REPLACE FUNCTION is_table_available(
    table_id UUID,
    check_date DATE,
    check_time TIME,
    duration_minutes INTEGER DEFAULT 120
)
RETURNS BOOLEAN AS $$
DECLARE
    conflict_count INTEGER;
    end_time TIME;
BEGIN
    end_time := check_time + (duration_minutes || ' minutes')::INTERVAL;
    
    -- Check for conflicting reservations
    SELECT COUNT(*)
    INTO conflict_count
    FROM reservations
    WHERE table_id = is_table_available.table_id
      AND reservation_date = check_date
      AND status IN ('confirmed', 'seated')
      AND (
          (reservation_time <= check_time AND reservation_time + (duration || ' minutes')::INTERVAL > check_time)
          OR
          (reservation_time < end_time AND reservation_time >= check_time)
      );
    
    RETURN conflict_count = 0;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get table capacity utilization
CREATE OR REPLACE FUNCTION get_table_utilization(branch_id UUID, check_date DATE)
RETURNS TABLE(
    hour_slot INTEGER,
    total_capacity INTEGER,
    reserved_capacity INTEGER,
    utilization_percentage DECIMAL(5,2)
) AS $$
BEGIN
    RETURN QUERY
    WITH hourly_slots AS (
        SELECT generate_series(9, 22) AS hour_slot
    ),
    table_capacity AS (
        SELECT SUM(capacity) AS total_cap
        FROM tables
        WHERE branch_id = get_table_utilization.branch_id
          AND is_active = true
    ),
    hourly_reservations AS (
        SELECT 
            EXTRACT(HOUR FROM reservation_time) AS hour_slot,
            SUM(t.capacity) AS reserved_cap
        FROM reservations r
        JOIN tables t ON r.table_id = t.id
        WHERE r.branch_id = get_table_utilization.branch_id
          AND r.reservation_date = check_date
          AND r.status IN ('confirmed', 'seated')
        GROUP BY EXTRACT(HOUR FROM reservation_time)
    )
    SELECT 
        hs.hour_slot,
        tc.total_cap::INTEGER,
        COALESCE(hr.reserved_cap, 0)::INTEGER,
        CASE 
            WHEN tc.total_cap > 0 THEN 
                ROUND((COALESCE(hr.reserved_cap, 0) / tc.total_cap * 100), 2)
            ELSE 0
        END
    FROM hourly_slots hs
    CROSS JOIN table_capacity tc
    LEFT JOIN hourly_reservations hr ON hs.hour_slot = hr.hour_slot
    ORDER BY hs.hour_slot;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get popular menu items
CREATE OR REPLACE FUNCTION get_popular_menu_items(
    branch_id UUID,
    start_date DATE DEFAULT CURRENT_DATE - INTERVAL '30 days',
    end_date DATE DEFAULT CURRENT_DATE,
    limit_count INTEGER DEFAULT 10
)
RETURNS TABLE(
    menu_item_id UUID,
    menu_item_name VARCHAR(255),
    total_quantity INTEGER,
    total_revenue DECIMAL(10,2),
    order_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        oi.menu_item_id,
        oi.name,
        SUM(oi.quantity)::INTEGER AS total_quantity,
        SUM(oi.total) AS total_revenue,
        COUNT(DISTINCT oi.order_id)::INTEGER AS order_count
    FROM order_items oi
    JOIN orders o ON oi.order_id = o.id
    WHERE o.branch_id = get_popular_menu_items.branch_id
      AND o.created_at::DATE BETWEEN start_date AND end_date
      AND o.status = 'completed'
      AND oi.menu_item_id IS NOT NULL
    GROUP BY oi.menu_item_id, oi.name
    ORDER BY total_quantity DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get sales trends
CREATE OR REPLACE FUNCTION get_sales_trends(
    branch_id UUID,
    start_date DATE DEFAULT CURRENT_DATE - INTERVAL '30 days',
    end_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE(
    sale_date DATE,
    total_orders INTEGER,
    total_revenue DECIMAL(10,2),
    average_order_value DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        o.created_at::DATE AS sale_date,
        COUNT(*)::INTEGER AS total_orders,
        SUM(o.total) AS total_revenue,
        AVG(o.total) AS average_order_value
    FROM orders o
    WHERE o.branch_id = get_sales_trends.branch_id
      AND o.created_at::DATE BETWEEN start_date AND end_date
      AND o.status = 'completed'
    GROUP BY o.created_at::DATE
    ORDER BY sale_date;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get review statistics
CREATE OR REPLACE FUNCTION get_review_stats(branch_id UUID)
RETURNS TABLE(
    total_reviews INTEGER,
    average_rating DECIMAL(3,2),
    rating_distribution JSONB,
    sentiment_distribution JSONB
) AS $$
DECLARE
    rating_dist JSONB;
    sentiment_dist JSONB;
BEGIN
    -- Get rating distribution
    SELECT jsonb_object_agg(rating::TEXT, count)
    INTO rating_dist
    FROM (
        SELECT rating, COUNT(*) as count
        FROM reviews
        WHERE branch_id = get_review_stats.branch_id
          AND status = 'approved'
        GROUP BY rating
    ) r;
    
    -- Get sentiment distribution
    SELECT jsonb_object_agg(sentiment, count)
    INTO sentiment_dist
    FROM (
        SELECT sentiment, COUNT(*) as count
        FROM reviews
        WHERE branch_id = get_review_stats.branch_id
          AND status = 'approved'
        GROUP BY sentiment
    ) s;
    
    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER,
        ROUND(AVG(rating), 2),
        COALESCE(rating_dist, '{}'::JSONB),
        COALESCE(sentiment_dist, '{}'::JSONB)
    FROM reviews
    WHERE branch_id = get_review_stats.branch_id
      AND status = 'approved';
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO restaurant_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO restaurant_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO restaurant_user;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO restaurant_user;

-- Create some useful views
CREATE OR REPLACE VIEW active_orders AS
SELECT 
    o.*,
    b.name as branch_name,
    t.name as table_name
FROM orders o
JOIN branches b ON o.branch_id = b.id
LEFT JOIN tables t ON o.table_id = t.id
WHERE o.status IN ('pending', 'confirmed', 'preparing', 'ready', 'served');

CREATE OR REPLACE VIEW daily_sales_summary AS
SELECT 
    o.branch_id,
    b.name as branch_name,
    o.created_at::DATE as sale_date,
    COUNT(*) as total_orders,
    SUM(o.total) as total_revenue,
    AVG(o.total) as average_order_value,
    SUM(CASE WHEN o.type = 'dine-in' THEN 1 ELSE 0 END) as dine_in_orders,
    SUM(CASE WHEN o.type = 'takeaway' THEN 1 ELSE 0 END) as takeaway_orders,
    SUM(CASE WHEN o.type = 'delivery' THEN 1 ELSE 0 END) as delivery_orders
FROM orders o
JOIN branches b ON o.branch_id = b.id
WHERE o.status = 'completed'
GROUP BY o.branch_id, b.name, o.created_at::DATE;

CREATE OR REPLACE VIEW table_status_summary AS
SELECT 
    ta.branch_id,
    ta.name as area_name,
    COUNT(*) as total_tables,
    SUM(CASE WHEN t.status = 'available' THEN 1 ELSE 0 END) as available_tables,
    SUM(CASE WHEN t.status = 'occupied' THEN 1 ELSE 0 END) as occupied_tables,
    SUM(CASE WHEN t.status = 'reserved' THEN 1 ELSE 0 END) as reserved_tables,
    SUM(CASE WHEN t.status = 'cleaning' THEN 1 ELSE 0 END) as cleaning_tables,
    SUM(t.capacity) as total_capacity,
    SUM(CASE WHEN t.status = 'available' THEN t.capacity ELSE 0 END) as available_capacity
FROM table_areas ta
LEFT JOIN tables t ON ta.id = t.area_id AND t.is_active = true
WHERE ta.is_active = true
GROUP BY ta.branch_id, ta.id, ta.name;

-- Add comments for documentation
COMMENT ON FUNCTION generate_order_number() IS 'Generates a unique order number in format ORD-YYYYMMDD-NNNN';
COMMENT ON FUNCTION calculate_order_total(UUID) IS 'Calculates the total amount for an order including tax and service charges';
COMMENT ON FUNCTION is_table_available(UUID, DATE, TIME, INTEGER) IS 'Checks if a table is available for reservation at a specific date and time';
COMMENT ON FUNCTION get_table_utilization(UUID, DATE) IS 'Returns hourly table utilization for a branch on a specific date';
COMMENT ON FUNCTION get_popular_menu_items(UUID, DATE, DATE, INTEGER) IS 'Returns the most popular menu items for a branch within a date range';
COMMENT ON FUNCTION get_sales_trends(UUID, DATE, DATE) IS 'Returns daily sales trends for a branch within a date range';
COMMENT ON FUNCTION get_review_stats(UUID) IS 'Returns review statistics including average rating and distributions';

COMMENT ON VIEW active_orders IS 'Shows all active orders with branch and table information';
COMMENT ON VIEW daily_sales_summary IS 'Daily sales summary by branch including order counts and revenue';
COMMENT ON VIEW table_status_summary IS 'Table status summary by area including capacity information';
