-- Check migration readiness for merchant_id to shop_id transition

-- Check if users table has merchant_id column
SELECT 
    column_name, 
    data_type, 
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'users' 
AND column_name IN ('merchant_id', 'shop_id');

-- Check if roles table has merchant_id column
SELECT 
    column_name, 
    data_type, 
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'roles' 
AND column_name IN ('merchant_id', 'shop_id');

-- Count records in users table
SELECT 
    COUNT(*) as total_users,
    COUNT(merchant_id) as users_with_merchant_id,
    COUNT(shop_id) as users_with_shop_id
FROM users;

-- Count records in roles table
SELECT 
    COUNT(*) as total_roles,
    COUNT(merchant_id) as roles_with_merchant_id,
    COUNT(shop_id) as roles_with_shop_id
FROM roles;

-- Check for orphaned records (users/roles with merchant_id that don't exist in shops)
SELECT 
    'users' as table_name,
    COUNT(*) as orphaned_records
FROM users u
LEFT JOIN shops s ON u.merchant_id = s.id
WHERE u.merchant_id IS NOT NULL AND s.id IS NULL

UNION ALL

SELECT 
    'roles' as table_name,
    COUNT(*) as orphaned_records
FROM roles r
LEFT JOIN shops s ON r.merchant_id = s.id
WHERE r.merchant_id IS NOT NULL AND s.id IS NULL;
