package main

import (
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"restaurant-backend/internal/models"
)

func main() {
	// Get database URL from environment
	dbURL := os.Getenv("DATABASE_URL")
	if dbURL == "" {
		log.Fatal("DATABASE_URL environment variable is required")
	}

	// Initialize logger
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// Connect to database
	db, err := gorm.Open(postgres.Open(dbURL), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	logger.Info("Starting tag migration...")

	// Migrate existing menu item tags to centralized system
	if err := migrateMenuItemTags(db, logger); err != nil {
		log.Fatalf("Failed to migrate menu item tags: %v", err)
	}

	// Migrate existing review tags to centralized system
	if err := migrateReviewTags(db, logger); err != nil {
		log.Fatalf("Failed to migrate review tags: %v", err)
	}

	logger.Info("Tag migration completed successfully!")
}

func migrateMenuItemTags(db *gorm.DB, logger *logrus.Logger) error {
	logger.Info("Migrating menu item tags...")

	// Get all menu items with tags
	var menuItems []models.MenuItem
	if err := db.Where("tags IS NOT NULL AND tags != '[]'").Find(&menuItems).Error; err != nil {
		return fmt.Errorf("failed to fetch menu items: %w", err)
	}

	logger.Infof("Found %d menu items with tags", len(menuItems))

	for _, item := range menuItems {
		if err := migrateItemTags(db, item, logger); err != nil {
			logger.Errorf("Failed to migrate tags for menu item %s: %v", item.ID, err)
			continue
		}
	}

	return nil
}

func migrateReviewTags(db *gorm.DB, logger *logrus.Logger) error {
	logger.Info("Migrating review tags...")

	// Get all reviews with tags
	var reviews []models.Review
	if err := db.Where("tags IS NOT NULL AND tags != '[]'").Find(&reviews).Error; err != nil {
		return fmt.Errorf("failed to fetch reviews: %w", err)
	}

	logger.Infof("Found %d reviews with tags", len(reviews))

	for _, review := range reviews {
		if err := migrateReviewItemTags(db, review, logger); err != nil {
			logger.Errorf("Failed to migrate tags for review %s: %v", review.ID, err)
			continue
		}
	}

	return nil
}

func migrateItemTags(db *gorm.DB, item models.MenuItem, logger *logrus.Logger) error {
	// Parse existing tags
	var existingTags []string
	if len(item.Tags) > 0 {
		existingTags = item.Tags
	}

	if len(existingTags) == 0 {
		return nil
	}

	logger.Infof("Migrating %d tags for menu item: %s", len(existingTags), item.Name)

	// Process each tag
	var tagIDs []uuid.UUID
	for _, tagName := range existingTags {
		tagName = strings.TrimSpace(tagName)
		if tagName == "" {
			continue
		}

		// Find or create centralized tag
		tag, err := findOrCreateTag(db, item.BranchID, tagName)
		if err != nil {
			logger.Errorf("Failed to find/create tag '%s': %v", tagName, err)
			continue
		}

		tagIDs = append(tagIDs, tag.ID)
	}

	// Create entity tag relationships
	for _, tagID := range tagIDs {
		entityTag := &models.EntityTag{
			TagID:      tagID,
			EntityType: "menu_item",
			EntityID:   item.ID,
		}

		// Check if relationship already exists
		var existing models.EntityTag
		if err := db.Where("tag_id = ? AND entity_type = ? AND entity_id = ?",
			tagID, "menu_item", item.ID).First(&existing).Error; err == nil {
			continue // Already exists
		}

		if err := db.Create(entityTag).Error; err != nil {
			logger.Errorf("Failed to create entity tag relationship: %v", err)
			continue
		}

		// Increment tag usage count
		if err := db.Model(&models.Tag{}).Where("id = ?", tagID).
			Update("usage_count", gorm.Expr("usage_count + ?", 1)).Error; err != nil {
			logger.Errorf("Failed to increment tag usage count: %v", err)
		}
	}

	logger.Infof("Successfully migrated %d tags for menu item: %s", len(tagIDs), item.Name)
	return nil
}

func migrateReviewItemTags(db *gorm.DB, review models.Review, logger *logrus.Logger) error {
	// Parse existing tags
	var existingTags []string
	if len(review.Tags) > 0 {
		existingTags = review.Tags
	}

	if len(existingTags) == 0 {
		return nil
	}

	logger.Infof("Migrating %d tags for review: %s", len(existingTags), review.ID)

	// Process each tag
	var tagIDs []uuid.UUID
	for _, tagName := range existingTags {
		tagName = strings.TrimSpace(tagName)
		if tagName == "" {
			continue
		}

		// Find or create centralized tag
		tag, err := findOrCreateTag(db, review.BranchID, tagName)
		if err != nil {
			logger.Errorf("Failed to find/create tag '%s': %v", tagName, err)
			continue
		}

		tagIDs = append(tagIDs, tag.ID)
	}

	// Create entity tag relationships
	for _, tagID := range tagIDs {
		entityTag := &models.EntityTag{
			TagID:      tagID,
			EntityType: "review",
			EntityID:   review.ID,
		}

		// Check if relationship already exists
		var existing models.EntityTag
		if err := db.Where("tag_id = ? AND entity_type = ? AND entity_id = ?",
			tagID, "review", review.ID).First(&existing).Error; err == nil {
			continue // Already exists
		}

		if err := db.Create(entityTag).Error; err != nil {
			logger.Errorf("Failed to create entity tag relationship: %v", err)
			continue
		}

		// Increment tag usage count
		if err := db.Model(&models.Tag{}).Where("id = ?", tagID).
			Update("usage_count", gorm.Expr("usage_count + ?", 1)).Error; err != nil {
			logger.Errorf("Failed to increment tag usage count: %v", err)
		}
	}

	logger.Infof("Successfully migrated %d tags for review: %s", len(tagIDs), review.ID)
	return nil
}

func findOrCreateTag(db *gorm.DB, branchID uuid.UUID, tagName string) (*models.Tag, error) {
	// Generate slug
	slug := models.Slugify(tagName)

	// Try to find existing tag
	var tag models.Tag
	if err := db.Where("branch_id = ? AND slug = ?", branchID, slug).First(&tag).Error; err == nil {
		return &tag, nil
	}

	// Determine category based on tag name
	category := categorizeTag(tagName)

	// Create new tag
	tag = models.Tag{
		BranchID:   branchID,
		Name:       tagName,
		Slug:       slug,
		Category:   category,
		Color:      getDefaultColorForCategory(category),
		Icon:       getDefaultIconForCategory(category),
		UsageCount: 0,
		IsSystem:   false,
		IsActive:   true,
	}

	if err := db.Create(&tag).Error; err != nil {
		return nil, fmt.Errorf("failed to create tag: %w", err)
	}

	return &tag, nil
}

func categorizeTag(tagName string) string {
	tagLower := strings.ToLower(tagName)

	// Dietary tags
	dietaryTags := []string{"vegetarian", "vegan", "gluten-free", "dairy-free", "nut-free", "keto", "low-carb", "high-protein"}
	for _, dietary := range dietaryTags {
		if strings.Contains(tagLower, dietary) {
			return "dietary"
		}
	}

	// Style tags
	styleTags := []string{"spicy", "mild", "grilled", "fried", "steamed", "raw", "baked", "roasted"}
	for _, style := range styleTags {
		if strings.Contains(tagLower, style) {
			return "style"
		}
	}

	// Popularity tags
	popularityTags := []string{"popular", "new", "chef", "bestseller", "trending", "signature", "special"}
	for _, popularity := range popularityTags {
		if strings.Contains(tagLower, popularity) {
			return "popularity"
		}
	}

	// Occasion tags
	occasionTags := []string{"date", "family", "business", "celebration", "comfort", "quick"}
	for _, occasion := range occasionTags {
		if strings.Contains(tagLower, occasion) {
			return "occasion"
		}
	}

	// Default to general category
	return "general"
}

func getDefaultColorForCategory(category string) string {
	switch category {
	case "dietary":
		return "#22c55e"
	case "style":
		return "#f59e0b"
	case "popularity":
		return "#ef4444"
	case "occasion":
		return "#8b5cf6"
	default:
		return "#8a745c"
	}
}

func getDefaultIconForCategory(category string) string {
	switch category {
	case "dietary":
		return "🥗"
	case "style":
		return "🍳"
	case "popularity":
		return "⭐"
	case "occasion":
		return "🎉"
	default:
		return "🏷️"
	}
}
