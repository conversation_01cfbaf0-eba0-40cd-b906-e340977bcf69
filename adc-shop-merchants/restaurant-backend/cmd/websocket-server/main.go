package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"restaurant-backend/internal/config"
	"restaurant-backend/internal/database"
	"restaurant-backend/internal/websocket"
	"restaurant-backend/pkg/logger"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// @title Restaurant WebSocket Service
// @version 1.0
// @description WebSocket service for real-time restaurant management updates
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.restaurant-api.com/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8200
// @BasePath /ws

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

func main() {
	// Load environment variables from .env file if it exists
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize logger
	logger := logger.NewLogger(cfg.Log.Level, cfg.Log.Format)
	logger.Info("Starting Restaurant WebSocket Service")

	// Initialize database connection for authentication and data access
	var dsn string
	if databaseURL := os.Getenv("DATABASE_URL"); databaseURL != "" {
		dsn = databaseURL
		logger.Info("Using DATABASE_URL for database connection")
	} else {
		dsn = cfg.GetDSN()
		logger.Info("Using individual DB config for database connection")
	}

	db, err := database.Initialize(dsn, logger)
	if err != nil {
		logger.Fatalf("Failed to initialize database: %v", err)
	}

	// Initialize WebSocket hub
	hub := websocket.NewHub(logger)
	go hub.Run()

	// Set Gin mode
	ginMode := cfg.Server.GinMode
	if ginMode != gin.DebugMode && ginMode != gin.ReleaseMode && ginMode != gin.TestMode {
		logger.Warnf("Invalid GIN_MODE '%s', defaulting to debug", ginMode)
		ginMode = gin.DebugMode
	}
	gin.SetMode(ginMode)

	// Initialize Gin router
	router := gin.New()

	// Add middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// CORS configuration for WebSocket
	config := cors.DefaultConfig()
	config.AllowOrigins = []string{
		"http://localhost:3000",
		"http://localhost:4000",
		"https://localhost:3000",
		"https://localhost:4000",
	}
	config.AllowMethods = []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"}
	config.AllowHeaders = []string{"Origin", "Content-Length", "Content-Type", "Authorization", "X-Requested-With"}
	config.AllowCredentials = true
	router.Use(cors.New(config))

	// Setup WebSocket routes
	setupWebSocketRoutes(router, hub, db, logger)

	// WebSocket server port
	wsPort := os.Getenv("WEBSOCKET_PORT")
	if wsPort == "" {
		wsPort = "8200"
	}

	// Create HTTP server
	server := &http.Server{
		Addr:         ":" + wsPort,
		Handler:      router,
		ReadTimeout:  60 * time.Second,
		WriteTimeout: 60 * time.Second,
		IdleTimeout:  120 * time.Second,
	}

	// Start server in a goroutine
	go func() {
		logger.Infof("WebSocket server starting on port %s", wsPort)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatalf("Failed to start WebSocket server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down WebSocket server...")

	// Give outstanding requests a deadline for completion
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown server
	if err := server.Shutdown(ctx); err != nil {
		logger.Fatalf("WebSocket server forced to shutdown: %v", err)
	}

	// Close database connection
	sqlDB, err := db.DB()
	if err == nil {
		sqlDB.Close()
	}

	logger.Info("WebSocket server exited")
}

// setupWebSocketRoutes configures all WebSocket routes
func setupWebSocketRoutes(router *gin.Engine, hub *websocket.Hub, db *gorm.DB, logger *logrus.Logger) {
	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "ok",
			"service": "websocket",
			"port":    "8200",
		})
	})

	// WebSocket connection endpoint
	router.GET("/ws", func(c *gin.Context) {
		websocket.HandleWebSocketConnection(hub, db, logger, c)
	})

	// WebSocket connection with authentication
	router.GET("/ws/auth", func(c *gin.Context) {
		websocket.HandleAuthenticatedWebSocketConnection(hub, db, logger, c)
	})

	// API endpoints for WebSocket management
	api := router.Group("/api/v1")
	{
		// Get connected clients info (for admin)
		api.GET("/websocket/clients", func(c *gin.Context) {
			clientsInfo := hub.GetConnectedClientsInfo()
			clientsCount := hub.GetConnectedClients()
			c.JSON(http.StatusOK, gin.H{
				"clients": clientsInfo,
				"count":   clientsCount,
			})
		})

		// Broadcast message to all clients
		api.POST("/websocket/broadcast", func(c *gin.Context) {
			var req struct {
				Type    string                 `json:"type" binding:"required"`
				Data    map[string]interface{} `json:"data"`
				Channel string                 `json:"channel"`
			}

			if err := c.ShouldBindJSON(&req); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
				return
			}

			message := websocket.Message{
				Type:      req.Type,
				Data:      req.Data,
				Timestamp: websocket.GetCurrentTimestamp(),
			}

			if req.Channel != "" {
				hub.BroadcastToChannel(req.Channel, message)
			} else {
				hub.Broadcast(message)
			}

			c.JSON(http.StatusOK, gin.H{"status": "message sent"})
		})

		// Send message to specific user
		api.POST("/websocket/send/:userID", func(c *gin.Context) {
			userID := c.Param("userID")

			var req struct {
				Type string                 `json:"type" binding:"required"`
				Data map[string]interface{} `json:"data"`
			}

			if err := c.ShouldBindJSON(&req); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
				return
			}

			message := websocket.Message{
				Type:      req.Type,
				Data:      req.Data,
				Timestamp: websocket.GetCurrentTimestamp(),
			}

			if hub.SendToUser(userID, message) {
				c.JSON(http.StatusOK, gin.H{"status": "message sent"})
			} else {
				c.JSON(http.StatusNotFound, gin.H{"error": "user not connected"})
			}
		})
	}
}

// Health check handler for Docker health checks
func init() {
	if len(os.Args) > 1 && os.Args[1] == "health" {
		// Simple health check
		resp, err := http.Get("http://localhost:8200/health")
		if err != nil || resp.StatusCode != http.StatusOK {
			os.Exit(1)
		}
		fmt.Println("WebSocket health check passed")
		os.Exit(0)
	}
}
