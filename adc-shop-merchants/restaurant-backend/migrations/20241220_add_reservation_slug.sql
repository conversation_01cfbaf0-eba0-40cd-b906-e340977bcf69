-- Migration: Add slug column to reservations table
-- Date: 2024-12-20
-- Description: Add slug field to reservations for slug-based routing

-- Add slug column to reservations table
ALTER TABLE reservations ADD COLUMN slug VARCHAR(255);

-- Create unique index on slug column
CREATE UNIQUE INDEX idx_reservations_slug ON reservations(slug);

-- Update existing reservations with generated slugs
-- This will generate slugs based on customer name, date, time, and party size
UPDATE reservations 
SET slug = CONCAT(
    LOWER(REPLACE(REPLACE(REPLACE(customer_name, ' ', '-'), '''', ''), '.', '')),
    '-',
    TO_CHAR(reservation_date, 'YYYY-MM-DD'),
    '-',
    TO_CHAR(reservation_time, 'HH24-MI'),
    '-party-',
    party_size::text,
    '-',
    SUBSTRING(id::text, 1, 8)  -- Add part of UUID for uniqueness
)
WHERE slug IS NULL OR slug = '';

-- Make slug column NOT NULL after populating existing records
ALTER TABLE reservations ALTER COLUMN slug SET NOT NULL;

-- Add comment to the column
COMMENT ON COLUMN reservations.slug IS 'Unique slug for URL routing';
