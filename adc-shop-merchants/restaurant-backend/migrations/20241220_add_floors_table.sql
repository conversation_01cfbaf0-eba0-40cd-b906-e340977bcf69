-- Migration: Add floors table and update table_areas to include floor_id
-- Date: 2024-12-20

-- Create floors table
CREATE TABLE IF NOT EXISTS floors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    branch_id UUID NOT NULL REFERENCES shop_branches(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    "order" INTEGER NOT NULL DEFAULT 1,
    layout JSONB DEFAULT '{"width": 800, "height": 600, "grid_size": 20, "show_grid": true}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for floors table
CREATE INDEX IF NOT EXISTS idx_floors_branch_id ON floors(branch_id);
CREATE INDEX IF NOT EXISTS idx_floors_order ON floors("order");
CREATE INDEX IF NOT EXISTS idx_floors_is_active ON floors(is_active);

-- Add unique constraint for floor name per branch
CREATE UNIQUE INDEX IF NOT EXISTS idx_floors_branch_name_unique 
ON floors(branch_id, name) WHERE is_active = true;

-- Add floor_id column to table_areas table
ALTER TABLE table_areas 
ADD COLUMN IF NOT EXISTS floor_id UUID REFERENCES floors(id) ON DELETE SET NULL;

-- Add index for floor_id in table_areas
CREATE INDEX IF NOT EXISTS idx_table_areas_floor_id ON table_areas(floor_id);

-- Insert default floors for existing branches
INSERT INTO floors (branch_id, name, description, "order", is_active)
SELECT 
    id as branch_id,
    'Ground Floor' as name,
    'Main dining area' as description,
    1 as "order",
    true as is_active
FROM shop_branches 
WHERE NOT EXISTS (
    SELECT 1 FROM floors WHERE floors.branch_id = shop_branches.id
);

-- Update existing table_areas to be associated with the ground floor
UPDATE table_areas 
SET floor_id = (
    SELECT f.id 
    FROM floors f 
    WHERE f.branch_id = table_areas.branch_id 
    AND f."order" = 1 
    AND f.is_active = true
    LIMIT 1
)
WHERE floor_id IS NULL;

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_floors_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_floors_updated_at
    BEFORE UPDATE ON floors
    FOR EACH ROW
    EXECUTE FUNCTION update_floors_updated_at();
