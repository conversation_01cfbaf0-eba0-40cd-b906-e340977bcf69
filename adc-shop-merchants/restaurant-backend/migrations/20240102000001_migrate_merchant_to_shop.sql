-- +goose Up
-- Migration to rename merchant_id to shop_id in users and roles tables

-- Step 1: Add shop_id column to users table (allow NULL initially)
ALTER TABLE users ADD COLUMN IF NOT EXISTS shop_id UUID;

-- Step 2: Copy data from merchant_id to shop_id in users table
UPDATE users SET shop_id = merchant_id WHERE merchant_id IS NOT NULL;

-- Step 3: Add shop_id column to roles table (allow NULL initially)
ALTER TABLE roles ADD COLUMN IF NOT EXISTS shop_id UUID;

-- Step 4: Copy data from merchant_id to shop_id in roles table
UPDATE roles SET shop_id = merchant_id WHERE merchant_id IS NOT NULL;

-- Step 5: Make shop_id NOT NULL after data migration
ALTER TABLE users ALTER COLUMN shop_id SET NOT NULL;
ALTER TABLE roles ALTER COLUMN shop_id SET NOT NULL;

-- Step 6: Add foreign key constraints for shop_id
ALTER TABLE users ADD CONSTRAINT IF NOT EXISTS fk_users_shop_id 
    FOREIGN KEY (shop_id) REFERENCES shops(id) ON DELETE CASCADE;

ALTER TABLE roles ADD CONSTRAINT IF NOT EXISTS fk_roles_shop_id 
    FOREIGN KEY (shop_id) REFERENCES shops(id) ON DELETE CASCADE;

-- Step 7: Create indexes for shop_id
CREATE INDEX IF NOT EXISTS idx_users_shop_id ON users(shop_id);
CREATE INDEX IF NOT EXISTS idx_roles_shop_id ON roles(shop_id);

-- Step 8: Drop old merchant_id columns (commented out for safety)
-- ALTER TABLE users DROP COLUMN IF EXISTS merchant_id;
-- ALTER TABLE roles DROP COLUMN IF EXISTS merchant_id;

-- +goose Down
-- Rollback migration

-- Step 1: Add back merchant_id columns if they don't exist
ALTER TABLE users ADD COLUMN IF NOT EXISTS merchant_id UUID;
ALTER TABLE roles ADD COLUMN IF NOT EXISTS merchant_id UUID;

-- Step 2: Copy data back from shop_id to merchant_id
UPDATE users SET merchant_id = shop_id WHERE shop_id IS NOT NULL;
UPDATE roles SET merchant_id = shop_id WHERE shop_id IS NOT NULL;

-- Step 3: Make merchant_id NOT NULL
ALTER TABLE users ALTER COLUMN merchant_id SET NOT NULL;
ALTER TABLE roles ALTER COLUMN merchant_id SET NOT NULL;

-- Step 4: Drop shop_id columns
ALTER TABLE users DROP COLUMN IF EXISTS shop_id;
ALTER TABLE roles DROP COLUMN IF EXISTS shop_id;

-- Step 5: Drop indexes
DROP INDEX IF EXISTS idx_users_shop_id;
DROP INDEX IF EXISTS idx_roles_shop_id;
