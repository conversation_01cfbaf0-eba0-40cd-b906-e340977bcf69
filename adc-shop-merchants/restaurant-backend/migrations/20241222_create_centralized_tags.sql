-- Migration: Create centralized tag system
-- Date: 2024-12-22
-- Description: Create centralized tag management system for all entities

-- Create tag_categories table
CREATE TABLE IF NOT EXISTS tag_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    branch_id UUID NOT NULL REFERENCES shop_branches(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#8a745c',
    icon VARCHAR(50),
    sort_order INTEGER DEFAULT 0,
    is_system BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create tags table
CREATE TABLE IF NOT EXISTS tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    branch_id UUID NOT NULL REFERENCES shop_branches(id) ON DELETE CASCADE,
    name VA<PERSON>HA<PERSON>(100) NOT NULL,
    slug VARCHAR(100) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL,
    color VARCHAR(7) DEFAULT '#8a745c',
    icon VARCHAR(50),
    usage_count INTEGER DEFAULT 0,
    is_system BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create entity_tags table for many-to-many relationships
CREATE TABLE IF NOT EXISTS entity_tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tag_id UUID NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
    entity_type VARCHAR(50) NOT NULL, -- 'menu_item', 'review', 'service', 'staff', etc.
    entity_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for tag_categories
CREATE INDEX IF NOT EXISTS idx_tag_categories_branch_id ON tag_categories(branch_id);
CREATE INDEX IF NOT EXISTS idx_tag_categories_slug ON tag_categories(slug);
CREATE INDEX IF NOT EXISTS idx_tag_categories_is_active ON tag_categories(is_active);
CREATE UNIQUE INDEX IF NOT EXISTS idx_tag_categories_branch_slug_unique 
ON tag_categories(branch_id, slug) WHERE is_active = true;

-- Create indexes for tags
CREATE INDEX IF NOT EXISTS idx_tags_branch_id ON tags(branch_id);
CREATE INDEX IF NOT EXISTS idx_tags_slug ON tags(slug);
CREATE INDEX IF NOT EXISTS idx_tags_category ON tags(category);
CREATE INDEX IF NOT EXISTS idx_tags_usage_count ON tags(usage_count);
CREATE INDEX IF NOT EXISTS idx_tags_is_active ON tags(is_active);
CREATE UNIQUE INDEX IF NOT EXISTS idx_tags_branch_slug_unique 
ON tags(branch_id, slug) WHERE is_active = true;

-- Create indexes for entity_tags
CREATE INDEX IF NOT EXISTS idx_entity_tags_tag_id ON entity_tags(tag_id);
CREATE INDEX IF NOT EXISTS idx_entity_tags_entity_type ON entity_tags(entity_type);
CREATE INDEX IF NOT EXISTS idx_entity_tags_entity_id ON entity_tags(entity_id);
CREATE INDEX IF NOT EXISTS idx_entity_tags_entity_type_id ON entity_tags(entity_type, entity_id);
CREATE UNIQUE INDEX IF NOT EXISTS idx_entity_tags_unique 
ON entity_tags(tag_id, entity_type, entity_id);

-- Insert default tag categories
INSERT INTO tag_categories (branch_id, name, slug, description, color, icon, sort_order, is_system, is_active)
SELECT 
    sb.id as branch_id,
    'Dietary' as name,
    'dietary' as slug,
    'Dietary restrictions and preferences' as description,
    '#22c55e' as color,
    '🥗' as icon,
    1 as sort_order,
    true as is_system,
    true as is_active
FROM shop_branches sb
WHERE NOT EXISTS (
    SELECT 1 FROM tag_categories tc 
    WHERE tc.branch_id = sb.id AND tc.slug = 'dietary'
);

INSERT INTO tag_categories (branch_id, name, slug, description, color, icon, sort_order, is_system, is_active)
SELECT 
    sb.id as branch_id,
    'Style' as name,
    'style' as slug,
    'Cooking style and preparation method' as description,
    '#f59e0b' as color,
    '🍳' as icon,
    2 as sort_order,
    true as is_system,
    true as is_active
FROM shop_branches sb
WHERE NOT EXISTS (
    SELECT 1 FROM tag_categories tc 
    WHERE tc.branch_id = sb.id AND tc.slug = 'style'
);

INSERT INTO tag_categories (branch_id, name, slug, description, color, icon, sort_order, is_system, is_active)
SELECT 
    sb.id as branch_id,
    'Popularity' as name,
    'popularity' as slug,
    'Popular and trending items' as description,
    '#ef4444' as color,
    '🔥' as icon,
    3 as sort_order,
    true as is_system,
    true as is_active
FROM shop_branches sb
WHERE NOT EXISTS (
    SELECT 1 FROM tag_categories tc 
    WHERE tc.branch_id = sb.id AND tc.slug = 'popularity'
);

INSERT INTO tag_categories (branch_id, name, slug, description, color, icon, sort_order, is_system, is_active)
SELECT 
    sb.id as branch_id,
    'Occasion' as name,
    'occasion' as slug,
    'Special occasions and events' as description,
    '#8b5cf6' as color,
    '🎉' as icon,
    4 as sort_order,
    true as is_system,
    true as is_active
FROM shop_branches sb
WHERE NOT EXISTS (
    SELECT 1 FROM tag_categories tc 
    WHERE tc.branch_id = sb.id AND tc.slug = 'occasion'
);

-- Insert default tags for each category
-- Dietary tags
INSERT INTO tags (branch_id, name, slug, category, color, icon, is_system, is_active)
SELECT 
    sb.id as branch_id,
    unnest(ARRAY['Vegetarian', 'Vegan', 'Gluten-Free', 'Dairy-Free', 'Nut-Free', 'Keto', 'Low-Carb', 'High-Protein']) as name,
    unnest(ARRAY['vegetarian', 'vegan', 'gluten-free', 'dairy-free', 'nut-free', 'keto', 'low-carb', 'high-protein']) as slug,
    'dietary' as category,
    '#22c55e' as color,
    unnest(ARRAY['🌱', '🌿', '🌾', '🥛', '🥜', '🥑', '🍃', '💪']) as icon,
    true as is_system,
    true as is_active
FROM shop_branches sb
WHERE NOT EXISTS (
    SELECT 1 FROM tags t 
    WHERE t.branch_id = sb.id AND t.category = 'dietary'
);

-- Style tags
INSERT INTO tags (branch_id, name, slug, category, color, icon, is_system, is_active)
SELECT 
    sb.id as branch_id,
    unnest(ARRAY['Spicy', 'Mild', 'Grilled', 'Fried', 'Steamed', 'Raw', 'Baked', 'Roasted']) as name,
    unnest(ARRAY['spicy', 'mild', 'grilled', 'fried', 'steamed', 'raw', 'baked', 'roasted']) as slug,
    'style' as category,
    '#f59e0b' as color,
    unnest(ARRAY['🌶️', '😌', '🔥', '🍟', '♨️', '🍣', '🍞', '🔥']) as icon,
    true as is_system,
    true as is_active
FROM shop_branches sb
WHERE NOT EXISTS (
    SELECT 1 FROM tags t 
    WHERE t.branch_id = sb.id AND t.category = 'style'
);

-- Popularity tags
INSERT INTO tags (branch_id, name, slug, category, color, icon, is_system, is_active)
SELECT 
    sb.id as branch_id,
    unnest(ARRAY['Popular', 'New', 'Chef Special', 'Bestseller', 'Trending', 'Signature']) as name,
    unnest(ARRAY['popular', 'new', 'chef-special', 'bestseller', 'trending', 'signature']) as slug,
    'popularity' as category,
    '#ef4444' as color,
    unnest(ARRAY['⭐', '✨', '👨‍🍳', '🏆', '📈', '✍️']) as icon,
    true as is_system,
    true as is_active
FROM shop_branches sb
WHERE NOT EXISTS (
    SELECT 1 FROM tags t 
    WHERE t.branch_id = sb.id AND t.category = 'popularity'
);

-- Occasion tags
INSERT INTO tags (branch_id, name, slug, category, color, icon, is_system, is_active)
SELECT 
    sb.id as branch_id,
    unnest(ARRAY['Date Night', 'Family Friendly', 'Business Lunch', 'Celebration', 'Comfort Food', 'Quick Bite']) as name,
    unnest(ARRAY['date-night', 'family-friendly', 'business-lunch', 'celebration', 'comfort-food', 'quick-bite']) as slug,
    'occasion' as category,
    '#8b5cf6' as color,
    unnest(ARRAY['💕', '👨‍👩‍👧‍👦', '💼', '🎉', '🤗', '⚡']) as icon,
    true as is_system,
    true as is_active
FROM shop_branches sb
WHERE NOT EXISTS (
    SELECT 1 FROM tags t 
    WHERE t.branch_id = sb.id AND t.category = 'occasion'
);

-- Create trigger function for updating updated_at timestamp
CREATE OR REPLACE FUNCTION update_tags_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at timestamps
CREATE TRIGGER update_tag_categories_updated_at 
    BEFORE UPDATE ON tag_categories
    FOR EACH ROW EXECUTE FUNCTION update_tags_updated_at();

CREATE TRIGGER update_tags_updated_at 
    BEFORE UPDATE ON tags
    FOR EACH ROW EXECUTE FUNCTION update_tags_updated_at();
