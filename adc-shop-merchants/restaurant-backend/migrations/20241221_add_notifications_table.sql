-- Migration: Add notifications table
-- Date: 2024-12-21

-- Create notifications table
CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    shop_id UUID NOT NULL REFERENCES shops(id) ON DELETE CASCADE,
    branch_id UUID NOT NULL REFERENCES shop_branches(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    
    -- Content
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    
    -- Classification
    type VARCHAR(50) NOT NULL,
    priority VARCHAR(20) NOT NULL DEFAULT 'medium',
    
    -- Status
    is_read BOOLEAN NOT NULL DEFAULT false,
    
    -- Optional fields
    link VARCHAR(500),
    action_label VARCHAR(100),
    data JSONB DEFAULT '{}',
    
    -- Timestamps
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notifications_shop_branch ON notifications(shop_id, branch_id);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type);
CREATE INDEX IF NOT EXISTS idx_notifications_priority ON notifications(priority);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_notifications_timestamp ON notifications(timestamp);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);

-- Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_notifications_shop_branch_read ON notifications(shop_id, branch_id, is_read);
CREATE INDEX IF NOT EXISTS idx_notifications_shop_branch_type ON notifications(shop_id, branch_id, type);
CREATE INDEX IF NOT EXISTS idx_notifications_shop_branch_priority ON notifications(shop_id, branch_id, priority);
CREATE INDEX IF NOT EXISTS idx_notifications_shop_branch_timestamp ON notifications(shop_id, branch_id, timestamp);

-- Add constraints for valid types and priorities
ALTER TABLE notifications ADD CONSTRAINT check_notification_type 
    CHECK (type IN ('order', 'reservation', 'review', 'system', 'staff', 'inventory', 'payment', 'promotion'));

ALTER TABLE notifications ADD CONSTRAINT check_notification_priority 
    CHECK (priority IN ('low', 'medium', 'high', 'urgent'));

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_notifications_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_notifications_updated_at
    BEFORE UPDATE ON notifications
    FOR EACH ROW
    EXECUTE FUNCTION update_notifications_updated_at();

-- Insert some sample notifications for testing
INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, data) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'New Order Received' as title,
    'Order #12345 has been placed and requires preparation.' as message,
    'order' as type,
    'high' as priority,
    false as is_read,
    '{"order_id": "12345", "amount": 45.99}' as data
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
LIMIT 1;

INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, data) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'Table Reservation' as title,
    'New reservation for 4 people at 7:00 PM tonight.' as message,
    'reservation' as type,
    'medium' as priority,
    false as is_read,
    '{"party_size": 4, "time": "19:00", "table": "T5"}' as data
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
LIMIT 1;

INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, data) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'Low Inventory Alert' as title,
    'Tomatoes are running low. Only 5 units remaining.' as message,
    'inventory' as type,
    'urgent' as priority,
    false as is_read,
    '{"item": "tomatoes", "remaining": 5, "threshold": 10}' as data
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
LIMIT 1;

INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, data) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'New Review Posted' as title,
    'A customer left a 5-star review for your restaurant!' as message,
    'review' as type,
    'low' as priority,
    true as is_read,
    '{"rating": 5, "customer": "John D.", "review_id": "rev_123"}' as data
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
LIMIT 1;

INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, data) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'System Maintenance' as title,
    'Scheduled maintenance will occur tonight from 2:00 AM to 4:00 AM.' as message,
    'system' as type,
    'medium' as priority,
    false as is_read,
    '{"start_time": "02:00", "end_time": "04:00", "date": "2024-12-22"}' as data
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
LIMIT 1;
