-- Migration: Add Analytics and Inventory Management Tables
-- Version: 006
-- Description: Creates tables for analytics metrics and inventory management

-- =====================================================
-- ANALYTICS TABLES
-- =====================================================

-- Sales metrics table
CREATE TABLE IF NOT EXISTS sales_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    branch_id UUID NOT NULL REFERENCES branches(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    hour INTEGER NOT NULL CHECK (hour >= 0 AND hour <= 23),
    metric_type VARCHAR(50) NOT NULL,
    value DECIMAL(15,2) NOT NULL DEFAULT 0,
    count INTEGER NOT NULL DEFAULT 1,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Customer metrics table
CREATE TABLE IF NOT EXISTS customer_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    branch_id UUID NOT NULL REFERENCES branches(id) ON DELETE CASCADE,
    customer_id UUID,
    date DATE NOT NULL,
    visit_count INTEGER NOT NULL DEFAULT 1,
    order_count INTEGER NOT NULL DEFAULT 0,
    total_spent DECIMAL(10,2) NOT NULL DEFAULT 0,
    avg_order_value DECIMAL(10,2) NOT NULL DEFAULT 0,
    last_visit TIMESTAMP WITH TIME ZONE NOT NULL,
    customer_type VARCHAR(20) NOT NULL DEFAULT 'new',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Menu item metrics table
CREATE TABLE IF NOT EXISTS menu_item_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    branch_id UUID NOT NULL REFERENCES branches(id) ON DELETE CASCADE,
    menu_item_id UUID NOT NULL REFERENCES menu_items(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    order_count INTEGER NOT NULL DEFAULT 0,
    quantity_sold INTEGER NOT NULL DEFAULT 0,
    revenue DECIMAL(10,2) NOT NULL DEFAULT 0,
    avg_rating DECIMAL(3,2) NOT NULL DEFAULT 0,
    view_count INTEGER NOT NULL DEFAULT 0,
    conversion_rate DECIMAL(5,2) NOT NULL DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Staff metrics table
CREATE TABLE IF NOT EXISTS staff_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    branch_id UUID NOT NULL REFERENCES branches(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    orders_processed INTEGER NOT NULL DEFAULT 0,
    revenue DECIMAL(10,2) NOT NULL DEFAULT 0,
    hours_worked DECIMAL(5,2) NOT NULL DEFAULT 0,
    avg_order_time DECIMAL(5,2) NOT NULL DEFAULT 0,
    customer_rating DECIMAL(3,2) NOT NULL DEFAULT 0,
    efficiency DECIMAL(5,2) NOT NULL DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table metrics table
CREATE TABLE IF NOT EXISTS table_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    branch_id UUID NOT NULL REFERENCES branches(id) ON DELETE CASCADE,
    table_id UUID NOT NULL REFERENCES tables(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    hour INTEGER NOT NULL CHECK (hour >= 0 AND hour <= 23),
    occupied_minutes INTEGER NOT NULL DEFAULT 0,
    turnover_count INTEGER NOT NULL DEFAULT 0,
    revenue DECIMAL(10,2) NOT NULL DEFAULT 0,
    utilization_rate DECIMAL(5,2) NOT NULL DEFAULT 0,
    avg_party_size DECIMAL(3,1) NOT NULL DEFAULT 0,
    avg_dining_time DECIMAL(5,2) NOT NULL DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Analytics reports table
CREATE TABLE IF NOT EXISTS analytics_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    branch_id UUID NOT NULL REFERENCES branches(id) ON DELETE CASCADE,
    report_type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    data JSONB DEFAULT '{}',
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    generated_by UUID NOT NULL REFERENCES users(id),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- INVENTORY TABLES
-- =====================================================

-- Suppliers table
CREATE TABLE IF NOT EXISTS suppliers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    contact_person VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    payment_terms VARCHAR(100),
    delivery_days INTEGER DEFAULT 3,
    min_order_amount DECIMAL(10,2) DEFAULT 0,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    rating DECIMAL(3,2) DEFAULT 0,
    notes TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Ingredients table
CREATE TABLE IF NOT EXISTS ingredients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    supplier_id UUID REFERENCES suppliers(id),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL,
    unit VARCHAR(20) NOT NULL,
    cost_per_unit DECIMAL(10,4) NOT NULL,
    sku VARCHAR(100) UNIQUE,
    barcode VARCHAR(100),
    min_stock_level DECIMAL(10,2) NOT NULL,
    max_stock_level DECIMAL(10,2) NOT NULL,
    reorder_point DECIMAL(10,2) NOT NULL,
    shelf_life INTEGER DEFAULT 0,
    storage_temp VARCHAR(50),
    is_perishable BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    allergen_info JSONB DEFAULT '[]',
    nutritional_info JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Inventory items table
CREATE TABLE IF NOT EXISTS inventory_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    branch_id UUID NOT NULL REFERENCES branches(id) ON DELETE CASCADE,
    ingredient_id UUID NOT NULL REFERENCES ingredients(id) ON DELETE CASCADE,
    current_stock DECIMAL(10,2) NOT NULL,
    reserved_stock DECIMAL(10,2) DEFAULT 0,
    available_stock DECIMAL(10,2) NOT NULL,
    last_restocked TIMESTAMP WITH TIME ZONE,
    expiry_date TIMESTAMP WITH TIME ZONE,
    batch_number VARCHAR(100),
    location VARCHAR(100),
    status VARCHAR(20) NOT NULL DEFAULT 'available',
    cost_per_unit DECIMAL(10,4) NOT NULL,
    total_value DECIMAL(15,2) NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(branch_id, ingredient_id)
);

-- Menu ingredients table (links menu items to ingredients)
CREATE TABLE IF NOT EXISTS menu_ingredients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    menu_item_id UUID NOT NULL REFERENCES menu_items(id) ON DELETE CASCADE,
    ingredient_id UUID NOT NULL REFERENCES ingredients(id) ON DELETE CASCADE,
    quantity DECIMAL(10,2) NOT NULL,
    unit VARCHAR(20) NOT NULL,
    is_optional BOOLEAN DEFAULT FALSE,
    cost DECIMAL(10,4) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Purchase orders table
CREATE TABLE IF NOT EXISTS purchase_orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    branch_id UUID NOT NULL REFERENCES branches(id) ON DELETE CASCADE,
    supplier_id UUID NOT NULL REFERENCES suppliers(id),
    order_number VARCHAR(100) UNIQUE NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    order_date TIMESTAMP WITH TIME ZONE NOT NULL,
    expected_date TIMESTAMP WITH TIME ZONE,
    received_date TIMESTAMP WITH TIME ZONE,
    subtotal DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    shipping_cost DECIMAL(10,2) DEFAULT 0,
    total DECIMAL(10,2) NOT NULL,
    notes TEXT,
    created_by UUID NOT NULL REFERENCES users(id),
    approved_by UUID REFERENCES users(id),
    received_by UUID REFERENCES users(id),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Purchase order items table
CREATE TABLE IF NOT EXISTS purchase_order_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    purchase_order_id UUID NOT NULL REFERENCES purchase_orders(id) ON DELETE CASCADE,
    ingredient_id UUID NOT NULL REFERENCES ingredients(id),
    quantity DECIMAL(10,2) NOT NULL,
    unit VARCHAR(20) NOT NULL,
    unit_price DECIMAL(10,4) NOT NULL,
    total DECIMAL(10,2) NOT NULL,
    received_qty DECIMAL(10,2) DEFAULT 0,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Stock movements table
CREATE TABLE IF NOT EXISTS stock_movements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    branch_id UUID NOT NULL REFERENCES branches(id) ON DELETE CASCADE,
    ingredient_id UUID NOT NULL REFERENCES ingredients(id),
    movement_type VARCHAR(20) NOT NULL,
    quantity DECIMAL(10,2) NOT NULL,
    unit VARCHAR(20) NOT NULL,
    reason VARCHAR(100) NOT NULL,
    reference VARCHAR(100),
    reference_type VARCHAR(50),
    cost_per_unit DECIMAL(10,4) NOT NULL,
    total_cost DECIMAL(10,2) NOT NULL,
    previous_stock DECIMAL(10,2) NOT NULL,
    new_stock DECIMAL(10,2) NOT NULL,
    performed_by UUID NOT NULL REFERENCES users(id),
    notes TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Waste records table
CREATE TABLE IF NOT EXISTS waste_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    branch_id UUID NOT NULL REFERENCES branches(id) ON DELETE CASCADE,
    ingredient_id UUID NOT NULL REFERENCES ingredients(id),
    quantity DECIMAL(10,2) NOT NULL,
    unit VARCHAR(20) NOT NULL,
    reason VARCHAR(100) NOT NULL,
    cost DECIMAL(10,2) NOT NULL,
    recorded_by UUID NOT NULL REFERENCES users(id),
    waste_date TIMESTAMP WITH TIME ZONE NOT NULL,
    notes TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Analytics indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sales_metrics_branch_date_hour ON sales_metrics(branch_id, date, hour);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sales_metrics_type ON sales_metrics(metric_type);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_metrics_branch_date ON customer_metrics(branch_id, date);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_metrics_customer ON customer_metrics(customer_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_menu_item_metrics_branch_date ON menu_item_metrics(branch_id, date);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_menu_item_metrics_item ON menu_item_metrics(menu_item_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_staff_metrics_branch_date ON staff_metrics(branch_id, date);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_staff_metrics_user ON staff_metrics(user_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_table_metrics_branch_date_hour ON table_metrics(branch_id, date, hour);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_table_metrics_table ON table_metrics(table_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_analytics_reports_branch_type ON analytics_reports(branch_id, report_type);

-- Inventory indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_suppliers_status ON suppliers(status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ingredients_category ON ingredients(category);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ingredients_supplier ON ingredients(supplier_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ingredients_active ON ingredients(is_active);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_inventory_items_branch ON inventory_items(branch_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_inventory_items_ingredient ON inventory_items(ingredient_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_inventory_items_status ON inventory_items(status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_inventory_items_expiry ON inventory_items(expiry_date) WHERE expiry_date IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_menu_ingredients_menu_item ON menu_ingredients(menu_item_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_menu_ingredients_ingredient ON menu_ingredients(ingredient_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_purchase_orders_branch_status ON purchase_orders(branch_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_purchase_orders_date ON purchase_orders(order_date);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_purchase_order_items_po ON purchase_order_items(purchase_order_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_stock_movements_branch_ingredient ON stock_movements(branch_id, ingredient_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_stock_movements_type ON stock_movements(movement_type);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_stock_movements_date ON stock_movements(created_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_waste_records_branch_date ON waste_records(branch_id, waste_date);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_waste_records_ingredient ON waste_records(ingredient_id);

-- =====================================================
-- TRIGGERS FOR UPDATED_AT
-- =====================================================

-- Analytics triggers
CREATE TRIGGER update_sales_metrics_updated_at BEFORE UPDATE ON sales_metrics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_metrics_updated_at BEFORE UPDATE ON customer_metrics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_menu_item_metrics_updated_at BEFORE UPDATE ON menu_item_metrics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_staff_metrics_updated_at BEFORE UPDATE ON staff_metrics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_table_metrics_updated_at BEFORE UPDATE ON table_metrics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_analytics_reports_updated_at BEFORE UPDATE ON analytics_reports FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Inventory triggers
CREATE TRIGGER update_suppliers_updated_at BEFORE UPDATE ON suppliers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ingredients_updated_at BEFORE UPDATE ON ingredients FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_inventory_items_updated_at BEFORE UPDATE ON inventory_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_menu_ingredients_updated_at BEFORE UPDATE ON menu_ingredients FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_purchase_orders_updated_at BEFORE UPDATE ON purchase_orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_purchase_order_items_updated_at BEFORE UPDATE ON purchase_order_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_stock_movements_updated_at BEFORE UPDATE ON stock_movements FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_waste_records_updated_at BEFORE UPDATE ON waste_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
