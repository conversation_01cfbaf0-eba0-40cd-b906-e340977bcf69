version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: restaurant_postgres
    environment:
      POSTGRES_DB: restaurant_db
      POSTGRES_USER: restaurant_user
      POSTGRES_PASSWORD: restaurant_pass
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - restaurant_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U restaurant_user -d restaurant_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: restaurant_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - restaurant_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: restaurant_api
    ports:
      - "8080:8080"
    environment:
      - ENV=development
      - PORT=8080
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=restaurant_db
      - DB_USER=restaurant_user
      - DB_PASSWORD=restaurant_pass
      - DB_SSL_MODE=disable
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - JWT_EXPIRES_IN=24h
      - UPLOAD_PATH=/app/uploads
      - LOG_LEVEL=debug
      - CORS_ALLOWED_ORIGINS=http://localhost:3000
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - restaurant_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Optional: Database administration tool
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: restaurant_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - restaurant_network
    profiles:
      - tools

  # Optional: Redis administration tool
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: restaurant_redis_commander
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - restaurant_network
    profiles:
      - tools

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  restaurant_network:
    driver: bridge
