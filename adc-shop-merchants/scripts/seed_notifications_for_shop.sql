-- Seed notifications for specific shop and branch by slug
-- This script creates one notification for each type and priority combination

-- Get the shop and branch IDs for weerawat-poseeya/posriya
WITH shop_branch_info AS (
    SELECT
        s.id as shop_id,
        sb.id as branch_id
    FROM shops s
    JOIN shop_branches sb ON s.id = sb.shop_id
    WHERE s.slug = 'weerawat-poseeya'
    AND sb.slug = 'posriya'
    LIMIT 1
)

-- Insert notifications for each type
INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp)
SELECT
    sbi.shop_id,
    sbi.branch_id,
    'New Order Received',
    'Order #ORD-2024-001 has been placed and requires preparation. Customer: <PERSON>, Total: $45.99',
    'order',
    'high',
    false,
    '/orders/ORD-2024-001',
    'View Order',
    '{"order_id": "ORD-2024-001", "customer_name": "<PERSON>", "amount": 45.99, "items_count": 3}'::jsonb,
    NOW() - INTERVAL '5 minutes'
FROM shop_branch_info sbi

UNION ALL

SELECT
    sbi.shop_id,
    sbi.branch_id,
    'Order Ready for Pickup',
    'Order #ORD-2024-002 is ready for pickup. Customer has been notified.',
    'order',
    'medium',
    false,
    '/orders/ORD-2024-002',
    'Mark as Collected',
    '{"order_id": "ORD-2024-002", "customer": "Sarah Wilson", "pickup_time": "18:30"}',
    NOW() - INTERVAL '15 minutes'
FROM shop_branch_info sbi

UNION ALL

SELECT
    sbi.shop_id,
    sbi.branch_id,
    'Table Reservation Confirmed',
    'New reservation for 4 people at 7:00 PM tonight. Table 5 has been reserved for the Smith family.',
    'reservation',
    'medium',
    false,
    '/reservations/RES-2024-001',
    'View Reservation',
    '{"reservation_id": "RES-2024-001", "party_size": 4, "time": "19:00", "table": "T5", "customer": "Smith Family"}',
    NOW() - INTERVAL '25 minutes'
FROM shop_branch_info sbi

UNION ALL

SELECT
    sbi.shop_id,
    sbi.branch_id,
    'Reservation Reminder',
    'Upcoming reservation in 30 minutes. Table 3 for Johnson party of 6.',
    'reservation',
    'low',
    true,
    '/reservations/RES-2024-002',
    'View Details',
    '{"reservation_id": "RES-2024-002", "party_size": 6, "time": "20:00", "table": "T3", "customer": "Johnson Family"}',
    NOW() - INTERVAL '2 hours'
FROM shop_branch_info sbi

UNION ALL

SELECT
    sbi.shop_id,
    sbi.branch_id,
    'New Customer Review',
    'Emma Thompson left a 5-star review: "Amazing food and excellent service!"',
    'review',
    'low',
    false,
    '/reviews/REV-2024-001',
    'View Review',
    '{"review_id": "REV-2024-001", "customer_name": "Emma Thompson", "rating": 5, "comment": "Amazing food and excellent service!"}',
    NOW() - INTERVAL '1 hour'
FROM shop_branch_info sbi

UNION ALL

SELECT
    sbi.shop_id,
    sbi.branch_id,
    'Review Response Needed',
    'Mike Davis left a 2-star review that requires your attention and response.',
    'review',
    'high',
    false,
    '/reviews/REV-2024-002',
    'Respond Now',
    '{"review_id": "REV-2024-002", "customer_name": "Mike Davis", "rating": 2, "requires_response": true}',
    NOW() - INTERVAL '3 hours'
FROM shop_branch_info sbi

UNION ALL

SELECT
    sbi.shop_id,
    sbi.branch_id,
    'Critical: Equipment Malfunction',
    'Kitchen freezer temperature is rising. Current temperature: 5°C. Immediate attention required!',
    'system',
    'urgent',
    false,
    '/equipment/freezer-01',
    'Check Equipment',
    '{"equipment": "freezer-01", "current_temp": 5, "normal_temp": -18, "alert_level": "critical"}',
    NOW() - INTERVAL '10 minutes'
FROM shop_branch_info sbi

UNION ALL

SELECT
    sbi.shop_id,
    sbi.branch_id,
    'System Maintenance Scheduled',
    'Scheduled maintenance will occur tonight from 2:00 AM to 4:00 AM. POS system may be temporarily unavailable.',
    'system',
    'medium',
    true,
    '/system/maintenance',
    'View Details',
    '{"start_time": "02:00", "end_time": "04:00", "date": "2024-12-22", "affected_systems": ["POS", "Inventory"]}',
    NOW() - INTERVAL '4 hours'
FROM shop_branch_info sbi

UNION ALL

SELECT
    sbi.shop_id,
    sbi.branch_id,
    'Staff Schedule Update',
    'Maria Garcia has requested a shift change for tomorrow. Please review and approve.',
    'staff',
    'medium',
    false,
    '/staff/schedule',
    'Review Schedule',
    '{"staff_member": "Maria Garcia", "requested_date": "2024-12-22", "shift_type": "evening", "reason": "family event"}',
    NOW() - INTERVAL '45 minutes'
FROM shop_branch_info sbi

UNION ALL

SELECT
    sbi.shop_id,
    sbi.branch_id,
    'Staff Time-Off Request',
    'David Wilson requested time off for next Friday. Approval needed.',
    'staff',
    'low',
    true,
    '/staff/requests',
    'Review Request',
    '{"staff_member": "David Wilson", "request_type": "time_off", "date": "2024-12-29", "reason": "personal"}',
    NOW() - INTERVAL '6 hours'
FROM shop_branch_info sbi

UNION ALL

SELECT
    sbi.shop_id,
    sbi.branch_id,
    'Critical: Low Inventory Alert',
    'Tomatoes are running low. Only 5 units remaining. Please restock immediately!',
    'inventory',
    'urgent',
    false,
    '/inventory/tomatoes',
    'Restock Now',
    '{"item": "tomatoes", "remaining": 5, "threshold": 10, "supplier": "Fresh Produce Co."}',
    NOW() - INTERVAL '30 minutes'
FROM shop_branch_info sbi

UNION ALL

SELECT
    sbi.shop_id,
    sbi.branch_id,
    'Inventory Restock Reminder',
    'Weekly inventory check due. Several items need restocking.',
    'inventory',
    'medium',
    true,
    '/inventory/dashboard',
    'View Inventory',
    '{"items_low": 8, "items_critical": 2, "last_check": "2024-12-15"}',
    NOW() - INTERVAL '8 hours'
FROM shop_branch_info sbi

UNION ALL

SELECT
    sbi.shop_id,
    sbi.branch_id,
    'Payment Processing Failed',
    'Payment for Order #ORD-2024-003 failed. Customer payment method was declined.',
    'payment',
    'high',
    false,
    '/orders/ORD-2024-003',
    'Contact Customer',
    '{"order_id": "ORD-2024-003", "amount": 32.50, "payment_method": "Credit Card", "error": "Card Declined"}',
    NOW() - INTERVAL '20 minutes'
FROM shop_branch_info sbi

UNION ALL

SELECT
    sbi.shop_id,
    sbi.branch_id,
    'Payment Refund Processed',
    'Refund of $28.75 has been processed for Order #ORD-2024-004.',
    'payment',
    'low',
    true,
    '/orders/ORD-2024-004',
    'View Details',
    '{"order_id": "ORD-2024-004", "refund_amount": 28.75, "reason": "customer_request"}',
    NOW() - INTERVAL '5 hours'
FROM shop_branch_info sbi

UNION ALL

SELECT
    sbi.shop_id,
    sbi.branch_id,
    'Holiday Promotion Active',
    'Your Christmas special promotion is now live! 20% off all desserts until December 25th.',
    'promotion',
    'low',
    false,
    '/promotions/christmas-2024',
    'View Promotion',
    '{"promotion_id": "christmas-2024", "discount": 20, "category": "desserts", "end_date": "2024-12-25"}',
    NOW() - INTERVAL '1 hour'
FROM shop_branch_info sbi

UNION ALL

SELECT
    sbi.shop_id,
    sbi.branch_id,
    'Flash Sale Starting Soon',
    'Your flash sale starts in 15 minutes! 30% off lunch specials for the next 2 hours.',
    'promotion',
    'medium',
    false,
    '/promotions/flash-sale-lunch',
    'Activate Sale',
    '{"promotion_id": "flash-sale-lunch", "discount": 30, "duration": "2 hours", "category": "lunch_specials"}',
    NOW() - INTERVAL '5 minutes'
FROM shop_branch_info sbi;
