#!/bin/bash

# Migration script to move existing code to separate applications
# This script copies and adapts existing code to the new structure

set -e

echo "🔄 Starting code migration..."

# Check if separate deployment structure exists
if [ ! -d "shop-management-app" ] || [ ! -d "customer-website" ] || [ ! -d "admin-platform" ]; then
    echo "❌ Separate deployment structure not found. Run setup-separate-deployments.sh first."
    exit 1
fi

# Function to copy and adapt files
copy_and_adapt() {
    local source_path=$1
    local dest_path=$2
    local app_name=$3
    
    if [ -f "$source_path" ] || [ -d "$source_path" ]; then
        echo "📁 Copying $source_path to $dest_path"
        cp -r "$source_path" "$dest_path"
        
        # Update import paths in TypeScript/JavaScript files
        find "$dest_path" -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | while read file; do
            # Update relative imports to use shared packages
            sed -i.bak 's|@/lib/types|@/shared/types|g' "$file"
            sed -i.bak 's|@/lib/utils|@/shared/utils|g' "$file"
            sed -i.bak 's|@/components/ui|@/shared/ui-components|g' "$file"
            
            # Remove backup files
            rm -f "${file}.bak"
        done
    else
        echo "⚠️  Source path $source_path not found, skipping..."
    fi
}

# Migrate Shop Management App
echo "🏪 Migrating Shop Management App..."

# Copy app routes (restaurant management)
copy_and_adapt "src/app/[locale]/app" "shop-management-app/src/app" "shop-management"

# Copy restaurant-specific components
copy_and_adapt "src/components/restaurant" "shop-management-app/src/components/restaurant" "shop-management"
copy_and_adapt "src/components/navigation" "shop-management-app/src/components/navigation" "shop-management"
copy_and_adapt "src/components/settings" "shop-management-app/src/components/settings" "shop-management"

# Copy hooks used by shop management
copy_and_adapt "src/hooks/useMenu.ts" "shop-management-app/src/hooks/useMenu.ts" "shop-management"
copy_and_adapt "src/hooks/useOrders.ts" "shop-management-app/src/hooks/useOrders.ts" "shop-management"
copy_and_adapt "src/hooks/useTables.ts" "shop-management-app/src/hooks/useTables.ts" "shop-management"
copy_and_adapt "src/hooks/useStaff.ts" "shop-management-app/src/hooks/useStaff.ts" "shop-management"
copy_and_adapt "src/hooks/useReservations.ts" "shop-management-app/src/hooks/useReservations.ts" "shop-management"
copy_and_adapt "src/hooks/useDashboardStats.ts" "shop-management-app/src/hooks/useDashboardStats.ts" "shop-management"

# Copy Redux store configuration
copy_and_adapt "src/lib/redux" "shop-management-app/src/lib/redux" "shop-management"

# Copy authentication configuration
copy_and_adapt "src/lib/auth.ts" "shop-management-app/src/lib/auth.ts" "shop-management"

# Migrate Admin Platform
echo "👨‍💼 Migrating Admin Platform..."

# Copy admin routes
copy_and_adapt "src/app/[locale]/admin" "admin-platform/src/app" "admin-platform"

# Copy admin-specific components
copy_and_adapt "src/components/admin" "admin-platform/src/components/admin" "admin-platform"

# Migrate Customer Website (create basic structure)
echo "🛒 Setting up Customer Website structure..."

# Create basic customer pages
mkdir -p customer-website/src/app/\[locale\]
mkdir -p customer-website/src/app/\[locale\]/restaurants
mkdir -p customer-website/src/app/\[locale\]/restaurants/\[slug\]
mkdir -p customer-website/src/app/\[locale\]/order
mkdir -p customer-website/src/app/\[locale\]/profile

# Copy authentication pages
copy_and_adapt "src/app/[locale]/login" "customer-website/src/app/[locale]/login" "customer-website"
copy_and_adapt "src/app/[locale]/register" "customer-website/src/app/[locale]/register" "customer-website"
copy_and_adapt "src/app/[locale]/auth" "customer-website/src/app/[locale]/auth" "customer-website"

# Migrate Shared Components
echo "📦 Migrating shared components..."

# Copy UI components to shared package
copy_and_adapt "src/components/ui" "shared-packages/ui-components" "shared"

# Copy common components
copy_and_adapt "src/components/common" "shared-packages/ui-components/common" "shared"

# Copy utilities to shared package
copy_and_adapt "src/lib/utils.ts" "shared-packages/utils/index.ts" "shared"
copy_and_adapt "src/lib/constants" "shared-packages/utils/constants" "shared"

# Copy types to shared package
copy_and_adapt "src/lib/types" "shared-packages/types" "shared"

# Create API client in shared package
mkdir -p shared-packages/api-client
cat > shared-packages/api-client/index.ts << 'EOF'
// Shared API client configuration

export const API_BASE_URL = process.env.BACKEND_URL || 'http://localhost:8080';

export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export class ApiClient {
  private baseUrl: string;
  private token?: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  setToken(token: string) {
    this.token = token;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      ...(this.token && { Authorization: `Bearer ${this.token}` }),
      ...options.headers,
    };

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.statusText}`);
    }

    return response.json();
  }

  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

export const apiClient = new ApiClient();
EOF

# Create package.json for shared API client
cat > shared-packages/api-client/package.json << 'EOF'
{
  "name": "@shared/api-client",
  "version": "0.1.0",
  "main": "index.ts",
  "types": "index.ts"
}
EOF

# Update backend CORS configuration
echo "🔧 Updating backend CORS configuration..."

# Create a patch file for the backend CORS configuration
cat > restaurant-backend/cors-update.patch << 'EOF'
--- a/internal/api/routes/routes.go
+++ b/internal/api/routes/routes.go
@@ -170,7 +170,11 @@ func setupMiddleware(router *gin.Engine, cfg *config.Config, logger *logrus.Log
 
 	// CORS middleware
 	router.Use(cors.New(cors.Config{
-		AllowOrigins:     cfg.CORS.AllowedOrigins,
+		AllowOrigins: []string{
+			"http://localhost:3001", // Shop Management App
+			"http://localhost:3002", // Customer Website
+			"http://localhost:3003", // Admin Platform
+		},
 		AllowMethods:     cfg.CORS.AllowedMethods,
 		AllowHeaders:     cfg.CORS.AllowedHeaders,
 		ExposeHeaders:    []string{"Content-Length"},
EOF

echo "✅ Migration complete!"
echo ""
echo "📋 Next steps:"
echo "1. Install dependencies in each application:"
echo "   cd shop-management-app && npm install"
echo "   cd customer-website && npm install"
echo "   cd admin-platform && npm install"
echo ""
echo "2. Update backend CORS configuration:"
echo "   Apply the CORS patch or manually update allowed origins"
echo ""
echo "3. Test each application:"
echo "   npm run dev (in each app directory)"
echo ""
echo "4. Update environment variables for each app"
echo "5. Customize authentication flows for each user type"
echo ""
echo "⚠️  Manual tasks required:"
echo "- Review and adapt copied components for each app's specific needs"
echo "- Update routing and navigation for each application"
echo "- Configure authentication providers for each app"
echo "- Test API integration with the shared backend"
