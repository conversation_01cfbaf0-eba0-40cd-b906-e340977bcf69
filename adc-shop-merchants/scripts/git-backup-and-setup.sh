#!/bin/bash

# Git backup and deployment separation setup script
# This script commits existing work and then sets up separate deployments

set -e

echo "🔒 Git Backup and Deployment Separation Setup"
echo "=============================================="

# Check if we're in a git repository
if [ ! -d ".git" ]; then
    echo "❌ Not in a git repository. Initializing git..."
    git init
    echo "✅ Git repository initialized"
fi

# Check git status
echo "📊 Checking current git status..."
git status

# Check if there are any uncommitted changes
if ! git diff-index --quiet HEAD -- 2>/dev/null; then
    echo "⚠️  You have uncommitted changes. Let's commit them first."
    
    # Show what will be committed
    echo "📋 Files to be committed:"
    git status --porcelain
    
    # Ask for confirmation
    read -p "Do you want to commit all changes? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # Add all files
        echo "📦 Adding all files to git..."
        git add .
        
        # Create commit message with timestamp
        TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
        COMMIT_MESSAGE="Pre-deployment separation backup - $TIMESTAMP

This commit contains the complete working state before separating into:
- Shop Management App
- Customer Website  
- Admin Platform

All existing functionality is preserved in this commit."

        # Commit changes
        echo "💾 Committing changes..."
        git commit -m "$COMMIT_MESSAGE"
        echo "✅ Changes committed successfully"
    else
        echo "❌ Aborting. Please commit your changes manually first."
        exit 1
    fi
else
    echo "✅ Working directory is clean"
fi

# Create a backup branch
BACKUP_BRANCH="backup-before-separation-$(date '+%Y%m%d-%H%M%S')"
echo "🌿 Creating backup branch: $BACKUP_BRANCH"
git checkout -b "$BACKUP_BRANCH"
git checkout main 2>/dev/null || git checkout master 2>/dev/null || echo "Staying on current branch"

echo "✅ Backup branch created: $BACKUP_BRANCH"

# Show current commit
echo "📍 Current commit:"
git log --oneline -1

# Create a tag for this state
TAG_NAME="pre-separation-backup-$(date '+%Y%m%d-%H%M%S')"
echo "🏷️  Creating tag: $TAG_NAME"
git tag -a "$TAG_NAME" -m "Backup before deployment separation - $(date '+%Y-%m-%d %H:%M:%S')"

echo "✅ Tag created: $TAG_NAME"

# Show git log
echo "📚 Recent commits:"
git log --oneline -5

echo ""
echo "🔒 BACKUP COMPLETE!"
echo "==================="
echo "✅ All changes committed"
echo "✅ Backup branch created: $BACKUP_BRANCH"
echo "✅ Backup tag created: $TAG_NAME"
echo ""
echo "🔄 You can restore this state anytime with:"
echo "   git checkout $TAG_NAME"
echo "   or"
echo "   git checkout $BACKUP_BRANCH"
echo ""

# Ask if user wants to proceed with separation setup
read -p "🚀 Do you want to proceed with deployment separation setup? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 Starting deployment separation setup..."
    echo ""
    
    # Make scripts executable
    echo "🔧 Making scripts executable..."
    chmod +x scripts/setup-separate-deployments.sh 2>/dev/null || echo "setup-separate-deployments.sh not found yet"
    chmod +x scripts/migrate-existing-code.sh 2>/dev/null || echo "migrate-existing-code.sh not found yet"
    chmod +x scripts/create-env-configs.sh 2>/dev/null || echo "create-env-configs.sh not found yet"
    
    # Check if separation scripts exist
    if [ -f "scripts/setup-separate-deployments.sh" ]; then
        echo "📁 Setting up project structure..."
        ./scripts/setup-separate-deployments.sh
        
        if [ -f "scripts/migrate-existing-code.sh" ]; then
            echo "🔄 Migrating existing code..."
            ./scripts/migrate-existing-code.sh
        fi
        
        if [ -f "scripts/create-env-configs.sh" ]; then
            echo "⚙️  Creating environment configurations..."
            ./scripts/create-env-configs.sh
        fi
        
        echo ""
        echo "✅ DEPLOYMENT SEPARATION COMPLETE!"
        echo "=================================="
        echo ""
        echo "📁 New structure created:"
        echo "├── shop-management-app/     (localhost:3001)"
        echo "├── customer-website/        (localhost:3002)"
        echo "├── admin-platform/          (localhost:3003)"
        echo "├── shared-packages/         (shared components)"
        echo "└── deployment/              (Docker configs)"
        echo ""
        echo "🔄 Next steps:"
        echo "1. Install dependencies:"
        echo "   cd shop-management-app && npm install && cd .."
        echo "   cd customer-website && npm install && cd .."
        echo "   cd admin-platform && npm install && cd .."
        echo ""
        echo "2. Update backend CORS configuration"
        echo "3. Start development servers:"
        echo "   ./scripts/dev-all.sh"
        echo ""
        echo "🔒 Your original code is safely backed up in:"
        echo "   Branch: $BACKUP_BRANCH"
        echo "   Tag: $TAG_NAME"
        
    else
        echo "❌ Separation scripts not found. Please run the individual setup scripts manually."
        echo ""
        echo "📋 Manual steps:"
        echo "1. Run: ./scripts/setup-separate-deployments.sh"
        echo "2. Run: ./scripts/migrate-existing-code.sh"
        echo "3. Run: ./scripts/create-env-configs.sh"
    fi
    
else
    echo "⏸️  Setup paused. Your code is safely backed up."
    echo "   Run the separation scripts when you're ready:"
    echo "   ./scripts/setup-separate-deployments.sh"
fi

echo ""
echo "🎉 Git backup completed successfully!"
echo "   You can always restore with: git checkout $TAG_NAME"
