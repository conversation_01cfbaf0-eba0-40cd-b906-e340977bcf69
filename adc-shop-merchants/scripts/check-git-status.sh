#!/bin/bash

# Git status check script
# This script shows you what will be committed before running the backup

echo "🔍 Git Status Check"
echo "==================="

# Check if we're in a git repository
if [ ! -d ".git" ]; then
    echo "❌ Not in a git repository"
    echo "   The backup script will initialize git for you"
    echo ""
    exit 0
fi

echo "📊 Current git status:"
echo ""

# Show current branch
echo "🌿 Current branch:"
git branch --show-current 2>/dev/null || echo "Unable to determine current branch"
echo ""

# Show recent commits
echo "📚 Recent commits:"
git log --oneline -5 2>/dev/null || echo "No commits yet"
echo ""

# Show uncommitted changes
echo "📝 Uncommitted changes:"
if git diff-index --quiet HEAD -- 2>/dev/null; then
    echo "✅ No uncommitted changes - working directory is clean"
else
    echo "⚠️  You have uncommitted changes:"
    echo ""
    
    # Show staged changes
    if ! git diff-index --quiet --cached HEAD -- 2>/dev/null; then
        echo "📦 Staged changes (ready to commit):"
        git diff --cached --name-status 2>/dev/null || echo "Unable to show staged changes"
        echo ""
    fi
    
    # Show unstaged changes
    if ! git diff-files --quiet 2>/dev/null; then
        echo "📝 Unstaged changes (need to be added):"
        git diff --name-status 2>/dev/null || echo "Unable to show unstaged changes"
        echo ""
    fi
    
    # Show untracked files
    if [ -n "$(git ls-files --others --exclude-standard 2>/dev/null)" ]; then
        echo "❓ Untracked files:"
        git ls-files --others --exclude-standard 2>/dev/null || echo "Unable to show untracked files"
        echo ""
    fi
fi

# Show what would be committed if we run git add .
echo "📋 Files that would be committed with 'git add .':"
git status --porcelain 2>/dev/null | head -20
if [ $(git status --porcelain 2>/dev/null | wc -l) -gt 20 ]; then
    echo "... and $(( $(git status --porcelain 2>/dev/null | wc -l) - 20 )) more files"
fi
echo ""

# Check for large files
echo "📏 Checking for large files (>10MB):"
find . -type f -size +10M -not -path "./.git/*" -not -path "./node_modules/*" 2>/dev/null | head -10
echo ""

# Show repository size
echo "💾 Repository information:"
if [ -d ".git" ]; then
    echo "Repository size: $(du -sh .git 2>/dev/null | cut -f1 || echo "Unknown")"
fi
echo "Working directory size: $(du -sh . --exclude=.git --exclude=node_modules 2>/dev/null | cut -f1 || echo "Unknown")"
echo ""

echo "✅ Git status check complete!"
echo ""
echo "🚀 Ready to run backup? Execute:"
echo "   chmod +x scripts/git-backup-and-setup.sh"
echo "   ./scripts/git-backup-and-setup.sh"
