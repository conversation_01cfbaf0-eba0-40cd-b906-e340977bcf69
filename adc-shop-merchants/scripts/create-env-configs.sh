#!/bin/bash

# Create environment configuration files for each application

set -e

echo "🔧 Creating environment configurations..."

# Shop Management App Environment
echo "🏪 Creating Shop Management App environment..."
cat > shop-management-app/.env.local << 'EOF'
# Shop Management App Environment Configuration

# Application
NEXT_PUBLIC_APP_NAME="Shop Management"
NEXT_PUBLIC_APP_URL=http://localhost:3001

# Backend API
BACKEND_URL=http://localhost:8080
NEXT_PUBLIC_API_URL=http://localhost:8080/api/v1

# Authentication
NEXTAUTH_URL=http://localhost:3001
NEXTAUTH_SECRET=your-shop-management-secret-key-here
NEXT_PUBLIC_AUTH_PROVIDER=credentials

# Database (if needed for direct access)
DATABASE_URL=your-database-url-here

# File Storage
NEXT_PUBLIC_STORAGE_URL=http://localhost:8080/storage

# Features
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_AI_FEATURES=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true

# OAuth Providers (for shop owners)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Stripe (for payment processing)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_SECRET_KEY=your-stripe-secret-key

# Redis (for caching)
REDIS_URL=redis://localhost:6379
EOF

cat > shop-management-app/.env.example << 'EOF'
# Shop Management App Environment Configuration

# Application
NEXT_PUBLIC_APP_NAME="Shop Management"
NEXT_PUBLIC_APP_URL=http://localhost:3001

# Backend API
BACKEND_URL=http://localhost:8080
NEXT_PUBLIC_API_URL=http://localhost:8080/api/v1

# Authentication
NEXTAUTH_URL=http://localhost:3001
NEXTAUTH_SECRET=your-shop-management-secret-key-here
NEXT_PUBLIC_AUTH_PROVIDER=credentials

# Database
DATABASE_URL=your-database-url-here

# File Storage
NEXT_PUBLIC_STORAGE_URL=http://localhost:8080/storage

# Features
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_AI_FEATURES=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true

# OAuth Providers
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Stripe
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_SECRET_KEY=your-stripe-secret-key

# Redis
REDIS_URL=redis://localhost:6379
EOF

# Customer Website Environment
echo "🛒 Creating Customer Website environment..."
cat > customer-website/.env.local << 'EOF'
# Customer Website Environment Configuration

# Application
NEXT_PUBLIC_APP_NAME="Customer Portal"
NEXT_PUBLIC_APP_URL=http://localhost:3002

# Backend API
BACKEND_URL=http://localhost:8080
NEXT_PUBLIC_API_URL=http://localhost:8080/api/v1

# Authentication
NEXTAUTH_URL=http://localhost:3002
NEXTAUTH_SECRET=your-customer-website-secret-key-here
NEXT_PUBLIC_AUTH_PROVIDER=credentials

# Database (if needed for direct access)
DATABASE_URL=your-database-url-here

# File Storage
NEXT_PUBLIC_STORAGE_URL=http://localhost:8080/storage

# Features
NEXT_PUBLIC_ENABLE_REVIEWS=true
NEXT_PUBLIC_ENABLE_RESERVATIONS=true
NEXT_PUBLIC_ENABLE_ORDERING=true
NEXT_PUBLIC_ENABLE_LOYALTY=true

# OAuth Providers (for customers)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
FACEBOOK_CLIENT_ID=your-facebook-client-id
FACEBOOK_CLIENT_SECRET=your-facebook-client-secret

# Payment Processing
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_SECRET_KEY=your-stripe-secret-key

# Maps and Location
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=your-google-analytics-id

# SEO
NEXT_PUBLIC_SITE_NAME="Restaurant Platform"
NEXT_PUBLIC_SITE_DESCRIPTION="Discover and order from local restaurants"
EOF

cat > customer-website/.env.example << 'EOF'
# Customer Website Environment Configuration

# Application
NEXT_PUBLIC_APP_NAME="Customer Portal"
NEXT_PUBLIC_APP_URL=http://localhost:3002

# Backend API
BACKEND_URL=http://localhost:8080
NEXT_PUBLIC_API_URL=http://localhost:8080/api/v1

# Authentication
NEXTAUTH_URL=http://localhost:3002
NEXTAUTH_SECRET=your-customer-website-secret-key-here
NEXT_PUBLIC_AUTH_PROVIDER=credentials

# Database
DATABASE_URL=your-database-url-here

# File Storage
NEXT_PUBLIC_STORAGE_URL=http://localhost:8080/storage

# Features
NEXT_PUBLIC_ENABLE_REVIEWS=true
NEXT_PUBLIC_ENABLE_RESERVATIONS=true
NEXT_PUBLIC_ENABLE_ORDERING=true
NEXT_PUBLIC_ENABLE_LOYALTY=true

# OAuth Providers
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
FACEBOOK_CLIENT_ID=your-facebook-client-id
FACEBOOK_CLIENT_SECRET=your-facebook-client-secret

# Payment Processing
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_SECRET_KEY=your-stripe-secret-key

# Maps and Location
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=your-google-analytics-id

# SEO
NEXT_PUBLIC_SITE_NAME="Restaurant Platform"
NEXT_PUBLIC_SITE_DESCRIPTION="Discover and order from local restaurants"
EOF

# Admin Platform Environment
echo "👨‍💼 Creating Admin Platform environment..."
cat > admin-platform/.env.local << 'EOF'
# Admin Platform Environment Configuration

# Application
NEXT_PUBLIC_APP_NAME="Admin Platform"
NEXT_PUBLIC_APP_URL=http://localhost:3003

# Backend API
BACKEND_URL=http://localhost:8080
NEXT_PUBLIC_API_URL=http://localhost:8080/api/v1

# Authentication
NEXTAUTH_URL=http://localhost:3003
NEXTAUTH_SECRET=your-admin-platform-secret-key-here
NEXT_PUBLIC_AUTH_PROVIDER=credentials

# Database (if needed for direct access)
DATABASE_URL=your-database-url-here

# File Storage
NEXT_PUBLIC_STORAGE_URL=http://localhost:8080/storage

# Features
NEXT_PUBLIC_ENABLE_SYSTEM_ANALYTICS=true
NEXT_PUBLIC_ENABLE_USER_MANAGEMENT=true
NEXT_PUBLIC_ENABLE_PLATFORM_SETTINGS=true

# OAuth Providers (for admins)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# System Monitoring
NEXT_PUBLIC_ENABLE_MONITORING=true
MONITORING_API_KEY=your-monitoring-api-key

# Email Service (for admin notifications)
SMTP_HOST=your-smtp-host
SMTP_PORT=587
SMTP_USER=your-smtp-user
SMTP_PASS=your-smtp-password

# Security
ADMIN_ACCESS_KEY=your-admin-access-key
EOF

cat > admin-platform/.env.example << 'EOF'
# Admin Platform Environment Configuration

# Application
NEXT_PUBLIC_APP_NAME="Admin Platform"
NEXT_PUBLIC_APP_URL=http://localhost:3003

# Backend API
BACKEND_URL=http://localhost:8080
NEXT_PUBLIC_API_URL=http://localhost:8080/api/v1

# Authentication
NEXTAUTH_URL=http://localhost:3003
NEXTAUTH_SECRET=your-admin-platform-secret-key-here
NEXT_PUBLIC_AUTH_PROVIDER=credentials

# Database
DATABASE_URL=your-database-url-here

# File Storage
NEXT_PUBLIC_STORAGE_URL=http://localhost:8080/storage

# Features
NEXT_PUBLIC_ENABLE_SYSTEM_ANALYTICS=true
NEXT_PUBLIC_ENABLE_USER_MANAGEMENT=true
NEXT_PUBLIC_ENABLE_PLATFORM_SETTINGS=true

# OAuth Providers
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# System Monitoring
NEXT_PUBLIC_ENABLE_MONITORING=true
MONITORING_API_KEY=your-monitoring-api-key

# Email Service
SMTP_HOST=your-smtp-host
SMTP_PORT=587
SMTP_USER=your-smtp-user
SMTP_PASS=your-smtp-password

# Security
ADMIN_ACCESS_KEY=your-admin-access-key
EOF

# Create development script
echo "🚀 Creating development script..."
cat > scripts/dev-all.sh << 'EOF'
#!/bin/bash

# Development script to run all applications

echo "🚀 Starting all development servers..."

# Function to run command in background and track PID
run_bg() {
    local name=$1
    local command=$2
    local dir=$3
    
    echo "Starting $name..."
    cd "$dir"
    $command &
    local pid=$!
    echo "$name PID: $pid"
    cd - > /dev/null
    return $pid
}

# Start backend
echo "🔧 Starting Backend API..."
cd restaurant-backend
make backend &
BACKEND_PID=$!
echo "Backend PID: $BACKEND_PID"
cd ..

# Wait a moment for backend to start
sleep 3

# Start frontend applications
run_bg "Shop Management App" "npm run dev" "shop-management-app"
SHOP_PID=$!

run_bg "Customer Website" "npm run dev" "customer-website"
CUSTOMER_PID=$!

run_bg "Admin Platform" "npm run dev" "admin-platform"
ADMIN_PID=$!

echo ""
echo "✅ All servers started!"
echo ""
echo "🌐 Access URLs:"
echo "- Shop Management: http://localhost:3001"
echo "- Customer Website: http://localhost:3002"
echo "- Admin Platform: http://localhost:3003"
echo "- Backend API: http://localhost:8080"
echo ""
echo "📋 Process IDs:"
echo "- Backend: $BACKEND_PID"
echo "- Shop Management: $SHOP_PID"
echo "- Customer Website: $CUSTOMER_PID"
echo "- Admin Platform: $ADMIN_PID"
echo ""
echo "To stop all servers, run: kill $BACKEND_PID $SHOP_PID $CUSTOMER_PID $ADMIN_PID"
echo "Or use Ctrl+C to stop this script and then kill remaining processes"

# Wait for user input to stop
read -p "Press Enter to stop all servers..."

# Kill all processes
kill $BACKEND_PID $SHOP_PID $CUSTOMER_PID $ADMIN_PID 2>/dev/null
echo "All servers stopped."
EOF

chmod +x scripts/dev-all.sh

echo "✅ Environment configurations created!"
echo ""
echo "📁 Created files:"
echo "- shop-management-app/.env.local"
echo "- shop-management-app/.env.example"
echo "- customer-website/.env.local"
echo "- customer-website/.env.example"
echo "- admin-platform/.env.local"
echo "- admin-platform/.env.example"
echo "- scripts/dev-all.sh"
echo ""
echo "⚠️  Important:"
echo "1. Update the secret keys and API credentials in each .env.local file"
echo "2. Copy your existing environment variables to the appropriate apps"
echo "3. Use scripts/dev-all.sh to start all applications at once"
echo "4. Each app runs on a different port to avoid conflicts"
