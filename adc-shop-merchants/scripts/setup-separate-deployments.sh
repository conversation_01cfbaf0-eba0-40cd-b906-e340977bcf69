#!/bin/bash

# Setup script for separating deployments
# This script creates the directory structure for separate applications

set -e

echo "🚀 Setting up separate deployment structure..."

# Create main directories
mkdir -p shop-management-app
mkdir -p customer-website
mkdir -p admin-platform
mkdir -p shared-packages

echo "📁 Created main application directories"

# Create shared packages structure
mkdir -p shared-packages/ui-components
mkdir -p shared-packages/types
mkdir -p shared-packages/utils
mkdir -p shared-packages/api-client

echo "📦 Created shared packages structure"

# Initialize shop management app
echo "🏪 Setting up Shop Management App..."
cd shop-management-app

# Create Next.js app structure
mkdir -p src/app
mkdir -p src/components
mkdir -p src/lib
mkdir -p src/hooks
mkdir -p public

# Create package.json for shop management
cat > package.json << 'EOF'
{
  "name": "shop-management-app",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev --port 3001",
    "build": "next build",
    "start": "next start --port 3001",
    "lint": "next lint"
  },
  "dependencies": {
    "next": "15.3.2",
    "react": "^19.0.0",
    "react-dom": "^19.0.0",
    "@reduxjs/toolkit": "^2.8.1",
    "react-redux": "^9.2.0",
    "next-auth": "^4.24.10",
    "next-intl": "^4.1.0",
    "lucide-react": "^0.509.0",
    "tailwindcss": "^4",
    "clsx": "^2.1.1",
    "tailwind-merge": "^3.3.0"
  },
  "devDependencies": {
    "@types/node": "^20",
    "@types/react": "^19",
    "@types/react-dom": "^19",
    "typescript": "^5",
    "eslint": "^9",
    "eslint-config-next": "15.3.2"
  }
}
EOF

# Create basic Next.js config
cat > next.config.ts << 'EOF'
import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  experimental: {
    appDir: true,
  },
  env: {
    BACKEND_URL: process.env.BACKEND_URL || 'http://localhost:8080',
    NEXTAUTH_URL: process.env.NEXTAUTH_URL || 'http://localhost:3001',
  },
};

export default nextConfig;
EOF

# Create TypeScript config
cat > tsconfig.json << 'EOF'
{
  "compilerOptions": {
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/shared/*": ["../shared-packages/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
EOF

cd ..

# Initialize customer website
echo "🛒 Setting up Customer Website..."
cd customer-website

mkdir -p src/app
mkdir -p src/components
mkdir -p src/lib
mkdir -p src/hooks
mkdir -p public

# Create package.json for customer website
cat > package.json << 'EOF'
{
  "name": "customer-website",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev --port 3002",
    "build": "next build",
    "start": "next start --port 3002",
    "lint": "next lint"
  },
  "dependencies": {
    "next": "15.3.2",
    "react": "^19.0.0",
    "react-dom": "^19.0.0",
    "@reduxjs/toolkit": "^2.8.1",
    "react-redux": "^9.2.0",
    "next-auth": "^4.24.10",
    "next-intl": "^4.1.0",
    "lucide-react": "^0.509.0",
    "tailwindcss": "^4",
    "clsx": "^2.1.1",
    "tailwind-merge": "^3.3.0"
  },
  "devDependencies": {
    "@types/node": "^20",
    "@types/react": "^19",
    "@types/react-dom": "^19",
    "typescript": "^5",
    "eslint": "^9",
    "eslint-config-next": "15.3.2"
  }
}
EOF

# Create Next.js config for customer website
cat > next.config.ts << 'EOF'
import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  experimental: {
    appDir: true,
  },
  env: {
    BACKEND_URL: process.env.BACKEND_URL || 'http://localhost:8080',
    NEXTAUTH_URL: process.env.NEXTAUTH_URL || 'http://localhost:3002',
  },
};

export default nextConfig;
EOF

# Create TypeScript config for customer website
cat > tsconfig.json << 'EOF'
{
  "compilerOptions": {
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/shared/*": ["../shared-packages/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
EOF

cd ..

# Initialize admin platform
echo "👨‍💼 Setting up Admin Platform..."
cd admin-platform

mkdir -p src/app
mkdir -p src/components
mkdir -p src/lib
mkdir -p src/hooks
mkdir -p public

# Create package.json for admin platform
cat > package.json << 'EOF'
{
  "name": "admin-platform",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev --port 3003",
    "build": "next build",
    "start": "next start --port 3003",
    "lint": "next lint"
  },
  "dependencies": {
    "next": "15.3.2",
    "react": "^19.0.0",
    "react-dom": "^19.0.0",
    "@reduxjs/toolkit": "^2.8.1",
    "react-redux": "^9.2.0",
    "next-auth": "^4.24.10",
    "next-intl": "^4.1.0",
    "lucide-react": "^0.509.0",
    "tailwindcss": "^4",
    "clsx": "^2.1.1",
    "tailwind-merge": "^3.3.0"
  },
  "devDependencies": {
    "@types/node": "^20",
    "@types/react": "^19",
    "@types/react-dom": "^19",
    "typescript": "^5",
    "eslint": "^9",
    "eslint-config-next": "15.3.2"
  }
}
EOF

cd ..

# Create shared packages
echo "📚 Setting up shared packages..."

# Create shared types package
cd shared-packages/types
cat > package.json << 'EOF'
{
  "name": "@shared/types",
  "version": "0.1.0",
  "main": "index.ts",
  "types": "index.ts"
}
EOF

cat > index.ts << 'EOF'
// Shared TypeScript types across all applications

export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'shop_owner' | 'staff' | 'customer';
  shopId?: string;
}

export interface Shop {
  id: string;
  name: string;
  slug: string;
  type: 'restaurant' | 'retail' | 'service' | 'digital';
  ownerId: string;
  branches: Branch[];
}

export interface Branch {
  id: string;
  name: string;
  slug: string;
  shopId: string;
  address: string;
  phone: string;
}

export interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  categoryId: string;
  branchId: string;
  available: boolean;
  imageUrl?: string;
}

export interface Order {
  id: string;
  orderNumber: string;
  customerId?: string;
  branchId: string;
  items: OrderItem[];
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'completed' | 'cancelled';
  total: number;
  createdAt: string;
}

export interface OrderItem {
  id: string;
  menuItemId: string;
  quantity: number;
  price: number;
  notes?: string;
}
EOF

cd ../..

echo "✅ Setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Run 'npm install' in each application directory"
echo "2. Copy existing components to appropriate applications"
echo "3. Update backend CORS configuration"
echo "4. Test each application independently"
echo ""
echo "🚀 Development servers:"
echo "- Shop Management: http://localhost:3001"
echo "- Customer Website: http://localhost:3002"  
echo "- Admin Platform: http://localhost:3003"
echo "- Backend API: http://localhost:8080"
