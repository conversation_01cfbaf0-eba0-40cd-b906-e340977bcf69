# ADC Restaurant Platform Documentation

## Project Overview

The ADC Restaurant Platform is a multi-shop platform that allows users to sell various types of products and services. The platform supports multiple shop types including restaurants, retail stores, service providers, digital goods, custom shops, and convenience stores.

### Key Features

- **Multi-shop Platform**: Support for various shop types with type-specific fields and functionality
- **Order Management**: Comprehensive order processing system for different shop types
- **Reservation System**: Table reservation and management for restaurants and service bookings
- **Inventory Management**: Stock tracking, alerts, and purchase order management
- **Payment Processing**: Support for multiple payment methods (cash, PromptPay, Stripe)
- **User Management**: Role-based access control and user permissions
- **Analytics**: Sales, inventory, and customer analytics
- **Internationalization**: Multi-language support with next-intl

## Architecture

### Application Structure

The application is built using Next.js with the App Router architecture. The main components of the architecture are:

1. **Main Application**: Customer-facing website for browsing and ordering
2. **Admin Site**: Separate application for merchant and admin management (to be moved)
3. **Order Service API**: Dedicated service for handling order processing (to be implemented)

### Tech Stack

- **Frontend**: Next.js, React, Tailwind CSS, shadcn/ui
- **State Management**: Redux Toolkit with RTK Query
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js
- **Payment Processing**: Stripe, PromptPay
- **Internationalization**: next-intl
- **Hosting**: Vercel (main app), separate hosting for admin and services

### Database Schema

The database schema is managed using Prisma and includes the following main models:

- User
- Merchant
- Item
- Order
- Reservation
- Payment
- Inventory
- MerchantSettings

Each model includes shop-type-specific fields to support different business models.

## Shop Types

The platform supports the following shop types:

1. **Restaurant**: Food ordering, table reservations, menu management
2. **Retail**: Product inventory, shipping options
3. **Service**: Appointment booking, service providers
4. **Digital**: Digital product delivery
5. **Custom**: Customizable shop type
6. **Convenience**: Quick service retail

Each shop type has specific attributes in the following models:
- Merchant
- Item
- MerchantSettings
- Order

## API Structure

### Main API Endpoints

The API is organized into the following main categories:

- `/api/merchants`: Merchant management
- `/api/items`: Product and service catalog
- `/api/orders`: Order processing
- `/api/reservations`: Booking and reservation management
- `/api/payments`: Payment processing
- `/api/users`: User management
- `/api/admin`: Admin functionality

### API Integration

The application is migrating from tRPC to RTK Query for API integration. All API endpoints are defined in `/src/lib/redux/api/endpoints/` with the base configuration in `/src/lib/redux/api/apiSlice.ts`.

## Authentication and Authorization

The platform implements role-based access control:

- **Unauthenticated Users**: Limited access to public pages
- **Authenticated Users**: Access to personal data and merchants they have roles for
- **Merchant Staff**: Access to specific merchant data based on assigned roles
- **Merchant Owners**: Full access to their merchant data
- **Admins**: System-wide access

## Development Guidelines

### Component Organization

- **Feature Components**: `/components/{feature-name}/`
- **UI Components**: `/components/ui/`
- **Shared Components**: `/components/shared/`

### State Management

- Use Redux Toolkit for global state
- Use RTK Query for API calls
- Follow the slice pattern for Redux organization

### Form Handling

- Use zod for data validation
- Use react-hook-form for form handling
- Validate forms on submission rather than during typing

### Styling

- Use Tailwind CSS for styling
- Use shadcn/ui component patterns
- Keep component-specific styles with the component

## Deployment Architecture

### Current Architecture

```
┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │
│  Next.js App    │────▶│   PostgreSQL    │
│  (Monolith)     │     │   Database      │
│                 │     │                 │
└─────────────────┘     └─────────────────┘
```

### Target Architecture

```
┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │
│  Next.js App    │────▶│   PostgreSQL    │
│  (Customer)     │     │   Database      │
│                 │     │                 │
└─────────────────┘     └─────────────────┘
       │                        ▲
       │                        │
       ▼                        │
┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │
│  Admin Site     │────▶│  Order Service  │
│  (Separate)     │     │     API         │
│                 │     │                 │
└─────────────────┘     └─────────────────┘
```

## Migration Plan

### Admin Site Migration

1. Create a new Next.js application for the admin site
2. Move admin components and pages from the main app
3. Set up authentication and authorization
4. Create API endpoints for admin functionality
5. Deploy the admin site separately

### Order Service API

1. Create a new microservice for order processing
2. Implement order creation, updating, and tracking
3. Set up database access and caching
4. Implement proper error handling and retry mechanisms
5. Deploy the service with auto-scaling capabilities

## Best Practices

- Use server-side components when possible
- Implement proper error handling
- Use modals for create and edit operations
- Follow the API Integration Guide for all API calls
- Implement Row-Level Security (RLS) for data access
- Use UUID for database IDs
- Implement proper validation for all forms
- Use webhooks for integration with external services
