package middleware

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// NextAuthToken represents the token structure from NextAuth
type NextAuthToken struct {
	UserID    string `json:"user_id"`
	Email     string `json:"email"`
	Role      string `json:"role"`
	IssuedAt  int64  `json:"issued_at"`
	ExpiresAt int64  `json:"expires_at"`
}

// AuthMiddleware validates NextAuth tokens from the frontend
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get the authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Authorization header required",
			})
			c.Abort()
			return
		}

		// Extract the token from Bearer token
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.J<PERSON>N(http.StatusUnauthorized, gin.H{
				"error": "Invalid authorization header format",
			})
			c.Abort()
			return
		}

		tokenString := tokenParts[1]

		// Decode the base64 token
		tokenBytes, err := base64.StdEncoding.DecodeString(tokenString)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid token format",
			})
			c.Abort()
			return
		}

		// Parse the JSON token
		var token NextAuthToken
		if err := json.Unmarshal(tokenBytes, &token); err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid token structure",
			})
			c.Abort()
			return
		}

		// Validate token expiration
		if time.Now().Unix() > token.ExpiresAt {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Token expired",
			})
			c.Abort()
			return
		}

		// Validate user information
		userEmail := c.GetHeader("X-User-Email")
		userID := c.GetHeader("X-User-ID")
		
		if userEmail != token.Email || userID != token.UserID {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Token mismatch",
			})
			c.Abort()
			return
		}

		// Set user information in context for use in handlers
		c.Set("user_id", token.UserID)
		c.Set("user_email", token.Email)
		c.Set("user_role", token.Role)
		c.Set("auth_source", "nextauth")

		// Log successful authentication
		fmt.Printf("Authenticated user: %s (%s)\n", token.Email, token.UserID)

		c.Next()
	}
}

// CORS middleware for development
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")
		
		// Allow specific origins
		allowedOrigins := []string{
			"http://localhost:4000",  // Next.js development
			"http://localhost:3000",  // Alternative Next.js port
		}
		
		for _, allowedOrigin := range allowedOrigins {
			if origin == allowedOrigin {
				c.Header("Access-Control-Allow-Origin", origin)
				break
			}
		}
		
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, PATCH")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization, X-User-Email, X-User-ID, X-User-Role, X-Auth-Source")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Max-Age", "86400")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}
		
		c.Next()
	}
}

// Example usage in main.go
func SetupRoutes() *gin.Engine {
	r := gin.Default()
	
	// Add CORS middleware first
	r.Use(CORSMiddleware())
	
	// API routes with authentication
	api := r.Group("/api/v1")
	api.Use(AuthMiddleware()) // Apply auth middleware to all API routes
	{
		api.GET("/merchants", GetMerchants)
		api.GET("/merchants/:id/convenience/products", GetConvenienceProducts)
		api.GET("/merchants/:id/services", GetServices)
		api.GET("/merchants/:id/appointments", GetAppointments)
		api.GET("/merchants/:id/digital-products", GetDigitalProducts)
		// ... other routes
	}
	
	return r
}

// Example handler that uses authenticated user context
func GetMerchants(c *gin.Context) {
	// Get user information from context
	userID := c.GetString("user_id")
	userEmail := c.GetString("user_email")
	userRole := c.GetString("user_role")
	
	fmt.Printf("GetMerchants called by user: %s (%s) with role: %s\n", userEmail, userID, userRole)
	
	// Your business logic here - fetch merchants for this user
	merchants, err := fetchMerchantsForUser(userEmail)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to fetch merchants",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"data": merchants,
		"user": gin.H{
			"id":    userID,
			"email": userEmail,
			"role":  userRole,
		},
	})
}

// Placeholder function - implement your actual business logic
func fetchMerchantsForUser(email string) ([]interface{}, error) {
	// TODO: Implement actual database query
	// For now, return empty array
	return []interface{}{}, nil
}
