# ADC Restaurant Platform Technical Architecture

## System Architecture

The ADC Restaurant Platform is being restructured into a microservices architecture with three main components:

1. **Main Application (Customer-facing)**: Next.js application for customers to browse, order, and manage their accounts
2. **Admin Site**: Separate Next.js application for merchant and admin management
3. **Order Service API**: Dedicated Node.js service for order processing

### Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                                                                 │
│                        Client Browsers                          │
│                                                                 │
└───────────────────────────────┬─────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                                                                 │
│                          API Gateway                            │
│                                                                 │
└───────┬─────────────────────────┬────────────────────────┬──────┘
        │                         │                        │
        ▼                         ▼                        ▼
┌───────────────┐       ┌──────────────────┐      ┌───────────────┐
│               │       │                  │      │               │
│ Main App      │       │ Admin Site       │      │ Order Service │
│ (Next.js)     │       │ (Next.js)        │      │ (Node.js)     │
│               │       │                  │      │               │
└───────┬───────┘       └────────┬─────────┘      └───────┬───────┘
        │                        │                        │
        │                        │                        │
        ▼                        ▼                        ▼
┌─────────────────────────────────────────────────────────────────┐
│                                                                 │
│                      PostgreSQL Database                        │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## Component Details

### Main Application

- **Technology**: Next.js with App Router
- **Purpose**: Customer-facing website for browsing and ordering
- **Key Features**:
  - Product/service browsing
  - Shopping cart
  - Checkout process
  - User account management
  - Order tracking
  - Reservations

### Admin Site

- **Technology**: Next.js with App Router
- **Purpose**: Merchant and admin management interface
- **Key Features**:
  - Dashboard and analytics
  - Order management
  - Inventory management
  - User management
  - Merchant settings
  - Content management

### Order Service API

- **Technology**: Node.js with Express
- **Purpose**: Dedicated service for order processing
- **Key Features**:
  - Order creation and validation
  - Order status management
  - Payment processing
  - Inventory updates
  - Notification dispatching
  - Analytics and reporting

## Database Architecture

The system uses a PostgreSQL database with the following schema organization:

### Core Models

- **User**: User accounts and authentication
- **Merchant**: Business information and settings
- **Item**: Products and services
- **Order**: Customer orders
- **Reservation**: Bookings and appointments
- **Payment**: Transaction records

### Shop Type-Specific Models

Each shop type has specific attributes in the following models:
- **Merchant**: Shop type-specific merchant settings
- **Item**: Shop type-specific product attributes
- **MerchantSettings**: Shop type-specific configuration
- **Order**: Shop type-specific order details

## API Architecture

### API Gateway

- Routes requests to appropriate services
- Handles authentication and authorization
- Implements rate limiting and caching
- Provides logging and monitoring

### Service APIs

- **Main App API**: Customer-facing functionality
- **Admin API**: Admin and merchant management
- **Order Service API**: Order processing and management

## Authentication and Authorization

- **JWT-based authentication**: Secure token-based authentication
- **Role-based access control**: Different permissions for different user roles
- **API key authentication**: For service-to-service communication
- **Row-level security**: Database-level access control

## Data Flow

### Order Creation Flow

1. Customer adds items to cart in Main App
2. Customer proceeds to checkout
3. Main App validates order and initiates payment
4. Order Service creates order record
5. Order Service processes payment
6. Order Service updates inventory
7. Order Service sends notifications
8. Main App displays order confirmation

### Admin Order Management Flow

1. Admin logs into Admin Site
2. Admin Site fetches orders from Order Service
3. Admin updates order status
4. Order Service processes status change
5. Order Service sends notifications
6. Admin Site displays updated order status

## Deployment Architecture

### Production Environment

- **Main App**: Deployed on Vercel
- **Admin Site**: Deployed on separate Vercel project
- **Order Service**: Deployed on cloud platform with auto-scaling
- **Database**: Managed PostgreSQL service
- **Redis**: For caching and session management
- **CDN**: For static assets and media

### Development Environment

- Local development setup with Docker Compose
- Shared development database
- Mock services for external integrations

## Security Considerations

- **HTTPS**: All communications encrypted
- **Authentication**: Secure authentication mechanisms
- **Authorization**: Role-based access control
- **Input Validation**: All user input validated
- **Rate Limiting**: Prevent abuse
- **Audit Logging**: Track all system changes
- **Data Encryption**: Sensitive data encrypted

## Monitoring and Logging

- **Application Monitoring**: Performance and error tracking
- **API Monitoring**: Request/response metrics
- **Database Monitoring**: Query performance
- **Log Aggregation**: Centralized logging
- **Alerting**: Automated alerts for issues

## Scaling Strategy

- **Horizontal Scaling**: Add more instances as load increases
- **Database Scaling**: Read replicas and sharding
- **Caching**: Reduce database load
- **CDN**: Offload static content
- **Microservices**: Independent scaling of components
