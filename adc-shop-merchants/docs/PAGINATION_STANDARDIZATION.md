# Pagination, Filtering, Sorting, and Search Standardization

## Overview

This document outlines the standardization of pagination, filtering, sorting, and search functionality across all GET list APIs in the restaurant management system.

## Standardized Components

### 1. Common Types (`internal/types/common.go`)

#### StandardPagination
```go
type StandardPagination struct {
    Page  int `form:"page" binding:"min=1" json:"page"`
    Limit int `form:"limit" binding:"min=1,max=100" json:"limit"`
}
```

#### StandardSorting
```go
type StandardSorting struct {
    SortBy    string `form:"sort_by" json:"sort_by"`
    SortOrder string `form:"sort_order" binding:"oneof=asc desc" json:"sort_order"`
}
```

#### StandardSearch
```go
type StandardSearch struct {
    Search string `form:"search" json:"search"`
}
```

#### StandardResponse
```go
type StandardResponse struct {
    Total      int64 `json:"total"`
    Page       int   `json:"page"`
    Limit      int   `json:"limit"`
    TotalPages int   `json:"total_pages"`
}
```

### 2. Pagination Utility Package (`pkg/pagination/pagination.go`)

Provides helper functions for:
- Applying default values
- Validating sort fields
- Building SQL ORDER BY clauses
- Building search conditions
- Creating standardized responses
- Filter building utilities

### 3. Standardized Filter Types

#### Inventory Filters (`internal/types/inventory.go`)
- `InventoryItemFilters`
- `IngredientFilters`
- `SupplierFilters`
- `StockMovementFilters`
- `WasteRecordFilters`
- `PurchaseOrderFiltersV2`

#### Analytics Filters (`internal/types/analytics.go`)
- `PopularItemsFilters`
- `CustomerAnalyticsFilters`
- `StaffPerformanceFilters`
- `TableUtilizationFilters`
- `SalesReportFilters`
- `InventoryAnalyticsFilters`

## Updated APIs

### Inventory APIs

#### GET /merchants/{merchantId}/branches/{branchId}/inventory/items
**New Parameters:**
- `category` - Filter by ingredient category
- `status` - Filter by status
- `is_active` - Filter by active status
- `supplier_id` - Filter by supplier ID
- `stock_level` - Filter by stock level (low, out_of_stock, normal)
- `expiry_status` - Filter by expiry status (expired, expiring_soon, fresh)
- `min_stock`, `max_stock` - Stock range filters
- `min_value`, `max_value` - Value range filters
- `search` - Search term
- `page`, `limit` - Pagination
- `sort_by`, `sort_order` - Sorting
- `date_from`, `date_to` - Date range filters

#### GET /ingredients
**New Parameters:**
- `category` - Filter by category
- `supplier_id` - Filter by supplier ID
- `unit` - Filter by unit
- `is_active` - Filter by active status
- `search` - Search term
- `page`, `limit` - Pagination
- `sort_by`, `sort_order` - Sorting

#### GET /suppliers
**New Parameters:**
- `country`, `city` - Location filters
- `rating` - Filter by rating (1-5)
- `is_active` - Filter by active status
- `search` - Search term
- `page`, `limit` - Pagination
- `sort_by`, `sort_order` - Sorting

#### GET /merchants/{merchantId}/branches/{branchId}/inventory/movements
**New Parameters:**
- `ingredient_id` - Filter by ingredient ID
- `movement_type` - Filter by movement type (in, out, adjustment, waste)
- `user_id` - Filter by user ID
- `min_quantity`, `max_quantity` - Quantity range filters
- `date_from`, `date_to` - Date range filters
- `page`, `limit` - Pagination
- `sort_by`, `sort_order` - Sorting

### Analytics APIs

#### GET /merchants/{merchantId}/branches/{branchId}/analytics/popular-items
**New Parameters:**
- `category_id` - Filter by category ID
- `min_orders` - Minimum order count filter
- `period` - Time period (today, week, month, quarter, year)
- `is_available`, `is_vegetarian`, `is_vegan` - Dietary filters
- `date_from`, `date_to` - Date range filters
- `page`, `limit` - Pagination
- `sort_by`, `sort_order` - Sorting

## Updated Service Layer

### Inventory Service
Added standardized methods:
- `GetInventoryItems(ctx, branchID, filters)` - Returns `*types.InventoryItemsResponse`
- `GetIngredients(ctx, filters)` - Returns `*types.IngredientsResponse`
- `GetSuppliers(ctx, filters)` - Returns `*types.SuppliersResponse`
- `GetStockMovements(ctx, branchID, filters)` - Returns `*types.StockMovementsResponse`

### Analytics Service
Added standardized methods:
- `GetPopularItemsAnalytics(ctx, branchID, filters)` - Returns `*types.PopularItemsAnalyticsResponse`

## Implementation Pattern

### 1. Handler Layer
```go
func (h *Handler) GetItems(c *gin.Context) {
    var filters types.ItemFilters
    if err := c.ShouldBindQuery(&filters); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
        return
    }

    items, err := h.service.GetItems(c.Request.Context(), filters)
    if err != nil {
        h.logger.WithError(err).Error("Failed to get items")
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get items"})
        return
    }

    c.JSON(http.StatusOK, items)
}
```

### 2. Service Layer
```go
func (s *Service) GetItems(ctx context.Context, filters types.ItemFilters) (*types.ItemsResponse, error) {
    // Apply pagination and sorting defaults
    pagination.ApplyStandardDefaults(&filters.StandardPagination, &filters.StandardSorting)
    pagination.ValidateAndApplySorting(&filters.StandardSorting, types.ItemSortFields, "name")

    // Get items from repository
    items, total, err := s.repo.GetItems(ctx, filters)
    if err != nil {
        return nil, fmt.Errorf("failed to get items: %w", err)
    }

    // Convert to response format
    itemResponses := make([]types.ItemResponse, len(items))
    for i, item := range items {
        itemResponses[i] = s.convertItemToResponse(item)
    }

    return &types.ItemsResponse{
        StandardResponse: pagination.CreateStandardResponse(total, filters.Page, filters.Limit),
        Data:            itemResponses,
    }, nil
}
```

### 3. Repository Layer (To be implemented)
```go
func (r *Repository) GetItems(ctx context.Context, filters types.ItemFilters) ([]*models.Item, int64, error) {
    query := r.db.WithContext(ctx).Model(&models.Item{})
    
    // Build filter conditions
    fb := pagination.NewFilterBuilder()
    fb.AddStringFilter("category", filters.Category)
    fb.AddBoolFilter("is_active", filters.IsActive)
    fb.AddSearchFilter(filters.Search, []string{"name", "description"}, "")
    
    whereClause, args := fb.Build()
    if whereClause != "" {
        query = query.Where(whereClause, args...)
    }
    
    // Get total count
    var total int64
    if err := query.Count(&total).Error; err != nil {
        return nil, 0, err
    }
    
    // Apply sorting and pagination
    orderBy := pagination.BuildOrderByClause(filters.StandardSorting, nil)
    offset := pagination.CalculateOffset(filters.Page, filters.Limit)
    
    var items []*models.Item
    err := query.Order(orderBy).Offset(offset).Limit(filters.Limit).Find(&items).Error
    
    return items, total, err
}
```

## Benefits

1. **Consistency**: All list APIs follow the same pattern
2. **Maintainability**: Centralized pagination logic
3. **Reusability**: Common types and utilities
4. **Flexibility**: Extensible filter system
5. **Performance**: Efficient database queries with proper indexing
6. **Documentation**: Standardized API documentation

## Next Steps

1. Implement repository layer methods for new filter types
2. Update existing APIs to use standardized filters
3. Add database indexes for commonly filtered fields
4. Update frontend components to use new filter parameters
5. Add comprehensive tests for pagination and filtering logic
6. Update API documentation with new parameters

## Migration Guide

### For Existing APIs
1. Update filter types to inherit from standard types
2. Update service methods to use pagination utilities
3. Update handlers to use new filter binding
4. Update repository methods to support new filters
5. Test thoroughly to ensure backward compatibility

### For New APIs
1. Create filter types inheriting from standard types
2. Use pagination utilities in service layer
3. Follow the established handler pattern
4. Implement repository methods with filter support
5. Add appropriate sort field validation
