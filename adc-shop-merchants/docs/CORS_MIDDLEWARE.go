package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// CORSMiddleware handles Cross-Origin Resource Sharing (CORS) for the API
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")
		
		// Define allowed origins
		allowedOrigins := []string{
			"http://localhost:4000",  // Next.js development server
			"http://localhost:3000",  // Alternative Next.js port
			"https://yourdomain.com", // Production domain
		}
		
		// Check if the origin is allowed
		isAllowed := false
		for _, allowedOrigin := range allowedOrigins {
			if origin == allowedOrigin {
				isAllowed = true
				break
			}
		}
		
		// Set CORS headers
		if isAllowed {
			c.<PERSON><PERSON>("Access-Control-Allow-Origin", origin)
		}
		
		// Set other CORS headers
		c.<PERSON>er("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, PATCH")
		c<PERSON><PERSON>("Access-Control-Allow-Headers", strings.Join([]string{
			"Content-Type",
			"Authorization", 
			"x-user-email", 
			"x-user-id", 
			"x-user-name",
			"Accept",
			"Origin",
			"X-Requested-With",
		}, ", "))
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Max-Age", "86400") // 24 hours
		
		// Handle preflight OPTIONS request
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}
		
		c.Next()
	}
}

// SimpleCORSMiddleware - A simpler version for development
func SimpleCORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Allow all origins in development (NOT for production)
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, PATCH")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization, x-user-email, x-user-id, x-user-name")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}
		
		c.Next()
	}
}
