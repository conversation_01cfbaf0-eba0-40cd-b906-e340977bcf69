# Centralized Tag System

## Overview

The Centralized Tag System provides a unified approach to managing tags across all entities in the restaurant management platform. This system replaces the previous decentralized tag implementation with a more robust, scalable, and feature-rich solution.

## Features

### 🏷️ **Centralized Management**
- Single source of truth for all tags
- Consistent tag usage across entities
- Tag analytics and usage tracking
- Category-based organization

### 🎯 **Smart Suggestions**
- Auto-complete based on existing tags
- Popular tags for quick selection
- Category-filtered suggestions
- Search functionality

### 📊 **Analytics & Insights**
- Tag usage statistics
- Popular tags tracking
- Category distribution
- Unused tag identification

### 🔧 **Flexible Architecture**
- Support for any entity type (menu items, reviews, services, etc.)
- Custom tag creation
- System vs user tags
- Active/inactive tag management

## Architecture

### Database Schema

```sql
-- Tag Categories
CREATE TABLE tag_categories (
    id UUID PRIMARY KEY,
    branch_id UUID NOT NULL,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#8a745c',
    icon VARCHAR(50),
    sort_order INTEGER DEFAULT 0,
    is_system BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- Tags
CREATE TABLE tags (
    id UUID PRIMARY KEY,
    branch_id UUID NOT NULL,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL,
    color VARCHAR(7) DEFAULT '#8a745c',
    icon VARCHAR(50),
    usage_count INTEGER DEFAULT 0,
    is_system BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- Entity Tag Relationships
CREATE TABLE entity_tags (
    id UUID PRIMARY KEY,
    tag_id UUID NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id UUID NOT NULL,
    created_at TIMESTAMP
);
```

### Backend Components

#### Models
- `Tag`: Core tag model with metadata
- `TagCategory`: Category organization for tags
- `EntityTag`: Many-to-many relationship between tags and entities

#### Repositories
- `TagRepository`: CRUD operations for tags and categories
- Analytics and search functionality
- Entity relationship management

#### Services
- `TagService`: Business logic for tag management
- `EntityTagService`: Entity-specific tag operations
- Integration with existing services

#### API Handlers
- RESTful endpoints for all tag operations
- Swagger documentation
- Proper error handling and validation

### Frontend Components

#### API Integration
- RTK Query endpoints for all tag operations
- TypeScript type definitions
- Proper caching and invalidation

#### UI Components
- `TagInput`: Reusable tag input with auto-complete
- Tag management interface
- Analytics dashboard

## API Endpoints

### Tags
```
GET    /v1/shops/{shopId}/branches/{branchId}/tags
POST   /v1/shops/{shopId}/branches/{branchId}/tags
GET    /v1/shops/{shopId}/branches/{branchId}/tags/{tagId}
PUT    /v1/shops/{shopId}/branches/{branchId}/tags/{tagId}
DELETE /v1/shops/{shopId}/branches/{branchId}/tags/{tagId}
GET    /v1/shops/{shopId}/branches/{branchId}/tags/popular
POST   /v1/shops/{shopId}/branches/{branchId}/tags/search
GET    /v1/shops/{shopId}/branches/{branchId}/tags/category/{category}
GET    /v1/shops/{shopId}/branches/{branchId}/tags/analytics
```

### Tag Categories
```
GET    /v1/shops/{shopId}/branches/{branchId}/tag-categories
POST   /v1/shops/{shopId}/branches/{branchId}/tag-categories
```

### Entity Tags
```
POST   /v1/shops/{shopId}/branches/{branchId}/entities/{type}/{id}/tags
GET    /v1/shops/{shopId}/branches/{branchId}/entities/{type}/{id}/tags
```

## Usage Examples

### Creating a Tag
```typescript
const { data: tag } = await createTag({
  shopId: 'shop-123',
  branchId: 'branch-456',
  tag: {
    name: 'Spicy',
    category: 'style',
    description: 'Spicy dishes',
    color: '#ff4444',
    icon: '🌶️'
  }
});
```

### Using TagInput Component
```tsx
<TagInput
  value={selectedTags}
  onChange={setSelectedTags}
  suggestions={availableTags}
  onSearch={handleTagSearch}
  categories={tagCategories}
  allowCustomTags={true}
  maxTags={10}
/>
```

### Syncing Entity Tags (Backend)
```go
// Sync menu item tags
err := entityTagService.SyncMenuItemTags(ctx, menuItemID, []string{
  "Spicy", "Popular", "Vegetarian"
})
```

## Migration

### Database Migration
1. Run the migration script: `20241222_create_centralized_tags.sql`
2. This creates the new tables and populates default categories

### Data Migration
1. Use the migration tool: `cmd/migrate-tags/main.go`
2. This migrates existing tags from menu items and reviews
3. Automatically categorizes tags based on content

```bash
# Run migration
cd restaurant-backend
DATABASE_URL="your-db-url" go run cmd/migrate-tags/main.go
```

## Default Categories

The system comes with pre-configured categories:

### 🥗 Dietary
- Vegetarian, Vegan, Gluten-Free, Dairy-Free, Nut-Free, Keto, Low-Carb, High-Protein

### 🍳 Style
- Spicy, Mild, Grilled, Fried, Steamed, Raw, Baked, Roasted

### ⭐ Popularity
- Popular, New, Chef Special, Bestseller, Trending, Signature

### 🎉 Occasion
- Date Night, Family Friendly, Business Lunch, Celebration, Comfort Food, Quick Bite

## Best Practices

### Tag Naming
- Use consistent naming conventions
- Keep tags concise and descriptive
- Avoid duplicate meanings
- Use proper capitalization

### Category Organization
- Group related tags together
- Use meaningful category names
- Assign appropriate colors and icons
- Maintain logical sort order

### Performance
- Use pagination for large tag lists
- Implement proper caching
- Index frequently queried fields
- Monitor tag usage analytics

## Troubleshooting

### Common Issues

1. **Tags not appearing in suggestions**
   - Check if tags are active (`is_active = true`)
   - Verify branch ID matches
   - Ensure proper API endpoint usage

2. **Migration failures**
   - Check database permissions
   - Verify existing data format
   - Review migration logs

3. **Performance issues**
   - Check database indexes
   - Monitor query performance
   - Consider pagination limits

### Debugging

Enable debug logging:
```go
logger.SetLevel(logrus.DebugLevel)
```

Check tag relationships:
```sql
SELECT t.name, et.entity_type, et.entity_id 
FROM tags t 
JOIN entity_tags et ON t.id = et.tag_id 
WHERE t.branch_id = 'your-branch-id';
```

## Future Enhancements

- Tag hierarchies (parent/child relationships)
- Tag synonyms and aliases
- Bulk tag operations
- Tag templates for different entity types
- Advanced analytics and reporting
- Tag import/export functionality
- Multi-language tag support
