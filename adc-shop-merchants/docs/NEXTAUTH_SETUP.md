# NextAuth Implementation Guide

This document outlines the NextAuth implementation for the ADC Shop Merchants application.

## Overview

NextAuth.js has been integrated to provide secure authentication with the following features:
- JWT-based sessions
- Credentials provider with Supabase backend
- Redux integration for state management
- Protected routes and middleware
- Role-based access control

## Architecture

### Core Components

1. **NextAuth Configuration** (`src/lib/auth/nextauth.config.ts`)
   - Credentials provider setup
   - JWT and session configuration
   - Callbacks for token and session handling

2. **API Route Handler** (`src/app/api/auth/[...nextauth]/route.ts`)
   - NextAuth API endpoints for authentication

3. **Session Provider** (`src/components/auth/NextAuthProvider.tsx`)
   - Wraps the app with NextAuth session context

4. **Auth Sync Provider** (`src/components/auth/AuthSyncProvider.tsx`)
   - Syncs NextAuth session with Redux state

5. **Custom Hook** (`src/hooks/useAuth.ts`)
   - Provides authentication utilities and state

## Environment Variables

Add these to your `.env.local` file:

```env
NEXTAUTH_SECRET=your-nextauth-secret-key-here
NEXTAUTH_URL=http://localhost:4000
```

## Usage Examples

### Login Component

```tsx
import { useAuth } from '@/hooks/useAuth'

export function LoginForm() {
  const { login, isLoading } = useAuth()
  
  const handleSubmit = async (email: string, password: string) => {
    try {
      await login(email, password)
      // Handle success
    } catch (error) {
      // Handle error
    }
  }
}
```

### Protected Component

```tsx
import { useAuth } from '@/hooks/useAuth'

export function ProtectedComponent() {
  const { user, isAuthenticated, isLoading } = useAuth()
  
  if (isLoading) return <div>Loading...</div>
  if (!isAuthenticated) return <div>Please login</div>
  
  return <div>Welcome {user?.name}</div>
}
```

### API Route Protection

```tsx
import { withAuth } from '@/lib/middleware/auth'

export const GET = withAuth(async (request, { user }) => {
  // Protected API route logic
  return NextResponse.json({ user })
}, {
  allowedRoles: ['admin', 'merchant']
})
```

## Security Features

1. **JWT Tokens**: Secure, stateless authentication
2. **Role-based Access**: User roles for authorization
3. **Protected Routes**: Middleware-level route protection
4. **Session Management**: Automatic session handling
5. **Error Handling**: Comprehensive error pages and handling

## Migration Notes

- Replaced Supabase direct auth with NextAuth + Supabase backend
- Updated Redux auth slice for NextAuth compatibility
- Modified middleware for NextAuth integration
- Updated all auth-related components

## Testing

To test the authentication:

1. Start the development server: `npm run dev`
2. Navigate to `/login`
3. Use valid Supabase credentials
4. Verify session persistence and protected route access

## Troubleshooting

### Common Issues

1. **NEXTAUTH_SECRET not set**: Ensure environment variable is configured
2. **Supabase connection**: Verify Supabase credentials and connection
3. **Middleware conflicts**: Check middleware configuration order
4. **Session not persisting**: Verify JWT secret and configuration

### Debug Mode

Enable debug mode in development:
```env
NODE_ENV=development
```

This will show detailed NextAuth logs in the console.
