# Default Roles and Permissions Guide

## Overview

This guide provides suggested default roles and permissions to help you get started with staff management in your restaurant. These roles are designed to cover common restaurant positions and their typical responsibilities.

## 🎯 **Suggested Default Roles**

### 1. **Owner/Manager** 
**Full access to all restaurant operations and settings**

**Ideal for:** Restaurant owners, general managers, operations managers

**Key Responsibilities:**
- Complete oversight of restaurant operations
- Staff management and scheduling
- Financial reporting and analytics
- System configuration and settings

**Permissions Include:**
- ✅ All dashboard and analytics access
- ✅ Complete staff management (hire, fire, schedule)
- ✅ Full order and menu management
- ✅ Table and reservation management
- ✅ Complete inventory and supplier management
- ✅ Customer and loyalty program management
- ✅ Payment processing and financial reports
- ✅ System settings and integrations

---

### 2. **Assistant Manager**
**Manage daily operations, staff, and customer service**

**Ideal for:** Assistant managers, shift supervisors, team leads

**Key Responsibilities:**
- Daily operations oversight
- Staff scheduling and supervision
- Customer service management
- Basic inventory monitoring

**Permissions Include:**
- ✅ Dashboard and basic analytics
- ✅ Staff management (create, edit, schedule)
- ✅ Order management and kitchen display
- ✅ Menu editing and pricing
- ✅ Table and reservation management
- ✅ Inventory monitoring and alerts
- ✅ Customer management
- ✅ Payment processing

---

### 3. **Server/Waiter**
**Take orders, manage tables, and serve customers**

**Ideal for:** Servers, waiters, waitresses, front-of-house staff

**Key Responsibilities:**
- Taking customer orders
- Managing assigned tables
- Processing payments
- Handling reservations

**Permissions Include:**
- ✅ Basic dashboard access
- ✅ Order creation and editing
- ✅ Menu viewing
- ✅ Table management
- ✅ Reservation management
- ✅ Customer information access
- ✅ Payment processing

---

### 4. **Kitchen Staff/Chef**
**Manage kitchen operations, menu items, and inventory**

**Ideal for:** Head chefs, sous chefs, line cooks, kitchen managers

**Key Responsibilities:**
- Kitchen operations management
- Menu item preparation
- Inventory monitoring
- Order fulfillment

**Permissions Include:**
- ✅ Dashboard access
- ✅ Kitchen display system
- ✅ Menu management (create, edit, categories)
- ✅ Inventory management and alerts
- ✅ Order viewing

---

### 5. **Cashier**
**Process payments and handle customer transactions**

**Ideal for:** Cashiers, front desk staff, payment processors

**Key Responsibilities:**
- Processing customer payments
- Handling refunds
- Basic order management
- Customer service

**Permissions Include:**
- ✅ Dashboard access
- ✅ Order viewing and creation
- ✅ Menu viewing
- ✅ Customer information access
- ✅ Payment processing and refunds

---

### 6. **Host/Hostess**
**Manage reservations, greet customers, and assign tables**

**Ideal for:** Hosts, hostesses, front-of-house coordinators

**Key Responsibilities:**
- Greeting and seating customers
- Managing reservations
- Table assignments
- Customer service

**Permissions Include:**
- ✅ Dashboard access
- ✅ Table viewing and management
- ✅ Complete reservation management
- ✅ Customer information access

---

### 7. **Inventory Manager**
**Manage inventory, suppliers, and purchase orders**

**Ideal for:** Inventory managers, purchasing coordinators, supply chain staff

**Key Responsibilities:**
- Inventory tracking and management
- Supplier relationship management
- Purchase order processing
- Cost management

**Permissions Include:**
- ✅ Dashboard and analytics access
- ✅ Complete inventory management
- ✅ Purchase order management
- ✅ Supplier management
- ✅ Stock alerts and monitoring
- ✅ Menu pricing access

---

## 📋 **Permission Categories**

### **Dashboard & Analytics**
- `dashboard.view` - View dashboard and overview
- `analytics.view` - View analytics and reports
- `analytics.export` - Export analytics reports

### **Staff Management**
- `staff.view` - View staff members
- `staff.create` - Create new staff members
- `staff.edit` - Edit staff member details
- `staff.delete` - Delete staff members
- `staff.manage_roles` - Manage staff roles and permissions
- `staff.manage_schedule` - Manage staff schedules

### **Order Management**
- `orders.view` - View orders
- `orders.create` - Create new orders
- `orders.edit` - Edit existing orders
- `orders.cancel` - Cancel orders
- `orders.refund` - Process order refunds
- `orders.kitchen_display` - Access kitchen display system

### **Menu Management**
- `menu.view` - View menu items
- `menu.create` - Create new menu items
- `menu.edit` - Edit menu items
- `menu.delete` - Delete menu items
- `menu.manage_categories` - Manage menu categories
- `menu.manage_pricing` - Manage menu pricing

### **Tables & Reservations**
- `tables.view` - View table layout and status
- `tables.manage` - Manage table assignments
- `tables.configure` - Configure table layout
- `reservations.view` - View reservations
- `reservations.create` - Create new reservations
- `reservations.edit` - Edit reservations
- `reservations.cancel` - Cancel reservations

### **Inventory Management**
- `inventory.view` - View inventory levels
- `inventory.manage` - Manage inventory items
- `inventory.purchase_orders` - Manage purchase orders
- `inventory.suppliers` - Manage suppliers
- `inventory.stock_alerts` - Manage stock alerts

### **Customer Management**
- `customers.view` - View customer information
- `customers.manage` - Manage customer profiles
- `customers.loyalty` - Manage loyalty programs

### **Financial Management**
- `payments.process` - Process payments
- `payments.refunds` - Process refunds
- `financial.view_reports` - View financial reports
- `financial.manage_taxes` - Manage tax settings

### **Settings & Configuration**
- `settings.shop` - Manage shop settings
- `settings.system` - Manage system settings
- `settings.integrations` - Manage integrations

---

## 🚀 **Getting Started**

1. **Review the suggested roles** and see which ones match your restaurant's structure
2. **Customize permissions** as needed for your specific operations
3. **Create staff accounts** and assign appropriate roles
4. **Train your team** on their access levels and responsibilities
5. **Monitor and adjust** permissions based on operational needs

## 💡 **Best Practices**

- **Start with minimal permissions** and add more as needed
- **Regularly review** staff access levels
- **Use role-based access** rather than individual permissions
- **Document** any custom roles you create
- **Train staff** on their system access and responsibilities

## 🔧 **Customization**

These are suggested starting points. You can:
- **Modify existing roles** to match your needs
- **Create custom roles** for unique positions
- **Adjust permissions** based on your restaurant's workflow
- **Add new permissions** as your system grows
