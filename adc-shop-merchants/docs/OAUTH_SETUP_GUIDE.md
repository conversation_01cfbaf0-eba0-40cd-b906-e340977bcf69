# OAuth Implementation Guide

This guide provides step-by-step instructions for setting up OAuth providers with NextAuth.js in the ADC Shop Merchants application.

## Overview

The application now supports multiple OAuth providers:
- **Google OAuth** - Most widely used
- **GitHub OAuth** - Popular for developers
- **Facebook OAuth** - Social media integration
- **Discord OAuth** - Gaming/community focused

## Prerequisites

1. NextAuth.js is already configured in the application
2. Environment variables are set up
3. OAuth provider applications are created

## Provider Setup Instructions

### 1. Google OAuth Setup

#### Step 1: Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google+ API

#### Step 2: Configure OAuth Consent Screen
1. Go to "APIs & Services" > "OAuth consent screen"
2. Choose "External" user type
3. Fill in required information:
   - App name: "ADC Shop Merchants"
   - User support email: your email
   - Developer contact information: your email

#### Step 3: Create OAuth Credentials
1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth client ID"
3. Choose "Web application"
4. Add authorized redirect URIs:
   - `http://localhost:4000/api/auth/callback/google` (development)
   - `https://yourdomain.com/api/auth/callback/google` (production)

#### Step 4: Add to Environment Variables
```env
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

### 2. GitHub OAuth Setup

#### Step 1: Create GitHub OAuth App
1. Go to GitHub Settings > Developer settings > OAuth Apps
2. Click "New OAuth App"
3. Fill in application details:
   - Application name: "ADC Shop Merchants"
   - Homepage URL: `http://localhost:4000` (development)
   - Authorization callback URL: `http://localhost:4000/api/auth/callback/github`

#### Step 2: Add to Environment Variables
```env
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
```

### 3. Facebook OAuth Setup

#### Step 1: Create Facebook App
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app
3. Add "Facebook Login" product

#### Step 2: Configure Facebook Login
1. Go to Facebook Login > Settings
2. Add Valid OAuth Redirect URIs:
   - `http://localhost:4000/api/auth/callback/facebook`
   - `https://yourdomain.com/api/auth/callback/facebook`

#### Step 3: Add to Environment Variables
```env
FACEBOOK_CLIENT_ID=your-facebook-app-id
FACEBOOK_CLIENT_SECRET=your-facebook-app-secret
```

### 4. Discord OAuth Setup

#### Step 1: Create Discord Application
1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Create a new application
3. Go to OAuth2 section

#### Step 2: Configure OAuth2
1. Add redirect URIs:
   - `http://localhost:4000/api/auth/callback/discord`
   - `https://yourdomain.com/api/auth/callback/discord`
2. Select required scopes: `identify`, `email`

#### Step 3: Add to Environment Variables
```env
DISCORD_CLIENT_ID=your-discord-client-id
DISCORD_CLIENT_SECRET=your-discord-client-secret
```

## Environment Configuration

Create or update your `.env.local` file:

```env
# NextAuth Configuration
NEXTAUTH_SECRET=your-nextauth-secret-key-here
NEXTAUTH_URL=http://localhost:4000

# OAuth Provider Configuration
# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# GitHub OAuth
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# Facebook OAuth
FACEBOOK_CLIENT_ID=your-facebook-app-id
FACEBOOK_CLIENT_SECRET=your-facebook-app-secret

# Discord OAuth
DISCORD_CLIENT_ID=your-discord-client-id
DISCORD_CLIENT_SECRET=your-discord-client-secret
```

## Testing OAuth Integration

### Development Testing
1. Start the development server: `npm run dev`
2. Navigate to `/login` or `/register`
3. Click on any OAuth provider button
4. Complete the OAuth flow
5. Verify successful authentication and redirect to `/app`

### Production Deployment
1. Update all OAuth provider redirect URIs to production URLs
2. Update `NEXTAUTH_URL` environment variable
3. Deploy application
4. Test OAuth flows in production environment

## Security Considerations

1. **Environment Variables**: Never commit OAuth secrets to version control
2. **Redirect URIs**: Only add trusted domains to OAuth provider settings
3. **HTTPS**: Always use HTTPS in production for OAuth callbacks
4. **Scope Permissions**: Only request necessary OAuth scopes
5. **Token Storage**: NextAuth handles secure token storage automatically

## Troubleshooting

### Common Issues

1. **Invalid Redirect URI**: Ensure callback URLs match exactly in provider settings
2. **Missing Environment Variables**: Check all required OAuth variables are set
3. **CORS Errors**: Verify domain settings in OAuth provider configuration
4. **Token Errors**: Check OAuth app permissions and scopes

### Debug Mode
Enable NextAuth debug mode in development:
```env
NEXTAUTH_DEBUG=true
```

## Features Implemented

- ✅ Multiple OAuth provider support
- ✅ Automatic user creation in Supabase
- ✅ Consistent design system integration
- ✅ Loading states and error handling
- ✅ Responsive OAuth buttons
- ✅ Secure token management
- ✅ Role-based access control

## Next Steps

1. Set up OAuth provider applications
2. Configure environment variables
3. Test OAuth flows
4. Deploy to production
5. Monitor OAuth authentication metrics
