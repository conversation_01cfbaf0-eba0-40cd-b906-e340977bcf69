# Centralized Tag System Integration Guide

## Overview

This guide shows how to integrate the centralized tag system with all modules in your restaurant management platform. The tag system provides consistent tagging across all entities.

## 🎯 **Integration Examples by Module**

### 1. **Menu Items** ✅ (Already Integrated)

**Backend Integration:**
```go
// In menu service
func (s *MenuService) CreateMenuItem(ctx context.Context, req *types.CreateMenuItemRequest) (*types.MenuItemResponse, error) {
    // ... create menu item logic ...
    
    // Sync tags with centralized system
    if len(req.Tags) > 0 && s.entityTagService != nil {
        if err := s.entityTagService.SyncMenuItemTags(ctx, item.ID, req.Tags); err != nil {
            s.logger.WithError(err).Error("Failed to sync menu item tags")
        }
    }
    
    return response, nil
}
```

**Frontend Integration:**
```tsx
// In MenuItemForm.tsx
<TagSelector
  shopId={shopId}
  branchId={branchId}
  value={watch('tags') || []}
  onChange={(tags) => setValue('tags', tags)}
  entityType="menu_item"
  placeholder="Add tags to help customers find this item..."
  allowCustomTags={true}
  maxTags={10}
/>
```

### 2. **Reviews**

**Backend Integration:**
```go
// In review service
func (s *ReviewService) CreateReview(ctx context.Context, req *types.CreateReviewRequest) (*models.Review, error) {
    // ... create review logic ...
    
    // Sync tags with centralized system
    if len(req.Tags) > 0 && s.entityTagService != nil {
        if err := s.entityTagService.SyncReviewTags(ctx, review.ID, req.Tags); err != nil {
            s.logger.WithError(err).Error("Failed to sync review tags")
        }
    }
    
    return review, nil
}
```

**Frontend Integration:**
```tsx
// In ReviewForm.tsx
<TagSelector
  shopId={shopId}
  branchId={branchId}
  value={reviewTags}
  onChange={setReviewTags}
  entityType="review"
  placeholder="Tag this review (e.g., excellent service, great food)..."
  categoryFilter="review"
  maxTags={5}
/>
```

### 3. **Staff Management**

**Use Cases:**
- Skills: "Barista", "Sommelier", "Pastry Chef"
- Certifications: "Food Safety", "Wine Expert"
- Specialties: "Italian Cuisine", "Cocktails"

**Backend Integration:**
```go
// In staff service
func (s *StaffService) UpdateStaffProfile(ctx context.Context, staffID uuid.UUID, req *types.UpdateStaffRequest) error {
    // ... update staff logic ...
    
    // Sync skills/certifications as tags
    if len(req.Skills) > 0 && s.entityTagService != nil {
        if err := s.entityTagService.SyncEntityTags(ctx, "staff", staffID, req.BranchID, req.Skills); err != nil {
            s.logger.WithError(err).Error("Failed to sync staff tags")
        }
    }
    
    return nil
}
```

**Frontend Integration:**
```tsx
// In StaffForm.tsx
<div className="space-y-4">
  <Label>Skills & Certifications</Label>
  <TagSelector
    shopId={shopId}
    branchId={branchId}
    value={staffSkills}
    onChange={setStaffSkills}
    entityType="staff"
    placeholder="Add skills, certifications, specialties..."
    categoryFilter="skills"
    allowCustomTags={true}
    maxTags={15}
  />
</div>
```

### 4. **Orders**

**Use Cases:**
- Order types: "Takeout", "Delivery", "Dine-in"
- Special requests: "No onions", "Extra spicy", "Gluten-free"
- Occasions: "Birthday", "Anniversary", "Business lunch"

**Backend Integration:**
```go
// In order service
func (s *OrderService) CreateOrder(ctx context.Context, req *types.CreateOrderRequest) (*models.Order, error) {
    // ... create order logic ...
    
    // Sync order tags (special requests, occasion, etc.)
    if len(req.Tags) > 0 && s.entityTagService != nil {
        if err := s.entityTagService.SyncOrderTags(ctx, order.ID, req.Tags); err != nil {
            s.logger.WithError(err).Error("Failed to sync order tags")
        }
    }
    
    return order, nil
}
```

**Frontend Integration:**
```tsx
// In OrderForm.tsx
<div className="space-y-4">
  <Label>Special Requests & Notes</Label>
  <TagSelector
    shopId={shopId}
    branchId={branchId}
    value={orderTags}
    onChange={setOrderTags}
    entityType="order"
    placeholder="Add special requests, dietary needs, occasion..."
    categoryFilter="order"
    allowCustomTags={true}
    maxTags={8}
  />
</div>
```

### 5. **Reservations**

**Use Cases:**
- Occasions: "Date night", "Business meeting", "Family dinner"
- Special needs: "Wheelchair accessible", "Quiet table", "Window seat"
- Group types: "Large party", "Kids friendly"

**Frontend Integration:**
```tsx
// In ReservationForm.tsx
<div className="space-y-4">
  <Label>Occasion & Special Requests</Label>
  <TagSelector
    shopId={shopId}
    branchId={branchId}
    value={reservationTags}
    onChange={setReservationTags}
    entityType="reservation"
    placeholder="Occasion, special requests, seating preferences..."
    categoryFilter="occasion"
    allowCustomTags={true}
    maxTags={6}
  />
</div>
```

### 6. **Tables**

**Use Cases:**
- Table features: "Window view", "Outdoor", "Private booth"
- Capacity: "2-person", "Family table", "Large group"
- Ambiance: "Romantic", "Business", "Casual"

**Frontend Integration:**
```tsx
// In TableForm.tsx
<div className="space-y-4">
  <Label>Table Features & Ambiance</Label>
  <TagSelector
    shopId={shopId}
    branchId={branchId}
    value={tableTags}
    onChange={setTableTags}
    entityType="table"
    placeholder="Features, ambiance, special characteristics..."
    categoryFilter="features"
    allowCustomTags={true}
    maxTags={8}
  />
</div>
```

### 7. **Campaigns**

**Use Cases:**
- Campaign types: "Seasonal", "Holiday", "Loyalty program"
- Target audience: "New customers", "VIP members", "Local residents"
- Channels: "Email", "SMS", "Social media"

**Frontend Integration:**
```tsx
// In CampaignForm.tsx
<div className="space-y-4">
  <Label>Campaign Tags</Label>
  <TagSelector
    shopId={shopId}
    branchId={branchId}
    value={campaignTags}
    onChange={setCampaignTags}
    entityType="campaign"
    placeholder="Campaign type, target audience, channels..."
    categoryFilter="marketing"
    allowCustomTags={true}
    maxTags={10}
  />
</div>
```

### 8. **Notifications**

**Use Cases:**
- Priority: "Urgent", "Important", "Info"
- Categories: "Order update", "Promotion", "System alert"
- Audience: "All staff", "Managers only", "Kitchen staff"

**Frontend Integration:**
```tsx
// In NotificationForm.tsx
<div className="space-y-4">
  <Label>Notification Tags</Label>
  <TagSelector
    shopId={shopId}
    branchId={branchId}
    value={notificationTags}
    onChange={setNotificationTags}
    entityType="notification"
    placeholder="Priority, category, target audience..."
    categoryFilter="system"
    allowCustomTags={false}
    maxTags={5}
  />
</div>
```

## 🔧 **Service Integration Pattern**

### 1. **Update Service Constructor**
```go
func NewYourService(
    // ... existing dependencies ...
    entityTagService EntityTagService,
    logger *logrus.Logger,
) YourService {
    return &yourService{
        // ... existing fields ...
        entityTagService: entityTagService,
        logger:           logger,
    }
}
```

### 2. **Add Tag Sync in Create/Update Methods**
```go
func (s *yourService) CreateEntity(ctx context.Context, req *types.CreateEntityRequest) (*models.Entity, error) {
    // ... create entity logic ...
    
    // Sync tags with centralized system
    if len(req.Tags) > 0 && s.entityTagService != nil {
        if err := s.entityTagService.SyncEntityTags(ctx, "entity_type", entity.ID, entity.BranchID, req.Tags); err != nil {
            s.logger.WithError(err).Error("Failed to sync entity tags")
            // Don't fail the entire operation, just log the error
        }
    }
    
    return entity, nil
}
```

### 3. **Add Tag Retrieval in Get Methods**
```go
func (s *yourService) GetEntity(ctx context.Context, entityID uuid.UUID) (*types.EntityResponse, error) {
    // ... get entity logic ...
    
    // Get tags for the entity
    if s.entityTagService != nil {
        tags, err := s.entityTagService.GetEntityTags(ctx, "entity_type", entityID)
        if err != nil {
            s.logger.WithError(err).Error("Failed to get entity tags")
        } else {
            response.Tags = convertTagsToStrings(tags)
        }
    }
    
    return response, nil
}
```

## 📊 **Analytics Integration**

### Tag Usage Analytics
```go
// Get tag usage across all entities
func (s *AnalyticsService) GetTagAnalytics(ctx context.Context, branchID uuid.UUID) (*types.TagAnalyticsResponse, error) {
    usage, err := s.entityTagService.GetTagUsageByEntity(ctx, branchID)
    if err != nil {
        return nil, err
    }
    
    return &types.TagAnalyticsResponse{
        UsageByEntity: usage,
        TotalTags:     calculateTotalTags(usage),
        MostUsedTags:  getMostUsedTags(ctx, branchID),
    }, nil
}
```

### Search by Tags
```go
// Find entities by tags
func (s *SearchService) SearchByTags(ctx context.Context, entityType string, branchID uuid.UUID, tags []string) ([]uuid.UUID, error) {
    return s.entityTagService.GetEntitiesByTags(ctx, entityType, branchID, tags)
}
```

## 🎨 **Frontend Best Practices**

### 1. **Consistent Tag Categories by Entity Type**
```tsx
const getDefaultCategoryForEntity = (entityType: string) => {
  switch (entityType) {
    case 'menu_item': return 'dietary';
    case 'review': return 'feedback';
    case 'staff': return 'skills';
    case 'order': return 'requests';
    case 'reservation': return 'occasion';
    case 'table': return 'features';
    case 'campaign': return 'marketing';
    case 'notification': return 'system';
    default: return '';
  }
};
```

### 2. **Entity-Specific Tag Limits**
```tsx
const getMaxTagsForEntity = (entityType: string) => {
  switch (entityType) {
    case 'menu_item': return 10;
    case 'review': return 5;
    case 'staff': return 15;
    case 'order': return 8;
    case 'reservation': return 6;
    case 'table': return 8;
    case 'campaign': return 10;
    case 'notification': return 5;
    default: return 5;
  }
};
```

### 3. **Reusable Tag Display Component**
```tsx
export function EntityTags({ entityType, entityId, shopId, branchId }: EntityTagsProps) {
  const { data: tags } = useGetEntityTagsQuery({ entityType, entityId, shopId, branchId });
  
  return (
    <div className="flex flex-wrap gap-1">
      {tags?.map((tag) => (
        <Badge key={tag.id} variant="outline" style={{ borderColor: tag.color }}>
          {tag.icon && <span className="mr-1">{tag.icon}</span>}
          {tag.name}
        </Badge>
      ))}
    </div>
  );
}
```

## 🚀 **Implementation Checklist**

### Backend
- [ ] Add EntityTagService dependency to all relevant services
- [ ] Update service constructors
- [ ] Add tag sync in create/update methods
- [ ] Add tag retrieval in get methods
- [ ] Update API request/response types to include tags

### Frontend
- [ ] Replace existing tag inputs with TagSelector component
- [ ] Add tag display components to entity views
- [ ] Update forms to include tag fields
- [ ] Add tag filtering to list views
- [ ] Implement tag-based search functionality

### Testing
- [ ] Test tag creation and assignment
- [ ] Test tag search and suggestions
- [ ] Test tag analytics
- [ ] Test cross-entity tag consistency
- [ ] Test performance with large tag datasets

This comprehensive integration ensures consistent tagging across your entire restaurant management platform! 🎉
