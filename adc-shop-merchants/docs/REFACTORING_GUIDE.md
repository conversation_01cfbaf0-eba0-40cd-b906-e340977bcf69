# Code Refactoring Guide

This document outlines the comprehensive refactoring improvements made to align the codebase with modern best practices.

## ✅ Completed Improvements

### 1. **shadcn/ui Component Migration**

#### Added Components:
- **Pagination** (`src/components/ui/pagination.tsx`)
  - Complete pagination component with Previous/Next navigation
  - Ellipsis support for large page counts
  - Accessible and responsive design

- **Tabs** (Enhanced existing `src/components/ui/tabs.tsx`)
  - Radix UI based tabs with proper accessibility
  - Customizable styling with earth tone theme

- **Data Table** (`src/components/ui/data-table.tsx`)
  - TanStack Table integration
  - Built-in search, sorting, and pagination
  - Column visibility toggle
  - Responsive design with earth tone styling

#### Updated Example:
```tsx
// Before: Custom tab implementation
<div className="flex border-b border-[#e2dcd4] gap-8">
  <button onClick={() => setActiveTab('floor-plan')}>Floor Plan</button>
</div>

// After: shadcn Tabs
<Tabs value={activeTab} onValueChange={setActiveTab}>
  <TabsList>
    <TabsTrigger value="floor-plan">Floor Plan</TabsTrigger>
  </TabsList>
  <TabsContent value="floor-plan">Content</TabsContent>
</Tabs>
```

### 2. **Form Management Standardization**

#### New Form Components:
- **FormWrapper** (`src/components/forms/FormWrapper.tsx`)
  - React Hook Form + Zod integration
  - Automatic error handling and validation
  - Loading states and submission handling

- **FormField Components** (`src/components/forms/FormField.tsx`)
  - InputField, TextareaField, SelectField
  - CheckboxField, RadioField
  - Consistent styling and error display

#### Usage Example:
```tsx
import { FormWrapper, InputField, SelectField } from '@/components/forms'
import { z } from 'zod'

const schema = z.object({
  name: z.string().min(1, 'Name is required'),
  category: z.string().min(1, 'Category is required'),
})

function MyForm() {
  return (
    <FormWrapper
      schema={schema}
      onSubmit={async (data) => {
        // Handle form submission
      }}
    >
      <InputField name="name" label="Name" required />
      <SelectField
        name="category"
        label="Category"
        options={[
          { value: 'appetizer', label: 'Appetizer' },
          { value: 'main', label: 'Main Course' }
        ]}
      />
    </FormWrapper>
  )
}
```

### 3. **Utility Functions**

#### Date & Number Formatting (`src/lib/utils/formatters.ts`)
```tsx
import { dateFormatters, numberFormatters } from '@/lib/utils/formatters'

// Date formatting
dateFormatters.display(new Date()) // "Jan 15, 2024"
dateFormatters.smart(new Date()) // "Today", "Yesterday", or date
dateFormatters.relative(new Date()) // "2 hours ago"

// Number formatting
numberFormatters.currency(1234.56) // "$1,234.56"
numberFormatters.percentage(0.1234) // "12.34%"
numberFormatters.compact(1234567) // "1.2M"
```

#### Pagination Utilities (`src/lib/utils/pagination.ts`)
```tsx
import { calculatePagination, paginateArray } from '@/lib/utils/pagination'

const { data, pagination } = paginateArray(items, page, limit)
const pageNumbers = generatePageNumbers(currentPage, totalPages)
```

#### Error Handling (`src/lib/utils/error-handling.ts`)
```tsx
import { errorHandlers, AppError } from '@/lib/utils/error-handling'

// Handle API errors
const error = errorHandlers.handleApiError(apiError)

// Show toast notifications
errorHandlers.showErrorToast('Something went wrong')
errorHandlers.showSuccessToast('Operation completed')
```

### 4. **Authentication Enhancement**

#### NextAuth Configuration (`src/lib/auth/nextauth.config.ts`)
- Credentials provider with Supabase integration
- JWT strategy with role-based access
- Custom pages for sign-in/out

#### Auth Middleware (`src/lib/middleware/auth.ts`)
```tsx
import { withAuth } from '@/lib/middleware/auth'

export const GET = withAuth(
  async (request, { user }) => {
    // Handler with authenticated user
  },
  { allowedRoles: ['admin', 'merchant'] }
)
```

## 🚧 Next Steps

### 1. **Golang Backend Setup**

#### Project Structure Created:
```
backend/
├── cmd/server/main.go
├── internal/
│   ├── handlers/
│   ├── models/
│   ├── services/
│   └── repositories/
├── migrations/
└── docker-compose.yml
```

#### Implementation Plan:
1. **Database Models** with GORM
   - User, Merchant, MenuItem, Reservation, Order models
   - Proper relationships and constraints

2. **API Handlers** with Gin
   - RESTful endpoints for all resources
   - Middleware for auth, CORS, logging

3. **Business Logic** in Services
   - Separation of concerns
   - Reusable business logic

### 2. **Complete API Migration**

#### Current State:
- ✅ RTK Query setup in place
- ✅ API slice configuration
- ❌ Need to migrate all endpoints to RTK Query

#### Migration Plan:
```tsx
// Replace direct API calls with RTK Query hooks
const { data: menuItems, isLoading } = useGetMenuItemsQuery(merchantId)
const [createMenuItem] = useCreateMenuItemMutation()
```

### 3. **Additional shadcn Components**

#### Missing Components to Add:
- **Toast** (Sonner integration complete)
- **Modal/Dialog** (Basic dialog exists, need specialized modals)
- **Badge** (For status indicators)
- **Card** (For consistent layouts)
- **Alert** (For notifications)

### 4. **Testing Setup**

#### Recommended Testing Stack:
- **Unit Tests**: Jest + React Testing Library
- **Integration Tests**: Playwright or Cypress
- **API Tests**: Supertest for backend

## 📋 Migration Checklist

### Immediate Actions:
- [ ] Install missing dependencies for backend
- [ ] Set up Golang development environment
- [ ] Create database migrations
- [ ] Implement core API endpoints

### Form Migration:
- [ ] Identify all existing forms
- [ ] Migrate to FormWrapper + FormField components
- [ ] Add proper validation schemas
- [ ] Test form submissions

### Component Migration:
- [ ] Replace custom modals with shadcn Dialog
- [ ] Add missing UI components (Badge, Card, Alert)
- [ ] Update existing components to use new utilities

### API Migration:
- [ ] Create RTK Query endpoints for all resources
- [ ] Replace mock data with real API calls
- [ ] Add proper error handling
- [ ] Implement optimistic updates

## 🎯 Benefits Achieved

1. **Consistency**: Standardized components and patterns
2. **Maintainability**: Centralized utilities and error handling
3. **Developer Experience**: Better TypeScript support and IntelliSense
4. **Performance**: Optimized re-renders and caching with RTK Query
5. **Accessibility**: shadcn components are accessible by default
6. **Scalability**: Modular architecture supports growth

## 📚 Additional Conventions

### File Naming:
- Components: PascalCase (`MenuItemForm.tsx`)
- Utilities: camelCase (`formatters.ts`)
- Hooks: camelCase with `use` prefix (`useMenuItems.ts`)

### Import Organization:
```tsx
// 1. React and Next.js
import React from 'react'
import { NextPage } from 'next'

// 2. Third-party libraries
import { z } from 'zod'

// 3. Internal utilities and hooks
import { dateFormatters } from '@/lib/utils/formatters'

// 4. Components
import { FormWrapper } from '@/components/forms'
import { Button } from '@/components/ui/button'
```

### Error Handling Pattern:
```tsx
try {
  const result = await apiCall()
  errorHandlers.showSuccessToast('Success!')
} catch (error) {
  const appError = errorHandlers.handleApiError(error)
  errorHandlers.showErrorToast(appError)
}
```

This refactoring provides a solid foundation for scalable, maintainable code that follows modern React and TypeScript best practices.

## 🚀 Installation & Setup

### Frontend Dependencies
```bash
# Install new dependencies
bun add @tanstack/react-table next-auth

# Install development dependencies if needed
bun add -D @types/next-auth
```

### Backend Setup
```bash
# Navigate to backend directory
cd backend

# Initialize Go modules
go mod init restaurant-backend
go mod tidy

# Start development with Docker
docker-compose up --build

# Or start individual services
docker-compose up postgres redis
go run cmd/server/main.go
```

### Environment Variables
Create `.env` files in both root and backend directories:

**Frontend (.env.local):**
```env
NEXTAUTH_URL=http://localhost:4000
NEXTAUTH_SECRET=your-nextauth-secret
NEXT_PUBLIC_API_URL=http://localhost:8080/api/v1
```

**Backend (.env):**
```env
PORT=8080
GIN_MODE=debug
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=restaurant_db
JWT_SECRET=your-super-secret-jwt-key
```

## 📝 Usage Examples

### Using New Form Components
```tsx
import { FormWrapper, InputField, SelectField } from '@/components/forms'
import { z } from 'zod'

const reservationSchema = z.object({
  customerName: z.string().min(1, 'Name is required'),
  partySize: z.number().min(1).max(20),
  date: z.string(),
  time: z.string(),
  specialRequests: z.string().optional(),
})

function ReservationForm() {
  return (
    <FormWrapper
      schema={reservationSchema}
      onSubmit={async (data) => {
        // Handle submission with RTK Query
        await createReservation(data).unwrap()
        errorHandlers.showSuccessToast('Reservation created!')
      }}
    >
      <FormGrid columns={2}>
        <InputField name="customerName" label="Customer Name" required />
        <InputField name="partySize" label="Party Size" type="number" required />
        <InputField name="date" label="Date" type="date" required />
        <InputField name="time" label="Time" type="time" required />
      </FormGrid>
      <TextareaField name="specialRequests" label="Special Requests" />
    </FormWrapper>
  )
}
```

### Using Data Table
```tsx
import { DataTable } from '@/components/ui/data-table'
import { ColumnDef } from '@tanstack/react-table'

const columns: ColumnDef<Reservation>[] = [
  {
    accessorKey: 'customerName',
    header: 'Customer',
  },
  {
    accessorKey: 'date',
    header: 'Date',
    cell: ({ row }) => dateFormatters.display(row.getValue('date')),
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => (
      <Badge variant={row.getValue('status') === 'confirmed' ? 'default' : 'secondary'}>
        {row.getValue('status')}
      </Badge>
    ),
  },
]

function ReservationsTable({ data }: { data: Reservation[] }) {
  return (
    <DataTable
      columns={columns}
      data={data}
      searchKey="customerName"
      searchPlaceholder="Search customers..."
    />
  )
}
```

### Using Formatters
```tsx
import { dateFormatters, numberFormatters } from '@/lib/utils/formatters'

function OrderSummary({ order }: { order: Order }) {
  return (
    <div>
      <p>Order Date: {dateFormatters.smart(order.createdAt)}</p>
      <p>Total: {numberFormatters.currency(order.total)}</p>
      <p>Items: {numberFormatters.withCommas(order.itemCount)}</p>
    </div>
  )
}
```

## 🔄 Migration Steps

### 1. Install Dependencies
```bash
bun install
```

### 2. Update Existing Forms
Replace existing form implementations with new FormWrapper components:

```tsx
// Before
const [formData, setFormData] = useState({})
const handleSubmit = (e) => { /* manual handling */ }

// After
<FormWrapper schema={schema} onSubmit={handleSubmit}>
  <InputField name="field" label="Label" />
</FormWrapper>
```

### 3. Replace Custom Tables
```tsx
// Before
<table>
  <thead>...</thead>
  <tbody>...</tbody>
</table>

// After
<DataTable columns={columns} data={data} />
```

### 4. Update Error Handling
```tsx
// Before
try {
  await apiCall()
} catch (error) {
  console.error(error)
}

// After
try {
  await apiCall()
  errorHandlers.showSuccessToast('Success!')
} catch (error) {
  errorHandlers.showErrorToast(error)
}
```

This refactoring provides a solid foundation for scalable, maintainable code that follows modern React and TypeScript best practices.
