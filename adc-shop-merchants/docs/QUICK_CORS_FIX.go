// Quick CORS Fix for your Go backend
// Add this middleware to your main.go or routes.go file

package main

import (
	"github.com/gin-gonic/gin"
)

// Add this function to your main.go
func setupCORS(r *gin.Engine) {
	r.Use(func(c *gin.Context) {
		// Set CORS headers for all requests
		c.<PERSON><PERSON>("Access-Control-Allow-Origin", "http://localhost:4000")
		c<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, PATCH")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Content-Type, Authorization, x-user-email, x-user-id, x-user-name")
		c.<PERSON><PERSON>("Access-Control-Allow-Credentials", "true")
		
		// Handle preflight OPTIONS requests
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		
		c.Next()
	})
}

// Update your main function like this:
func main() {
	r := gin.Default()
	
	// Add CORS middleware FIRST
	setupCORS(r)
	
	// Then add your other middleware and routes
	// ... your existing code
	
	r.Run(":8080")
}

// Alternative: If you're using gin-contrib/cors package
// go get github.com/gin-contrib/cors
/*
import (
	"github.com/gin-contrib/cors"
	"time"
)

func setupCORSWithPackage(r *gin.Engine) {
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"http://localhost:4000"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"},
		AllowHeaders:     []string{"Content-Type", "Authorization", "x-user-email", "x-user-id", "x-user-name"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))
}
*/
