# Backend Authentication Integration Guide

This guide explains how to integrate the frontend authentication with your Go backend API.

## 🔐 Authentication Headers Sent by Frontend

The frontend sends the following headers with each API request:

```
Authorization: Bearer <EMAIL>
X-User-Email: <EMAIL>
X-User-Id: 100635498199217100373
X-User-Name: Scandine
Content-Type: application/json
```

## 🛠 Backend Middleware Implementation

### Go Gin Middleware Example

Create an authentication middleware in your Go backend:

```go
package middleware

import (
    "net/http"
    "strings"

    "github.com/gin-gonic/gin"
)

// AuthMiddleware validates the authentication headers from NextAuth
func AuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // Get the authorization header
        authHeader := c.GetHeader("Authorization")
        if authHeader == "" {
            c.JSON(http.StatusUnauthorized, gin.H{
                "error": "Authorization header required",
            })
            c.Abort()
            return
        }

        // Extract the token (email) from Bearer token
        tokenParts := strings.Split(authHeader, " ")
        if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
            c.JSON(http.StatusUnauthorized, gin.H{
                "error": "Invalid authorization header format",
            })
            c.Abort()
            return
        }

        userEmail := tokenParts[1]

        // Get additional user information from headers
        userId := c.GetHeader("X-User-Id")
        userName := c.GetHeader("X-User-Name")

        // Validate the user (you can check against your database)
        if !isValidUser(userEmail) {
            c.JSON(http.StatusUnauthorized, gin.H{
                "error": "Invalid user",
            })
            c.Abort()
            return
        }

        // Set user information in context for use in handlers
        c.Set("user_email", userEmail)
        c.Set("user_id", userId)
        c.Set("user_name", userName)

        c.Next()
    }
}

// isValidUser validates if the user exists and is authorized
func isValidUser(email string) bool {
    // Implement your user validation logic here
    // For now, we'll allow any email that looks valid
    return strings.Contains(email, "@")
}
```

### Apply Middleware to Routes

```go
package main

import (
    "github.com/gin-gonic/gin"
    "your-app/middleware"
    "your-app/handlers"
)

func main() {
    r := gin.Default()

    // CORS middleware (important for frontend integration)
    r.Use(func(c *gin.Context) {
        c.Header("Access-Control-Allow-Origin", "http://localhost:4000")
        c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, PATCH")
        c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization, x-user-email, x-user-id, x-user-name")
        c.Header("Access-Control-Allow-Credentials", "true")
        c.Header("Access-Control-Max-Age", "86400") // 24 hours

        if c.Request.Method == "OPTIONS" {
            c.AbortWithStatus(204)
            return
        }

        c.Next()
    })

    // API routes with authentication
    api := r.Group("/api/v1")
    api.Use(middleware.AuthMiddleware())
    {
        api.GET("/merchants", handlers.GetMerchants)
        api.GET("/merchants/:id/menu/items", handlers.GetMenuItems)
        api.POST("/merchants/:id/menu/items", handlers.CreateMenuItem)
        // ... other routes
    }

    r.Run(":8080")
}
```

## 📊 User Context in Handlers

Access user information in your handlers:

```go
package handlers

import (
    "net/http"
    "github.com/gin-gonic/gin"
)

func GetMerchants(c *gin.Context) {
    // Get user information from context
    userEmail := c.GetString("user_email")
    userId := c.GetString("user_id")
    userName := c.GetString("user_name")

    // Use user information to filter merchants
    merchants, err := getMerchantsForUser(userEmail)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{
            "error": "Failed to fetch merchants",
        })
        return
    }

    c.JSON(http.StatusOK, gin.H{
        "data": merchants,
        "user": gin.H{
            "email": userEmail,
            "id": userId,
            "name": userName,
        },
    })
}

func getMerchantsForUser(email string) ([]Merchant, error) {
    // Implement your business logic to fetch merchants for the user
    // This could involve database queries based on user permissions
    return []Merchant{}, nil
}
```

## 🔧 Environment Configuration

Make sure your backend has the correct CORS configuration:

```go
// CORS configuration for development
func setupCORS() gin.HandlerFunc {
    return func(c *gin.Context) {
        origin := c.Request.Header.Get("Origin")

        // Allow specific origins
        allowedOrigins := []string{
            "http://localhost:4000",  // Development frontend
            "http://localhost:3000",  // Alternative dev port
            "https://yourdomain.com", // Production frontend
        }

        for _, allowedOrigin := range allowedOrigins {
            if origin == allowedOrigin {
                c.Header("Access-Control-Allow-Origin", origin)
                break
            }
        }

        c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
        c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization, X-User-Email, X-User-Id, X-User-Name")
        c.Header("Access-Control-Allow-Credentials", "true")

        if c.Request.Method == "OPTIONS" {
            c.AbortWithStatus(204)
            return
        }

        c.Next()
    }
}
```

## 🧪 Testing the Integration

### 1. Test Authentication Headers

Create a simple test endpoint:

```go
func TestAuth(c *gin.Context) {
    userEmail := c.GetString("user_email")
    userId := c.GetString("user_id")
    userName := c.GetString("user_name")

    c.JSON(http.StatusOK, gin.H{
        "message": "Authentication successful",
        "user": gin.H{
            "email": userEmail,
            "id": userId,
            "name": userName,
        },
        "headers": gin.H{
            "authorization": c.GetHeader("Authorization"),
            "user_email": c.GetHeader("X-User-Email"),
            "user_id": c.GetHeader("X-User-Id"),
            "user_name": c.GetHeader("X-User-Name"),
        },
    })
}
```

### 2. Test from Frontend

Add this test endpoint to your routes and call it from the frontend to verify authentication is working.

## 🚨 Security Considerations

### Production Recommendations

1. **Use JWT Tokens**: Instead of sending email directly, use proper JWT tokens
2. **Token Validation**: Validate tokens against your user database
3. **Rate Limiting**: Implement rate limiting to prevent abuse
4. **HTTPS Only**: Use HTTPS in production
5. **Token Expiration**: Implement token refresh mechanisms

### Enhanced Security Implementation

```go
// Enhanced authentication with JWT validation
func EnhancedAuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        authHeader := c.GetHeader("Authorization")
        if authHeader == "" {
            c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization required"})
            c.Abort()
            return
        }

        // In production, validate JWT token here
        // token := extractToken(authHeader)
        // claims, err := validateJWT(token)
        // if err != nil {
        //     c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
        //     c.Abort()
        //     return
        // }

        // For now, use the simple email-based auth
        userEmail := strings.TrimPrefix(authHeader, "Bearer ")

        // Validate user exists in database
        user, err := getUserByEmail(userEmail)
        if err != nil {
            c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
            c.Abort()
            return
        }

        // Set user context
        c.Set("user", user)
        c.Next()
    }
}
```

## 📋 Troubleshooting

### Common Issues

1. **401 Unauthorized**: Check CORS configuration and authentication headers
2. **CORS Errors**: Ensure proper CORS middleware setup
3. **Missing Headers**: Verify frontend is sending all required headers
4. **Database Errors**: Check user table schema and permissions

### Debug Logging

Add logging to your middleware:

```go
func AuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        log.Printf("Auth Headers: %+v", map[string]string{
            "Authorization": c.GetHeader("Authorization"),
            "X-User-Email": c.GetHeader("X-User-Email"),
            "X-User-Id": c.GetHeader("X-User-Id"),
            "X-User-Name": c.GetHeader("X-User-Name"),
        })

        // ... rest of middleware logic
    }
}
```

This integration will allow your Go backend to properly authenticate users from the NextAuth frontend and provide user-specific data.
