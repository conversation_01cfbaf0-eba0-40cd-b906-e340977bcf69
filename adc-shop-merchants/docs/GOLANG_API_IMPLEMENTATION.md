# Golang API Implementation Task

## Database Configuration

**Supabase PostgreSQL Connection:**
```
postgresql://postgres:<EMAIL>:5432/postgres
```

## Implementation Tasks

### Phase 1: Core Setup ✅

#### 1. Database Models
- [x] User model with authentication fields
- [x] Merchant model for restaurant businesses
- [x] Branch model for multiple locations
- [x] MenuItem model for menu management
- [x] Table model for table management
- [x] Reservation model for booking system
- [x] Order and OrderItem models for order management
- [x] UserMerchant junction table for role-based access

#### 2. Database Configuration
- [x] GORM setup with PostgreSQL driver
- [x] Support for Supabase connection string
- [x] Auto-migration functionality
- [x] Connection pooling configuration

### Phase 2: Repository Layer 🚧

#### 1. Base Repository Pattern
- [x] Common pagination utilities
- [x] Base filtering structures
- [x] Validation helpers

#### 2. Reservation Repository ✅
- [x] CRUD operations for reservations
- [x] Advanced filtering (status, date, table, search)
- [x] Pagination support
- [x] Statistics and analytics
- [x] Table availability checking
- [x] Suggested time slots

#### 3. Remaining Repositories (TODO)
- [ ] User repository with authentication
- [ ] Merchant repository with business logic
- [ ] MenuItem repository with categorization
- [ ] Table repository with layout management
- [ ] Order repository with status tracking

### Phase 3: Service Layer (TODO)

#### 1. Authentication Service
```go
type AuthService interface {
    Login(email, password string) (*AuthResponse, error)
    Register(userData *RegisterRequest) (*User, error)
    RefreshToken(refreshToken string) (*AuthResponse, error)
    ValidateToken(token string) (*User, error)
}
```

#### 2. Reservation Service
```go
type ReservationService interface {
    CreateReservation(req *CreateReservationRequest) (*Reservation, error)
    UpdateReservation(id string, req *UpdateReservationRequest) (*Reservation, error)
    CancelReservation(id string) error
    ConfirmReservation(id string) error
    CheckAvailability(req *AvailabilityRequest) (*AvailabilityResponse, error)
    GetReservationStats(merchantID string, period string) (*ReservationStats, error)
}
```

#### 3. Menu Service
```go
type MenuService interface {
    CreateMenuItem(req *CreateMenuItemRequest) (*MenuItem, error)
    UpdateMenuItem(id string, req *UpdateMenuItemRequest) (*MenuItem, error)
    DeleteMenuItem(id string) error
    GetMenuItems(merchantID string, filters MenuFilters) ([]MenuItem, *PaginationResult, error)
    UpdateAvailability(id string, available bool) error
}
```

### Phase 4: Handler Layer (TODO)

#### 1. Authentication Handlers
- [ ] POST /api/v1/auth/login
- [ ] POST /api/v1/auth/register
- [ ] POST /api/v1/auth/refresh
- [ ] POST /api/v1/auth/logout

#### 2. Reservation Handlers
- [ ] GET /api/v1/merchants/:merchantId/reservations
- [ ] POST /api/v1/merchants/:merchantId/reservations
- [ ] GET /api/v1/merchants/:merchantId/reservations/:id
- [ ] PUT /api/v1/merchants/:merchantId/reservations/:id
- [ ] DELETE /api/v1/merchants/:merchantId/reservations/:id
- [ ] POST /api/v1/merchants/:merchantId/reservations/:id/confirm
- [ ] POST /api/v1/merchants/:merchantId/reservations/:id/cancel
- [ ] GET /api/v1/merchants/:merchantId/reservations/availability
- [ ] GET /api/v1/merchants/:merchantId/reservations/stats

#### 3. Menu Handlers
- [ ] GET /api/v1/merchants/:merchantId/menu-items
- [ ] POST /api/v1/merchants/:merchantId/menu-items
- [ ] GET /api/v1/merchants/:merchantId/menu-items/:id
- [ ] PUT /api/v1/merchants/:merchantId/menu-items/:id
- [ ] DELETE /api/v1/merchants/:merchantId/menu-items/:id

### Phase 5: Middleware (TODO)

#### 1. Authentication Middleware
```go
func AuthRequired() gin.HandlerFunc {
    return func(c *gin.Context) {
        token := extractToken(c)
        user, err := validateToken(token)
        if err != nil {
            c.JSON(401, gin.H{"error": "Unauthorized"})
            c.Abort()
            return
        }
        c.Set("user", user)
        c.Next()
    }
}
```

#### 2. Role-Based Access Control
```go
func RequireRole(roles ...string) gin.HandlerFunc {
    return func(c *gin.Context) {
        user := c.MustGet("user").(*User)
        if !hasRole(user, roles) {
            c.JSON(403, gin.H{"error": "Forbidden"})
            c.Abort()
            return
        }
        c.Next()
    }
}
```

#### 3. Merchant Access Control
```go
func RequireMerchantAccess() gin.HandlerFunc {
    return func(c *gin.Context) {
        user := c.MustGet("user").(*User)
        merchantID := c.Param("merchantId")
        if !hasAccessToMerchant(user, merchantID) {
            c.JSON(403, gin.H{"error": "Access denied"})
            c.Abort()
            return
        }
        c.Next()
    }
}
```

## Environment Setup

### 1. Environment Variables
```env
# Database
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# Server
PORT=8080
GIN_MODE=debug

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRY=24h

# CORS
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:4000
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization
```

### 2. Docker Setup
```bash
# Start the API server
cd backend
docker-compose up --build

# Or run locally
go mod tidy
go run cmd/server/main.go
```

## Testing Strategy

### 1. Unit Tests
```go
func TestReservationRepository_Create(t *testing.T) {
    db := setupTestDB()
    repo := NewReservationRepository(db)
    
    reservation := &models.Reservation{
        MerchantID:    "merchant-1",
        CustomerName:  "John Doe",
        Date:          time.Now().AddDate(0, 0, 1),
        Time:          "19:00",
        PartySize:     4,
        Status:        "pending",
    }
    
    err := repo.Create(reservation)
    assert.NoError(t, err)
    assert.NotEmpty(t, reservation.ID)
}
```

### 2. Integration Tests
```go
func TestReservationAPI_CreateReservation(t *testing.T) {
    router := setupTestRouter()
    
    payload := `{
        "customerName": "John Doe",
        "date": "2024-12-25",
        "time": "19:00",
        "partySize": 4
    }`
    
    req := httptest.NewRequest("POST", "/api/v1/merchants/merchant-1/reservations", strings.NewReader(payload))
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("Authorization", "Bearer "+testToken)
    
    w := httptest.NewRecorder()
    router.ServeHTTP(w, req)
    
    assert.Equal(t, 201, w.Code)
}
```

## API Documentation

### 1. Swagger Setup
```go
// @title Restaurant Management API
// @version 1.0
// @description API for restaurant management system
// @host localhost:8080
// @BasePath /api/v1
// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
```

### 2. Endpoint Documentation
```go
// CreateReservation creates a new reservation
// @Summary Create a new reservation
// @Description Create a new table reservation for a merchant
// @Tags reservations
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param reservation body CreateReservationRequest true "Reservation data"
// @Success 201 {object} Reservation
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Security BearerAuth
// @Router /merchants/{merchantId}/reservations [post]
```

## Deployment

### 1. Production Environment
```env
GIN_MODE=release
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
JWT_SECRET=production-secret-key
CORS_ALLOWED_ORIGINS=https://yourdomain.com
```

### 2. Health Checks
```go
router.GET("/health", func(c *gin.Context) {
    c.JSON(200, gin.H{
        "status":    "ok",
        "timestamp": time.Now().Unix(),
        "version":   "1.0.0",
    })
})
```

## Next Steps

1. **Complete Repository Layer** - Implement remaining repositories
2. **Build Service Layer** - Add business logic and validation
3. **Create Handler Layer** - Implement HTTP endpoints
4. **Add Middleware** - Authentication and authorization
5. **Write Tests** - Unit and integration tests
6. **API Documentation** - Complete Swagger documentation
7. **Deploy** - Set up production deployment

## Priority Order

1. **User & Auth** (High Priority)
2. **Reservation System** (High Priority) ✅
3. **Menu Management** (Medium Priority)
4. **Table Management** (Medium Priority)
5. **Order System** (Low Priority)
6. **Analytics & Reports** (Low Priority)
