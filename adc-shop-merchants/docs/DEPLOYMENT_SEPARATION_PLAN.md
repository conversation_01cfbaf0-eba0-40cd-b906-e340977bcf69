# Deployment Separation Plan

## Overview

This document outlines the plan to separate the current monolithic Next.js application into three distinct deployments:

1. **Shop Management App** - For restaurant owners and staff
2. **Customer Website** - For end customers to browse and order
3. **Admin Platform** - For platform administrators

## Current Architecture

```
Current Monolithic App (localhost:4000)
├── /app/* - Shop Management (Restaurant owners/staff)
├── /admin/* - Platform Administration
├── /auth/* - Authentication pages
└── API Routes - Proxy to Go backend (localhost:8080)
```

## Target Architecture

```
Shop Management App (shop.domain.com)
├── Dashboard, Menu, Orders, Tables, etc.
├── Authentication for shop owners/staff
└── API calls to shared backend

Customer Website (domain.com)
├── Browse restaurants/shops
├── Menu viewing, ordering
├── Customer authentication
└── API calls to shared backend

Admin Platform (admin.domain.com)
├── Platform administration
├── Merchant management
├── System analytics
└── API calls to shared backend

Shared Go Backend (api.domain.com)
├── All business logic
├── Database operations
├── Authentication services
└── File storage
```

## Implementation Steps

### Phase 1: Project Structure Setup

1. **Create separate Next.js applications**:
   ```
   /shop-management-app
   /customer-website
   /admin-platform
   /shared-backend (existing restaurant-backend)
   ```

2. **Extract shared components and utilities**:
   ```
   /shared-packages
   ├── /ui-components
   ├── /types
   ├── /utils
   └── /api-client
   ```

### Phase 2: Shop Management App

**Target URL**: `shop.domain.com` or `manage.domain.com`

**Features to migrate**:
- Restaurant dashboard (`/app/restaurant/*`)
- Menu management
- Order management
- Table management
- Staff management
- Analytics and reports
- Settings

**Authentication**:
- Shop owners and staff only
- Role-based access control
- Multi-shop support

### Phase 3: Customer Website

**Target URL**: `domain.com`

**New features to build**:
- Restaurant discovery/browsing
- Menu viewing by restaurant
- Online ordering system
- Customer registration/login
- Order tracking
- Reservation booking
- Customer reviews

**Authentication**:
- Customer accounts
- Guest ordering
- Social login options

### Phase 4: Admin Platform

**Target URL**: `admin.domain.com`

**Features to migrate**:
- Platform administration (`/admin/*`)
- Merchant management
- System analytics
- User management
- Platform settings

**Authentication**:
- Platform administrators only
- Super admin privileges

## Technical Considerations

### Shared Backend API

The Go backend will serve all three applications with:
- Unified authentication system
- Role-based API access
- CORS configuration for multiple domains
- Rate limiting per application type

### Database Schema

Current schema supports the separation:
- `shops` table for restaurant data
- `users` table with roles for different user types
- Existing relationships support multi-tenancy

### Deployment Strategy

1. **Development Environment**:
   - Shop Management: `localhost:3001`
   - Customer Website: `localhost:3002`
   - Admin Platform: `localhost:3003`
   - Backend API: `localhost:8080`

2. **Production Environment**:
   - Use subdomain routing
   - Shared SSL certificates
   - Load balancing if needed
   - CDN for static assets

### Authentication Flow

```
User Login → Determine User Type → Redirect to Appropriate App
├── Shop Owner/Staff → Shop Management App
├── Customer → Customer Website
└── Admin → Admin Platform
```

## Migration Timeline

### Week 1-2: Setup and Planning
- [ ] Create separate project directories
- [ ] Set up shared package structure
- [ ] Configure build and deployment scripts
- [ ] Update backend CORS for multiple origins

### Week 3-4: Shop Management App
- [ ] Migrate existing `/app` routes
- [ ] Extract and adapt components
- [ ] Test authentication flow
- [ ] Deploy to staging environment

### Week 5-6: Customer Website
- [ ] Build customer-facing pages
- [ ] Implement ordering system
- [ ] Create customer authentication
- [ ] Integrate with existing backend APIs

### Week 7-8: Admin Platform
- [ ] Migrate existing `/admin` routes
- [ ] Adapt admin components
- [ ] Test admin authentication
- [ ] Deploy to staging environment

### Week 9-10: Integration and Testing
- [ ] End-to-end testing across all apps
- [ ] Performance optimization
- [ ] Security review
- [ ] Production deployment

## Benefits of Separation

1. **Scalability**: Each app can be scaled independently
2. **Security**: Better isolation between user types
3. **Performance**: Smaller bundle sizes, faster loading
4. **Maintenance**: Easier to maintain and update
5. **Team Organization**: Different teams can work on different apps
6. **SEO**: Better SEO for customer-facing website
7. **Deployment**: Independent deployment cycles

## Shared Resources

### UI Components
- Button, Input, Modal components
- Navigation components
- Form components
- Data display components

### Utilities
- API client configuration
- Authentication helpers
- Date/time utilities
- Validation schemas

### Types
- TypeScript interfaces
- API response types
- Database model types

## Implementation Commands

### Step 1: Setup Project Structure
```bash
# Make scripts executable
chmod +x scripts/setup-separate-deployments.sh
chmod +x scripts/migrate-existing-code.sh
chmod +x scripts/create-env-configs.sh

# Run setup
./scripts/setup-separate-deployments.sh
```

### Step 2: Migrate Existing Code
```bash
# Migrate existing code to new structure
./scripts/migrate-existing-code.sh
```

### Step 3: Create Environment Configurations
```bash
# Create environment files
./scripts/create-env-configs.sh
```

### Step 4: Install Dependencies
```bash
# Install dependencies for each app
cd shop-management-app && npm install && cd ..
cd customer-website && npm install && cd ..
cd admin-platform && npm install && cd ..
```

### Step 5: Update Backend CORS
Update your Go backend CORS configuration to allow the new origins:
```go
AllowOrigins: []string{
    "http://localhost:3001", // Shop Management
    "http://localhost:3002", // Customer Website
    "http://localhost:3003", // Admin Platform
    "https://shop.yourdomain.com",
    "https://yourdomain.com",
    "https://admin.yourdomain.com",
}
```

### Step 6: Development Testing
```bash
# Start all development servers
./scripts/dev-all.sh
```

### Step 7: Production Deployment
```bash
# Build and deploy with Docker
cd deployment
docker-compose up -d
```

## Domain Configuration

### DNS Records
```
A     yourdomain.com          → Your server IP
A     shop.yourdomain.com     → Your server IP
A     admin.yourdomain.com    → Your server IP
A     api.yourdomain.com      → Your server IP
```

### SSL Certificates
Use Let's Encrypt or your preferred SSL provider:
```bash
# Example with certbot
certbot certonly --webroot -w /var/www/html -d yourdomain.com
certbot certonly --webroot -w /var/www/html -d shop.yourdomain.com
certbot certonly --webroot -w /var/www/html -d admin.yourdomain.com
certbot certonly --webroot -w /var/www/html -d api.yourdomain.com
```

## Next Steps

1. **Immediate**: Create project structure and shared packages
2. **Short-term**: Begin migration of shop management app
3. **Medium-term**: Build customer website
4. **Long-term**: Optimize and enhance each application independently

This separation will provide a solid foundation for scaling the platform and improving user experience across different user types.
