// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Define your models here
// Example:
model Communication {
  id         String   @id @default(uuid())
  merchantId String
  userId     String?
  type       String   // 'email' or 'sms'
  recipient  String
  subject    String?
  content    String
  status     String   // 'pending', 'sent', 'failed'
  metadata   Json?
  sentAt     DateTime?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}
