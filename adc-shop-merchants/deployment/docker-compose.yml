# Docker Compose configuration for separate deployments
version: '3.8'

services:
  # Backend API Service
  backend-api:
    build:
      context: ../restaurant-backend
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - PORT=8080
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_URL=redis://redis:6379
      - CORS_ALLOWED_ORIGINS=https://shop.yourdomain.com,https://yourdomain.com,https://admin.yourdomain.com
    depends_on:
      - redis
      - postgres
    networks:
      - app-network
    restart: unless-stopped

  # Shop Management App
  shop-management:
    build:
      context: ../shop-management-app
      dockerfile: Dockerfile
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_APP_NAME=Shop Management
      - NEXT_PUBLIC_API_URL=https://api.yourdomain.com/api/v1
      - NEXTAUTH_URL=https://shop.yourdomain.com
      - NEXTAUTH_SECRET=${SHOP_NEXTAUTH_SECRET}
      - BACKEND_URL=http://backend-api:8080
    depends_on:
      - backend-api
    networks:
      - app-network
    restart: unless-stopped

  # Customer Website
  customer-website:
    build:
      context: ../customer-website
      dockerfile: Dockerfile
    ports:
      - "3002:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_APP_NAME=Customer Portal
      - NEXT_PUBLIC_API_URL=https://api.yourdomain.com/api/v1
      - NEXTAUTH_URL=https://yourdomain.com
      - NEXTAUTH_SECRET=${CUSTOMER_NEXTAUTH_SECRET}
      - BACKEND_URL=http://backend-api:8080
      - NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=${GOOGLE_MAPS_API_KEY}
    depends_on:
      - backend-api
    networks:
      - app-network
    restart: unless-stopped

  # Admin Platform
  admin-platform:
    build:
      context: ../admin-platform
      dockerfile: Dockerfile
    ports:
      - "3003:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_APP_NAME=Admin Platform
      - NEXT_PUBLIC_API_URL=https://api.yourdomain.com/api/v1
      - NEXTAUTH_URL=https://admin.yourdomain.com
      - NEXTAUTH_SECRET=${ADMIN_NEXTAUTH_SECRET}
      - BACKEND_URL=http://backend-api:8080
    depends_on:
      - backend-api
    networks:
      - app-network
    restart: unless-stopped

  # Redis for caching and queues
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - app-network
    restart: unless-stopped

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - shop-management
      - customer-website
      - admin-platform
      - backend-api
    networks:
      - app-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  app-network:
    driver: bridge
