# ADC Restaurant Platform

This is a [Next.js](https://nextjs.org) project for the ADC Restaurant Platform, a comprehensive solution for restaurant management.

## Getting Started

### Prerequisites

- [Node.js](https://nodejs.org/) (v18 or later)
- [Bun](https://bun.sh/) for faster package management and running
- [Docker](https://www.docker.com/) and [Docker Compose](https://docs.docker.com/compose/) (optional, for database)
- [PostgreSQL](https://www.postgresql.org/) (if not using Docker)

### Setup

1. Clone the repository
2. Copy `.env.example` to `.env` and update the values
3. Install dependencies:

```bash
make install
# or
bun install
```

4. Set up the database:

```bash
# Using Docker (recommended)
make docker-up

# Or manually set up PostgreSQL and then run
make db-setup
make db-migrate
make db-seed
```

### Running the Development Servers

You can use the Makefile to run both frontend and backend servers:

```bash
# Run both frontend and backend servers
make dev

# Run only the frontend server
make frontend

# Run only the backend server (when implemented)
make backend
```

Alternatively, you can use the standard commands:

```bash
bun run dev
```

Open [http://localhost:4000](http://localhost:4000) with your browser to see the result.

You can start editing the pages in the `src/app` directory. The pages auto-update as you edit the files.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Development Commands

The Makefile provides several useful commands for development:

```bash
# Show all available commands
make help

# Database commands
make db-setup     # Generate Prisma client
make db-migrate   # Run database migrations
make db-seed      # Seed the database
make db-studio    # Open Prisma Studio

# Docker commands
make docker-up    # Start Docker containers
make docker-down  # Stop Docker containers

# Build commands
make build        # Build for production
make clean        # Clean build artifacts
```

## Project Structure

- `/src/app` - Next.js application pages and API routes
- `/src/components` - React components
- `/src/lib` - Utility functions and shared code
- `/src/services` - Business logic and data access
- `/prisma` - Database schema and migrations

## Deployment

The application is designed to be deployed in multiple parts:

1. **Main Application**: Deploy on Vercel or similar platform
2. **Order Service API**: Deploy on a separate server (when implemented)
3. **Database**: Use a managed PostgreSQL service

For the main application, the easiest way to deploy is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out the [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
